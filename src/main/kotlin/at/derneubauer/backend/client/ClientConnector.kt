package at.derneubauer.backend.client

import at.derneubauer.backend.db.issue.AcceptedIssueDo
import at.derneubauer.backend.db.issue.IssueDo
import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.*
import at.derneubauer.backend.web.error.NeubauerException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Controller
import org.springframework.web.HttpRequestHandler
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import java.io.IOException
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.*
import java.util.zip.CRC32
import java.util.zip.CheckedInputStream
import jakarta.servlet.ServletException
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse


enum class Action private constructor(val actionName: String) {
    ASSIGN("assignIssue"),
    CLOSE("closeIssue"),
    ADD_ATTACHMENT("addAttachment"),
    INIT("init"),
    REOPEN("reopenIssue"),
    REVOKE("revokeIssue"),
    REQ_PIC("requestPictures"),
    REQ_PERSON_PIC("requestPersonPictures"),
    REQ_PIC_RESPONSE("requestPicturesResponse"),
    REQ_PERSON_PIC_RESPONSE("requestPersonPicturesResponse"),
    JSON("json"),
    REQ_TIME_ENTRIES("requestTimeEntries"),
    SHARE_DATA("shareData"),
    REASSIGN("reassignIssue"),
    ADD_PERSON_ATTACHMENT("addPersonAttachment"),
    PERSON_ATTACHMENT_FINISHED("personAttachmentFinished"),
    RETRIEVE_DATA("retrieveData"),
    RETRIEVE_DATA_RESPONSE("retrieveDataResponse"),
    GET_EVENTS("getEvents"),
    UNKNOWN("unknown");


    companion object {
        fun forActionName(actionName: String?): Action {
            for (action in values()) {
                if (action.actionName == actionName) {
                    return action
                }
            }
            return UNKNOWN
        }
    }
}

@Controller
class ClientConnector(private val userService: UserService,
                      private val acceptedIssueService: AcceptedIssueService,
                      private val issueService: IssueService,
                      private val permissionService: PermissionService,
                      private val imageService: ImageService,
                      private val loggedInUserResolver: LoggedInUserResolver) : HttpRequestHandler {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    @Throws(ServletException::class, IOException::class)
    @RequestMapping(value = ["/neubauer-web/ClientConnector"])
    @ResponseBody
    override fun handleRequest(request: HttpServletRequest, response: HttpServletResponse) {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.USE_APP, user)

        try {
            val action = request.getParameter("action")

            var lastEventId: Long = 0
            try {
                lastEventId = request.getParameter("lastEventId").toLong()
            } catch (e: NumberFormatException) {
                log.error("lastEventId sent by client is not numeric: {}", request.getParameter("lastEventId"))
            }

            if (request is MultipartHttpServletRequest) {
                handleMultipart(user, action, request.parameterMap, request)
                return
            }

            log.debug("Client acknowledged events until ID {} for user {}.", lastEventId, user.username)

            handleAction(user, action, request.parameterMap)

            collectEvents(lastEventId, response)
        } catch(ne: NeubauerException) {
            log.error(ne.message, ne)
            response.status = ne.statusCode
        } catch (e: Exception) {
            log.error(e.message, e)
            response.status = HttpServletResponse.SC_INTERNAL_SERVER_ERROR
        }

    }

    private fun collectEvents(lastEventId: Long, response: HttpServletResponse) {
        val lastEventTimestamp: Long = if (lastEventId > 0) lastEventId / 100000 else 0
        val since = ZonedDateTime.ofInstant(Instant.ofEpochMilli(lastEventTimestamp), ZoneOffset.UTC)

        var runningIdPart: Long = if (lastEventId > 0) lastEventId % 100000 else 0 // counts from 0 to 99.999, then starts over at 0

        val events = findCreatedAndUpdatedTechniciansSince(since)
            .plus(findCreatedAndUpdatedIssuesSince(since))
            .plus(findAcceptedOrPutBackIssueSince(since))
            .sortedBy { it.timestamp }
            .map {
                runningIdPart = incrementRunningId(runningIdPart)
                it.apply { idPostfix = runningIdPart }
            }
            .sortedBy { it.eventId() } // in case of running id overflow (99.999 -> 0) and same timestamp the items need to be sorted again

        for (event in events) {
            response.characterEncoding = "UTF-8"
            response.writer.println(event.asFormattedString())
        }
    }

    private fun incrementRunningId(id: Long): Long {
        if (id > 99999) {
            return 0
        }

        return id + 1
    }

    private fun handleAction(user: UserDo, action: String?, parameters: Map<String, Array<String>>, file: MultipartFile? = null) {
        when (Action.forActionName(action)) {
            Action.ASSIGN -> {
                permissionService.ensurePermission(NeubauerPermission.ACCEPT_ISSUE, user)
                val issueId = getLongParameter("issueId", parameters)
                acceptedIssueService.acceptIssue(user.id, issueId)
            }

            Action.CLOSE -> {
                permissionService.ensurePermission(NeubauerPermission.SET_ISSUE_TECHNICIAN_DONE, user)
                val issueId = getLongParameter("issueId", parameters)
                issueService.setIssueToTechnicianDone(issueId, user.id)
            }

            Action.ADD_ATTACHMENT -> {
                val issueId = getLongParameter("issueId", parameters)
                val attachment = file ?: throw NeubauerException("No file to add as attachment")

                if (parameters.containsKey("attachmentType")) {
                    val attachmentType = getStringParameter("attachmentType", parameters)

                    if (attachmentType == "PIC") {
                        imageService.uploadIssueImage(issueId, attachment)
                    } else {
                        throw NeubauerException("Unhandled attachmentType: ${attachmentType}")
                    }
                } else {
                    throw NeubauerException("Unhandled missing attachmentType")
                }
            }

            Action.ADD_PERSON_ATTACHMENT -> {
                val personId = getLongParameter("personId", parameters)
                val attachment = file ?: throw NeubauerException("No file to add as attachment")

                if (parameters.containsKey("attachmentType")) {
                    val attachmentType = getStringParameter("attachmentType", parameters)

                    if (attachmentType == "PIC") {
                        imageService.uploadUserImage(personId, attachment)
                    } else {
                        throw NeubauerException("Unhandled attachmentType: ${attachmentType}")
                    }
                } else {
                    throw NeubauerException("Unhandled missing attachmentType")
                }
            }

            Action.PERSON_ATTACHMENT_FINISHED -> {
                // ignoring for now
                // only seems to send email in legacy system
            }

            Action.GET_EVENTS -> {
                // nothing to do
            }

            Action.INIT -> {
                // nothing to do
            }

            Action.REOPEN -> {
                permissionService.ensurePermission(NeubauerPermission.REOPEN_ISSUE, user)
                val issueId = getLongParameter("issueId", parameters)
                issueService.reopenIssue(user, issueId)
            }

            Action.REASSIGN -> {
                permissionService.ensurePermission(NeubauerPermission.REOPEN_ISSUE, user)
                permissionService.ensurePermission(NeubauerPermission.ACCEPT_ISSUE, user)
                val issueId = getLongParameter("issueId", parameters)
                issueService.reopenIssue(user, issueId)
                acceptedIssueService.acceptIssue(user.id, issueId)
            }

            Action.REVOKE -> {
                permissionService.ensurePermission(NeubauerPermission.PUT_BACK_ISSUE, user)
                val issueId = getLongParameter("issueId", parameters)
                acceptedIssueService.putBackIssue(user.id, issueId)
            }

//            Action.REQ_PIC -> {
                // looks like this could be used to trigger the picture upload of another person from the app
//            }

            Action.REQ_PIC_RESPONSE -> {
                // ignoring for now
                // only seems to send email in legacy system
            }

            Action.REQ_PERSON_PIC_RESPONSE -> {
                // ignoring for now
                // only seems to send email in legacy system
            }

//            Action.REQ_TIME_ENTRIES -> {
//                issueId = getLongParameter("issueId", parameters)
////                issueManagementService!!.requestTimeEntries(userName, issueId)
//            }

//            Action.JSON -> {
//                entity = getStringParameter("entity", parameters)
//                json = getStringParameter("json", parameters)
////                jsonService!!.handleJson(entity, json)
//            }

            Action.SHARE_DATA -> {
                // ignoring for now
                // only seems to send email in legacy system
            }

//            Action.RETRIEVE_DATA -> return null//issueManagementService!!.handleRetrieveData(userName)

//            Action.RETRIEVE_DATA_RESPONSE -> {
//                val attachmentId = getLongParameter("attachmentId", parameters)
////                issueManagementService!!.confirmUntransmittedAttachments(attachmentId)
//            }

            else -> {
                log.warn("Got unknown or invalid action: user {}, action {}, params {}", user.username, action)
            }
        }
    }

    private fun findCreatedAndUpdatedTechniciansSince(since: ZonedDateTime): List<ResponseLine> {
        val newlyCreatedTechnicians = userService.findTechniciansCreatedSince(since)
        val updatedTechnicians = userService.findTechniciansUpdatedSince(since)

        val updatedIssuesWithoutDuplicates = updatedTechnicians.minus(newlyCreatedTechnicians)

        return newlyCreatedTechnicians.map {
            convertUserToResponseString(it, "i")
        }.plus(updatedIssuesWithoutDuplicates.map {
            convertUserToResponseString(it, "u")
        })
    }

    private fun convertUserToResponseString(user: UserDo, insertOrUpdatePrefix: String): ResponseLine {
        //i{291};4;Person;[username;admin|firstname;Anton|lastname;Administrator|roles;SUPERVISOR,ADMINISTRATOR,USER,TECHNICIAN|phone;43676845438304]
        val phoneNumber = user.phoneNumber ?: ""
        val updatedAtTimestamp = user.updatedAt.toInstant().toEpochMilli()

        val sb = StringBuilder().apply {
            append("${user.id};")
            append("Person;")
            append("[")
            append("username;${user.username}|")
            append("firstname;${user.firstName}|")
            append("lastname;${user.lastName}|")
            append("roles;${user.role}|")
            append("phone;${phoneNumber}")
            append("]")
        }

        return cleanupTemplate(
            insertOrUpdatePrefix,
            updatedAtTimestamp,
            sb.toString()
        )
    }

    private fun findCreatedAndUpdatedIssuesSince(since: ZonedDateTime): List<ResponseLine> {
        val newlyCreatedIssues = issueService.findOpenIssuesCreatedSince(since)
        val updatedIssues = issueService.findOpenIssuesUpdatedSince(since)
        val recentlyClosedButReopenedIssues = issueService.findStatusHistoryForIssuesThatHaveBeenReopenedAndAreStillOpen(since)

        val updatedIssuesWithoutDuplicates = updatedIssues
            .minus(newlyCreatedIssues)
            .minus(recentlyClosedButReopenedIssues)

        val closedIssues = issueService.findIssuesThatHaveBeenClosedSince(since)

        return newlyCreatedIssues.map {
            convertIssueToResponseString(it, "i")
        }.plus(recentlyClosedButReopenedIssues.map {
            convertIssueToResponseString(it, "i")
        }).plus(updatedIssuesWithoutDuplicates.map {
            convertIssueToResponseString(it, "u")
        }).plus(closedIssues.map {
            convertIssueToDeleteResponseString(it)
        })
}

    private fun convertIssueToResponseString(issue: IssueDo, insertOrUpdatePrefix: String): ResponseLine {
        val address = issue.address ?: ""
        val suggestedDate = issue.suggestedDate ?: ""
        val contactPerson = issue.contactPerson ?: ""
        val description = issue.description ?: ""
        val notes = issue.note ?: ""
        val createdAtTimestamp = issue.createdAt.toInstant().toEpochMilli()
        val updatedAtTimestamp = issue.updatedAt.toInstant().toEpochMilli()

        val status = convertStatusToResponseString(issue)

        val sb = StringBuilder().apply {
            append("${issue.id};")
            append("Issue;")
            append("[")
            append("externalId;${issue.externalId}|")
            append("customerName;${address}|")
            append("appointment;${suggestedDate}|")
            append("contactPerson;${contactPerson}|")
            append("workload;${description}|")
            append("creationDate;${createdAtTimestamp}|")
            append("status;${status}|")
            append("notes;${notes}")
            append("]")
        }

        return cleanupTemplate(
            insertOrUpdatePrefix,
            updatedAtTimestamp,
            sb.toString()
        )
    }

    private fun convertIssueToDeleteResponseString(issue: IssueDo): ResponseLine {
        val updatedAtTimestamp = issue.updatedAt.toInstant().toEpochMilli()

        val sb = StringBuilder().apply {
            append("${issue.id};")
            append("Issue")
        }

        return cleanupTemplate(
            "d",
            updatedAtTimestamp,
            sb.toString()
        )
    }

    private fun findAcceptedOrPutBackIssueSince(since: ZonedDateTime): List<ResponseLine> {
        return acceptedIssueService.allAcceptedIssuesSince(since).map {
            convertAcceptedIssueToResponseString(it)
        }
    }

    private fun convertAcceptedIssueToResponseString(acceptedIssue: AcceptedIssueDo): ResponseLine {
        val actionType = if (acceptedIssue.accepted) "assignIssue" else "revokeIssue"
        val createdAtTimestamp = acceptedIssue.createdAt.toInstant().toEpochMilli()

        val sb = StringBuilder().apply {
            append("${actionType};")
            append("[")
            append("issueId;${acceptedIssue.issueId}|")
            append("personId;${acceptedIssue.technicianId}")
            append("]")
        }

        //"a{144};assignIssue;[issueId;6|personId;1]",
        //"a{144};revokeIssue;[issueId;6|personId;1]",
        return cleanupTemplate(
            "a",
            createdAtTimestamp,
            sb.toString()
        )
    }

    private fun cleanupTemplate(prefix: String, timestamp: Long, content: String): ResponseLine {
        return ResponseLine(
            prefix,
            timestamp,
            content
        )
    }

    private fun convertStatusToResponseString(it: IssueDo): String {
        return when(it.status) {
            NeubauerIssueStatus.NEW.toString() -> "NEW"
            NeubauerIssueStatus.REOPENED.toString() -> "REOPENED"
            NeubauerIssueStatus.PLANNED.toString() -> "NEW"
            NeubauerIssueStatus.ACCEPTED.toString() -> "ASSIGNED" //"OWN"
            NeubauerIssueStatus.DONE_TECHNICIAN.toString() -> "CLOSED"
            NeubauerIssueStatus.CLOSED.toString() -> "CLOSED"
            else -> {
                log.error("Unable to map issue status for response ${it.status}")
                throw NeubauerException("Unhandled status ${it.status}")
            }
        }
    }

    private fun getFormData(parameters: Map<String, Array<String>>): Map<String, String> {
        val formData = HashMap<String, String>()
        if (parameters.containsKey("notes")) {
            formData["notes"] = getStringParameter("notes", parameters)
        }
        return formData
    }

    private fun getLongParameter(key: String, parameters: Map<String, Array<String>>): Long {
        val stringArray = parameters[key] ?: throw NeubauerException("Unable to parse parameter ${key} as Long")
        return java.lang.Long.parseLong(stringArray[0])
    }

    private fun getStringParameter(key: String, parameters: Map<String, Array<String>>): String {
        val stringArray = parameters[key] ?: throw NeubauerException("Unable to parse parameter ${key} as String")
        val parameter = stringArray[0]
        return parameter.replace("#n#".toRegex(), "\n")
    }

    private fun handleMultipart(user: UserDo, action: String?, parameters: Map<String, Array<String>>, request: HttpServletRequest) {
        val multiPartRequest = request as? MultipartHttpServletRequest ?: throw RuntimeException("Unable to parse multi part request")
        val file = multiPartRequest.getFile("file")

        val expectedChecksum = getLongParameter("crc32Checksum", parameters)
        val actualChecksum = file?.let { crc32Checksum(it) }

        if (expectedChecksum != actualChecksum) {
            throw NeubauerException(
                String.format(
                    "Transmitted CRC32 checksum %1d of file does not match locally calculated CRC32 checksum %2d",
                    expectedChecksum, actualChecksum
                )
            )
        }

        handleAction(user, action, parameters, file)
    }

    private fun crc32Checksum(file: MultipartFile): Long {
        file.inputStream.use { inputStream ->
            CheckedInputStream(inputStream, CRC32()).use { cis ->
                val buf = ByteArray(128)
                while (cis.read(buf) >= 0) {}

                return cis.checksum.value
            }
        }
    }
}

data class ResponseLine(
    val prefix: String,
    val timestamp: Long,
    val content: String,
    var idPostfix: Long = 0
) {
    fun asFormattedString(): String {
        return """${prefix}{${eventId()}};${content}"""
    }

    fun eventId(): String {
        val idPostFix = idPostfix.toString().takeLast(5).padStart(5, '0')
        return "${timestamp}${idPostFix}"
    }
}
