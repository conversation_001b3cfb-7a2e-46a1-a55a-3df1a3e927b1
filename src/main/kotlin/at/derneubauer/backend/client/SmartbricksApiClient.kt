package at.derneubauer.backend.client

import at.derneubauer.backend.config.NeubauerConfig
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClient

@Service
class SmartbricksApiClient(
    private val webClientBuilder: WebClient.Builder,
    private val neubauerConfig: NeubauerConfig,
) {
    private val webClient = webClientBuilder
        .baseUrl(neubauerConfig.smartbricks!!.baseUrl)
        .build()

    fun createUser(request: CreateUserRequest): CreateUserResponse = webClient.post()
        .uri("/api/users")
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .retrieve()
        .bodyToMono(CreateUserResponse::class.java)
        .block()!!

    fun getUser(userId: String): User = webClient.get()
        .uri("/api/users/{id}", userId)
        .retrieve()
        .bodyToMono(User::class.java)
        .block()!!
}

data class CreateUserRequest(val name: String, val email: String)
data class CreateUserResponse(val id: String, val name: String)
data class User(val id: String, val name: String, val email: String)