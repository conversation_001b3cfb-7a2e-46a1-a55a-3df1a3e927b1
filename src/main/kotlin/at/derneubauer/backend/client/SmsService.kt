package at.derneubauer.backend.client

import at.derneubauer.backend.db.issue.AcceptedIssueMapper
import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.db.user.UserMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class SmsService(private val smsClient: SmsAtClient,
                 private val acceptedIssueMapper: AcceptedIssueMapper,
                 private val userMapper: UserMapper) {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    fun requestIssueImages(issueId: Long, requestingUser: UserDo) {
        val message = generateMessageToRequestIssueImages(issueId, requestingUser.id)

        val acceptedIssues = acceptedIssueMapper.findByIssueId(issueId)

        acceptedIssues.forEach { acceptedIssue ->
            sendSmsToUser(acceptedIssue.technicianId, message)
        }
    }

    fun requestUserImages(userId: Long, requestingUser: UserDo) {
        val message = generateMessageToRequestUserImages(userId, requestingUser.id)
        sendSmsToUser(userId, message)
    }

    private fun sendSmsToUser(userId: Long, message: String) {
        val user = userMapper.findById(userId)
        val recipientNumber = user?.phoneNumber

        if (user == null) {
            log.error("Unable to send SMS to user with ID ${userId} because the user could not be found")
            return
        }

        if (recipientNumber == null) {
            log.error("Unable send SMS to user with ID ${userId} because he/she does not have a phone number")
            return
        }

        smsClient.sendSms(recipientNumber, message)
    }

    private fun generateMessageToRequestIssueImages(issueId: Long, requestingUserId: Long): String {
        return "[mo-servx]\naction:requestPictures\nissueId:${issueId}\nrequesterId:${requestingUserId}"
    }

    private fun generateMessageToRequestUserImages(userId: Long, requestingUserId: Long): String {
        return "[mo-servx]\naction:requestPersonPictures\npersonId:${userId}\nrequesterId:${requestingUserId}"
    }
}
