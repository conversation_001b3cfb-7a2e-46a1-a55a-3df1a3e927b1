package at.derneubauer.backend.client

import at.derneubauer.backend.config.NeubauerConfig
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.RestTemplate

@Service
class SmsAtClient(private val restTemplateBuilder: RestTemplateBuilder,
                  private val neubauerConfig: NeubauerConfig) {

    fun sendSms(recipientNumber: String, message: String) {
        try {
            val request = SmsAtRequest(listOf(recipientNumber), message)
            val response = restTemplate().exchange("${neubauerConfig.sms!!.endpointUrl}smsmessaging/text", HttpMethod.POST, entityWithAuth(request), SmsAtResponse::class.java)

            if (!(response.body?.statusCode == 2001 || response.body?.statusCode == 2000)) {
                throw NeubauerException("Unable to send SMS, status returned by sms.at API: ${response.body?.statusCode}: ${response.body?.statusMessage}")
            }
        } catch (e: HttpClientErrorException) {
            throw NeubauerException("Unable to send SMS", e)
        }
    }

    private fun restTemplate(): RestTemplate {
        return restTemplateBuilder.build()
    }

    private fun entityWithAuth(request: Any? = null): HttpEntity<Any?> {
        val headers = HttpHeaders()
        headers.set("Authorization", "Bearer ${neubauerConfig.sms!!.accessToken}")
        headers.set("Content-Type", "application/json")

        return HttpEntity<Any?>(request, headers)
    }
}

data class SmsAtRequest(
    val recipientAddressList: List<String>,
    val messageContent: String,
    val contentCategory: String = "informational",
    val maxSmsPerMessage: Int = 1
)

data class SmsAtResponse(
    val statusCode: Int,
    val statusMessage: String
)
