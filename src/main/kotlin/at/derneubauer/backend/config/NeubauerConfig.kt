package at.derneubauer.backend.config

import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service

@Service
@ConfigurationProperties(prefix = "neubauer")
data class NeubauerConfig(
    var offa: NeubauerOffaConfig? = null,
    var baseUrl: String = "",
    var dev: NeubauerDevelopmentConfig? = null,
    var spring: NeubauerSpringConfig? = null,
    var filestore: NeubauerFilestoreConfig? = null,
    var issueImageFilestore: NeubauerIssueScanFilestoreConfig? = null,
    var issueDocumentFilestore: NeubauerIssueDocumentFilestoreConfig? = null,
    var sms: NeubauerSmsConfig? = null,
    var smartbricks: NeubauerSmartbricksConfig? = null,
)

@Service
data class NeubauerOffaConfig(
    var user: String = "",
    var password: String = ""
)

@Service
data class NeubauerDevelopmentConfig(
    var testSupportEndpointsActive: Boolean? = false
)

@Service
@ConfigurationProperties(prefix = "spring")
data class NeubauerSpringConfig(
    var data: NeubauerSpringDataConfig? = null
)

@Service
data class NeubauerSpringDataConfig(
    var redis: NeubauerRedisConfig? = null
)

@Service
data class NeubauerRedisConfig(
    var host: String = "",
    var port: Int = 0
)

@Service
data class NeubauerFilestoreConfig(
    var s3Region: String = "",
    var s3Bucket: String = "",
    var s3AccessKeyId: String = "",
    var s3SecretAccessKey: String = "",
    var s3Endpoint: String? = null,
    var pathStyleAccessEnabled: Boolean = false
)

@Service
data class NeubauerIssueScanFilestoreConfig(
    var s3Region: String = "",
    var s3Bucket: String = "",
    var s3AccessKeyId: String = "",
    var s3SecretAccessKey: String = "",
    var s3Endpoint: String? = null,
    var pathStyleAccessEnabled: Boolean = false,
    var issuePrefixToRemove: String = "",
    var issueScanFileExtension: String = ""
)

@Service
data class NeubauerIssueDocumentFilestoreConfig(
    var s3Region: String = "",
    var s3Bucket: String = "",
    var s3AccessKeyId: String = "",
    var s3SecretAccessKey: String = "",
    var s3Endpoint: String? = null,
    var pathStyleAccessEnabled: Boolean = false
)

@Service
data class NeubauerSmsConfig(
    var endpointUrl: String = "",
    var accessToken: String = ""
)

@Service
data class NeubauerSmartbricksConfig(
    var baseUrl: String = "",
)

@Component
class NeubauerConfigurationValidator(val config: NeubauerConfig) {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    @EventListener
    fun handleContextRefresh(event: ContextRefreshedEvent) {
        log.debug("neubauer.baseUrl: ${config.baseUrl}")
        log.debug("neubauer.dev.testSupportEndpointsActive: ${config.dev?.testSupportEndpointsActive}")
        log.debug("spring.redis.host: ${config.spring?.data?.redis?.host}")
        log.debug("spring.redis.port: ${config.spring?.data?.redis?.port}")
        log.debug("neubauer.filestore.s3Region: ${config.filestore?.s3Region}")
        log.debug("neubauer.filestore.s3Bucket: ${config.filestore?.s3Bucket}")
        log.debug("neubauer.filestore.s3Endpoint: ${config.filestore?.s3Endpoint}")
        log.debug("neubauer.filestore.s3AccessKeyId: <redacted>")
        log.debug("neubauer.filestore.s3SecretAccessKey: <redacted>")
        log.debug("neubauer.sms.endpointUrl: ${config.sms?.endpointUrl}")
        log.debug("neubauer.sms.accessToken: <redacted>")
        log.debug("neubauer.smartbricks.baseUrl: ${config.smartbricks?.baseUrl}")

        if (StringUtils.isBlank(config.baseUrl)) {
            throw RuntimeException("Config parameter neubauer.baseUrl is not set!")
        }

        if (config.dev?.testSupportEndpointsActive == true) {
            log.warn("Test support endpoints are active. If this is not a production system you can ignore this warning, otherwise please disable neubauer.dev.testSupportEndpointsActive!")
        }

        if (StringUtils.isBlank(config.spring?.data?.redis?.host)) {
            throw RuntimeException("Config parameter spring.redis.host is not set!")
        }

        if (config.spring?.data?.redis?.port == null) {
            throw RuntimeException("Config parameter spring.redis.port is not set!")
        }

        if (StringUtils.isBlank(config.offa?.user)) {
            throw RuntimeException("Config parameter neubauer.offa.user is not set!")
        }

        if (StringUtils.isBlank(config.offa?.password)) {
            throw RuntimeException("Config parameter neubauer.offa.password is not set!")
        }

        if (StringUtils.isBlank(config.filestore?.s3Region)) {
            throw RuntimeException("Config parameter neubauer.filestore.s3Region is not set!")
        }

        if (StringUtils.isBlank(config.filestore?.s3Bucket)) {
            throw RuntimeException("Config parameter neubauer.filestore.s3Bucket is not set!")
        }

        if (StringUtils.isBlank(config.filestore?.s3AccessKeyId)) {
            throw RuntimeException("Config parameter neubauer.filestore.s3AccessKeyId is not set!")
        }

        if (StringUtils.isBlank(config.filestore?.s3SecretAccessKey)) {
            throw RuntimeException("Config parameter neubauer.filestore.s3SecretAccessKey is not set!")
        }

        if (StringUtils.isNotBlank(config.filestore?.s3Endpoint)) {
            log.warn("Config parameter neubauer.filestore.s3Endpoint is set. May not be using the correct endpoint (please check above).")
        }

        if (StringUtils.isBlank(config.sms?.endpointUrl)) {
            throw RuntimeException("Config parameter neubauer.sms.endpointUrl is not set!")
        }

        if (StringUtils.isBlank(config.sms?.accessToken)) {
            throw RuntimeException("Config parameter neubauer.sms.accessToken is not set!")
        }

        if (StringUtils.isBlank(config.smartbricks?.baseUrl)) {
            throw RuntimeException("Config parameter neubauer.smartbricks.baseUrl is not set!")
        }
    }
}
