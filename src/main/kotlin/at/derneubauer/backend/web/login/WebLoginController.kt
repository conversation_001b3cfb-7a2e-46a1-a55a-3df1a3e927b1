package at.derneubauer.backend.web.login

import at.derneubauer.backend.rest.error.NoLoggedInUserFoundException
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import at.derneubauer.backend.security.LoggedInUserResolver
import org.springframework.boot.info.BuildProperties
import org.springframework.ui.Model

@Controller
class WebLoginController(
    private val userResolver: LoggedInUserResolver,
    private val buildInfo: BuildProperties,
) {

    @GetMapping("/login")
    fun login(model: Model): String {
        model.addAttribute("version", buildInfo.version)
        return "login"
    }

    @GetMapping("/")
    fun redirectFromRoot(): String {
        try {
            userResolver.resolveLoggedInUser()
            return "redirect:/dashboard"
        } catch (e: NoLoggedInUserFoundException) {}

        return "redirect:/login"
    }

}
