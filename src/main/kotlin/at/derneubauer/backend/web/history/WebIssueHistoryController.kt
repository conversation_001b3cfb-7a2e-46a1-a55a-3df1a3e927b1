package at.derneubauer.backend.web.history

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.*
import at.derneubauer.backend.util.ColorMappings
import at.derneubauer.backend.util.NeubauerDateFormatter
import org.springframework.context.MessageSource
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import java.util.*

@Controller
class WebIssueHistoryController(private val userResolver: LoggedInUserResolver,
                                private val permissionService: PermissionService,
                                private val issueService: IssueService,
                                private val messageSource: MessageSource,
                                private val colorMappings: ColorMappings) {

    @GetMapping("/admin/issues/{issueId}/history")
    fun historyForIssue(@PathVariable issueId: Long, model: Model, locale: Locale): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_HISTORY, user)

        model.addAttribute("issueId", issueId)
        model.addAttribute("historyList", issueService.historyForIssue(issueId).map { it -> IssueHistoryViewModel(it, locale, messageSource, colorMappings) })

        return "history/list"
    }
}

class IssueHistoryViewModel(private val issue: IssueHistoryDto, private val locale: Locale, private val messageSource: MessageSource, private val colorMappings: ColorMappings) {

    fun getIssueStatus(): String? {
        val issueStatus = issue.issueStatus ?: return null

        return messageSource.getMessage(
            issueStatus.localizationKey,
            arrayOf(),
            locale)
    }

    fun getUserDisplayName(): String {
        return issue.requestingUserDisplayName
    }

    fun getTimestamp(): String {
        return NeubauerDateFormatter.dateTimeFormatter.format(issue.timestamp)
    }

    fun getGradientStartColor(): String {
        return colorMappings.forEventType(EventType.ISSUE, issue.issueStatus?.getName()).startColor
    }

    fun getGradientEndColor(): String {
        return colorMappings.forEventType(EventType.ISSUE, issue.issueStatus?.getName()).endColor
    }

    fun getActionText(): String {
        return when (issue.type) {
            IssueHistoryType.ACCEPTED_STATUS -> {
                getAcceptedActionText()
            }
            IssueHistoryType.ISSUE_STATUS -> {
                messageSource.getMessage("issue.history.statusChange", arrayOf(), locale)
            }
        } ?: ""
    }

    private fun getAcceptedActionText(): String? {
        if (issue.requestingUserId == issue.assignedUserId) {
            if (issue.accepted == true) {
                return messageSource.getMessage("issue.history.accepted", arrayOf(), locale)
            } else {
                return messageSource.getMessage("issue.history.putBack", arrayOf(), locale)
            }
        } else if (issue.accepted == false) {
            return messageSource.getMessage("issue.history.revoke", arrayOf("${issue.assignedUserDisplayName}"), locale)
        } else {
            return null
        }
    }
}
