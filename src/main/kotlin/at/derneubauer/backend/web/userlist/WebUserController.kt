package at.derneubauer.backend.web.userlist

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.NeubauerRole
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.UserService
import at.derneubauer.backend.util.InvalidPasswordException
import at.derneubauer.backend.web.error.NeubauerException
import org.hibernate.validator.constraints.Length
import org.springframework.context.MessageSource
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.validation.BindingResult
import org.springframework.validation.FieldError
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.servlet.mvc.support.RedirectAttributes
import java.io.Serializable
import java.util.*
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank


@Controller
class WebUserController(val messageSource: MessageSource,
                        val userResolver: LoggedInUserResolver,
                        val permissionService: PermissionService,
                        val userService: UserService) {

    companion object {
        private val springBindingPrefix = "org.springframework.validation.BindingResult"
        private val createFormBinding = "userCreateFormBindingResult"
        private val editFormBinding = "userEditFormBindingResult"
    }

    @GetMapping("/admin/users")
    fun userlist(model: Model): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_USER_LIST, user)
        model.addAttribute("users", userService.getAllUsers())
        return "users/user-list"
    }

    @GetMapping("/admin/users/create")
    fun create(userForm: UserForm, model: Model): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.CREATE_USER, user)

        val formBinding = model.asMap().get(createFormBinding)
        if (formBinding != null) {
            model.addAttribute("${springBindingPrefix}.userForm", formBinding)
        }

        model.addAttribute("roles", NeubauerRole.sortedValues())
        return "users/user-create"
    }

    @PostMapping("/admin/users/create")
    fun create(@Valid @ModelAttribute("userForm") userForm: UserForm, bindingResult: BindingResult, redirectAttributes: RedirectAttributes, locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        val redirectUrl = "redirect:/admin/users/create"
        permissionService.ensurePermission(NeubauerPermission.CREATE_USER, user)
        redirectAttributes.addFlashAttribute(createFormBinding, bindingResult)

        if (bindingResult.hasErrors()) {
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        }

        try {
            userService.createUser(
                userForm.username,
                userForm.password,
                userForm.firstName,
                userForm.lastName,
                userForm.phoneNumber,
                userForm.role
            )
            return redirectBackWithSuccess("user.create.success", locale, redirectAttributes, "redirect:/admin/users")
        } catch (e: InvalidPasswordException) {
            bindingResult.addError(FieldError("userCreateForm", "password", messageSource.getMessage(e.localizationKey, arrayOf(), locale)))
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    @GetMapping("/admin/users/{userId}")
    fun editUser(@PathVariable userId: Long, userEditForm: UserEditForm, model: Model): String {
        val loggedInUser = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_USER_DETAIL, loggedInUser)

        val userToEdit = userService.getUserById(userId)

        model.addAttribute("userId", userToEdit.id)
        model.addAttribute("userDisabled", userToEdit.disabled)

        val formBinding = model.asMap().get(editFormBinding)
        val isInitialLoadOfEditForm = userEditForm.username.isBlank()

        if (formBinding != null) {
            model.addAttribute("${springBindingPrefix}.userEditForm", formBinding)
        } else if (isInitialLoadOfEditForm) {
            userEditForm.username = userToEdit.username
            userEditForm.firstName = userToEdit.firstName
            userEditForm.lastName = userToEdit.lastName
            userEditForm.phoneNumber = userToEdit.phoneNumber ?: ""
            userEditForm.role = userToEdit.role
        }

        model.addAttribute("roles", NeubauerRole.sortedValues())
        return "users/user-edit"
    }

    @PostMapping("/admin/users/{userId}")
    fun edit(@PathVariable userId: Long, @Valid @ModelAttribute("userEditForm") userEditForm: UserEditForm, bindingResult: BindingResult, redirectAttributes: RedirectAttributes, locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        val redirectUrl = "redirect:/admin/users/${userId}"
        permissionService.ensurePermission(NeubauerPermission.EDIT_USER, user)
        redirectAttributes.addFlashAttribute(editFormBinding, bindingResult)

        if (bindingResult.hasErrors()) {
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        }

        try {
            userService.editUser(
                userId,
                userEditForm.username,
                userEditForm.password,
                userEditForm.firstName,
                userEditForm.lastName,
                userEditForm.phoneNumber,
                userEditForm.role
            )
            return redirectBackWithSuccess("user.edit.success", locale, redirectAttributes, redirectUrl)
        } catch (e: InvalidPasswordException) {
            bindingResult.addError(FieldError("userEditForm", "password", messageSource.getMessage(e.localizationKey, arrayOf(), locale)))
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    @PostMapping("/admin/users/{userId}/enable")
    fun enable(@PathVariable userId: Long, redirectAttributes: RedirectAttributes, locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        val redirectUrl = "redirect:/admin/users/${userId}"
        permissionService.ensurePermission(NeubauerPermission.EDIT_USER, user)

        try {
            userService.enableUser(userId)
            return redirectBackWithSuccess("user.enable.success", locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    @PostMapping("/admin/users/{userId}/disable")
    fun disable(@PathVariable userId: Long, redirectAttributes: RedirectAttributes, locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        val redirectUrl = "redirect:/admin/users/${userId}"
        permissionService.ensurePermission(NeubauerPermission.EDIT_USER, user)

        try {
            userService.disableUser(userId)
            return redirectBackWithSuccess("user.disable.success", locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    private fun redirectBackWithSuccess(localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): String {
        return redirectWithMessage("success", localizationKey, locale, redirectAttributes, redirectUrl)
    }

    private fun redirectBackWithError(localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): String {
        return redirectWithMessage("error", localizationKey, locale, redirectAttributes, redirectUrl)
    }

    private fun redirectWithMessage(attributeName: String, localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): String {
        redirectAttributes.addFlashAttribute(attributeName, messageSource.getMessage(localizationKey, arrayOf(), locale))
        return redirectUrl
    }
}

data class UserForm (

    @get:NotBlank(message="{errors.username.notBlank}")
    @get:Length(max=255, message="{errors.input.tooLong}")
    var username: String = "",

    @get:NotBlank(message="{errors.password.notBlank}")
    var password: String = "",

    @get:NotBlank(message="{errors.firstName.notBlank}")
    @get:Length(max=255, message="{errors.input.tooLong}")
    var firstName: String = "",

    @get:NotBlank(message="{errors.lastName.notBlank}")
    @get:Length(max=255, message="{errors.input.tooLong}")
    var lastName: String = "",

    @get:Length(max=255, message="{errors.input.tooLong}")
    var phoneNumber: String = "",

    @get:Length(max=255, message="{errors.input.tooLong}")
    var role: String  = ""
) : Serializable

data class UserEditForm (

    @get:NotBlank(message="{errors.username.notBlank}")
    @get:Length(max=255, message="{errors.input.tooLong}")
    var username: String = "",

    var password: String = "",

    @get:NotBlank(message="{errors.firstName.notBlank}")
    @get:Length(max=255, message="{errors.input.tooLong}")
    var firstName: String = "",

    @get:NotBlank(message="{errors.lastName.notBlank}")
    @get:Length(max=255, message="{errors.input.tooLong}")
    var lastName: String = "",

    @get:Length(max=255, message="{errors.input.tooLong}")
    var phoneNumber: String = "",

    @get:Length(max=255, message="{errors.input.tooLong}")
    var role: String  = ""
) : Serializable
