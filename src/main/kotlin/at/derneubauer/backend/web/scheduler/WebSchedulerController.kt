package at.derneubauer.backend.web.scheduler

import at.derneubauer.backend.db.resourceplan.AbsenceAssignmentDo
import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.AbsenceService
import at.derneubauer.backend.service.AssignmentDateAndTechnicianDto
import at.derneubauer.backend.service.NeubauerAbsenceType
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.web.error.NeubauerException
import org.hibernate.validator.constraints.Length
import org.springframework.context.MessageSource
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.support.RedirectAttributes
import org.springframework.web.servlet.view.RedirectView
import java.io.Serializable
import java.time.ZonedDateTime
import java.time.format.DateTimeParseException
import java.util.*
import jakarta.validation.Valid

@Controller
class WebSchedulerController(private val userResolver: LoggedInUserResolver,
                             private val permissionService: PermissionService,
                             private val absenceService: AbsenceService,
                             private val messageSource: MessageSource) {

    companion object {
        private val springBindingPrefix = "org.springframework.validation.BindingResult"
        private val absenceDetailBinding = "userCreateFormBindingResult"
    }

    @GetMapping("/scheduler/technician")
    fun employee(model: Model, @RequestParam date: String?): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_RESOURCE_PLAN_TECHNICIAN, user)

        model.addAttribute("type", "technician")
        model.addAttribute("date", date)
        return "scheduler"
    }

    @GetMapping("/scheduler/assistant")
    fun assistant(model: Model): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_RESOURCE_PLAN_ASSISTANT, user)

        model.addAttribute("type", "assistant")
        return "scheduler"
    }

    @GetMapping("/absence/{plannedResourceId}")
    fun absenceDetail(@PathVariable plannedResourceId: Long, absenceDetail: AbsenceDetail, model: Model, bindingResult: BindingResult, redirectAttributes: RedirectAttributes, locale: Locale): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ABSENCE_DETAIL, user)

        val plannedAbsence = absenceService.getAbsenceById(plannedResourceId)
        val formattedPlannedAbsence = AbsenceDetailViewModel(plannedAbsence)

        val formBinding = model.asMap().get(WebSchedulerController.absenceDetailBinding)
        val isInitialLoadOfEditForm = absenceDetail.note.isBlank()

        if (formBinding != null) {
            model.addAttribute("${WebSchedulerController.springBindingPrefix}.absenceDetail", formBinding)
        } else if (isInitialLoadOfEditForm) {
            absenceDetail.note = formattedPlannedAbsence.getNote()
            absenceDetail.absenceType = formattedPlannedAbsence.getAbsenceType()
            absenceDetail.startDate = formattedPlannedAbsence.getStartDateFormatted()
            absenceDetail.endDate = formattedPlannedAbsence.getEndDateFormatted()
            absenceDetail.startTime = formattedPlannedAbsence.getStartTimeFormatted()
            absenceDetail.endTime = formattedPlannedAbsence.getEndTimeFormatted()
        }

        model.addAttribute("absenceTypes", NeubauerAbsenceType.values().sortedWith(compareBy({ messageSource.getMessage(it.localizationKey, arrayOf(), locale) })))
        return "absence/detail"
    }

    @PostMapping("/absence/{plannedResourceId}")
    fun absenceDetailSave(@PathVariable plannedResourceId: Long, @Valid @ModelAttribute("absenceDetail") absenceDetail: AbsenceDetail, bindingResult: BindingResult, redirectAttributes: RedirectAttributes, locale: Locale): Any {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.EDIT_ABSENCE, user)

        val redirectUrl = "/absence/{plannedResourceId}"
        redirectAttributes.addFlashAttribute(WebSchedulerController.absenceDetailBinding, bindingResult)

        if (bindingResult.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", bindingResult.getFieldError())
            return redirectBackWithError("errors.generic", locale, redirectAttributes, redirectUrl)
        }

        try {
            val startDate = formatDate(absenceDetail.startDate + " - " + absenceDetail.startTime)
            val endDate = formatDate(absenceDetail.endDate + " - " + absenceDetail.endTime)

            absenceService.editAbsenceDetail(plannedResourceId, NeubauerAbsenceType.valueOf(absenceDetail.absenceType), absenceDetail.note, startDate, endDate)

            return redirectBackWithSuccess("absence.success", locale, redirectAttributes, redirectUrl)
        } catch (e: NeubauerException) {
            return redirectBackWithError(e.localizationKey, locale, redirectAttributes, redirectUrl)
        }
    }

    private fun formatDate(date: String): ZonedDateTime {
        try {
            return ZonedDateTime.parse(date, NeubauerDateFormatter.dateTimeFormatter)
        } catch (e: DateTimeParseException) {
            throw InvalidDateFormatException("", "Unable to parse date.", e)
        }
    }

    private fun redirectBackWithSuccess(localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): RedirectView {
        return redirectWithMessage("success", localizationKey, locale, redirectAttributes, redirectUrl)
    }

    private fun redirectBackWithError(localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): RedirectView {
        return redirectWithMessage("error", localizationKey, locale, redirectAttributes, redirectUrl)
    }

    private fun redirectWithMessage(attributeName: String, localizationKey: String, locale: Locale, redirectAttributes: RedirectAttributes, redirectUrl: String): RedirectView {
        redirectAttributes.addFlashAttribute(attributeName, messageSource.getMessage(localizationKey, arrayOf(), locale))

        val redirectView = RedirectView(redirectUrl, true)

        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)

        return redirectView
    }
}

open class InvalidDateOrTimeFormatException(val fieldName: String, message: String, t: Throwable) : NeubauerException(message, t) {
    override val localizationKey = "errors.dateformat"
    override val statusCode = HttpStatus.BAD_REQUEST.value()
}

class InvalidDateFormatException(fieldName: String, message: String, t: Throwable) : InvalidDateOrTimeFormatException(fieldName, message, t) {}

class InvalidTimeFormatException(fieldName: String, message: String, t: Throwable) : InvalidDateOrTimeFormatException(fieldName, message, t) {
    override val localizationKey = "errors.timeformat"
}

data class AbsenceDetail(

    @get:Length(max = 64, message = "{errors.input.tooLong}")
    var absenceType: String = "",

    @get:Length(max = 8192, message = "{errors.input.tooLong}")
    var note: String = "",

    var startDate: String = "",

    var endDate: String = "",

    var startTime: String = "",

    var endTime: String = ""

) : Serializable

class AssignmentDateAndTechnicianViewModel(private val assignment: AssignmentDateAndTechnicianDto) {

    fun getStartDateFormatted(): String {
        return NeubauerDateFormatter.dateTimeFormatter.format(assignment.startDate)
    }

    fun getFullNameOfTechnician(): String {
        return "${assignment.firstName} ${assignment.lastName}"
    }
}

class AbsenceDetailViewModel(private val absence: AbsenceAssignmentDo) {

    fun getAbsenceType(): String {
        return absence.absenceType
    }

    fun getNote(): String {
        return absence.note ?: ""
    }

    fun getStartDateFormatted(): String {
        return NeubauerDateFormatter.dateFormatter.format(absence.startDate)
    }

    fun getEndDateFormatted(): String {
        return NeubauerDateFormatter.dateFormatter.format(absence.endDate)
    }

    fun getStartTimeFormatted(): String {
        return NeubauerDateFormatter.timeFormatter.format(absence.startDate)
    }

    fun getEndTimeFormatted(): String {
        return NeubauerDateFormatter.timeFormatter.format(absence.endDate)
    }
}
