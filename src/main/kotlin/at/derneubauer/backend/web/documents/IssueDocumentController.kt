package at.derneubauer.backend.web.documents

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.IssueDocumentService
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.Model
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable

@Controller
class IssueDocumentController(
    private val userResolver: LoggedInUserResolver,
    private val permissionService: PermissionService,
    private val issueDocumentService: IssueDocumentService,
) {
    @GetMapping("/admin/issues/{issueId}/documents")
    fun getDocumentsForIssue(
        @PathVariable issueId: Long,
        model: Model,
    ): String {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_DOCUMENTS, user)

        model.addAttribute("documentList", issueDocumentService.getDocumentsForIssueId(issueId))

        return "documents/list"
    }

    @GetMapping(
        "/documents/{documentId}",
        produces = [MediaType.APPLICATION_OCTET_STREAM_VALUE]
    )
    fun downloadFileById(@PathVariable documentId: Long): ResponseEntity<ByteArray> {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.DOWNLOAD_ISSUE_DOCUMENTS, user)

        val fileDownload = issueDocumentService.downloadDocumentWithId(documentId)
        val responseHeader = HttpHeaders().apply {
            set("Content-Disposition", "attachment; filename=${fileDownload.filename}")
        }
        return ResponseEntity.ok().headers(responseHeader).body(fileDownload.byteArray)
    }
}
