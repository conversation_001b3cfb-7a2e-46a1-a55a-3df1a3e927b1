package at.derneubauer.backend.web.error

import org.slf4j.LoggerFactory
import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.context.support.MessageSourceAccessor
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.ModelAndView
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler

@ControllerAdvice(annotations = [Controller::class])
@Order(2)
class NeubauerWebExceptionHandler(private val messageSource: MessageSource) : ResponseEntityExceptionHandler() {
    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    @ExceptionHandler(Exception::class)
    fun handleAnyException(e: Exception, request: WebRequest): ModelAndView {
        return handleError("errors.generic", e, HttpStatus.INTERNAL_SERVER_ERROR.value())
    }

    @ExceptionHandler(NeubauerException::class)
    fun handleNeubauerException(e: NeubauerException, request: WebRequest): ModelAndView {
        return handleError(e.localizationKey, e, e.statusCode)
    }

    private fun handleError(localizationKey: String, e: Exception, httpStatusCode: Int): ModelAndView {
        log.error("Exception occurred", e)

        val messages = MessageSourceAccessor(messageSource, LocaleContextHolder.getLocale())
        val localizedMessage = messages.getMessage(localizationKey)

        val mav = ModelAndView()
        mav.addObject("errorCode", httpStatusCode)
        mav.addObject("errorMessage", localizedMessage)
        mav.viewName = "error/generic"
        return mav
    }

}

open class NeubauerException : RuntimeException {
    open val localizationKey = "errors.generic"
    open val statusCode = HttpStatus.INTERNAL_SERVER_ERROR.value()

    constructor(message: String, t: Throwable) : super(message, t)
    constructor(message: String?) : super(message)
    constructor() : super()
}
