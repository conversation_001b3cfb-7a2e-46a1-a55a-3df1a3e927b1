package at.derneubauer.backend.web.issues

import at.derneubauer.backend.service.IssueScanService
import at.derneubauer.backend.service.IssueService
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import jakarta.validation.Valid

@RestController
class IssueScanController(private val issueScanService: IssueScanService, private val issueService: IssueService) {

    @GetMapping("/issue/{issueId}/scan/keys")
    fun scanKeys(@PathVariable issueId: Long): Collection<String> {
        val issue = issueService.findIssue(issueId)
        return issueScanService.retrieveAllKeys(issue.externalId)
    }

    @GetMapping("/issue/scan/{key}.pdf", produces = [MediaType.APPLICATION_PDF_VALUE])
    fun scanPdf(@PathVariable key: String): ByteArray {
        return issueScanService.retrieveIssueScanForKey("$key.pdf")
    }

    @GetMapping("/issue/scan/{key}.jpg", produces = [MediaType.IMAGE_JPEG_VALUE])
    fun scanJpg(@PathVariable key: String): ByteArray {
        return issueScanService.retrieveIssueScanForKey("$key.jpg")
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("/issue/{issueId}/scan", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun uploadScan(@Valid @RequestParam("scan") scan: MultipartFile, @PathVariable issueId: Long): ResponseEntity<Any> {
        val issue = issueService.findIssue(issueId)
        val result = issueScanService.storeFile(issue.externalId, scan)
        return if (result) {
            ResponseEntity.ok().build()
        } else {
            ResponseEntity.badRequest().build()
        }
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("/issue/scan/{key}/delete")
    fun deleteScan(@PathVariable key: String): ResponseEntity<Any> {
        return if (issueScanService.deleteFile(key)) {
            ResponseEntity.ok().build()
        } else {
            ResponseEntity.badRequest().build()
        }
    }

}
