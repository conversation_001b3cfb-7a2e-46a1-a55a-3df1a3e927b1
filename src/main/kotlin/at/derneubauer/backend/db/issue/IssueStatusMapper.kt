package at.derneubauer.backend.db.issue

import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

const val selectIssueStatusFields = """
    iss.id,
    iss.status,
    iss.issue_id,
    iss.requester_id,
    u.first_name,
    u.last_name,
    iss.created_at
"""

@Mapper
interface IssueStatusMapper {

    @Results(id = "issueStatusResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "status", javaType = String::class),
        Arg(column = "issue_id", javaType = Long::class),
        Arg(column = "requester_id", javaType = java.lang.Long::class),
        Arg(column = "first_name", javaType = String::class),
        Arg(column = "last_name", javaType = String::class),
        Arg(column = "created_at", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectIssueStatusFields}
            FROM issue_status iss
            LEFT OUTER JOIN users u
                ON (iss.requester_id = u.id)
            WHERE
                iss.issue_id = #{issueId}
    """)
    fun findAllByIssueId(@Param("issueId") issueId: Long): Collection<IssueStatusDo>

    @ResultMap("issueStatusResult")
    @Select("""
        SELECT
                ${selectIssueStatusFields}
            FROM issue_status iss
            LEFT OUTER JOIN users u
                ON (iss.requester_id = u.id)
            WHERE
                iss.issue_id = #{issueId}
                AND iss.created_at <= #{until}
            ORDER BY iss.created_at DESC
            LIMIT 1
    """)
    fun findLatestForIssueUntilDate(@Param("issueId") issueId: Long, @Param("until") until: ZonedDateTime): IssueStatusDo?

    @ResultMap("issueStatusResult")
    @Select("""
        SELECT
                ${selectIssueStatusFields}
            FROM issue_status iss
            LEFT OUTER JOIN users u
                ON (iss.requester_id = u.id)
            WHERE
                iss.issue_id = #{issueId}
                AND iss.status != #{currentStatus}
            ORDER BY iss.created_at DESC
            LIMIT 1
    """)
    fun findLatestDifferentForIssue(@Param("issueId") issueId: Long, @Param("currentStatus") currentStatus: String): IssueStatusDo?
}

data class IssueStatusDo(
    var id: Long,
    var status: String,
    var issueId: Long,
    var requesterId: Long?,
    var requesterFirstName: String?,
    var requesterLastName: String?,
    var createdAt: ZonedDateTime
) : Serializable