package at.derneubauer.backend.db.issue

import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

const val selectAcceptedIssueFields = """
    a.id,
    a.technician_id,
    a.issue_id,
    a.accepted,
    a.created_at
"""

const val selectAcceptedIssueWithHistoryFields = """
    a.id,
    a.technician_id,
    a.issue_id,
    a.requester_id,
    t.first_name as technician_first_name,
    t.last_name as technician_last_name,
    r.first_name as requester_first_name,
    r.last_name as requester_last_name,
    a.accepted,
    a.created_at
"""

@Mapper
interface AcceptedIssueMapper {

    @Results(id = "acceptedIssueResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "technician_id", javaType = Long::class),
        Arg(column = "issue_id", javaType = Long::class),
        Arg(column = "accepted", javaType = Boolean::class),
        Arg(column = "created_at", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectAcceptedIssueFields}
            FROM v_accepted_issues a
    """)
    fun findAll(): Collection<AcceptedIssueDo>

    @ResultMap("acceptedIssueResult")
    @Select("""
        SELECT
                ${selectAcceptedIssueFields}
            FROM v_accepted_issues a
            INNER JOIN v_issues_with_status i
                ON (a.issue_id = i.id)
            WHERE
                i.status != 'CLOSED'
                AND a.created_at > #{since}
    """)
    fun findForNonClosedIssuesSince(@Param("since") since: ZonedDateTime): Collection<AcceptedIssueDo>

    @ResultMap("acceptedIssueResult")
    @Select("""
        SELECT
                ${selectAcceptedIssueFields}
            FROM v_accepted_issues a
            WHERE
                a.issue_id = #{issueId}
    """)
    fun findByIssueId(@Param("issueId") issueId: Long): Collection<AcceptedIssueDo>

    @Results(id = "selectAcceptedIssueWithHistoryFields")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "technician_id", javaType = Long::class),
        Arg(column = "issue_id", javaType = Long::class),
        Arg(column = "requester_id", javaType = Long::class),
        Arg(column = "accepted", javaType = Boolean::class),
        Arg(column = "technician_first_name", javaType = String::class),
        Arg(column = "technician_last_name", javaType = String::class),
        Arg(column = "requester_first_name", javaType = String::class),
        Arg(column = "requester_last_name", javaType = String::class),
        Arg(column = "created_at", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectAcceptedIssueWithHistoryFields}
            FROM accepted_issues a
            INNER JOIN users t on a.technician_id = t.id
            INNER JOIN users r on a.requester_id = r.id
            WHERE
                a.issue_id = #{issueId}
    """)
    fun findWithHistoryByIssueId(@Param("issueId") issueId: Long): Collection<AcceptedIssueHistoryDo>

    @ResultMap("acceptedIssueResult")
    @Select("""
        SELECT
                ${selectAcceptedIssueFields}
            FROM v_accepted_issues a
            WHERE
                a.issue_id = #{issueId}
                AND a.technician_id = #{technicianId}
            LIMIT 1
    """)
    fun findByIssueAndTechnicianId(@Param("issueId") issueId: Long, @Param("technicianId") technicianId: Long): AcceptedIssueDo?

    @Insert("""
        INSERT INTO accepted_issues
            (
                technician_id,
                requester_id,
                issue_id,
                accepted
            )
            values(
                #{technicianId},
                #{requesterId},
                #{issueId},
                #{accepted}
            )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(insertDo: AcceptedIssueInsertDo)

}

data class AcceptedIssueDo(
    var id: Long,
    var technicianId: Long,
    var issueId: Long,
    var accepted: Boolean,
    var createdAt: ZonedDateTime
) : Serializable

data class AcceptedIssueInsertDo(
    var technicianId: Long,
    var requesterId: Long,
    var issueId: Long,
    var accepted: Boolean,
    var id: Long? = null
)

data class AcceptedIssueHistoryDo (
    var id: Long,
    var technicianId: Long,
    var issueId: Long,
    var requesterId: Long,
    var accepted: Boolean,
    var technicianFirstName: String,
    var technicianLastName: String,
    var requesterFirstName: String,
    var requesterLastName: String,
    var createdAt: ZonedDateTime
)
