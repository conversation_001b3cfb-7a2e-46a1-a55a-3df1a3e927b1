package at.derneubauer.backend.db.issue

import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

const val selectIssueFields = """
    i.id,
    i.external_id,
    i.contact_person,
    i.address,
    i.suggested_date,
    i.description,
    i.note,
    i.status,
    i.updated_at,
    i.created_at
"""

@Mapper
interface IssueMapper {

    @Results(id = "issueResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "external_id", javaType = String::class),
        Arg(column = "contact_person", javaType = String::class),
        Arg(column = "address", javaType = String::class),
        Arg(column = "suggested_date", javaType = String::class),
        Arg(column = "description", javaType = String::class),
        Arg(column = "note", javaType = String::class),
        Arg(column = "status", javaType = String::class),
        Arg(column = "updated_at", javaType = ZonedDateTime::class),
        Arg(column = "created_at", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectIssueFields}
            FROM issues
    """)
    fun findAll(): Collection<IssueDo>

    @ResultMap("issueResult")
    @Select("""
        SELECT
                ${selectIssueFields}
            FROM v_issues_with_status i
            WHERE
                status != 'CLOSED'
            ORDER BY external_id DESC
    """)
    fun findOpenIssuesOrderByExternalIdDesc(): Collection<IssueDo>

    @ResultMap("issueResult")
    @Select("""
        SELECT
                ${selectIssueFields}
            FROM v_issues_with_status i
            WHERE
                status != 'CLOSED'
                AND created_at > #{since}
            ORDER BY created_at ASC
    """)
    fun findOpenIssuesCreatedSince(@Param("since") since: ZonedDateTime): Collection<IssueDo>

    @ResultMap("issueResult")
    @Select("""
        SELECT
                ${selectIssueFields}
            FROM v_issues_with_status i
            WHERE
                status != 'CLOSED'
                AND updated_at > #{since}
                AND created_at < updated_at
            ORDER BY updated_at ASC
    """)
    fun findOpenIssuesUpdatedSince(@Param("since") since: ZonedDateTime): Collection<IssueDo>

    @ResultMap("issueResult")
    @Select("""
        SELECT
                ${selectIssueFields}
            FROM v_issues_with_status i
            WHERE
                status = 'CLOSED'
                AND in_status_since > #{since}
                AND created_at < #{since}
            ORDER BY in_status_since ASC
    """)
    fun findIssuesThatHaveBeenClosedSince(@Param("since") since: ZonedDateTime): Collection<IssueDo>

    @ResultMap("issueResult")
    @Select("""
        SELECT
                ${selectIssueFields}
            FROM v_issues_with_status i
            WHERE
                id = #{issueId}
    """)
    fun findByIssueId(@Param("issueId") issueId: Long): IssueDo?

    @ResultMap("issueResult")
    @Select("""
        SELECT
                DISTINCT ${selectIssueFields}
            FROM v_issues_with_status i
            INNER JOIN issue_status iss
                ON (i.id = iss.issue_id)
            WHERE
                i.status != 'CLOSED'
                AND iss.status = 'REOPENED'
                AND iss.created_at > #{since};
    """)
    fun findAllReopenedSinceThatAreStillOpen(@Param("since") since: ZonedDateTime): Collection<IssueDo>

    @Select("""
        SELECT *
        FROM v_assignable_subissues
    """)
    fun findAllAssignableSubIssues() : Collection<IssueDo>

    @Insert("""
        INSERT INTO issue_status (issue_id, requester_id, status)
            SELECT #{issueId}, #{requesterId}, #{to}
            FROM dual
            WHERE EXISTS (
                SELECT *
                FROM v_issues_with_status
                WHERE id = #{issueId} and status = #{from}
            )
    """)
    fun updateIssueStatus(@Param("issueId") issueId: Long, @Param("requesterId") requesterId: Long, @Param("from") from: String, @Param("to") to: String)

    @ConstructorArgs(
        Arg(column = "count", javaType = Int::class)
    )
    @Select("""
        SELECT
                COUNT(*) as count
            FROM v_issues_with_status i
            WHERE
                external_id LIKE #{externalIdForQuery}
                AND COALESCE(contact_person, '') LIKE #{contactPersonForQuery}
                AND COALESCE(address, '') LIKE #{addressForQuery}
                AND COALESCE(description, '') LIKE #{descriptionForQuery}
                AND status LIKE #{statusForQuery}
    """)
    fun totalIssueCount(queryParameters: PaginatedSearchQueryParameters): Int

    @Update("""
        UPDATE issues
            SET
                note = #{note}
            WHERE
                id = #{issueId}
    """)
    fun editNote(@Param("issueId") issueId: Long, @Param("note") note: String)

}

data class IssueDo(
    var id: Long,
    var externalId: String,
    var contactPerson: String?,
    var address: String?,
    var suggestedDate: String?,
    var description: String?,
    var note: String?,
    var status: String,
    var updatedAt: ZonedDateTime,
    var createdAt: ZonedDateTime
) : Serializable
