package at.derneubauer.backend.db.issue

import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

@Mapper
interface IssueWithTechnicianAssignmentsMapper {

    @Results(id = "issueWithTechnicianAssignmentsResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "external_id", javaType = String::class),
        Arg(column = "contact_person", javaType = String::class),
        Arg(column = "address", javaType = String::class),
        Arg(column = "description", javaType = String::class),
        Arg(column = "status", javaType = String::class),
        Arg(column = "updated_at", javaType = ZonedDateTime::class),
        Arg(column = "created_at", javaType = ZonedDateTime::class),
        Arg(column = "assignment_count", javaType = Int::class),
        Arg(column = "assignment_dates", javaType = String::class)
    )
    @Select("""
        SELECT
                i.id,
                i.external_id,
                i.contact_person,
                i.address,
                i.description,
                i.status,
                i.updated_at,
                i.created_at,
                COUNT(*) as assignment_count,
                GROUP_CONCAT(
                    CONCAT(
                        UNIX_TIMESTAMP(ita.start_date),
                        '-',
                        UNIX_TIMESTAMP(ita.end_date)
                    )
                    ORDER BY
                        ita.start_date DESC,
                        ita.end_date DESC
                    SEPARATOR ','
                ) as assignment_dates
            FROM v_issues_with_status i
            LEFT JOIN issues_technicians_assignment ita
                ON (i.id = ita.issue_id)
            WHERE
                i.external_id LIKE #{externalIdForQuery}
                AND COALESCE(i.contact_person, '') LIKE #{contactPersonForQuery}
                AND COALESCE(i.address, '') LIKE #{addressForQuery}
                AND COALESCE(i.description, '') LIKE #{descriptionForQuery}
                AND i.status LIKE #{statusForQuery}
            GROUP BY i.id
            ORDER BY i.external_id DESC
            LIMIT #{offset}, #{count}
    """)
    fun findAllPaginatedOrderByExternalIdDesc(queryParameters: PaginatedSearchQueryParameters): Collection<IssueWithTechnicianAssignmentsDo>
}

data class IssueWithTechnicianAssignmentsDo(
    var id: Long,
    var externalId: String,
    var contactPerson: String?,
    var address: String?,
    var description: String?,
    var status: String,
    var updatedAt: ZonedDateTime,
    var createdAt: ZonedDateTime,
    var countAssignments: Int,
    var assignmentDates: String?
) : Serializable

data class PaginatedSearchQueryParameters(
    val offset: Int,
    val count: Int,
    var externalId: String = "",
    var contactPerson: String = "",
    var address: String = "",
    var description: String = "",
    var status: String = ""
) {
    fun getExternalIdForQuery(): String {
        return "%${externalId}%"
    }

    fun getContactPersonForQuery(): String {
        return "%${contactPerson}%"
    }

    fun getAddressForQuery(): String {
        return "%${address}%"
    }

    fun getDescriptionForQuery(): String {
        return "%${description}%"
    }

    fun getStatusForQuery(): String {
        return "%${status}%"
    }
}
