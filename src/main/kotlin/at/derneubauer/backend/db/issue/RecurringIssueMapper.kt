package at.derneubauer.backend.db.issue

import at.derneubauer.backend.service.IssueDetailDto
import io.lettuce.core.dynamic.annotation.Param
import org.apache.ibatis.annotations.*
import java.time.ZonedDateTime

@Mapper
interface RecurringIssueMapper {

    @Insert("""
        INSERT INTO recurring_issues 
        (
            issue_id,
            sub_issue_id,
            placeholder_date_from,
            placeholder_date_to,
            user_id
        ) VALUES (
            #{issueId},
            #{subIssueId},
            #{placeholderDateFrom},
            #{placeholderDateTo},
            #{userId}
        )
    """)
    fun createRecurringIssue(
            issueId: Long,
            subIssueId: Long? = null,
            placeholderDateFrom: ZonedDateTime,
            placeholderDateTo: ZonedDateTime,
            userId: Long
    )

    @Select("""
        SELECT *
        FROM recurring_issues
        WHERE issue_id = #{issueId}
        ORDER BY placeholder_date_from ASC
    """)
    fun findRecurringIssuesForIssue(issueId: Long): Collection<RecurringIssueDo>

    @Select("""
        SELECT *
        FROM recurring_issues
        WHERE sub_issue_id IS NOT NULL
    """)
    fun findAssignedRecurringIssues(): Collection<RecurringIssueDo>

    @Select("""
        SELECT *
        FROM recurring_issues
        WHERE sub_issue_id = #{subIssueId}
    """)
    fun findMainIssueForRecurring(subIssueId: Long): RecurringIssueDo?

    @Select("""
        SELECT * 
        FROM recurring_issues
        WHERE sub_issue_id IS NULL
            AND placeholder_date_from >= #{fromDate}
            AND placeholder_date_to < #{toDate}
    """)
    fun findRecurringIssuesForDateRange(fromDate: ZonedDateTime, toDate: ZonedDateTime): Collection<RecurringIssueDo>

    @Select("""
        SELECT * 
        FROM recurring_issues
        WHERE id = #{id}
    """)
    fun findRecurringIssueForId(id: Long): RecurringIssueDo

    @Select("""
        SELECT issue_id
        FROM recurring_issues
        UNION DISTINCT
        SELECT sub_issue_id
        FROM recurring_issues
        WHERE sub_issue_id IS NOT NULL
    """)
    fun findIssueIdsInRecurringIssues(): Collection<Long>

    @Update("""
        UPDATE recurring_issues
            SET user_id = #{userId},
                placeholder_date_from = #{placeholderDateFrom},
                placeholder_date_to = #{placeholderDateTo}
            WHERE id = #{id}
    """)
    fun updateRecurringIssue(id: Long, userId: Long, placeholderDateFrom: ZonedDateTime, placeholderDateTo: ZonedDateTime)

    @Update("""
        UPDATE recurring_issues
            SET sub_issue_id = #{subIssueId}
            WHERE id = #{id}
    """)
    fun assignSubIssue(id: Long, subIssueId: Long)

    @Delete("""
        DELETE FROM recurring_issues
            WHERE id = #{id}
    """)
    fun deleteRecurringIssue(id: Long)

    @Update("""
        UPDATE recurring_issues
            SET sub_issue_id = NULL
            WHERE sub_issue_id = #{subIssueId}
    """)
    fun unassignSubissue(subIssueId: Long)

    @Update("""
        DELETE FROM recurring_issues
            WHERE sub_issue_id = #{subIssueId}
    """)
    fun deleteRecurringIssueBySubIssueId(subIssueId: Long)
}

data class RecurringIssueDo(
        var id: Long,
        var issueId: Long,
        var subIssueId: Long?,
        var placeholderDateFrom: ZonedDateTime,
        var placeholderDateTo: ZonedDateTime,
        var userId: Long
)
