package at.derneubauer.backend.db.resourceplan

import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

const val selectAbsenceAssignmentFields = """
    aa.id,
    aa.user_id,
    aa.absence_type,
    aa.note,
    aa.start_date,
    aa.end_date
"""

@Mapper
interface AbsenceAssignmentMapper {

    @Results(id = "absenceAssignmentResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "user_id", javaType = Long::class),
        Arg(column = "absence_type", javaType = String::class),
        Arg(column = "note", javaType = String::class),
        Arg(column = "start_date", javaType = ZonedDateTime::class),
        Arg(column = "end_date", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectAbsenceAssignmentFields}
            FROM absences_assignment aa
            INNER JOIN users u
                ON (aa.user_id = u.id)
            WHERE
                u.role = #{role}
            AND
                aa.start_date < #{endDate}
            AND
                #{startDate} < aa.end_date
    """)
    fun findByRoleAndDate(@Param("role") role: String, @Param("startDate") startDate: ZonedDateTime, @Param("endDate") endDate: ZonedDateTime): Collection<AbsenceAssignmentDo>

    @ResultMap("absenceAssignmentResult")
    @Select("""
        SELECT
                ${selectAbsenceAssignmentFields}
            FROM absences_assignment aa
            WHERE
                aa.id = #{id}
    """)
    fun findById(@Param("id") id: Long): AbsenceAssignmentDo

    @Insert("""
        INSERT INTO absences_assignment
            (
                user_id,
                absence_type,
                note,
                start_date,
                end_date
            )
            values(
                #{userId},
                #{absenceType},
                #{note},
                #{startDate},
                #{endDate}
            )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun createAbsenceAssignment(absenceAssignment: AbsenceAssignmentInsertDo)

    @Update("""
        UPDATE absences_assignment
        SET
            absence_type = #{absenceType},
            note = #{note},
            start_date = #{startDate},
            end_date = #{endDate}
        WHERE
            id = #{id}
    """)
    fun editAbsenceAssignmentDetail(absenceAssignmentUpdateDo: AbsenceAssignmentUpdateDetailDo)

    @Update("""
        UPDATE absences_assignment
        SET
            user_id = #{userId},
            start_date = #{startDate},
            end_date = #{endDate}
        WHERE
            id = #{id}
    """)
    fun editAbsenceAssignmentDragAndDrop(absenceAssignmentUpdateDo: AbsenceAssignmentUpdateDragAndDropDo)

    @Delete("""
        DELETE
            FROM absences_assignment
        WHERE
            id = #{id}
    """)
    fun deleteAbsenceAssignment(@Param("id") id: Long)
}

data class AbsenceAssignmentDo(
    var id: Long,
    var userId: Long,
    var absenceType: String,
    var note: String?,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime
) : Serializable

data class AbsenceAssignmentInsertDo(
    var userId: Long,
    var absenceType: String,
    var note: String?,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime,
    var id: Long? = null
) : Serializable

data class AbsenceAssignmentUpdateDragAndDropDo(
    var id: Long,
    var userId: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime
) : Serializable

data class AbsenceAssignmentUpdateDetailDo(
    var id: Long,
    var absenceType: String,
    var note: String?,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime
) : Serializable