package at.derneubauer.backend.db.resourceplan

import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

const val selectIssuesTechniciansAssigmentFields = """
    ita.id,
    ita.user_id,
    ita.issue_id,
    i.external_id AS issue_external_id,
    i.address AS issue_address,
    i.status AS issue_status,
    ita.start_date,
    ita.end_date,
    ita.updated_at,
    ita.created_at
"""

const val selectAssignmentDatesAndTechniciansFields = """
    ita.id,
    ita.start_date,
    ita.end_date,
    u.first_name,
    u.last_name
"""

@Mapper
interface IssuesTechniciansAssignmentMapper {

    @Results(id = "issuesTechniciansAssignmentResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "user_id", javaType = Long::class),
        Arg(column = "issue_id", javaType = Long::class),
        Arg(column = "issue_external_id", javaType = String::class),
        Arg(column = "issue_address", javaType = String::class),
        Arg(column = "issue_status", javaType = String::class),
        Arg(column = "start_date", javaType = ZonedDateTime::class),
        Arg(column = "end_date", javaType = ZonedDateTime::class),
        Arg(column = "updated_at", javaType = ZonedDateTime::class),
        Arg(column = "created_at", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectIssuesTechniciansAssigmentFields}
            FROM issues_technicians_assignment ita
            INNER JOIN v_issues_with_status i
                ON (ita.issue_id = i.id)
    """)
    fun findAll(): Collection<IssuesTechniciansAssignmentDo>

    @ResultMap("issuesTechniciansAssignmentResult")
    @Select("""
        SELECT
                ${selectIssuesTechniciansAssigmentFields}
            FROM issues_technicians_assignment ita
            INNER JOIN v_issues_with_status i
                ON (ita.issue_id = i.id)
            WHERE
                ita.start_date < #{endDate}
            AND
                #{startDate} < ita.end_date
    """)
    fun findByDate(@Param("startDate") startDate: ZonedDateTime, @Param("endDate") endDate: ZonedDateTime): Collection<IssuesTechniciansAssignmentDo>

    @ResultMap("issuesTechniciansAssignmentResult")
    @Select("""
        SELECT
                ${selectIssuesTechniciansAssigmentFields}
            FROM issues_technicians_assignment ita
            INNER JOIN v_issues_with_status i
                ON (ita.issue_id = i.id)
            WHERE
                ita.user_id = #{userId}
    """)
    fun findByUserId(@Param("userId") userId: Long): Collection<IssuesTechniciansAssignmentDo>

    @ResultMap("issuesTechniciansAssignmentResult")
    @Select("""
        SELECT
                ${selectIssuesTechniciansAssigmentFields}
            FROM issues_technicians_assignment ita
            INNER JOIN v_issues_with_status i
                ON (ita.issue_id = i.id)
            WHERE
                ita.issue_id = #{issueId}
            ORDER BY ita.created_at
            LIMIT 1
    """)
    fun findByIssueId(@Param("issueId") issueId: Long): IssuesTechniciansAssignmentDo?

    @Results(id = "assignmentDatesAndTechniciansResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "start_date", javaType = ZonedDateTime::class),
        Arg(column = "end_date", javaType = ZonedDateTime::class),
        Arg(column = "first_name", javaType = String::class),
        Arg(column = "last_name", javaType = String::class)
    )
    @Select("""
        SELECT
                ${selectAssignmentDatesAndTechniciansFields}
            FROM users u
            INNER JOIN issues_technicians_assignment ita
                ON (ita.issue_id = #{issueId} AND ita.user_id = u.id)
            ORDER BY ita.start_date ASC
    """)
    fun findAssignmentsAndTechniciansByIssuesId(@Param("issueId") issueId: Long): Collection<AssignmentDatesAndTechniciansDo>

    @ResultMap("assignmentDatesAndTechniciansResult")
    @Select("""
        SELECT
                ${selectAssignmentDatesAndTechniciansFields}
            FROM issues_technicians_assignment ita
            INNER JOIN users u
                ON (ita.user_id = u.id)
            WHERE
                ita.id = #{id}
    """)
    fun findAssignmentDatesAndTechniciansById(@Param("id") id: Long): AssignmentDatesAndTechniciansDo?

    @ResultMap("issuesTechniciansAssignmentResult")
    @Select("""
        SELECT
                ${selectIssuesTechniciansAssigmentFields}
            FROM issues_technicians_assignment ita
            INNER JOIN v_issues_with_status i
                ON (ita.issue_id = i.id)
            WHERE
                ita.id = #{id}
    """)
    fun findById(@Param("id") id: Long): IssuesTechniciansAssignmentDo?

    @Select("""
        SELECT
                ita.start_date
            FROM issues_technicians_assignment ita
            WHERE
                ita.issue_id = #{issueId}
            ORDER BY
                ita.start_date DESC
            LIMIT 1
    """)
    fun findLatestAssignmentDateForIssue(@Param("issueId") issueId: Long): ZonedDateTime?

    @Insert("""
        INSERT INTO issues_technicians_assignment
            (
                issue_id,
                user_id,
                start_date,
                end_date
            )
            values(
                #{issueId},
                #{userId},
                #{startDate},
                #{endDate}
            )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun createIssueTechnicianAssignment(issuesTechniciansAssignment: IssuesTechniciansAssignmentsInsertDo)

    @Update("""
        UPDATE issues_technicians_assignment
        SET
            user_id = #{userId},
            start_date = #{startDate},
            end_date = #{endDate}
        WHERE
            id = #{id}
    """)
    fun editIssueTechnicianAssignment(issuesTechniciansAssignment: IssuesTechniciansAssignmentsUpdateDo)

    @Update("""
        UPDATE issues_technicians_assignment
        SET
            start_date = #{startDate},
            end_date = #{endDate}
        WHERE
            id = #{id}
    """)
    fun editIssueTechnicianAssignmentDate(issuesTechniciansAssignment: IssuesTechniciansAssignmentsUpdateDateDo)

    @Delete("""
        DELETE
            FROM issues_technicians_assignment
        WHERE
            id = #{id}
    """)
    fun deleteIssueTechnicianAssignment(@Param("id") id: Long)
}

data class IssuesTechniciansAssignmentDo(
    var id: Long,
    var userId: Long,
    var issueId: Long,
    var issueExternalId: String,
    var issueAddress: String?,
    var issueStatus: String,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime,
    var updatedAt: ZonedDateTime,
    var createdAt: ZonedDateTime
) : Serializable

data class AssignmentDatesAndTechniciansDo(
    var id: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime,
    var firstName: String,
    var lastName: String
) : Serializable

data class IssuesTechniciansAssignmentsUpdateDo(
    var id: Long,
    var userId: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime
) : Serializable

data class IssuesTechniciansAssignmentsUpdateDateDo(
    var id: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime
) : Serializable

data class IssuesTechniciansAssignmentsInsertDo(
    var issueId: Long,
    var userId: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime,
    var id: Long? = null
) : Serializable
