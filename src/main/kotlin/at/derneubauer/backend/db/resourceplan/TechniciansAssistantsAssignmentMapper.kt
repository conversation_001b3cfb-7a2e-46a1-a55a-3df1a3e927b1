package at.derneubauer.backend.db.resourceplan

import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime


const val selectTechnicianAssignmentFields = """
    taa.id,
    taa.technician_id,
    taa.assistant_id,
    taa.start_date,
    taa.end_date,
    u.first_name,
    u.last_name
"""

@Mapper
interface TechniciansAssistantsAssignmentMapper {

    @Results(id = "technicianAssistantAssignmentResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "technician_id", javaType = Long::class),
        Arg(column = "assistant_id", javaType = Long::class),
        Arg(column = "start_date", javaType = ZonedDateTime::class),
        Arg(column = "end_date", javaType = ZonedDateTime::class),
        Arg(column = "first_name", javaType = String::class),
        Arg(column = "last_name", javaType = String::class)
    )
    @Select("""
        SELECT
                ${selectTechnicianAssignmentFields}
            FROM technicians_assistants_assignment taa
            INNER JOIN users u
                ON (taa.technician_id = u.id)
            WHERE
                taa.start_date < #{endDate}
            AND
                #{startDate} < taa.end_date
    """)
    fun findByDate(@Param("startDate") startDate: ZonedDateTime, @Param("endDate") endDate: ZonedDateTime): Collection<TechniciansAssistantsAssignmentsDo>

    @ResultMap("technicianAssistantAssignmentResult")
    @Select("""
        SELECT
                ${selectTechnicianAssignmentFields}
            FROM technicians_assistants_assignment taa
            INNER JOIN users u
                ON (taa.technician_id = u.id)
            WHERE
                taa.id = #{id}
    """)
    fun findById(@Param("id") id: Long) : TechniciansAssistantsAssignmentsDo

    @Insert("""
        INSERT INTO technicians_assistants_assignment
            (
                assistant_id,
                technician_id,
                start_date,
                end_date
            )
            values(
                #{assistantId},
                #{technicianId},
                #{startDate},
                #{endDate}
            )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun createAssistantTechnicianAssignment(techniciansAssistantsAssignment: TechniciansAssistantsAssignmentsInsertDo)

    @Update("""
        UPDATE technicians_assistants_assignment
        SET
            assistant_id = #{assistantId},
            start_date = #{startDate},
            end_date = #{endDate}
        WHERE
            id = #{id}
    """)
    fun editAssistantTechnicianAssignment(techniciansAssistantsAssignment: TechniciansAssistantsAssignmentsUpdateDo)

    @Delete("""
        DELETE
            FROM technicians_assistants_assignment
        WHERE
            id = #{id}
    """)
    fun deleteAssistantTechnicianAssignment(@Param("id") id: Long)
}

data class TechniciansAssistantsAssignmentsDo(
    var id: Long,
    var technicianId: Long,
    var assistantId: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime,
    var technicianFirstName: String,
    var technicianLastName: String
) : Serializable

data class TechniciansAssistantsAssignmentsUpdateDo (
    var id: Long,
    var assistantId: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime
) : Serializable

data class TechniciansAssistantsAssignmentsInsertDo (
    var technicianId: Long,
    var assistantId: Long,
    var startDate: ZonedDateTime,
    var endDate: ZonedDateTime,
    var id: Long? = null
) : Serializable

