package at.derneubauer.backend.db.documents

import at.derneubauer.backend.db.documents.model.DocumentDo
import at.derneubauer.backend.offa.unmarshal.OffaDocumentType
import org.apache.ibatis.annotations.Arg
import org.apache.ibatis.annotations.ConstructorArgs
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.ResultMap
import org.apache.ibatis.annotations.Results
import org.apache.ibatis.annotations.Select
import java.time.OffsetDateTime

const val DOCUMENT_SELECT_FIELDS = """
    doc.id,
    doc.file_storage_key,
    doc.file_size,
    doc.document_type,
    doc.document_number,
    doc.document_text,
    doc.document_file_name,
    doc.project_number,
    doc.project_text,
    doc.created_at,
    doc.updated_at
"""

@Mapper
interface IssueDocumentMapper {
    @Results(id = "documentResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "file_storage_key", javaType = String::class),
        Arg(column = "file_size", javaType = Long::class),
        Arg(column = "document_type", javaType = OffaDocumentType::class),
        Arg(column = "document_number", javaType = String::class),
        Arg(column = "document_text", javaType = String::class),
        Arg(column = "document_file_name", javaType = String::class),
        Arg(column = "project_number", javaType = String::class),
        Arg(column = "project_text", javaType = String::class),
        Arg(column = "created_at", javaType = OffsetDateTime::class),
        Arg(column = "updated_at", javaType = OffsetDateTime::class),
    )
    @Select(
        """
            SELECT $DOCUMENT_SELECT_FIELDS
            FROM documents doc INNER JOIN issue_documents i_doc ON doc.id = i_doc.document_id
            WHERE i_doc.issue_id = #{issueId}
            ORDER BY doc.updated_at DESC
        """
    )
    fun getDocumentsForIssueId(@Param("issueId") issueId: Long): List<DocumentDo>

    @ResultMap("documentResult")
    @Select(
        """
            SELECT $DOCUMENT_SELECT_FIELDS
            FROM documents doc
            WHERE doc.id = #{documentId}
        """
    )
    fun getDocumentById(@Param("documentId") documentId: Long): DocumentDo?
}
