package at.derneubauer.backend.db.documents.model

import at.derneubauer.backend.offa.unmarshal.OffaDocumentType
import java.time.OffsetDateTime

data class DocumentDo(
    val id: Long,
    val fileStorageKey: String,
    val fileSize: Long,
    val documentType: OffaDocumentType,
    val documentNumber: String,
    val documentText: String?,
    val documentFileName: String,
    val projectNumber: String,
    val projectText: String?,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
)
