package at.derneubauer.backend.db.image

import at.derneubauer.backend.service.ImageStatus
import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

const val selectImageFields = """
    id,
    ref_type,
    ref_id,
    image,
    thumbnail,
    width,
    height,
    status,
    uploaded_at,
    updated_at
"""

@Mapper
interface ImageMapper {

    @Results(id = "imageResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "ref_type", javaType = String::class),
        Arg(column = "ref_id", javaType = Long::class),
        Arg(column = "image", javaType = String::class),
        Arg(column = "thumbnail", javaType = String::class),
        Arg(column = "width", javaType = Int::class),
        Arg(column = "height", javaType = Int::class),
        Arg(column = "status", javaType = String::class),
        Arg(column = "uploaded_at", javaType = ZonedDateTime::class),
        Arg(column = "updated_at", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectImageFields}
            FROM
                v_images_added
            WHERE
                ref_type = #{refType}
                AND ref_id = #{refId}
            ORDER BY
                uploaded_at DESC;
    """)
    fun readEntriesForTypeAndId(
        @Param("refType") refType: String,
        @Param("refId") refId: Long
    ): List<ImageDo>


    @ResultMap("imageResult")
    @Select(""" 
        SELECT ${selectImageFields}
        FROM v_images_latest_status
        WHERE updated_at > #{since}
    """)
    fun findAllWithLatestStatusSince(@Param("since") since: ZonedDateTime) : Collection<ImageDo>

    @ResultMap("imageResult")
    @Select("""
        SELECT
                ${selectImageFields}
            FROM
                v_images_added
            WHERE
                id = #{id}
    """)
    fun findById(@Param("id") id: Long): ImageDo?


    @ResultMap("imageResult")
    @Select("""
        SELECT
                ${selectImageFields}
            FROM
                v_images_added
            WHERE
                id = #{id}
                AND ref_id = #{refId}
                AND ref_type = #{refType}
    """)
    fun findByIdRefIdAndType(@Param("id") id: Long, @Param("refId") refId: Long, @Param("refType") refType: String): ImageDo?

    @Insert("""
        INSERT INTO images
            SET
                image = #{image},
                thumbnail = #{thumbnail},
                width = #{width},
                height = #{height},
                uploaded_at = #{uploadedAt};
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insertEntryForTypeAndRefId(image: ImageUploadDo): Long

    @Insert("""
        INSERT INTO image_status
            SET
                image_id = #{image_id},
                ref_type = #{refType},
                ref_id = #{refId},
                status = #{status}
    """)
    fun updateImageStatus(imageStatusDo: ImageStatusDo)
}

data class ImageDo(
        var id: Long,
        var refType: String,
        var refId: Long,
        var image: String,
        var thumbnail: String,
        var width: Int,
        var height: Int,
        var status: String,
        var uploadedAt: ZonedDateTime,
        var updatedAt: ZonedDateTime
) : Serializable

data class ImageUploadDo(
        var image: String,
        var thumbnail: String,
        var width: Int,
        var height: Int,
        var uploadedAt: ZonedDateTime,
        var id: Long? = null
) : Serializable

data class ImageStatusDo(
    var image_id: Long,
    var refType: String,
    var refId: Long,
    var status: String
) : Serializable