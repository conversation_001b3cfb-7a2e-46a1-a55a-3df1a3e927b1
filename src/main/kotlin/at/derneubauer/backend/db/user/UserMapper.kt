package at.derneubauer.backend.db.user

import at.derneubauer.backend.security.NeubauerRole
import org.apache.ibatis.annotations.*
import java.io.Serializable
import java.time.ZonedDateTime

const val selectUserFields = """
    id,
    username,
    password,
    first_name,
    last_name,
    phone_number,
    locale,
    secret_id,
    disabled,
    role,
    updated_at,
    created_at
"""

@Mapper
interface UserMapper {

    @Results(id = "userResult")
    @ConstructorArgs(
        Arg(column = "id", javaType = Long::class, id = true),
        Arg(column = "username", javaType = String::class),
        Arg(column = "password", javaType = String::class),
        Arg(column = "first_name", javaType = String::class),
        Arg(column = "last_name", javaType = String::class),
        Arg(column = "phone_number", javaType = String::class),
        Arg(column = "locale", javaType = String::class),
        Arg(column = "secret_id", javaType = String::class),
        Arg(column = "disabled", javaType = Boolean::class),
        Arg(column = "role", javaType = String::class),
        Arg(column = "updated_at", javaType = ZonedDateTime::class),
        Arg(column = "created_at", javaType = ZonedDateTime::class)
    )
    @Select("""
        SELECT
                ${selectUserFields}
            FROM users
            WHERE
                username = #{username}
    """)
    fun findByUsername(@Param("username") username: String): UserDo?

    @ResultMap("userResult")
    @Select("""
        SELECT
            ${selectUserFields}
        FROM users
    """)
    fun findAll(): Collection<UserDo>


    @ResultMap("userResult")
    @Select("""
        SELECT
            ${selectUserFields}
        FROM users
        ORDER BY username ASC
    """)
    fun findAllOrderByUsername(): Collection<UserDo>

    @ResultMap("userResult")
    @Select("""
        SELECT
                ${selectUserFields}
            FROM users
            WHERE
                role = #{role} and disabled = false
            ORDER BY last_name, first_name ASC
    """)
    fun findActiveUsersByRoleOrderByLastName(@Param("role") role: NeubauerRole): Collection<UserDo>

    @ResultMap("userResult")
    @Select("""
        SELECT
                ${selectUserFields}
            FROM users
            WHERE
                id = #{userId}
    """)
    fun findById(@Param("userId") userId: Long): UserDo?

    @Insert("""
        INSERT INTO users
            (
                username,
                password,
                first_name,
                last_name,
                phone_number,
                locale,
                role
            )
            values(
                #{username},
                #{password},
                #{firstName},
                #{lastName},
                #{phoneNumber},
                #{locale},
                #{role}
            )
    """)
    fun createUser(
            @Param("username") username: String,
            @Param("password") password: String,
            @Param("firstName") firstName: String,
            @Param("lastName") lastName: String,
            @Param("phoneNumber") phoneNumber: String?,
            @Param("locale") locale: String,
            @Param("role") role: String)


    @Update("""
        UPDATE users
        SET
            username = #{username},
            password = #{password},
            first_name = #{firstName},
            last_name = #{lastName},
            phone_number = #{phoneNumber},
            role = #{role}
        WHERE
            id = #{userId}
    """)
    fun editUserChangePassword(
            @Param("userId") userId: Long,
            @Param("username") username: String,
            @Param("password") password: String?,
            @Param("firstName") firstName: String,
            @Param("lastName") lastName: String,
            @Param("phoneNumber") phoneNumber: String?,
            @Param("role") role: String)

    @Update("""
        UPDATE users
        SET
            username = #{username},
            first_name = #{firstName},
            last_name = #{lastName},
            phone_number = #{phoneNumber},
            role = #{role}
        WHERE
            id = #{userId}
    """)
    fun editUserKeepPassword(
            @Param("userId") userId: Long,
            @Param("username") username: String,
            @Param("firstName") firstName: String,
            @Param("lastName") lastName: String,
            @Param("phoneNumber") phoneNumber: String?,
            @Param("role") role: String)

    @Update("""
        UPDATE users
        SET
            disabled = #{disabled}
        WHERE
            id = #{userId}
    """)
    fun setDisabledStatusForUser(
        @Param("userId") userId: Long,
        @Param("disabled") disabled: Boolean)

    @ResultMap("userResult")
    @Select("""
        SELECT
                ${selectUserFields}
            FROM users
            WHERE
                created_at > #{since}
                AND role = 'TECHNICIAN'
            ORDER BY created_at ASC
    """)
    fun findTechniciansCreatedSince(@Param("since") since: ZonedDateTime): Collection<UserDo>

    @ResultMap("userResult")
    @Select("""
        SELECT
                ${selectUserFields}
            FROM users
            WHERE
                updated_at > #{since}
                AND created_at < updated_at
                AND role = 'TECHNICIAN'
            ORDER BY updated_at ASC
    """)
    fun findTechniciansUpdatedSince(@Param("since") since: ZonedDateTime): Collection<UserDo>
}

data class UserDo(
    var id: Long,
    var username: String,
    var password: String,
    var firstName: String,
    var lastName: String,
    var phoneNumber: String?,
    var locale: String?,
    var secretId: String,
    var disabled: Boolean,
    var role: String,
    var updatedAt: ZonedDateTime,
    var createdAt: ZonedDateTime
) : Serializable
