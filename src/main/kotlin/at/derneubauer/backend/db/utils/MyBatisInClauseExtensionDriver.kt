package at.derneubauer.backend.db.utils

import org.apache.ibatis.mapping.SqlSource
import org.apache.ibatis.scripting.LanguageDriver
import org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
import org.apache.ibatis.session.Configuration
import java.util.regex.Pattern

// Taken from https://stackoverflow.com/a/29076097
class MyBatisInClauseExtensionDriver : XMLLanguageDriver(), LanguageDriver {
    private val inPattern: Pattern = Pattern.compile("\\(#\\{(\\w+)\\}\\)")
    override fun createSqlSource(configuration: Configuration?, script: String, parameterType: Class<*>?): SqlSource {
        var newScript = script
        val matcher = inPattern.matcher(newScript)
        if (matcher.find()) {
            newScript = matcher.replaceAll("(<foreach collection=\"$1\" item=\"__item\" separator=\",\" >#{__item}</foreach>)")
        }
        newScript = "<script>$newScript</script>"
        return super.createSqlSource(configuration, newScript, parameterType)
    }
}
