package at.derneubauer.backend.service

import at.derneubauer.backend.db.issue.*
import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.rest.app.NoteRequest
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class IssueService(private val issueMapper: IssueMapper,
                   private val issueWithTechnicianAssignmentMapper: IssueWithTechnicianAssignmentsMapper,
                   private val issueStatusMapper: IssueStatusMapper,
                   private val acceptedIssueService: AcceptedIssueService,
                   private val permissionService: PermissionService) {

    fun allOpenIssues(): Collection<IssueDo> = issueMapper.findOpenIssuesOrderByExternalIdDesc()

    fun findIssue(issueId: Long): IssueDto {
        val issue = issueMapper.findByIssueId(issueId) ?: throw IssueNotFoundResourcePlanServiceException(issueId)

        return IssueDto(
                issue.id,
                issue.externalId,
                issue.contactPerson,
                issue.address,
                issue.suggestedDate,
                issue.description,
                issue.note,
                NeubauerIssueStatus.valueOf(issue.status)
        )
    }

    fun findAssignableSubIssues(): Collection<IssueDo> = issueMapper.findAllAssignableSubIssues()

    fun allIssuesWithAssignmentsPaginated(queryParameters: PaginatedSearchQueryParameters): Collection<IssueWithTechnicianAssignmentsDo> =
            issueWithTechnicianAssignmentMapper.findAllPaginatedOrderByExternalIdDesc(queryParameters)

    fun updateIssueStatus(issueId: Long, requesterId: Long, from: NeubauerIssueStatus, to: NeubauerIssueStatus) =
            issueMapper.updateIssueStatus(issueId, requesterId, from.getName(), to.getName())

    fun getTotalIssueCount(queryParameters: PaginatedSearchQueryParameters): Int =
            issueMapper.totalIssueCount(queryParameters)

    fun editNote(issueId: Long, note: String) {
        try {
            issueMapper.editNote(issueId, note)
        } catch (e: DataIntegrityViolationException) {
            throw DatabaseIssueServiceException(e)
        }
    }

    fun appendNote(issueId: Long, note: NoteRequest, username: String) {
        val issue = findIssue(issueId)

        val header = "$username, ${NeubauerDateFormatter.dateTimeFormatter.format(note.date)}"
        val message = "$header\n${note.note.trim()}"

        val appendedMessage = if (issue.note.isNullOrBlank()) {
            message
        } else {
            issue.note + "\n\n" + message
        }

        editNote(issueId, appendedMessage)
    }

    fun setIssueToTechnicianDone(issueId: Long, technicianId: Long) {
        if (acceptedIssueService.hasTechnicianAcceptedIssue(issueId, technicianId)) {
            issueMapper.updateIssueStatus(issueId, technicianId, NeubauerIssueStatus.ACCEPTED.getName(), NeubauerIssueStatus.DONE_TECHNICIAN.getName())
            acceptedIssueService.resetAssignments(issueId, technicianId)
        } else {
            throw NotAcceptedIssueServiceException()
        }
    }

    fun findOpenIssuesCreatedSince(since: ZonedDateTime): Collection<IssueDo> {
        return issueMapper.findOpenIssuesCreatedSince(since)
    }

    fun findOpenIssuesUpdatedSince(since: ZonedDateTime): Collection<IssueDo> {
        return issueMapper.findOpenIssuesUpdatedSince(since)
    }

    fun findIssuesThatHaveBeenClosedSince(since: ZonedDateTime): Collection<IssueDo> {
        return issueMapper.findIssuesThatHaveBeenClosedSince(since)
    }

    fun historyForIssue(issueId: Long): Collection<IssueHistoryDto> {
        val issueStatusHistory = issueStatusMapper.findAllByIssueId(issueId).map {
            IssueHistoryDto(
                    it.issueId,
                    NeubauerIssueStatus.valueOf(it.status),
                    it.requesterId,
                    null,
                    if (it.requesterFirstName != null && it.requesterLastName != null) "${it.requesterFirstName} ${it.requesterLastName}" else "",
                    null,
                    null,
                    it.createdAt,
                    IssueHistoryType.ISSUE_STATUS
            )
        }
        val acceptedIssueHistory = acceptedIssueService.historyForIssue(issueId).map {
            IssueHistoryDto(
                    it.issueId,
                    null,
                    it.requesterId,
                    it.technicianId,
                    "${it.requesterFirstName} ${it.requesterLastName}",
                    "${it.technicianFirstName} ${it.technicianLastName}",
                    it.accepted,
                    it.createdAt,
                    IssueHistoryType.ACCEPTED_STATUS
            )
        }

        return issueStatusHistory.plus(acceptedIssueHistory).sortedByDescending { it.timestamp }
    }

    fun findStatusHistoryForIssuesThatHaveBeenReopenedAndAreStillOpen(since: ZonedDateTime): Collection<IssueDo> {
        val reopenedIssues = issueMapper.findAllReopenedSinceThatAreStillOpen(since)

        val recentlyReopenedIssues = mutableListOf<IssueDo>()

        reopenedIssues.forEach { issue ->
            val latestStatusBefore = issueStatusMapper.findLatestForIssueUntilDate(issue.id, since)

            if (latestStatusBefore?.status == NeubauerIssueStatus.CLOSED.getName()) {
                recentlyReopenedIssues.add(issue)
            }
        }

        return recentlyReopenedIssues
    }

    fun reopenIssue(user: UserDo, issueId: Long) {
        val issue = findIssue(issueId)

        if (isIssueInStateThatCanBeReopened(issue.status)) {
            ensurePermissionForReopeningIssue(user, issue)
            updateIssueStatus(issue.id, user.id, issue.status, NeubauerIssueStatus.REOPENED)
            acceptedIssueService.resetAssignments(issue.id, user.id)
        } else {
            throw WrongStatusIssueServiceException(issue.id)
        }
    }

    fun closeIssue(user: UserDo, issueId: Long) {
        val issue = findIssue(issueId)

        if (isIssueInStateThatCanBeClosed(issue.status)) {
            updateIssueStatus(issueId, user.id, issue.status, NeubauerIssueStatus.CLOSED)
        } else {
            throw WrongStatusIssueServiceException(issue.id)
        }
    }

    fun isIssueInStateThatCanBeReopened(status: NeubauerIssueStatus): Boolean =
            (status == NeubauerIssueStatus.CLOSED || status == NeubauerIssueStatus.DONE_TECHNICIAN)

    private fun ensurePermissionForReopeningIssue(user: UserDo, issue: IssueDto) {
        when (issue.status) {
            NeubauerIssueStatus.DONE_TECHNICIAN -> permissionService.ensurePermission(NeubauerPermission.REOPEN_ISSUE_FROM_DONE_TECHNICIAN, user)
            NeubauerIssueStatus.CLOSED -> permissionService.ensurePermission(NeubauerPermission.REOPEN_ISSUE_FROM_CLOSED, user)
            else -> throw WrongStatusIssueServiceException(issue.id)
        }
    }

    fun isIssueInStateThatCanBeClosed(status: NeubauerIssueStatus): Boolean = status != NeubauerIssueStatus.CLOSED
}

data class IssueHistoryDto(val issueId: Long,
                           val issueStatus: NeubauerIssueStatus?,
                           val requestingUserId: Long?,
                           val assignedUserId: Long?,
                           val requestingUserDisplayName: String,
                           val assignedUserDisplayName: String?,
                           val accepted: Boolean?,
                           val timestamp: ZonedDateTime,
                           val type: IssueHistoryType)

enum class IssueHistoryType {
    ISSUE_STATUS,
    ACCEPTED_STATUS
}

open class IssueServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class DatabaseIssueServiceException(t: Throwable) : IssueServiceException("Database exception", t) {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
    override val localizationKey = "errors.issue.note.format"
}

class IssueNotFoundIssueServiceException(val issueId: Long) : IssueServiceException("Unable to find issue with ID ${issueId}") {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
}

class NotAcceptedIssueServiceException : IssueServiceException("Issue not accepted by technician who is trying to change it's status to TECHNICIAN_DONE")


class WrongStatusIssueServiceException(val issueId: Long) : IssueServiceException("Issue with ID ${issueId} is in wrong state")


enum class NeubauerIssueStatus(val localizationKey: String) {
    NEW("status.new"),
    REOPENED("status.reopened"),
    PLANNED("status.planned"),
    ACCEPTED("status.accepted"),
    NOT_STARTED("status.not_started"),
    DONE_TECHNICIAN("status.done_technician"),
    CLOSED("status.closed");

    fun getName() = name
}


data class IssueDto(
        var id: Long,
        var externalId: String,
        var contactPerson: String?,
        var address: String?,
        var suggestedDate: String?,
        var description: String?,
        var note: String?,
        var status: NeubauerIssueStatus
)
