package at.derneubauer.backend.service

import at.derneubauer.backend.db.issue.IssueMapper
import at.derneubauer.backend.db.resourceplan.*
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

@Service
class ResourcePlanService(private val issuesTechniciansAssignmentMapper: IssuesTechniciansAssignmentMapper,
                          private val issueService: IssueService,
                          private val issueMapper: IssueMapper) {

    val minimumDurationInMinutes = 120

    fun getResourcePlan(fromDate: ZonedDateTime, mode: ResourcePlanMode): Collection<PlannedResourceDto> {
        val endDate = fromDate.plusDays(daysForMode(mode))
        return getPlannedIssues(fromDate, endDate)
    }

    fun createIssueTechniciansAssignment(requesterId: Long, issueId: Long, technicianId: Long, from: ZonedDateTime, to: ZonedDateTime): PlannedResourceDto {
        issueService.updateIssueStatus(issueId, requesterId, NeubauerIssueStatus.NEW, NeubauerIssueStatus.PLANNED)

        val issueAssignment = IssuesTechniciansAssignmentsInsertDo(issueId, technicianId, from, to)
        issuesTechniciansAssignmentMapper.createIssueTechnicianAssignment(issueAssignment)
        val issueAssignmentId = issueAssignment.id
                ?: throw NoSuchIssuesTechniciansAssignmentResourcePlanServiceException()
        return getPlannedIssue(issueAssignmentId)
    }

    fun updateIssueTechniciansAssignment(id: Long, technicianId: Long, from: ZonedDateTime, to: ZonedDateTime): PlannedResourceDto {
        val issueAssignment = IssuesTechniciansAssignmentsUpdateDo(id, technicianId, from, to)
        issuesTechniciansAssignmentMapper.editIssueTechnicianAssignment(issueAssignment)
        return getPlannedIssue(id)
    }

    fun updateIssueTechniciansAssignmentDate(id: Long, from: ZonedDateTime, to: ZonedDateTime): PlannedResourceDto {
        val lengthInMinutes = from.until(to, ChronoUnit.MINUTES)
        if (lengthInMinutes < minimumDurationInMinutes) {
            throw AssignmentTooShortResourcePlanServiceException()
        }

        val issueAssignment = IssuesTechniciansAssignmentsUpdateDateDo(id, from, to)
        issuesTechniciansAssignmentMapper.editIssueTechnicianAssignmentDate(issueAssignment)
        return getPlannedIssue(id)
    }

    fun deleteIssueTechnicianAssignment(id: Long, requesterId: Long) {
        val issueTechnicianAssignment = issuesTechniciansAssignmentMapper.findById(id)

        issuesTechniciansAssignmentMapper.deleteIssueTechnicianAssignment(id)

        if (issueTechnicianAssignment?.issueStatus == NeubauerIssueStatus.PLANNED.getName()) {
            if (issuesTechniciansAssignmentMapper.findAssignmentsAndTechniciansByIssuesId(issueTechnicianAssignment.issueId).isEmpty()) {
                issueMapper.updateIssueStatus(issueTechnicianAssignment.issueId, requesterId, issueTechnicianAssignment.issueStatus, NeubauerIssueStatus.NEW.getName())
            }
        }
    }

    fun getAssignmentForIssue(id: Long) = issuesTechniciansAssignmentMapper.findByIssueId(id)

    fun daysForMode(mode: ResourcePlanMode): Long {
        return when (mode) {
            ResourcePlanMode.DAILY -> 1
            ResourcePlanMode.WEEKLY -> 5
        }
    }

    private fun getPlannedIssue(id: Long): PlannedResourceDto {
        val plannedIssue = issuesTechniciansAssignmentMapper.findById(id)
                ?: throw NoSuchIssuesTechniciansAssignmentResourcePlanServiceException()
        return convertTechnicianAssignmentDoToDto(plannedIssue)
    }

    private fun getPlannedIssues(fromDate: ZonedDateTime, endDate: ZonedDateTime): Collection<PlannedResourceDto> {
        return issuesTechniciansAssignmentMapper.findByDate(fromDate, endDate).map { issueAssignment ->
            convertTechnicianAssignmentDoToDto(issueAssignment)
        }
    }

    private fun convertTechnicianAssignmentDoToDto(issueAssignment: IssuesTechniciansAssignmentDo): PlannedResourceDto {
        return PlannedResourceDto(
                issueAssignment.id,
                issueAssignment.startDate,
                issueAssignment.endDate,
                issueAssignment.userId,
                EventType.ISSUE,
                issueAssignment.issueId,
                issueAssignment.issueExternalId,
                issueAssignment.issueAddress,
                issueAssignment.issueStatus
        )
    }

    fun getIssueDetail(issueId: Long): IssueDetailDto {
        val issue = issueMapper.findByIssueId(issueId)
                ?: throw IssueNotFoundResourcePlanServiceException(issueId)
        val assignmentsWithTechniciansList = issuesTechniciansAssignmentMapper.findAssignmentsAndTechniciansByIssuesId(issueId).map { adt ->
            AssignmentDateAndTechnicianDto(
                    adt.id,
                    adt.startDate,
                    adt.endDate,
                    adt.firstName,
                    adt.lastName
            )
        }

        return IssueDetailDto(
                issue.id,
                issue.externalId,
                issue.contactPerson,
                issue.address,
                issue.suggestedDate,
                issue.description,
                issue.note,
                NeubauerIssueStatus.valueOf(issue.status),
                assignmentsWithTechniciansList
        )
    }

    fun getIssueAssignmentDetail(assignmentId: Long): AssignmentDateAndTechnicianDto? {
        val issueTechnicianAssignment = issuesTechniciansAssignmentMapper.findAssignmentDatesAndTechniciansById(assignmentId)
                ?: throw NoSuchIssuesTechniciansAssignmentResourcePlanServiceException()
        return AssignmentDateAndTechnicianDto(
                issueTechnicianAssignment.id,
                issueTechnicianAssignment.startDate,
                issueTechnicianAssignment.endDate,
                issueTechnicianAssignment.firstName,
                issueTechnicianAssignment.lastName
        )
    }

    fun getLatestAssignmentDateForIssue(issueId: Long): ZonedDateTime? {
        return issuesTechniciansAssignmentMapper.findLatestAssignmentDateForIssue(issueId)
    }
}

open class ResourcePlanServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class IssueNotFoundResourcePlanServiceException(val issueId: Long) : ResourcePlanServiceException("Unable to find issue with id ${issueId}") {
    override val statusCode = HttpStatus.NOT_FOUND.value()
    override val localizationKey = "errors.issue.notFound"
}

class NoSuchIssuesTechniciansAssignmentResourcePlanServiceException() : ResourcePlanServiceException("No such Issue Technician Assignment found!") {}

class AssignmentTooShortResourcePlanServiceException() : ResourcePlanServiceException("Assignment is too short!") {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
    override val localizationKey = "errors.assignment.tooShort"
}

enum class ResourcePlanMode {
    DAILY,
    WEEKLY
}

enum class EventType {
    ISSUE,
    ABSENCE,
    ASSISTANT_ASSIGNMENT,
    RECURRING_ISSUE_PLACEHOLDER
}

data class PlannedResourceDto(
        val id: Long,
        val start: ZonedDateTime,
        val end: ZonedDateTime,
        val userId: Long,
        val eventType: EventType,
        val plannedResourceId: Long,
        val title: String,
        val description: String?,
        val status: String?
)

data class IssueDetailDto(
        var id: Long,
        var externalId: String,
        var contactPerson: String?,
        var address: String?,
        var suggestedDate: String?,
        var description: String?,
        var note: String?,
        var status: NeubauerIssueStatus,
        var assignedDatesAndTechnicians: List<AssignmentDateAndTechnicianDto>
)

data class AssignmentDateAndTechnicianDto(
        var id: Long,
        var startDate: ZonedDateTime,
        var endDate: ZonedDateTime,
        var firstName: String,
        var lastName: String
)
