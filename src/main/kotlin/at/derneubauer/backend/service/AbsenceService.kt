package at.derneubauer.backend.service

import at.derneubauer.backend.db.resourceplan.*
import at.derneubauer.backend.security.NeubauerRole
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.time.ZonedDateTime


@Service
class AbsenceService(private val absenceAssignmentMapper: AbsenceAssignmentMapper,
                     private val resourcePlanService: ResourcePlanService)
{
    fun getAbsencesForResourcePlan(role: NeubauerRole, fromDate: ZonedDateTime, mode: ResourcePlanMode): Collection<PlannedResourceDto> {
        val endDate = fromDate.plusDays(resourcePlanService.daysForMode(mode))
        return getPlannedAbsencesByRole(role, fromDate, endDate)
    }

    private fun getPlannedAbsencesByRole(role: <PERSON>eu<PERSON>uerR<PERSON>, fromDate: ZonedDateTime, endDate: ZonedDateTime): Collection<PlannedResourceDto> {
        return absenceAssignmentMapper.findByRoleAndDate(role.getName(), fromDate, endDate).map { absenceAssignment ->
            convertAbsenceAssignmentDoToDto(absenceAssignment)
        }
    }

    private fun getPlannedAbsence(id: Long): PlannedResourceDto? {
        return absenceAssignmentMapper.findById(id).let { convertAbsenceAssignmentDoToDto(it) }
    }

    fun getAbsenceById(id: Long): AbsenceAssignmentDo {
        return absenceAssignmentMapper.findById(id)
    }

    private fun convertAbsenceAssignmentDoToDto(absenceAssignment: AbsenceAssignmentDo): PlannedResourceDto {
        return PlannedResourceDto(
            absenceAssignment.id,
            absenceAssignment.startDate,
            absenceAssignment.endDate,
            absenceAssignment.userId,
            EventType.ABSENCE,
            absenceAssignment.id,
            NeubauerAbsenceType.valueOf(absenceAssignment.absenceType).localizationKey,
            null,
            null
        )
    }

    fun createAbsence(userId: Long, from: ZonedDateTime, to: ZonedDateTime) : PlannedResourceDto {
        if (from > to) {
            throw EndDateMustBeAfterStartDateAbsenceServiceException()
        }

        val absenceAssignment = AbsenceAssignmentInsertDo(userId, NeubauerAbsenceType.OTHER.getName(), null, from, to)
        absenceAssignmentMapper.createAbsenceAssignment(absenceAssignment)

        val absenceAssignmentId = absenceAssignment.id ?: throw UnableToCreateAbsenceAssignmentAbsenceServiceException()

        return getPlannedAbsence(absenceAssignmentId)
            ?: throw NoAbsenceAssignmentAbsenceServiceException()
    }

    fun editAbsenceDetail(id: Long, absenceType: NeubauerAbsenceType, note: String?, from: ZonedDateTime, to: ZonedDateTime) {
        if (from > to) {
            throw EndDateMustBeAfterStartDateAbsenceServiceException()
        }

        try {
            val absenceAssignment = AbsenceAssignmentUpdateDetailDo(id, absenceType.getName(), note, from, to)
            absenceAssignmentMapper.editAbsenceAssignmentDetail(absenceAssignment)
        } catch (e: DataIntegrityViolationException) {
            throw DatabaseAbsenceServiceException(e)
        }
    }

    fun editAbsenceDragAndDrop(id: Long, userId: Long, from: ZonedDateTime, to: ZonedDateTime): PlannedResourceDto {
        if (from > to) {
            throw EndDateMustBeAfterStartDateAbsenceServiceException()
        }

        val absenceAssignment = AbsenceAssignmentUpdateDragAndDropDo(id, userId, from, to)
        absenceAssignmentMapper.editAbsenceAssignmentDragAndDrop(absenceAssignment)
        return getPlannedAbsence(id)
            ?: throw NoAbsenceAssignmentAbsenceServiceException()
    }

    fun deleteAbsenceAssignment(id: Long) {
        absenceAssignmentMapper.deleteAbsenceAssignment(id)
    }
}


open class AbsenceServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class DatabaseAbsenceServiceException(t: Throwable) : AbsenceServiceException("Database exception", t) {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
    override val localizationKey = "errors.issue.note.format"
}

class UnableToCreateAbsenceAssignmentAbsenceServiceException() : AbsenceServiceException("Unable to create absence assignment") {}

class NoAbsenceAssignmentAbsenceServiceException() : AbsenceServiceException("No such Absence Assignment found!") {}

class EndDateMustBeAfterStartDateAbsenceServiceException() : AbsenceServiceException("End date must be after start date!") {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
    override val localizationKey = "errors.absence.start_before_end"
}


enum class NeubauerAbsenceType(val localizationKey: String) {
    DOCTOR("absence.type.doctor"),
    VOCATIONAL_SCHOOL("absence.type.vocationalSchool"),
    HOLIDAY("absence.type.holiday"),
    DAY_OFF("absence.type.dayOff"),
    HOME_EARLY("absence.type.homeEarly"),
    MILITARY_SERVICE("absence.type.militaryService"),
    INVENTORY("absence.type.inventory"),
    SICK_LEAVE("absence.type.sickLeave"),
    FINAL_EXAM("absence.type.finalExam"),
    FINAL_EXAM_PREP_COURSE("absence.type.finalExamPrepCourse"),
    SCHOOL_HOLIDAYS("absence.type.schoolHolidays"),
    SEMINAR("absence.type.seminar"),
    OTHER("absence.type.other"),
    VACATION("absence.type.vacation"),
    COMP_TIME("absence.type.compTime"),
    CIVIL_SERVICE("absence.type.civilService");

    fun getName() = name
}

