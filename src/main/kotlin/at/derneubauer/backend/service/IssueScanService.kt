package at.derneubauer.backend.service

import at.derneubauer.backend.config.NeubauerConfig
import com.amazonaws.SdkClientException
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import com.amazonaws.services.s3.model.S3Object
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream
import java.io.File

@Service
class IssueScanService(
    private val s3Client: AmazonS3,
    private val neubauerConfig: NeubauerConfig,
    private val imageScalingService: ImageScalingService
) {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)

        enum class MimeType(val mime: String) {
            IMAGE("image/")
        }

        fun isImageFile(file: MultipartFile): Boolean =
            file.contentType?.startsWith(MimeType.IMAGE.mime) ?: false
    }

    fun retrieveAllKeys(externalId: String): Collection<String> {
        val withoutPrefix = removeIdPrefix(externalId)
        val result = s3Client.listObjectsV2(neubauerConfig.issueImageFilestore!!.s3Bucket, withoutPrefix)
        return result.objectSummaries.map { it.key }
    }

    fun retrieveIssueScanForKey(key: String): ByteArray {
        val s3Content = s3Client.getObject(neubauerConfig.issueImageFilestore!!.s3Bucket, key)
        s3Content?.objectContent.use { s3InputStream ->
            return IOUtils.toByteArray(s3InputStream)
        }
    }

    private fun removeIdPrefix(externalId: String): String {
        val prefixToRemove = neubauerConfig.issueImageFilestore?.issuePrefixToRemove ?: ""
        return externalId.removePrefix(prefixToRemove)
    }

    fun storeFile(externalId: String, file: MultipartFile): Boolean {
        val filename = file.originalFilename
        if (filename == null) {
            log.error("cannot store file without filename")
            return false
        }

        val timestamp = System.currentTimeMillis()
        val extension = File(filename).extension
        val idWithoutPrefix = removeIdPrefix(externalId)

        val key = "$idWithoutPrefix upload $timestamp.$extension"
        val metaData = ObjectMetadata()
        if (isImageFile(file)) {
            val byteArray = imageScalingService.resizeImage(file)
            metaData.apply { contentLength = byteArray.size.toLong() }
        } else {
            metaData.apply { contentLength = file.size }
        }

        ByteArrayInputStream(file.bytes).use { inputStream ->
            try {
                s3Client.putObject(neubauerConfig.issueImageFilestore!!.s3Bucket, key, inputStream, metaData)
            } catch (sdkClientException: SdkClientException) {
                log.error("Could not upload file", sdkClientException)
                return false
            }
        }

        return true
    }

    fun deleteFile(key: String): Boolean {
        try {
            s3Client.deleteObject(neubauerConfig.issueImageFilestore!!.s3Bucket, key)
        } catch (sdkClientException: SdkClientException) {
            log.error("Could not delete file", sdkClientException)
            return false
        }
        return true;
    }
}
