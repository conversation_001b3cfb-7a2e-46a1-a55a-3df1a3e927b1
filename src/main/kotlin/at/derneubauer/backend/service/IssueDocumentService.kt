package at.derneubauer.backend.service

import at.derneubauer.backend.store.IssueDocumentStore
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.web.documents.model.FileDownload
import at.derneubauer.backend.web.documents.model.IssueDocumentListItem
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.stereotype.Service

@Service
class IssueDocumentService(
    private val issueDocumentStore: IssueDocumentStore,
    private val documentFileService: DocumentFileService,
) {
    fun getDocumentsForIssueId(issueId: Long): List<IssueDocumentListItem> =
        issueDocumentStore.getDocumentsForIssueId(issueId).map {
            IssueDocumentListItem(
                id = it.id,
                documentType = it.documentType,
                filename = it.documentFileName,
                updatedAt = NeubauerDateFormatter.dateTimeFormatter.format(it.updatedAt),
            )
        }

    fun downloadDocumentWithId(documentId: Long): FileDownload {
        val documentDo = issueDocumentStore.getDocumentById(documentId)
            ?: throw NeubauerException("Document with id $documentId not found")
        return FileDownload(
            filename = documentDo.documentFileName,
            byteArray = documentFileService.downloadDocumentFromS3(documentDo.fileStorageKey)
        )
    }
}
