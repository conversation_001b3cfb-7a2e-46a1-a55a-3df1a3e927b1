package at.derneubauer.backend.service

import at.derneubauer.backend.config.NeubauerConfig
import at.derneubauer.backend.db.image.ImageDo
import at.derneubauer.backend.db.image.ImageMapper
import at.derneubauer.backend.db.image.ImageStatusDo
import at.derneubauer.backend.db.image.ImageUploadDo
import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.rest.ChecksumMismatchException
import at.derneubauer.backend.web.error.NeubauerException
import at.derneubauer.backend.web.images.ImageOrientation
import at.derneubauer.backend.web.images.PDFTemplate
import com.amazonaws.AmazonServiceException
import com.amazonaws.SdkClientException
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import com.amazonaws.services.s3.model.S3Object
import com.sksamuel.scrimage.ImmutableImage
import com.sksamuel.scrimage.nio.JpegWriter
import jakarta.annotation.PostConstruct
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import org.thymeleaf.TemplateEngine
import org.thymeleaf.context.Context
import org.xhtmlrenderer.pdf.ITextRenderer
import java.awt.AlphaComposite
import java.awt.Color
import java.awt.image.BufferedImage
import java.io.ByteArrayInputStream
import java.io.File
import java.lang.Math.max
import java.time.ZonedDateTime
import java.util.*
import java.util.zip.CRC32
import java.util.zip.CheckedInputStream
import javax.imageio.ImageIO


@Service
class ImageService(private val imageMapper: ImageMapper,
                   private val s3Client: AmazonS3,
                   private val neubauerConfig: NeubauerConfig,
                   private val templateEngine: TemplateEngine,
                   private val imageScalingService: ImageScalingService,
                   @Value("\${neubauer.filestore.s3Bucket}") private var s3Bucket: String) {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
        const val IMAGE_BASE_PATH = "images"
        const val THUMBNAIL_BASE_PATH = "thumbnails"
    }

    @PostConstruct
    private fun createS3BucketIfNecessary() {
        try {
            if (!s3Client.doesBucketExistV2(s3Bucket)) {
                s3Client.createBucket(s3Bucket)
            }
        } catch (ex: SdkClientException) {
            log.error("Cannot making the request or handling the response from file store", ex)
        } catch (ex: AmazonServiceException) {
            log.error("File store cannot handle the request", ex)
        }
    }

    fun readIssueImagesWithRefId(refId: Long): List<ImageDo> {
        return imageMapper.readEntriesForTypeAndId(
            ImageType.ISSUE.toString(),
            refId
        )
    }

    fun readUserImagesWithRefId(refId: Long): List<ImageDo> {
        return imageMapper.readEntriesForTypeAndId(
            ImageType.USER.toString(),
            refId
        )
    }

    fun uploadUserImage(userId: Long, file: MultipartFile) {
        uploadImage(file, ImageType.USER, userId)
    }

    fun uploadIssueImage(issueId: Long, file: MultipartFile) {
        uploadImage(file, ImageType.ISSUE, issueId)
    }

    fun moveUserImageToIssue(imageId: Long, sourceUserId: Long, targetIssueId: Long) {
        moveImageToIssue(imageId, sourceUserId, ImageType.USER, targetIssueId)
    }

    fun moveIssueImageToOtherIssue(imageId: Long, sourceIssueId: Long, targetIssueId: Long) {
        moveImageToIssue(imageId, sourceIssueId, ImageType.ISSUE, targetIssueId)
    }

    fun uploadFile(file: MultipartFile, user: UserDo, expectedChecksum: Long, issueId: Long?) {
        val actualChecksum = crc32Checksum(file)

        if (expectedChecksum != actualChecksum) {
            throw ChecksumMismatchException(expectedChecksum, actualChecksum)
        }

        if (issueId != null) {
            uploadIssueImage(issueId, file)
        } else {
            uploadUserImage(user.id, file)
        }
    }

    private fun crc32Checksum(file: MultipartFile): Long {
        file.inputStream.use { inputStream ->
            CheckedInputStream(inputStream, CRC32()).use { cis ->
                val buf = ByteArray(128)
                while (cis.read(buf) >= 0) {
                }

                return cis.checksum.value
            }
        }
    }

    private fun moveImageToIssue(imageId: Long, sourceRefId: Long, sourceImageType: ImageType, targetIssueId: Long) {
        imageMapper.findByIdRefIdAndType(imageId, sourceRefId, sourceImageType.name)
            ?: throw ImageNotFoundImageServiceException()

        imageMapper.updateImageStatus(ImageStatusDo(imageId, sourceImageType.name, sourceRefId, ImageStatus.DELETED.name))
        imageMapper.updateImageStatus(ImageStatusDo(imageId, ImageType.ISSUE.name, targetIssueId, ImageStatus.ADDED.name))
    }

    private fun uploadImage(file: MultipartFile, imageType: ImageType, refId: Long) {
        if (file.isEmpty) {
            return
        }

        val imageInfo = storeImageOnS3(file)

        val image = ImageUploadDo(
            imageInfo.fileName,
            imageInfo.fileName,
            imageInfo.width,
            imageInfo.height,
            ZonedDateTime.now()
        )

        imageMapper.insertEntryForTypeAndRefId(image)

        val imageId = image.id ?: throw NeubauerException("Missing imageId after image upload")
        imageMapper.updateImageStatus(ImageStatusDo(imageId, imageType.name, refId, ImageStatus.ADDED.name))
    }

    private fun storeImageOnS3(file: MultipartFile): ImageInfo {
        val fileName = "${ZonedDateTime.now().toEpochSecond()}_${UUID.randomUUID()}"
        storeImageFullSize(file, fileName)
        return storeImageThumbnail(file, fileName)
    }

    private fun storeImageFullSize(file: MultipartFile, fileName: String) {
        val byteArray = imageScalingService.resizeImage(file)
        ByteArrayInputStream(byteArray).use { bais ->
            val objectMetadata = ObjectMetadata()
            objectMetadata.contentLength = byteArray.size.toLong()
            s3Client.putObject(neubauerConfig.filestore!!.s3Bucket, "${IMAGE_BASE_PATH}/${fileName}", bais, objectMetadata)
        }
    }

    private fun storeImageThumbnail(file: MultipartFile, fileName: String): ImageInfo {
        val image = ImmutableImage.loader().fromBytes(file.bytes)
        val scaledImage = image.cover(250, 250)
        val byteArray = scaledImage.bytes(JpegWriter())

        ByteArrayInputStream(byteArray).use { bais ->
            val objectMetadata = ObjectMetadata()
            objectMetadata.contentLength = byteArray.size.toLong()
            s3Client.putObject(neubauerConfig.filestore!!.s3Bucket, "${THUMBNAIL_BASE_PATH}/${fileName}", bais, objectMetadata)
        }

        return ImageInfo(fileName, image.width, image.height)
    }

    fun retrieveImage(imageId: Long, imageType: ImageType): ByteArray {
        val imageDo = imageMapper.findById(imageId) ?: throw ImageNotFoundImageServiceException()

        val s3Content = retrieveImageFromS3(imageType, imageDo)
        s3Content?.objectContent.use { s3InputStream ->
            return IOUtils.toByteArray(s3InputStream)
        }
    }

    /**
     * CAUTION:
     * make sure you delete the files after use
     */
    fun createImageFileWithOrientation(imageId: Long, imageType: ImageType, imageOrientation: ImageOrientation, imageSize: Int): File {
        val imageDo = imageMapper.findById(imageId) ?: throw ImageNotFoundImageServiceException()

        val tmpFile = File.createTempFile("image", ".jpg")

        val s3Content = retrieveImageFromS3(imageType, imageDo)
        s3Content?.objectContent.use { s3InputStream ->

            val image = ImmutableImage.loader().fromStream(s3InputStream).let {
                if(imageOrientation != imageOrientationForImage(it)) {
                    it.rotateLeft()
                } else {
                    it
                }
            }

            val framedImage = centerImageInFrame(imageOrientation,  image)

            ImageIO.write(framedImage, "JPEG", tmpFile)
        }

        return tmpFile
    }

    private fun imageOrientationForImage(image: ImmutableImage): ImageOrientation {
        return if (image.width > image.height) {
            ImageOrientation.LANDSCAPE
        } else {
            ImageOrientation.PORTRAIT
        }
    }

    private fun centerImageInFrame(imageOrientation: ImageOrientation, bufferedImage: ImmutableImage): BufferedImage {
        var widthRatio = 4
        var heightRatio = 3

        if (imageOrientation == ImageOrientation.PORTRAIT) {
            widthRatio = 3
            heightRatio = 4
        }

        val width = max(bufferedImage.width, bufferedImage.height / heightRatio * widthRatio)
        val height = max(bufferedImage.height, bufferedImage.width / widthRatio * heightRatio)


        val x = (width - bufferedImage.width) / 2
        val y = (height - bufferedImage.height) / 2

        val image = BufferedImage(width, height, BufferedImage.TYPE_3BYTE_BGR)
        val imageGraphics = image.createGraphics()
        imageGraphics.color = Color.WHITE
        imageGraphics.fillRect(0, 0, width, height)
        imageGraphics.composite = AlphaComposite.SrcOver
        imageGraphics.drawImage(bufferedImage.toNewBufferedImage(BufferedImage.TYPE_3BYTE_BGR), x, y, null)
        imageGraphics.dispose()

        return image
    }

    /**
     * CAUTION:
     * make sure you delete the file after use
     */
    fun createPDF(title: String, dateForFooter: String, type: ImageType, config: PDFTemplate, imageIds: Array<Long>, locale: Locale): File {
        val imageFiles = createImageFilesWithOrientation(imageIds, config.imageOrientation, config.imageSize, type)
        val urls = imageFiles.map { it.absolutePath }

        val pdfTmpFile = File.createTempFile("images", ".pdf")

        val context = Context(locale)
        context.setVariable("chunkedUrls", urls.chunked(config.chunkSize))
        context.setVariable("layoutToRender", config.layout)
        context.setVariable("title", title)
        context.setVariable("dateForFooter", dateForFooter)
        context.setVariable("fontBaseUrl", this::class.java.classLoader.getResource("fonts/Roboto/").toString())

        val template = templateEngine.process("images/template", context)

        pdfTmpFile.outputStream().use { fileOutputStream ->
            val renderer = ITextRenderer()
            renderer.setDocumentFromString(template)
            renderer.layout()
            renderer.createPDF(fileOutputStream)
        }

        imageFiles.forEach { it.delete() }

        return pdfTmpFile
    }

    /**
     * CAUTION:
     * make sure you delete the file after use
     */
    private fun createImageFilesWithOrientation(imageIds: Array<Long>, orientation: ImageOrientation, imageSize: Int, imageType: ImageType): List<File> {
        return imageIds.map { imageId ->
            createImageFileWithOrientation(imageId, imageType, orientation, imageSize)
        }
    }

    private fun retrieveImageFromS3(imageType: ImageType, imageDo: ImageDo): S3Object? {
        if (imageType.toString() != imageDo.refType) {
            throw ImageNotFoundImageServiceException()
        }

        return s3Client.getObject(neubauerConfig.filestore!!.s3Bucket, "${IMAGE_BASE_PATH}/${imageDo.image}")
    }

    fun retrieveThumbnail(imageId: Long, imageType: ImageType): ByteArray {
        val imageDo = imageMapper.findById(imageId) ?: throw ImageNotFoundImageServiceException()

        if (imageType.toString() != imageDo.refType) {
            throw ImageNotFoundImageServiceException()
        }
        val s3Content = s3Client.getObject(neubauerConfig.filestore!!.s3Bucket, "${THUMBNAIL_BASE_PATH}/${imageDo.thumbnail}")
        s3Content.objectContent.use { s3InputStream ->
            return IOUtils.toByteArray(s3InputStream)
        }
    }

    fun deleteIssueImage(refId: Long, id: Long) {
        deleteImageInternal(id, ImageType.ISSUE, refId)
    }

    fun deleteUserImage(refId: Long, id: Long) {
        deleteImageInternal(id, ImageType.USER, refId)
    }

    fun allImagesSince(since: ZonedDateTime): Collection<ImageDo> {
        return imageMapper.findAllWithLatestStatusSince(since)
    }

    private fun deleteImageInternal(id: Long, imageType: ImageType, refId: Long) {
        val imageDo = imageMapper.findById(id)
            ?: throw ImageNotFoundImageServiceException()

        if (imageType.toString() != imageDo.refType) {
            throw ImageNotFoundImageServiceException()
        }

        imageMapper.updateImageStatus(ImageStatusDo(id, imageType.name, refId, ImageStatus.DELETED.name))

        s3Client.deleteObject(neubauerConfig.filestore!!.s3Bucket, "${IMAGE_BASE_PATH}/${imageDo.image}")
        s3Client.deleteObject(neubauerConfig.filestore!!.s3Bucket, "${THUMBNAIL_BASE_PATH}/${imageDo.thumbnail}")
    }
}

data class ImageInfo(
    val fileName: String,
    val width: Int,
    val height: Int
)

enum class ImageType {
    ISSUE, USER
}

enum class ImageStatus {
    ADDED, DELETED
}

open class ImageServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class ImageNotFoundImageServiceException() : ImageServiceException("Unable to find image") {
    override val localizationKey = "errors.image.notFound"
    override val statusCode = HttpStatus.NOT_FOUND.value()
}

class ImageUnreadableImageServiceException(fileName: String) : ImageServiceException("Unable to read image ${fileName}") {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
}
