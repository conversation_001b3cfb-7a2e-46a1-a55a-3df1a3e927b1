package at.derneubauer.backend.service

import at.derneubauer.backend.db.issue.RecurringIssueDo
import at.derneubauer.backend.db.issue.RecurringIssueMapper
import at.derneubauer.backend.db.resourceplan.IssuesTechniciansAssignmentMapper
import at.derneubauer.backend.web.userlist.RecurringIssueTimeUnit
import org.springframework.stereotype.Service
import java.lang.IllegalStateException
import java.time.ZonedDateTime

@Service
class RecurringIssueService(
        private val recurringIssueMapper: RecurringIssueMapper,
        private val issuesTechniciansAssignmentMapper: IssuesTechniciansAssignmentMapper,
        private val resourcePlanService: ResourcePlanService
) {

    companion object {
        const val PLACEHOLDER_TITLE_KEY = "placeholder.title"
    }

    fun generateRecurringIssues(issueId: Long, repetitions: Int, interval: Long, unit: RecurringIssueTimeUnit) {
        val issueAssignment = issuesTechniciansAssignmentMapper.findByIssueId(issueId)
                ?: throw IllegalStateException("Cannot generate issues: Issue $issueId does not have a date set.")

        val startDate = recurringIssueMapper.findRecurringIssuesForIssue(issueId)
            .maxOfOrNull { issue -> issue.placeholderDateFrom }
            ?: issueAssignment.startDate
        val endDate = recurringIssueMapper.findRecurringIssuesForIssue(issueId)
            .maxOfOrNull { issue -> issue.placeholderDateTo }
            ?: issueAssignment.endDate

        repeat(repetitions) { repetition ->
            val nextInterval = (repetition + 1) * interval;

            val nextDateStart: ZonedDateTime
            val nextDateEnd: ZonedDateTime


            if (unit == RecurringIssueTimeUnit.DAYS) {
                nextDateStart = startDate.plusDays(nextInterval)
                nextDateEnd = endDate.plusDays(nextInterval)
            } else {
                nextDateStart = startDate.plusMonths(nextInterval)
                nextDateEnd = endDate.plusMonths(nextInterval)
            }

            recurringIssueMapper.createRecurringIssue(
                    issueId = issueId,
                    placeholderDateFrom = nextDateStart,
                    placeholderDateTo = nextDateEnd,
                    userId = issueAssignment.userId
            )
        }
    }

    fun findRecurringIssuesForIssueId(issueId: Long): Collection<RecurringIssueDo> {
        val subIssues = recurringIssueMapper.findRecurringIssuesForIssue(issueId)
        return subIssues.map { recurringIssue ->
            recurringIssue.subIssueId?.let {
                val assignment = issuesTechniciansAssignmentMapper.findByIssueId(it) ?: return@let recurringIssue
                RecurringIssueDo(
                        id = recurringIssue.id,
                        issueId = recurringIssue.issueId,
                        subIssueId = recurringIssue.subIssueId,
                        userId = assignment.userId,
                        placeholderDateFrom = assignment.startDate,
                        placeholderDateTo = assignment.endDate
                )
            } ?: recurringIssue
        }
    }

    fun findAssignedRecurringIssues() =
            recurringIssueMapper.findAssignedRecurringIssues()

    fun findMainIssueForRecurring(subIssueId: Long) =
            recurringIssueMapper.findMainIssueForRecurring(subIssueId)

    fun updateRecurringIssue(id: Long, userId: Long, startDate: ZonedDateTime, endDate: ZonedDateTime): PlannedResourceDto {
        recurringIssueMapper.updateRecurringIssue(id, userId, startDate, endDate)
        val updatedEntry = recurringIssueMapper.findRecurringIssueForId(id)
        return convertRecurringIssueToPlannedResource(updatedEntry)
    }

    fun assignSubIssue(requesterId: Long, id: Long, subIssueId: Long): PlannedResourceDto? {
        recurringIssueMapper.assignSubIssue(id, subIssueId)
        val recurringIssue = recurringIssueMapper.findRecurringIssueForId(id)

        // only create assignment if not already present
        return if (resourcePlanService.getAssignmentForIssue(subIssueId) == null) {
            resourcePlanService.createIssueTechniciansAssignment(
                requesterId,
                subIssueId,
                recurringIssue.userId,
                recurringIssue.placeholderDateFrom,
                recurringIssue.placeholderDateTo
            )
        } else null
    }

    fun getRecurringIssuePlaceholdersForResourcePlan(startDate: ZonedDateTime, mode: ResourcePlanMode): Collection<PlannedResourceDto> {
        val toDate = startDate.plusDays(resourcePlanService.daysForMode(mode))
        return recurringIssueMapper.findRecurringIssuesForDateRange(startDate, toDate).map { recurringIssue ->
            convertRecurringIssueToPlannedResource(recurringIssue)
        }
    }

    fun deleteRecurringIssue(id: Long) = recurringIssueMapper.deleteRecurringIssue(id)

    fun unassignSubIssueForAssigment(assignmentId: Long) {
        val assignment = issuesTechniciansAssignmentMapper.findById(assignmentId) ?: return
        recurringIssueMapper.unassignSubissue(assignment.issueId)
    }

    fun deleteRecurringIssueBySubIssueId(issueId: Long) {
        recurringIssueMapper.deleteRecurringIssueBySubIssueId(issueId)
    }

    fun unassignSubIssue(issueId: Long) {
        recurringIssueMapper.unassignSubissue(issueId)
    }

    fun findIssueIdsInRecurringIssues() = recurringIssueMapper.findIssueIdsInRecurringIssues()

    private fun convertRecurringIssueToPlannedResource(recurringIssue: RecurringIssueDo): PlannedResourceDto {
        return PlannedResourceDto(
                id = recurringIssue.id,
                start = recurringIssue.placeholderDateFrom,
                end = recurringIssue.placeholderDateTo,
                userId = recurringIssue.userId,
                eventType = EventType.RECURRING_ISSUE_PLACEHOLDER,
                plannedResourceId = recurringIssue.issueId,
                title = PLACEHOLDER_TITLE_KEY,
                description = null,
                status = null
        )
    }
}