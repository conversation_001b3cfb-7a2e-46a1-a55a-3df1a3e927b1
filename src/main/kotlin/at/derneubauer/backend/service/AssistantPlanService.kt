package at.derneubauer.backend.service

import at.derneubauer.backend.db.resourceplan.*
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.stereotype.Service
import java.time.ZonedDateTime


@Service
class AssistantPlanService(private val resourcePlanService: ResourcePlanService,
                           private val techniciansAssistantsAssignmentMapper: TechniciansAssistantsAssignmentMapper)
{

    fun getAssistantResourcePlan(fromDate: ZonedDateTime, mode: ResourcePlanMode): Collection<PlannedResourceDto> {
        val endDate = fromDate.plusDays(resourcePlanService.daysForMode(mode))
        return getPlannedAssistances(fromDate, endDate)
    }

    private fun getPlannedAssistances(fromDate: ZonedDateTime, endDate: ZonedDateTime): Collection<PlannedResourceDto> {
        return techniciansAssistantsAssignmentMapper.findByDate(fromDate, endDate).map { assistantAssignment ->
            convertAssistantAssignmentDoToDto(assistantAssignment)
        }
    }

    private fun getPlannedAssistance(id: Long): PlannedResourceDto? {
        return techniciansAssistantsAssignmentMapper.findById(id)
            .let { convertAssistantAssignmentDoToDto(it) }
    }

    private fun convertAssistantAssignmentDoToDto(assistantAssignment: TechniciansAssistantsAssignmentsDo): PlannedResourceDto {
        return PlannedResourceDto(
            assistantAssignment.id,
            assistantAssignment.startDate,
            assistantAssignment.endDate,
            assistantAssignment.assistantId,
            EventType.ASSISTANT_ASSIGNMENT,
            assistantAssignment.technicianId,
            "${assistantAssignment.technicianLastName} ${assistantAssignment.technicianFirstName}",
            null,
            null
        )
    }

    fun createAssistantTechniciansAssignment(technicianId: Long, assistantId: Long, from: ZonedDateTime, to: ZonedDateTime) : PlannedResourceDto {
        val assistantAssignment = TechniciansAssistantsAssignmentsInsertDo(technicianId, assistantId, from, to)
        techniciansAssistantsAssignmentMapper.createAssistantTechnicianAssignment(assistantAssignment)

        val issueAssignmentId = assistantAssignment.id
            ?: throw NoSuchTechniciansAssistantsAssignmentResourcePlanServiceException()
        return getPlannedAssistance(issueAssignmentId)
            ?: throw NoSuchTechniciansAssistantsAssignmentResourcePlanServiceException()
    }

    fun updateAssistantTechniciansAssignment(id: Long, assistantId: Long, from: ZonedDateTime, to: ZonedDateTime) : PlannedResourceDto {
        var assistantAssignment = TechniciansAssistantsAssignmentsUpdateDo(id, assistantId, from, to)
        techniciansAssistantsAssignmentMapper.editAssistantTechnicianAssignment(assistantAssignment)
        return getPlannedAssistance(id)
            ?: throw NoSuchTechniciansAssistantsAssignmentResourcePlanServiceException()
    }

    fun deleteAssistantTechnicianAssignment(id: Long) {
        techniciansAssistantsAssignmentMapper.deleteAssistantTechnicianAssignment(id)
    }
}

open class AssistantPlanServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class NoSuchTechniciansAssistantsAssignmentResourcePlanServiceException() : AssistantPlanServiceException("No such Technician Assistant Assignment found!") {}