package at.derneubauer.backend.service

import at.derneubauer.backend.offa.service.OffaDocumentUploadException
import at.derneubauer.backend.web.error.NeubauerException
import com.amazonaws.AmazonServiceException
import com.amazonaws.SdkClientException
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import jakarta.annotation.PostConstruct
import org.apache.commons.io.IOUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@Service
class DocumentFileService(
    private val s3Client: AmazonS3,
    @Value("\${neubauer.issue-document-filestore.s3Bucket}") private var s3Bucket: String,
) {
    companion object {
        val log: Logger = LoggerFactory.getLogger(this::class.java)

        const val DOCUMENT_BASE_PATH = "documents"
    }

    @PostConstruct
    private fun createS3BucketIfNecessary() {
        try {
            if (!s3Client.doesBucketExistV2(s3Bucket)) {
                s3Client.createBucket(s3Bucket)
            }
        } catch (ex: SdkClientException) {
            log.error("Error making the request or handling the response from file store", ex)
        } catch (ex: AmazonServiceException) {
            log.error("File store cannot handle the request", ex)
        }
    }

    fun uploadDocumentToS3(document: MultipartFile): String {
        val fileKey = UUID.randomUUID().toString()

        document.inputStream.use { inputStream ->
            val metaData = ObjectMetadata().apply {
                contentLength = document.size
            }

            try {
                s3Client.putObject(
                    s3Bucket,
                    "${DOCUMENT_BASE_PATH}/${fileKey}",
                    inputStream,
                    metaData,
                )
            } catch (e: SdkClientException) {
                throw OffaDocumentUploadException(e)
            }
        }

        return fileKey
    }

    fun downloadDocumentFromS3(key: String): ByteArray {
        try {
            val s3Content = s3Client.getObject(s3Bucket, "$DOCUMENT_BASE_PATH/$key")
            s3Content?.objectContent.use { s3InputStream ->
                return IOUtils.toByteArray(s3InputStream)
            }
        } catch (e: SdkClientException) {
            throw NeubauerException("Could not download document $key from filestorage", e)
        }
    }
}
