package at.derneubauer.backend.service

import at.derneubauer.backend.db.issue.*
import at.derneubauer.backend.db.resourceplan.IssuesTechniciansAssignmentMapper
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class AcceptedIssueService(private val acceptedIssueMapper: AcceptedIssueMapper,
                           private val issueMapper: IssueMapper,
                           private val issueStatusMapper: IssueStatusMapper,
                           private val issuesTechniciansAssignmentMapper: IssuesTechniciansAssignmentMapper) {

    fun allAcceptedIssuesSince(since: ZonedDateTime): Collection<AcceptedIssueDo> {
        return acceptedIssueMapper.findForNonClosedIssuesSince(since)
    }

    fun acceptIssue(technicianId: Long, issueId: Long) {
        val issue = issueMapper.findByIssueId(issueId) ?: throw NoSuchIssueAcceptedIssueServiceException(issueId)

        if(arrayOf(NeubauerIssueStatus.PLANNED, NeubauerIssueStatus.NEW, NeubauerIssueStatus.REOPENED).any { it.getName() == issue.status }) {
            issueMapper.updateIssueStatus(issue.id, technicianId, issue.status, NeubauerIssueStatus.ACCEPTED.getName())
        }

        if(arrayOf(NeubauerIssueStatus.PLANNED, NeubauerIssueStatus.NEW, NeubauerIssueStatus.REOPENED, NeubauerIssueStatus.ACCEPTED).any { it.getName() == issue.status }) {
            acceptedIssueMapper.insert(AcceptedIssueInsertDo(technicianId, technicianId, issueId, accepted = true))
        } else {
            throw WrongStatusAcceptedIssueServiceException(issue.status)
        }
    }

    fun putBackIssue(technicianId: Long, issueId: Long) {
        val issue = issueMapper.findByIssueId(issueId) ?: throw NoSuchIssueAcceptedIssueServiceException(issueId)

        acceptedIssueMapper.insert(AcceptedIssueInsertDo(technicianId, technicianId, issueId, accepted = false))

        if(NeubauerIssueStatus.ACCEPTED.getName() == issue.status) {
            val lastDifferentIssueStatus = issueStatusMapper.findLatestDifferentForIssue(issue.id, issue.status)

            if (lastDifferentIssueStatus != null) {
                issueMapper.updateIssueStatus(issue.id, technicianId, issue.status, lastDifferentIssueStatus.status)
            } else if (acceptedIssueMapper.findByIssueId(issue.id).none { it.accepted }) {
                if (issuesTechniciansAssignmentMapper.findAssignmentsAndTechniciansByIssuesId(issue.id).isEmpty()) {
                    issueMapper.updateIssueStatus(issue.id, technicianId, issue.status, NeubauerIssueStatus.NEW.getName())
                } else {
                    issueMapper.updateIssueStatus(issue.id, technicianId, issue.status, NeubauerIssueStatus.PLANNED.getName())
                }
            }
        }
    }

    fun resetAssignments(issueId: Long, requesterId: Long) {
        acceptedIssueMapper.findByIssueId(issueId).filter { it.accepted }.forEach {
            acceptedIssueMapper.insert(AcceptedIssueInsertDo(it.technicianId, requesterId, issueId, accepted = false))
        }
    }

    fun hasTechnicianAcceptedIssue(issueId: Long, technicianId: Long): Boolean {
        val acceptedIssue = acceptedIssueMapper.findByIssueAndTechnicianId(issueId, technicianId)
            ?: throw IssueNotFoundIssueServiceException(issueId)

        return acceptedIssue.accepted
    }

    fun historyForIssue(issueId: Long): Collection<AcceptedIssueHistoryDo> {
        return acceptedIssueMapper.findWithHistoryByIssueId(issueId)
    }

}

open class AcceptedIssueServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class NoSuchIssueAcceptedIssueServiceException(val issueId: Long) : AcceptedIssueServiceException("Unable to find issue with ID ${issueId}!") {}
class WrongStatusAcceptedIssueServiceException(val status: String) : AcceptedIssueServiceException("Issue cannot be accepted because it has an unexpected status: ${status}!") {}
