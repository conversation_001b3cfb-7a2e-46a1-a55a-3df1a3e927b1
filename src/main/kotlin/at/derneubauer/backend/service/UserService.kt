package at.derneubauer.backend.service

import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.db.user.UserMapper
import at.derneubauer.backend.security.NeubauerRole
import at.derneubauer.backend.util.NeubauerPasswordEncoder
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.dao.DuplicateKeyException
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class UserService(
        private val userMapper: UserMapper,
        private val passwordEncoder: NeubauerPasswordEncoder) {

    fun getAllTechnicians(): Collection<UserDo> {
        return userMapper.findActiveUsersByRoleOrderByLastName(NeubauerRole.TECHNICIAN)
    }

    fun getAllAssistants(): Collection<UserDo> {
        return userMapper.findActiveUsersByRoleOrderByLastName(NeubauerRole.ASSISTANT)
    }

    fun getAllUsers(): Collection<UserDo> {
        return userMapper.findAll()
    }

    fun getAllUsersOrderByUsername(): Collection<UserDo> {
        return userMapper.findAllOrderByUsername()
    }

    fun getUserById(userId: Long): UserDo {
        val user = userMapper.findById(userId)
            ?: throw UserNotFoundUserServiceException(userId)

        return user
    }

    fun createUser(username: String, password: String, firstName: String, lastName: String, phoneNumber: String?, role: String) {
        try {
            val encodedPassword = passwordEncoder.encode(password)

            userMapper.createUser(
                username,
                encodedPassword,
                firstName,
                lastName,
                phoneNumber,
                "de-AT",
                role
            )
        } catch (e: DuplicateKeyException) {
            throw DuplicateUsernameUserServiceException(e)
        }
    }

    fun editUser(userId: Long, username: String, password: String, firstName: String, lastName: String, phoneNumber: String, role: String) {
        try {
            if(password.isNotBlank()) {
                val encodedPassword = passwordEncoder.encode(password)
                userMapper.editUserChangePassword(
                        userId,
                        username,
                        encodedPassword,
                        firstName,
                        lastName,
                        phoneNumber,
                        role
                )
            } else {
                userMapper.editUserKeepPassword(
                        userId,
                        username,
                        firstName,
                        lastName,
                        phoneNumber,
                        role
                )
            }
        } catch(e: DuplicateKeyException) {
            throw DuplicateUsernameUserServiceException(e)
        }
    }

    fun enableUser(userId: Long) {
        userMapper.setDisabledStatusForUser(userId, false)
    }

    fun disableUser(userId: Long) {
        userMapper.setDisabledStatusForUser(userId, true)
    }

    fun findTechniciansCreatedSince(since: ZonedDateTime): Collection<UserDo> {
        return userMapper.findTechniciansCreatedSince(since)
    }

    fun findTechniciansUpdatedSince(since: ZonedDateTime): Collection<UserDo> {
        return userMapper.findTechniciansUpdatedSince(since)
    }
}

open class UserServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class DuplicateUsernameUserServiceException(t: Throwable) : UserServiceException("Duplicate user!", t) {
    override val statusCode = HttpStatus.CONFLICT.value()
    override val localizationKey = "errors.user.duplicateUsername"
}

class UserNotFoundUserServiceException(val userId: Long) : UserServiceException("Unable to find user with id ${userId}") {
    override val statusCode = HttpStatus.NOT_FOUND.value()
    override val localizationKey = "errors.user.notFound"
}
