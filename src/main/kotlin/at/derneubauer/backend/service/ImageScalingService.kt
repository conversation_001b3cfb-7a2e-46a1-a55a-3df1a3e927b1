package at.derneubauer.backend.service

import com.sksamuel.scrimage.ImmutableImage
import com.sksamuel.scrimage.nio.JpegWriter
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class ImageScalingService {

    private companion object {
        const val imageLongestEdgeToScaleTo = 1400
    }

    fun resizeImage(file: MultipartFile): ByteArray {
        val image = ImmutableImage.loader().fromBytes(file.bytes)
        val scaledImage = if (image.width > image.height) {
            image.scaleToWidth(imageLongestEdgeToScaleTo)
        } else {
            image.scaleToHeight(imageLongestEdgeToScaleTo)
        }
        return scaledImage.bytes(JpegWriter())
    }
}
