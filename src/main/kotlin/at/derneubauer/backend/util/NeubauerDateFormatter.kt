package at.derneubauer.backend.util

import java.time.ZoneId
import java.time.format.DateTimeFormatter

class NeubauerDateFormatter {

    companion object {
        val timeZone = ZoneId.of("Europe/Vienna")
        var dateFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy").withZone(timeZone)
        var dateTimeFormatter = DateTimeFormatter.ofPattern("dd.MM.yyyy - HH:mm").withZone(timeZone)
        var timeFormatter = DateTimeFormatter.ofPattern("HH:mm").withZone(timeZone)
        var dateFormatterWithNamedDayAndMonth = DateTimeFormatter.ofPattern("EEEE, dd. MMMM yyyy").withZone(timeZone)
        var fileNameDateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(timeZone)
        var dateTimeWithTzFormatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME
    }
}
