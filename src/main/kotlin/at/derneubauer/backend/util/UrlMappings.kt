package at.derneubauer.backend.util

import at.derneubauer.backend.config.NeubauerConfig
import at.derneubauer.backend.service.EventType
import at.derneubauer.backend.web.userlist.DetailAssignmentPreselectMode
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

@Service
class UrlMappings(private val neubauerConfig: NeubauerConfig) {

    fun detailUrlForEventType(eventType: EventType, plannedResourceId: Long, detailUrlType: DetailUrlType = DetailUrlType.PRIMARY, assignmentId: Long? = null, optionalUrlPathSuffix: String = "", shouldValidate: Boolean = true): String {
        return when (eventType) {
            EventType.ISSUE, EventType.RECURRING_ISSUE_PLACEHOLDER -> {
                val prefix = "${neubauerConfig.baseUrl}/issue/${plannedResourceId}"
                val modePart = when (detailUrlType) {
                    DetailUrlType.PRIMARY -> "?mode=${DetailAssignmentPreselectMode.DETAIL}"
                    DetailUrlType.SECONDARY -> "?mode=${DetailAssignmentPreselectMode.ASSIGNMENT}"
                    DetailUrlType.TERTIARY -> "?mode=${DetailAssignmentPreselectMode.RECURRING_ISSUE}"
                }
                val assignmentIdPart = if (assignmentId != null) "&assignment=${assignmentId}" else ""
                val shouldValidatePart = "&shouldValidate=${shouldValidate}"

                "${prefix}${optionalUrlPathSuffix}${modePart}${assignmentIdPart}${shouldValidatePart}"
            }
            EventType.ABSENCE -> "${neubauerConfig.baseUrl}/absence/${plannedResourceId}"
            EventType.ASSISTANT_ASSIGNMENT -> ""
        }
    }

    fun recurringIssueUrl(plannedResourceId: Long, assignmentId: Long? = null): String {
        val assignmentIdPart = if (assignmentId != null) "&assignment=${assignmentId}" else ""
        return "${neubauerConfig.baseUrl}/issue/${plannedResourceId}/recurringIssue?mode=${DetailAssignmentPreselectMode.RECURRING_ISSUE}${assignmentIdPart}"
    }

    fun assignSubissueUrl(issueId: Long, assignmentId: Long? = null): String {
        val assignmentIdPart = if (assignmentId != null) "?assignment=${assignmentId}" else ""
        return "${neubauerConfig.baseUrl}/issue/${issueId}/subIssueAssignment${assignmentIdPart}"
    }

    fun issueWithBaseUrl(issueId: Long, assignmentId: Long? = null, hasSubIssuesLeft: Boolean): String {
        val assignmentIdPart = if (assignmentId != null) "&assignment=${assignmentId}" else ""

        val mode = if (assignmentId == null && !hasSubIssuesLeft) {
            // assignmentId == null means that the deletion was performed within the detail view of the issue itself and
            // not the calendar entry, within this detail view the tab "recurring issues" is only available if there are
            // any subissues, so in order to avoid staying on a white page / non-existing tab after deleting the placeholders
            // (in case the recurring issue only had placeholders as subissues) redirect to the DETAIL tab
            DetailAssignmentPreselectMode.DETAIL
        } else {
            // if the deletion happened from the detail view of the calendar entry or the issue
            // has still non-placeholder issues redirect / stay on the recurring issue tab
            DetailAssignmentPreselectMode.RECURRING_ISSUE
        }

        return "${neubauerConfig.baseUrl}/issue/${issueId}?mode=${mode}${assignmentIdPart}"
    }

    fun issueUrl(plannedResourceId: Long?, assignmentId: Long? = null): String? {
        if (plannedResourceId == null) return null

        val assignmentIdPart = if (assignmentId != null) "&assignment=${assignmentId}" else ""
        return "${plannedResourceId}?mode=${DetailAssignmentPreselectMode.RECURRING_ISSUE}${assignmentIdPart}"
    }

    fun dateUrl(date: ZonedDateTime): String {
        val formattedDate = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(date)
        return "${neubauerConfig.baseUrl}/scheduler/technician?date=$formattedDate"
    }

    fun scanUploadUrl(issueId: Long): String {
        return "${neubauerConfig.baseUrl}/issue/${issueId}/scan"
    }

    fun scanDeleteBaseUrl(): String {
        return "${neubauerConfig.baseUrl}/issue/scan"
    }
}

enum class DetailUrlType {
    PRIMARY,
    SECONDARY,
    TERTIARY
}
