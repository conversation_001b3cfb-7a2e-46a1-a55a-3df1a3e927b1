package at.derneubauer.backend.util

import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Component
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import org.thymeleaf.context.ITemplateContext
import org.thymeleaf.dialect.AbstractProcessorDialect
import org.thymeleaf.engine.AttributeName
import org.thymeleaf.model.IProcessableElementTag
import org.thymeleaf.processor.IProcessor
import org.thymeleaf.processor.element.AbstractAttributeTagProcessor
import org.thymeleaf.processor.element.IElementTagStructureHandler
import org.thymeleaf.standard.processor.AbstractStandardExpressionAttributeTagProcessor
import org.thymeleaf.templatemode.TemplateMode

@Component
class BpoDialect(val permissionService: PermissionService) : AbstractProcessorDialect("bytepoetsProcessor", "bpo", 1000) {

    override fun getProcessors(dialectPrefix: String?): MutableSet<IProcessor> {
        val processors = HashSet<IProcessor>()
        processors.add(BpoSecurityProcessor(permissionService))
        processors.add(BpoDisableIfNoPermissionProcessor(permissionService))
        processors.add(BpoActiveNavigationItemProcessor())
        return processors
    }
}

class BpoSecurityProcessor(val permissionService: PermissionService) : AbstractStandardExpressionAttributeTagProcessor(TemplateMode.HTML, "bpo", "required-permission", 1000, true, false) {

    override fun doProcess(context: ITemplateContext?, tag: IProcessableElementTag?, attributeName: AttributeName?, attributeValue: String?, expressionResult: Any?, structureHandler: IElementTagStructureHandler?) {
        val requiredPermissionString = attributeValue ?: return
        val permission = NeubauerPermission.valueOf(requiredPermissionString)

        if (!permissionService.loggedInUserHasPermission(permission)) {
            structureHandler?.removeElement()
        }
    }
}

class BpoDisableIfNoPermissionProcessor(val permissionService: PermissionService) : AbstractStandardExpressionAttributeTagProcessor(TemplateMode.HTML, "bpo", "disabled-without-permission", 999, false, false) {

    override fun doProcess(context: ITemplateContext?, tag: IProcessableElementTag?, attributeName: AttributeName?, attributeValue: String?, expressionResult: Any?, structureHandler: IElementTagStructureHandler?) {
        val requiredPermissionString = attributeValue ?: return
        val permission = NeubauerPermission.valueOf(requiredPermissionString)

        if (!permissionService.loggedInUserHasPermission(permission)) {
            structureHandler?.setAttribute("disabled", "")
        }
    }
}

class BpoActiveNavigationItemProcessor : AbstractAttributeTagProcessor(TemplateMode.HTML, "bpo", null, false, "mark-if-active-nav-item", true, 998, true) {

    override fun doProcess(context: ITemplateContext?, tag: IProcessableElementTag?, attributeName: AttributeName?, attributeValue: String?, structureHandler: IElementTagStructureHandler?) {
        attributeValue ?: return
        val requestAttributes = RequestContextHolder.currentRequestAttributes() as? ServletRequestAttributes
        val requestUri = requestAttributes?.request?.requestURI

        if (requestUri?.startsWith(attributeValue, ignoreCase = true) == true) {
            val oldClasses = tag?.getAttributeValue("class")
            val newClasses = StringUtils.join(arrayOf(oldClasses, "active").filterNotNull(), ' ')
            structureHandler?.setAttribute("class", newClasses)
        }
    }
}
