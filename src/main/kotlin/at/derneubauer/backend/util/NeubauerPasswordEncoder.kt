package at.derneubauer.backend.util

import at.derneubauer.backend.web.error.NeubauerException
import org.apache.commons.lang3.StringUtils
import org.springframework.http.HttpStatus
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service

@Service
class NeubauerPasswordEncoder(val passwordEncoder: PasswordEncoder) : PasswordEncoder {
    override fun encode(rawPassword: CharSequence): String {
        checkIfPasswordMatchesCriteria(rawPassword)
        val encodedPassword = passwordEncoder.encode(rawPassword)

        if (!passwordEncoder.matches(rawPassword, encodedPassword)) {
            throw InvalidPasswordException("Password contains invalid characters, please try another one")
        }

        return encodedPassword
    }

    override fun matches(rawPassword: CharSequence, encodedPassword: String): <PERSON><PERSON><PERSON> {
        return passwordEncoder.matches(rawPassword, encodedPassword)
    }

    protected fun checkIfPasswordMatchesCriteria(rawPassword: CharSequence) {
        if (StringUtils.isBlank(rawPassword) || StringUtils.length(rawPassword) < 8) {
            throw PasswordTooShortException("Password is too short")
        }

        if (StringUtils.length(rawPassword) > 40) {
            throw PasswordTooLongException("Password is too long")
        }

        Regex("""^(?=.*?\p{Lower})(?=.*?\p{Upper})(?=.*?\p{Digit}).+$""").find(rawPassword)
            ?: throw PasswordComplexityException("Password must contain at least one lower case letter, one upper case letter and one digit")

    }
}

open class InvalidPasswordException(message: String?) : NeubauerException(message) {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
    override val localizationKey = "errors.password.invalid.generic"
}

class PasswordTooShortException(message: String?) : InvalidPasswordException(message) {
    override val localizationKey = "errors.password.invalid.tooShort"
}

class PasswordTooLongException(message: String?) : InvalidPasswordException(message) {
    override val localizationKey = "errors.password.invalid.tooLong"
}

class PasswordComplexityException(message: String?) : InvalidPasswordException(message) {
    override val localizationKey = "errors.password.invalid.criteriaNotMet"
}
