package at.derneubauer.backend.util

import at.derneubauer.backend.service.EventType
import org.springframework.stereotype.Service

data class GradientColor(
    val startColor: String,
    var endColor: String
)

@Service
class ColorMappings {

    private val issueStatusColors: Map<String, GradientColor> = mapOf(
        "NEW" to GradientColor("#6398D7", "#245288"),
        "REOPENED" to GradientColor("#89C9F9", "#5E93BC"),
        "PLANNED" to GradientColor("#FBE160", "#F1B115"),
        "ACCEPTED" to GradientColor("#A0D962", "#769B29"),
        "NOT_STARTED" to GradientColor("#F8703F", "#CD1212"),
        "DONE_TECHNICIAN" to GradientColor("#8F74B0", "#54347B"),
        "CLOSED" to GradientColor("#7B8894", "#323232")
    )

    fun forEventType(eventType: EventType, status: String?): GradientColor {
        val gradient = when (eventType) {
            EventType.ISSUE -> issueStatusColors[status]
            EventType.ABSENCE -> GradientColor("#FF9728", "#D45F01")
            EventType.ASSISTANT_ASSIGNMENT -> GradientColor("#A0D962", "#769B29")
            EventType.RECURRING_ISSUE_PLACEHOLDER -> GradientColor("#CECECE", "#A8A8A8")
        }

        return gradient ?: GradientColor("#FFFFFF", "#FFFFFF")
    }
}
