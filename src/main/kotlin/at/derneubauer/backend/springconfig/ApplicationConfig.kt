package at.derneubauer.backend.springconfig

import at.derneubauer.backend.config.NeubauerConfig
import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.client.builder.AwsClientBuilder
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.AmazonS3ClientBuilder
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import org.apache.commons.lang3.StringUtils
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.context.support.ResourceBundleMessageSource
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean
import org.springframework.web.servlet.LocaleResolver
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver
import java.util.*


@Configuration
class ApplicationConfig(val neubauerConfig: NeubauerConfig) {

    @Bean
    fun localeResolver(): LocaleResolver {
//        val resolver = SessionLocaleResolver() // session resolver uses default locale before it uses accept header
        val resolver = AcceptHeaderLocaleResolver()
        resolver.setDefaultLocale(Locale.ENGLISH)
        return resolver
    }

    @Bean
    fun messageSource(): ResourceBundleMessageSource {
        val source = ResourceBundleMessageSource()
        source.setBasename("i18n/messages")
        source.setDefaultEncoding("UTF-8")
        source.setUseCodeAsDefaultMessage(true)
        return source
    }

    @Bean
    fun validator(): LocalValidatorFactoryBean {
        val bean = LocalValidatorFactoryBean()
        bean.setValidationMessageSource(messageSource())
        return bean
    }

    @Bean
    @Primary
    fun objectMapper(builder: Jackson2ObjectMapperBuilder): ObjectMapper {
        val objectMapper: ObjectMapper = builder.createXmlMapper(false).build()
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
        return objectMapper
    }

    @Bean
    fun threadPoolTaskScheduler(): ThreadPoolTaskScheduler {
        val threadPoolTaskScheduler = ThreadPoolTaskScheduler()
        threadPoolTaskScheduler.poolSize = 5
        threadPoolTaskScheduler.threadNamePrefix = "ThreadPoolTaskScheduler"
        return threadPoolTaskScheduler
    }

    @Bean
    fun s3Client(): AmazonS3 {
        val awsCredentials = BasicAWSCredentials(neubauerConfig.filestore!!.s3AccessKeyId, neubauerConfig.filestore!!.s3SecretAccessKey)

        val s3ClientBuilder = AmazonS3ClientBuilder
            .standard()
            .withCredentials(AWSStaticCredentialsProvider(awsCredentials))

        if (StringUtils.isNotBlank(neubauerConfig.filestore!!.s3Endpoint)) {
            s3ClientBuilder.withEndpointConfiguration(AwsClientBuilder.EndpointConfiguration(neubauerConfig.filestore!!.s3Endpoint!!, neubauerConfig.filestore!!.s3Region))
        } else {
            s3ClientBuilder.withRegion(Regions.fromName(neubauerConfig.filestore!!.s3Region))
        }

        if (neubauerConfig.filestore!!.pathStyleAccessEnabled) {
            s3ClientBuilder.withPathStyleAccessEnabled(true)
        }

        return s3ClientBuilder.build()
    }

}
