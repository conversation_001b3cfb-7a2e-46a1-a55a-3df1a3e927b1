package at.derneubauer.backend.springconfig

import at.derneubauer.backend.config.NeubauerConfig
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisStandaloneConfiguration
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory
import org.springframework.session.data.redis.config.ConfigureRedisAction
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession

// @see
// https://docs.spring.io/spring-session/docs/current/reference/html5/guides/java-redis.html

// session timeout was previously -1, so the session would never expure. this does not work anymore with spring boot 3.
// the default is 30 minutes. we set it to 30 days
// so if a user does not send a request within 30 days of his last request, he will be logged out

@Configuration
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 2_592_000) // 30 days in seconds 
class HttpSessionConfiguration(val config: NeubauerConfig) {

    // disable automatic configuration of redis instance as it fails on secured instances (e.g. amazon elasticache)
    @Bean
    fun configureRedisAction(): ConfigureRedisAction = ConfigureRedisAction.NO_OP

    @Bean
    fun connectionFactory(): LettuceConnectionFactory {
        return LettuceConnectionFactory(
            RedisStandaloneConfiguration(
                config.spring!!.data!!.redis!!.host,
                config.spring!!.data!!.redis!!.port
            )
        )
    }
}
