package at.derneubauer.backend.web.ressourceplan

import at.derneubauer.backend.db.issue.IssueDo
import at.derneubauer.backend.db.issue.IssueWithTechnicianAssignmentsDo
import at.derneubauer.backend.db.issue.PaginatedSearchQueryParameters
import at.derneubauer.backend.rest.resourceplan.ResourcePlanConverterService
import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.NeubauerRole
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.*
import at.derneubauer.backend.util.ColorMappings
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.util.UrlMappings
import org.slf4j.LoggerFactory
import org.springframework.context.MessageSource
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.view.RedirectView
import java.time.Instant
import java.time.ZonedDateTime
import java.util.*
import jakarta.validation.Valid

@RestController
@RequestMapping("/admin/api/v1")
class ResourcePlanController(private val userResolver: LoggedInUserResolver,
                             private val permissionService: PermissionService,
                             private val resourcePlanService: ResourcePlanService,
                             private val issueService: IssueService,
                             private val urlMappings: UrlMappings,
                             private val colorMappings: ColorMappings,
                             private val messageSource: MessageSource,
                             private val converter: ResourcePlanConverterService,
                             private val absenceService: AbsenceService,
                             private val recurringIssueService: RecurringIssueService) {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    @GetMapping("issues/open")
    fun openIssues(locale: Locale): Collection<IssueResponse> {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_LIST, user)

        val recurringIssuesIds = recurringIssueService.findIssueIdsInRecurringIssues()

        return issueService.allOpenIssues().map { issue ->
            val isRecurring = recurringIssuesIds.contains(issue.id)
            convertIssueToIssueResponse(issue, locale, isRecurring)
        }
    }

    @GetMapping("issues/assignable")
    fun assignableSubIssues(locale: Locale): Collection<IssueResponse> {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_LIST, user)

        return issueService.findAssignableSubIssues().map { issue ->
            convertIssueToIssueResponse(issue, locale, false)
        }
    }

    @GetMapping("issues/all")
    fun allIssues(locale: Locale, @RequestParam take: Int, @RequestParam skip: Int, @RequestParam getParams: Map<String, Any>?): PaginatedIssuesWithTechnicianAssignmentsResponse {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ISSUE_LIST, user)

        val queryParameters = PaginatedSearchQueryParameters(skip, take)

        if (getParams != null) {
            val filterKeys = getParams.keys.filter { it.contains("""filter\[filters\]\[[0-9]\]\[field\]""".toRegex()) }
            filterKeys.forEach {
                val filterName = getParams[it].toString()
                val filterValue = getParams[it.replace("field", "value")]

                when (filterName) {
                    "address" -> queryParameters.address = filterValue.toString()
                    "externalId" -> queryParameters.externalId = filterValue.toString()
                    "contactPerson" -> queryParameters.contactPerson = filterValue.toString()
                    "status" -> queryParameters.status = filterValue.toString().replace("ALL", "")
                    "description" -> queryParameters.description = filterValue.toString()
                }
            }
        }

        val issues = issueService.allIssuesWithAssignmentsPaginated(queryParameters)

        val data = issues.map { issue ->
            val assignmentStartAndEndDateStrings = issue.assignmentDates?.split(',') ?: listOf<String>()

            val assignmentDates = assignmentStartAndEndDateStrings.map { dateString ->
                convertToAssignmentDate(dateString)
            }

            convertIssueAssignmentToResponse(assignmentDates, issue, locale)
        }

        val totalCount = issueService.getTotalIssueCount(queryParameters)

        return PaginatedIssuesWithTechnicianAssignmentsResponse(
                data,
                totalCount
        )
    }

    private fun convertIssueToIssueResponse(issue: IssueDo, locale: Locale, isRecurring: Boolean): IssueResponse {
        return IssueResponse(issue.id,
                issue.externalId,
                issue.contactPerson ?: "",
                issue.address ?: "",
                issue.suggestedDate ?: "",
                issue.description ?: "",
                issue.note ?: "",
                messageSource.getMessage(NeubauerIssueStatus.valueOf(issue.status).localizationKey, arrayOf(), locale),
                urlMappings.detailUrlForEventType(EventType.ISSUE, issue.id),
                "",
                EventType.ISSUE,
                isRecurring
        )
    }

    private fun convertIssueAssignmentToResponse(assignmentDates: List<AssignmentDate>, issue: IssueWithTechnicianAssignmentsDo, locale: Locale): IssuesWithTechnicianAssignmentsResponse {
        val latestAssignmentDate = assignmentDates.firstOrNull()?.start

        val gradientColor = colorMappings.forEventType(EventType.ISSUE, issue.status)

        return IssuesWithTechnicianAssignmentsResponse(
                issue.id,
                issue.externalId,
                issue.contactPerson ?: "",
                issue.address ?: "",
                issue.description ?: "",
                messageSource.getMessage(NeubauerIssueStatus.valueOf(issue.status).localizationKey, arrayOf(), locale),
                issue.countAssignments,
                latestAssignmentDate ?: "",
                assignmentDates,
                gradientColor.startColor,
                gradientColor.endColor,
                urlMappings.detailUrlForEventType(EventType.ISSUE, issue.id),
                ""
        )
    }

    private fun convertToAssignmentDate(dateString: String): AssignmentDate {
        val assignmentStartAndEndDates = dateString.split('-')
        val assignmentStartUtcTime = Instant.ofEpochSecond(assignmentStartAndEndDates[0].toLong())
        val assignmentEndUtcTime = Instant.ofEpochSecond(assignmentStartAndEndDates[1].toLong())
        return AssignmentDate(
                NeubauerDateFormatter.dateTimeFormatter.format(assignmentStartUtcTime),
                NeubauerDateFormatter.dateTimeFormatter.format(assignmentEndUtcTime)
        )
    }

    @GetMapping("technicians/assignments")
    fun technicianAssignments(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: ZonedDateTime,
                              @RequestParam mode: ResourcePlanMode,
                              locale: Locale): Collection<UserAssignmentResponseItem> {
        val user = userResolver.resolveLoggedInUser()

        val issues = mutableListOf<UserAssignmentResponseItem>()

        if (permissionService.hasPermission(NeubauerPermission.VIEW_TECHNICIAN_ASSIGNMENT_LIST, user)) {
            val allRecurringIds = recurringIssueService.findIssueIdsInRecurringIssues()
            issues.addAll(resourcePlanService.getResourcePlan(from, mode).map {
                val isRecurring = allRecurringIds.contains(it.plannedResourceId)
                converter.convertAssignmentToResponseModel(it, locale, isRecurring)
            })
        }

        if (permissionService.hasPermission(NeubauerPermission.VIEW_TECHNICIAN_ABSENCES, user)) {
            issues.addAll(absenceService.getAbsencesForResourcePlan(NeubauerRole.TECHNICIAN, from, mode).map {
                converter.convertAssignmentToResponseModel(it, locale)
            })
        }

        if (permissionService.hasPermission(NeubauerPermission.VIEW_RECURRING_ISSUE_PLACEHOLDERS, user)) {
            issues.addAll(recurringIssueService.getRecurringIssuePlaceholdersForResourcePlan(from, mode).map {
                converter.convertAssignmentToResponseModel(it, locale, true)
            })
        }

        return issues
    }

    @PostMapping("technicians/assignments/create")
    fun createTechnicianAssignment(@Valid @RequestBody techniciansAssignmentsRequest: CreateAssignmentsRequest, locale: Locale): UserAssignmentResponseItem {
        val user = userResolver.resolveLoggedInUser()

        if (techniciansAssignmentsRequest.eventType == EventType.ABSENCE) {
            permissionService.ensurePermission(NeubauerPermission.CREATE_ABSENCE, user)
            return absenceService.createAbsence(
                    techniciansAssignmentsRequest.userId,
                    techniciansAssignmentsRequest.start,
                    techniciansAssignmentsRequest.end
            ).let { converter.convertAssignmentToResponseModel(it, locale) }
        } else {
            permissionService.ensurePermission(NeubauerPermission.CREATE_ISSUE_TECHNICIAN_ASSIGNMENT, user)
            val allRecurringIds = recurringIssueService.findIssueIdsInRecurringIssues()
            val isRecurring = allRecurringIds.contains(techniciansAssignmentsRequest.plannedResourceId)

            return resourcePlanService.createIssueTechniciansAssignment(
                    user.id,
                    techniciansAssignmentsRequest.plannedResourceId,
                    techniciansAssignmentsRequest.userId,
                    techniciansAssignmentsRequest.start,
                    techniciansAssignmentsRequest.end
            ).let { converter.convertAssignmentToResponseModel(it, locale, isRecurring) }
        }
    }

    @PostMapping("technicians/assignments/update")
    fun updateTechnicianAssignment(@Valid @RequestBody techniciansAssignmentsRequest: UpdateAssignmentsRequest, locale: Locale): UserAssignmentResponseItem? {
        val user = userResolver.resolveLoggedInUser()

        when (techniciansAssignmentsRequest.eventType) {
            EventType.ABSENCE -> {
                permissionService.ensurePermission(NeubauerPermission.EDIT_ABSENCE, user)
                return absenceService.editAbsenceDragAndDrop(
                        techniciansAssignmentsRequest.id,
                        techniciansAssignmentsRequest.userId,
                        techniciansAssignmentsRequest.start,
                        techniciansAssignmentsRequest.end
                ).let { converter.convertAssignmentToResponseModel(it, locale) }
            }
            EventType.RECURRING_ISSUE_PLACEHOLDER -> {
                permissionService.ensurePermission(NeubauerPermission.EDIT_RECURRING_ISSUE_PLACEHOLDERS, user)
                return recurringIssueService.updateRecurringIssue(
                        techniciansAssignmentsRequest.id,
                        techniciansAssignmentsRequest.userId,
                        techniciansAssignmentsRequest.start,
                        techniciansAssignmentsRequest.end
                ).let { converter.convertAssignmentToResponseModel(it, locale, true) }
            }
            else -> {
                permissionService.ensurePermission(NeubauerPermission.EDIT_ISSUE_TECHNICIAN_ASSIGNMENT, user)
                return resourcePlanService.updateIssueTechniciansAssignment(
                        techniciansAssignmentsRequest.id,
                        techniciansAssignmentsRequest.userId,
                        techniciansAssignmentsRequest.start,
                        techniciansAssignmentsRequest.end
                ).let {
                    val allRecurringIssues = recurringIssueService.findIssueIdsInRecurringIssues()
                    val isRecurring = allRecurringIssues.contains(it.plannedResourceId)
                    converter.convertAssignmentToResponseModel(it, locale, isRecurring)
                }
            }
        }
    }

    @PostMapping("technicians/assignments/delete")
    fun deleteTechnicianAssignment(@Valid @RequestBody techniciansAssignmentsRequest: DeleteAssignmentsRequest) {
        val user = userResolver.resolveLoggedInUser()

        when (techniciansAssignmentsRequest.eventType) {
            EventType.ABSENCE -> {
                permissionService.ensurePermission(NeubauerPermission.DELETE_ABSENCE, user)
                absenceService.deleteAbsenceAssignment(techniciansAssignmentsRequest.id)
            }
            EventType.RECURRING_ISSUE_PLACEHOLDER -> {
                permissionService.ensurePermission(NeubauerPermission.DELETE_RECURRING_ISSUE_PLACEHOLDERS, user)
                recurringIssueService.deleteRecurringIssue(techniciansAssignmentsRequest.id)
            }
            else -> {
                permissionService.ensurePermission(NeubauerPermission.DELETE_ISSUE_TECHNICIAN_ASSIGNMENT, user)
                recurringIssueService.unassignSubIssueForAssigment(techniciansAssignmentsRequest.id)
                resourcePlanService.deleteIssueTechnicianAssignment(
                        techniciansAssignmentsRequest.id,
                        user.id
                )
            }
        }
    }

    @GetMapping(
        value = [
            "technicians/assignments/deleteIssuePlaceholder/{issueId}/{assignmentId}",
            "technicians/assignments/deleteIssuePlaceholder/{issueId}"
        ]
    )
    fun deleteIssuePlaceholder(
        @PathVariable("issueId") issueId: Long,
        @PathVariable("assignmentId") assignmentId: Long?,
    ): RedirectView {
        val user = userResolver.resolveLoggedInUser()
        val recurringIssues = recurringIssueService.findRecurringIssuesForIssueId(issueId)
        permissionService.ensurePermission(NeubauerPermission.DELETE_RECURRING_ISSUE_PLACEHOLDERS, user)

        var nonPlaceholderIssues = 0
        recurringIssues.forEach {
            if (it.subIssueId == null) {
                recurringIssueService.deleteRecurringIssue(it.id)
            } else {
                nonPlaceholderIssues++;
            }
        }

        val redirectUrl = urlMappings.issueWithBaseUrl(
            issueId = issueId,
            assignmentId = assignmentId,
            hasSubIssuesLeft = nonPlaceholderIssues > 0
        )
        val redirectView = RedirectView(redirectUrl, true)

        // redirect using HTTP 303 with location, otherwise 302 is sent and http is requested which is upgraded to https again
        redirectView.setHttp10Compatible(false)
        return redirectView
    }
}

data class CreateAssignmentsRequest(
        val plannedResourceId: Long,
        val userId: Long,
        val eventType: EventType,
        val start: ZonedDateTime,
        val end: ZonedDateTime
)

data class UpdateAssignmentsRequest(
        val id: Long,
        val userId: Long,
        val eventType: EventType,
        val start: ZonedDateTime,
        val end: ZonedDateTime
)

data class DeleteAssignmentsRequest(
        val id: Long,
        val eventType: EventType
)

data class UserAssignmentResponseItem(
        val id: Long,
        val start: ZonedDateTime,
        val end: ZonedDateTime,
        val userId: Long,
        val eventType: EventType,
        val title: String,
        val description: String?,
        val gradientStartColor: String,
        val gradientEndColor: String,
        val detailUrl: String,
        val secondaryDetailUrl: String,
        val recurringDetailUrl: String,
        val isRecurringIssue: Boolean
)

data class IssueResponse(
        val id: Long,
        val externalId: String,
        val contactPerson: String,
        val address: String,
        val suggestedDate: String,
        val description: String,
        val note: String,
        val status: String,
        val detailUrl: String,
        val secondaryDetailUrl: String,
        val eventType: EventType,
        val isRecurringIssue: Boolean
)

data class IssuesWithTechnicianAssignmentsResponse(
        val id: Long,
        val externalId: String,
        val contactPerson: String,
        val address: String,
        val description: String,
        val status: String,
        val countAssignments: Int,
        val latestAssignmentDateStart: String,
        val assignmentDates: Collection<AssignmentDate>,
        val gradientStartColor: String,
        val gradientEndColor: String,
        val detailUrl: String,
        val secondaryDetailUrl: String
)

data class AssignmentDate(
        val start: String,
        val end: String
)

data class PaginatedIssuesWithTechnicianAssignmentsResponse(
        val data: Collection<IssuesWithTechnicianAssignmentsResponse>,
        val total: Int
)
