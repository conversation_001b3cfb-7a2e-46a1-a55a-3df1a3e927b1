package at.derneubauer.backend.rest.resourceplan

import at.derneubauer.backend.db.resourceplan.IssuesTechniciansAssignmentMapper
import at.derneubauer.backend.service.EventType
import at.derneubauer.backend.service.PlannedResourceDto
import at.derneubauer.backend.util.ColorMappings
import at.derneubauer.backend.util.DetailUrlType
import at.derneubauer.backend.util.UrlMappings
import at.derneubauer.backend.web.ressourceplan.UserAssignmentResponseItem
import org.springframework.context.MessageSource
import org.springframework.stereotype.Service
import java.util.*

@Service
class ResourcePlanConverterService(private val colorMappings: ColorMappings,
                                   private val urlMappings: UrlMappings,
                                   private val messageSource: MessageSource,
                                   private val issuesTechniciansAssignmentMapper: IssuesTechniciansAssignmentMapper) {

    fun convertAssignmentToResponseModel(assignment: PlannedResourceDto, locale: Locale, isRecurringIssue: Boolean = false): UserAssignmentResponseItem {
        val gradientColor = colorMappings.forEventType(assignment.eventType, assignment.status)

        val title = when (assignment.eventType) {
            EventType.ABSENCE, EventType.RECURRING_ISSUE_PLACEHOLDER -> messageSource.getMessage(assignment.title, arrayOf(), locale)
            else -> assignment.title
        }

        val id = if (assignment.eventType == EventType.RECURRING_ISSUE_PLACEHOLDER) {
            val issueAssignment = issuesTechniciansAssignmentMapper.findById(assignment.plannedResourceId)
            issueAssignment?.id
        } else {
            assignment.id
        }

        return UserAssignmentResponseItem(
                id = assignment.id,
                start = assignment.start,
                end = assignment.end,
                userId = assignment.userId,
                eventType = assignment.eventType,
                title = title,
                description = assignment.description,
                gradientStartColor = gradientColor.startColor,
                gradientEndColor = gradientColor.endColor,
                detailUrl = urlMappings.detailUrlForEventType(assignment.eventType, assignment.plannedResourceId, DetailUrlType.PRIMARY, id),
                secondaryDetailUrl = urlMappings.detailUrlForEventType(assignment.eventType, assignment.plannedResourceId, DetailUrlType.SECONDARY, id),
                recurringDetailUrl = urlMappings.detailUrlForEventType(assignment.eventType, assignment.plannedResourceId, DetailUrlType.TERTIARY, id),
                isRecurringIssue = isRecurringIssue
        )
    }
}
