package at.derneubauer.backend.rest.resourceplan

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.NeubauerRole
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.*
import at.derneubauer.backend.web.ressourceplan.*
import org.slf4j.LoggerFactory
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import java.time.ZonedDateTime
import java.util.*
import jakarta.validation.Valid

@RestController
@RequestMapping("/admin/api/v1")
class AssistantPlanController(private val userResolver: LoggedInUserResolver,
                              private val permissionService: PermissionService,
                              private val assistantPlanService: AssistantPlanService,
                              private val absenceService: AbsenceService,
                              private val converter: ResourcePlanConverterService) {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    @GetMapping("assistants/assignments")
    fun assistantAssignments(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) from: ZonedDateTime,
                             @RequestParam mode: ResourcePlanMode,
                             locale: Locale): Collection<UserAssignmentResponseItem> {
        val user = userResolver.resolveLoggedInUser()

        var assignments = mutableListOf<UserAssignmentResponseItem>()

        if (permissionService.hasPermission(NeubauerPermission.VIEW_ASSISTANT_ASSIGNMENT_LIST, user)) {
            assignments.addAll(assistantPlanService.getAssistantResourcePlan(from, mode).map {
                converter.convertAssignmentToResponseModel(it, locale)
            })
        }

        if (permissionService.hasPermission(NeubauerPermission.VIEW_ASSISTANT_ABSENCES, user)) {
            assignments.addAll(absenceService.getAbsencesForResourcePlan(NeubauerRole.ASSISTANT, from, mode).map {
                converter.convertAssignmentToResponseModel(it, locale)
            })
        }

        return assignments
    }

    @PostMapping("assistants/assignments/create")
    fun createAssistantAssignment(@Valid @RequestBody assistantAssignmentsRequest: CreateAssignmentsRequest, locale: Locale): UserAssignmentResponseItem {
        val user = userResolver.resolveLoggedInUser()

        if(assistantAssignmentsRequest.eventType == EventType.ABSENCE) {
            permissionService.ensurePermission(NeubauerPermission.CREATE_ABSENCE, user)
            return absenceService.createAbsence(
                assistantAssignmentsRequest.userId,
                assistantAssignmentsRequest.start,
                assistantAssignmentsRequest.end
            ).let { converter.convertAssignmentToResponseModel(it, locale) }
        } else {
            permissionService.ensurePermission(NeubauerPermission.CREATE_ASSISTANT_TECHNICIAN_ASSIGNMENT, user)
            return assistantPlanService.createAssistantTechniciansAssignment(
                technicianId = assistantAssignmentsRequest.plannedResourceId,
                assistantId = assistantAssignmentsRequest.userId,
                from = assistantAssignmentsRequest.start,
                to = assistantAssignmentsRequest.end
            ).let { converter.convertAssignmentToResponseModel(it, locale) }
        }
    }

    @PostMapping("assistants/assignments/update")
    fun updateAssistantAssignment(@Valid @RequestBody assistantAssignmentsRequest: UpdateAssignmentsRequest, locale: Locale): UserAssignmentResponseItem? {
        val user = userResolver.resolveLoggedInUser()

        if(assistantAssignmentsRequest.eventType == EventType.ABSENCE) {
            permissionService.ensurePermission(NeubauerPermission.EDIT_ABSENCE, user)
            return absenceService.editAbsenceDragAndDrop(
                assistantAssignmentsRequest.id,
                assistantAssignmentsRequest.userId,
                assistantAssignmentsRequest.start,
                assistantAssignmentsRequest.end
            ).let { converter.convertAssignmentToResponseModel(it, locale) }
        } else {
            permissionService.ensurePermission(NeubauerPermission.EDIT_ASSISTANT_TECHNICIAN_ASSIGNMENT, user)
            return assistantPlanService.updateAssistantTechniciansAssignment(
                id = assistantAssignmentsRequest.id,
                assistantId = assistantAssignmentsRequest.userId,
                from = assistantAssignmentsRequest.start,
                to = assistantAssignmentsRequest.end
            ).let { converter.convertAssignmentToResponseModel(it, locale) }
        }
    }

    @PostMapping("assistants/assignments/delete")
    fun deleteAssistantAssignment(@Valid @RequestBody techniciansAssignmentsRequest: DeleteAssignmentsRequest) {
        val user = userResolver.resolveLoggedInUser()

        if(techniciansAssignmentsRequest.eventType == EventType.ABSENCE) {
            permissionService.ensurePermission(NeubauerPermission.DELETE_ABSENCE, user)
            absenceService.deleteAbsenceAssignment(techniciansAssignmentsRequest.id)
        } else {
            permissionService.ensurePermission(NeubauerPermission.DELETE_ASSISTANT_TECHNICIAN_ASSIGNMENT, user)
            assistantPlanService.deleteAssistantTechnicianAssignment(techniciansAssignmentsRequest.id)
        }
    }
}
