package at.derneubauer.backend.rest.resourceplan

import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.NeubauerRole
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.*
import at.derneubauer.backend.util.NeubauerDateFormatter
import org.springframework.context.MessageSource
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*
import org.thymeleaf.TemplateEngine
import org.thymeleaf.context.Context
import org.xhtmlrenderer.pdf.ITextRenderer
import java.io.ByteArrayOutputStream
import java.time.ZonedDateTime
import java.util.*
import jakarta.servlet.http.HttpServletResponse


@RestController
@RequestMapping("/admin/api/v1")
class PrintController(private val userResolver: LoggedInUserResolver,
                      private val permissionService: PermissionService,
                      private val userService: UserService,
                      private val resourcePlanService: ResourcePlanService,
                      private val assistantPlanService: AssistantPlanService,
                      private val absenceService: AbsenceService,
                      private val templateEngine: TemplateEngine,
                      private val messageSource: MessageSource) {

    companion object {
        const val FILENAME_ISSUEDETAILS = "Auftragdetails_"
        const val FILENAME_TAGESEINSATZPLAN = "Tageseinsatzplan_"
    }

    @ResponseBody
    @GetMapping("/print/resourceplan", produces = [MediaType.APPLICATION_PDF_VALUE])
    fun printResourceplan(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) reportDate: ZonedDateTime,
                          response: HttpServletResponse,
                          locale: Locale): ByteArray {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.PRINT_RESOURCE_PLAN, user)

        val assistantAssignments = collectAssistantAssignments(user, reportDate, locale)
        val technicianAssignments = collectTechnicianAssignments(user, reportDate, locale)

        val currentDate = ZonedDateTime.now()
        setFileNameForResponse(reportDate, response, FILENAME_TAGESEINSATZPLAN)

        return generateReport(locale, reportDate, currentDate, technicianAssignments, assistantAssignments)
    }

    @ResponseBody
    @GetMapping("/print/printIssueDetail", produces = [MediaType.APPLICATION_PDF_VALUE])
    fun printIssueDetail(@RequestParam issueId: Long,
                         response: HttpServletResponse,
                         locale: Locale): ByteArray {

        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.PRINT_ISSUE_DETAILS, user)

        val issueDetail = resourcePlanService.getIssueDetail(issueId)

        val printDetailIssueAssignmentItem = issueDetail.assignedDatesAndTechnicians.map { assignment ->
            val startTime = NeubauerDateFormatter.timeFormatter.format(assignment.startDate)
            val endTime = NeubauerDateFormatter.timeFormatter.format(assignment.endDate)
            val time = "$startTime - $endTime"
            val date = NeubauerDateFormatter.dateFormatter.format(assignment.startDate)
            val technician = "${assignment.firstName} ${assignment.lastName}"

            PrintDetailIssueAssignmentItem(time,date,technician)
        }

        val currentDate = ZonedDateTime.now()
        setFileNameForResponse(currentDate, response, FILENAME_ISSUEDETAILS)

        return generateIssueDetailReport(locale, currentDate, issueDetail, printDetailIssueAssignmentItem)
    }

    private fun collectAssistantAssignments(user: UserDo, reportDate: ZonedDateTime, locale: Locale): List<PrintUserAssignmentResponseItem> {
        var allAssistantAssignments = mutableListOf<PlannedResourceDto>()
        if (permissionService.hasPermission(NeubauerPermission.VIEW_ASSISTANT_LIST, user)) {
            val assistants = userService.getAllAssistants()

            if (permissionService.hasPermission(NeubauerPermission.VIEW_ASSISTANT_ASSIGNMENT_LIST, user)) {
                allAssistantAssignments = assistantPlanService.getAssistantResourcePlan(reportDate, ResourcePlanMode.DAILY).toMutableList()
            }

            if (permissionService.hasPermission(NeubauerPermission.VIEW_ASSISTANT_ABSENCES, user)) {
                val allAssistantAbsences = absenceService.getAbsencesForResourcePlan(NeubauerRole.ASSISTANT, reportDate, ResourcePlanMode.DAILY)
                allAssistantAssignments.addAll(allAssistantAbsences)
            }

            return convertUserAssignmentsToPrintResponseModel(assistants, allAssistantAssignments, locale)
        }

        return emptyList()
    }

    private fun collectTechnicianAssignments(user: UserDo, reportDate: ZonedDateTime, locale: Locale): List<PrintUserAssignmentResponseItem> {
        var allTechnicianAssignments = mutableListOf<PlannedResourceDto>()
        if (permissionService.hasPermission(NeubauerPermission.VIEW_TECHNICIAN_LIST, user)) {
            val technicians = userService.getAllTechnicians()

            if (permissionService.hasPermission(NeubauerPermission.VIEW_TECHNICIAN_ASSIGNMENT_LIST, user)) {
                allTechnicianAssignments = resourcePlanService.getResourcePlan(reportDate, ResourcePlanMode.DAILY).toMutableList()
            }

            if (permissionService.hasPermission(NeubauerPermission.VIEW_TECHNICIAN_ABSENCES, user)) {
                val allTechnicianAbsences = absenceService.getAbsencesForResourcePlan(NeubauerRole.TECHNICIAN, reportDate, ResourcePlanMode.DAILY)
                allTechnicianAssignments.addAll(allTechnicianAbsences)
            }

            return convertUserAssignmentsToPrintResponseModel(technicians, allTechnicianAssignments, locale)
        }

        return emptyList()
    }

    private fun convertUserAssignmentsToPrintResponseModel(users: Collection<UserDo>, allAssignments: Collection<PlannedResourceDto>, locale: Locale): List<PrintUserAssignmentResponseItem> {
        return users.map { user ->
            val assignments = allAssignments
                    .filter { assignment ->
                        assignment.userId == user.id
                    }
                    .sortedBy { it.end }
                    .sortedBy { it.start }

            val assignmentsResponseItems = assignments.map { assignment ->
                val time = NeubauerDateFormatter.timeFormatter.format(assignment.start) + " - " + NeubauerDateFormatter.timeFormatter.format(assignment.end)
                val hasAbsence = assignment.eventType == EventType.ABSENCE
                var absence = ""
                var title = ""
                val description = assignment.description ?: ""
                if (hasAbsence) {
                    absence = messageSource.getMessage(assignment.title, arrayOf(), locale)
                } else {
                    title = assignment.title
                }

                PrintAssignmentResponseItem(time, hasAbsence, absence, title, description)
            }

            PrintUserAssignmentResponseItem("${user.lastName} ${user.firstName}", assignmentsResponseItems)
        }.sortedBy { it.name }
    }

    private fun setFileNameForResponse(reportDate: ZonedDateTime?, response: HttpServletResponse, filename: String) {
        val dateForFileName = NeubauerDateFormatter.fileNameDateFormatter.format(reportDate)
        val filenameWithDate = "$filename${dateForFileName}.pdf"

        response.setHeader("Content-Disposition", "attachment; filename=${filenameWithDate}")
    }

    private fun generateReport(locale: Locale, reportDate: ZonedDateTime, currentDate: ZonedDateTime?, technicianAssignments: List<PrintUserAssignmentResponseItem>, assistantAssignments: List<PrintUserAssignmentResponseItem>): ByteArray {
        ByteArrayOutputStream().use { byteOutStream ->
            val reportDateFormatted = NeubauerDateFormatter.dateFormatterWithNamedDayAndMonth.withLocale(locale).format(reportDate)
            val dateForFooter = NeubauerDateFormatter.dateTimeFormatter.format(currentDate)

            val context = Context(locale)
            context.setVariable("logo", getLogoBase64Encoded())
            context.setVariable("title", reportDateFormatted)
            context.setVariable("technicianAssignments", technicianAssignments)
            context.setVariable("assistantAssignments", assistantAssignments)
            context.setVariable("footerDescriptionDate", dateForFooter)
            context.setVariable("fontBaseUrl", this::class.java.classLoader.getResource("fonts/Roboto/").toString())

            val template = templateEngine.process("print/resourceplan", context)

            val renderer = ITextRenderer()
            renderer.setDocumentFromString(template)
            renderer.layout()
            renderer.createPDF(byteOutStream)

            return byteOutStream.toByteArray()
        }
    }

    private fun getLogoBase64Encoded(): String? {
        val logoByteArray = this::class.java.classLoader.getResource("static/assets/images/logo.png").readBytes()
        return Base64.getEncoder().encodeToString(logoByteArray)
    }

    private fun generateIssueDetailReport(locale: Locale, currentDate: ZonedDateTime?, issueDetail: IssueDetailDto, assignment: List<PrintDetailIssueAssignmentItem>): ByteArray {
    ByteArrayOutputStream().use { byteOutStream ->
            val dateForFooter = NeubauerDateFormatter.dateTimeFormatter.format(currentDate)

            val context = Context(locale)
            context.setVariable("logo", getLogoBase64Encoded())
            context.setVariable("issue", issueDetail)
            context.setVariable("assignment", assignment)
            context.setVariable("footerDescriptionDate", dateForFooter)
            context.setVariable("fontBaseUrl", this::class.java.classLoader.getResource("fonts/Roboto/").toString())
            val template = templateEngine.process("print/issue-detail", context)

            val renderer = ITextRenderer()
            renderer.setDocumentFromString(template)
            renderer.layout()
            renderer.createPDF(byteOutStream)

            return byteOutStream.toByteArray()
        }
    }
}

data class PrintUserAssignmentResponseItem(
        val name: String,
        val assignments: Collection<PrintAssignmentResponseItem>
)

data class PrintDetailIssueAssignmentItem(
        val time: String,
        val date: String,
        val technician: String
)

data class PrintAssignmentResponseItem(
        val time: String,
        val hasAbsence: Boolean,
        val absence: String,
        val assignmentTitle: String,
        val assignmentDescription: String
)
