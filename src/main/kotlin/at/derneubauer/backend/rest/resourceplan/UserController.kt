package at.derneubauer.backend.web.ressourceplan

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.EventType
import at.derneubauer.backend.service.UserService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/admin/api/v1")
class UserController(private val userResolver: LoggedInUserResolver,
                     private val permissionService: PermissionService,
                     private val userService: UserService) {


    @GetMapping("assistants")
    fun assistants(): Collection<UserResponse> {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_ASSISTANT_LIST, user)

        return userService.getAllAssistants().map { assistant ->
            UserResponse(assistant.id, "${assistant.lastName} ${assistant.firstName}")
        }
    }

    @GetMapping("technicians")
    fun technicians(): Collection<UserResponse> {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_TECHNICIAN_LIST, user)
        return userService.getAllTechnicians().map { technician ->
            UserResponse(technician.id, "${technician.lastName} ${technician.firstName}", EventType.ASSISTANT_ASSIGNMENT)
        }
    }

    @GetMapping("users")
    fun users(): Collection<UserListResponse> {
        val user = userResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.VIEW_USER_LIST, user)
        return userService.getAllUsers().map { u ->
            val status = if (u.disabled) "DISABLED" else "ENABLED"

            UserListResponse(
                u.id,
                u.username,
                u.firstName,
                u.lastName,
                u.phoneNumber,
                status,
                u.role
            )
        }
    }
}

data class UserResponse(
    val id: Long,
    val displayName: String,
    val eventType: EventType? = null
)

data class UserListResponse(
    val id: Long,
    val username: String,
    val firstName: String,
    val lastName: String,
    val phoneNumber: String?,
    val status: String,
    val role: String
)
