package at.derneubauer.backend.rest.permission

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.PermissionResolver
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping("/api/v1")
class PermissionController(
    val permissionResolver: PermissionResolver,
    val userResolver: LoggedInUserResolver) {

    @GetMapping("permissions")
    fun permissions(): PermissionResponse {
        val user = userResolver.resolveLoggedInUser()
        val authorities = permissionResolver.resolvePermissionsFor(user)
        val permissions = authorities.map { it.name }
        return PermissionResponse(permissions)
    }
}

data class PermissionResponse(
    val permissions: Collection<String>
)
