package at.derneubauer.backend.rest

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionResolver
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.AuthenticationProvider
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.authentication.WebAuthenticationDetails
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler
import org.springframework.web.bind.annotation.*
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jakarta.validation.constraints.NotBlank
import org.springframework.http.MediaType


@RestController
@RequestMapping("/api/v1")
class RestLoginController(
    @Qualifier("restAuthenticationProvider")
    private val authenticationProvider: AuthenticationProvider,

    private val loggedInUserResolver: LoggedInUserResolver,
    private val permissionResolver: PermissionResolver
) {

    @PostMapping("login")
    fun login(
        @RequestBody loginRequest: LoginRequest,
        request: HttpServletRequest,
        response: HttpServletResponse
    ): LoginResponse {
        val token = UsernamePasswordAuthenticationToken(loginRequest.user, loginRequest.password)
        token.details = WebAuthenticationDetails(request)
        val authentication = authenticationProvider.authenticate(token)
        SecurityContextHolder.getContext().authentication = authentication

        val user = loggedInUserResolver.resolveLoggedInUser()
        val permissions = permissionResolver.resolvePermissionsFor(user)

        response.setHeader("Access-Control-Expose-Headers", "x-auth-token")
        response.setHeader("Access-Control-Allow-Credentials", "x-auth-token")

        return LoginResponse(
            user.id,
            user.username,
            user.firstName,
            user.lastName,
            user.locale,
            permissions
        )
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("logout")
    fun logout(request: HttpServletRequest, response: HttpServletResponse) {
        SecurityContextLogoutHandler().logout(request, response, SecurityContextHolder.getContext().authentication)
    }
}

data class LoginRequest(
    @get:NotBlank
    val user: String,

    @get:NotBlank
    val password: String
)

data class LoginResponse(
    val userId: Long,
    val username: String,
    val firstName: String,
    val lastName: String,
    val locale: String?,
    val permissions: List<NeubauerPermission>
)
