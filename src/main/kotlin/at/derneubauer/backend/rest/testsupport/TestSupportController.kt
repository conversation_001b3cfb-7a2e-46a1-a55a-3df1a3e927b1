package at.derneubauer.backend.rest.testsupport

import at.derneubauer.backend.config.NeubauerConfig
import at.derneubauer.backend.offa.db.OffaIssueInsertDto
import at.derneubauer.backend.offa.db.OffaToIssueMapper
import at.derneubauer.backend.service.ImageService
import at.derneubauer.backend.service.NeubauerIssueStatus
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("/api/v1/test-support")
class TestSupportController(
        private val neubauerConfig: NeubauerConfig,
        private val imageService: ImageService,
        private val offaToIssueMapper: OffaToIssueMapper) {

    @GetMapping("/status")
    fun status(): TestSupportStatusResponse {
        if (!isTestSupportEnabled()) {
            return TestSupportStatusResponse.OFF
        }

        return TestSupportStatusResponse.ON
    }

    private fun isTestSupportEnabled(): Boolean {
        return neubauerConfig.dev?.testSupportEndpointsActive == true
    }

    // TODO remove once real upload is implemented
    @PostMapping("/issue-image-upload")
    fun uploadIssueImage(@RequestParam("issueId") issueId: Long, @RequestParam("file") file: MultipartFile) {
        if (!isTestSupportEnabled()) {
            throw TestSupportDisabledException()
        }

        imageService.uploadIssueImage(issueId, file)
    }

    @PostMapping("/issues")
    fun insertIssue(@RequestBody issue: OffaIssueInsertDto) {
        if (!isTestSupportEnabled()) {
            throw TestSupportDisabledException()
        }

        offaToIssueMapper.insert(issue)
        val issueId = issue.id ?: throw RuntimeException("Unabel to insert issue for test")
        offaToIssueMapper.insertNewStatusForIssue(issueId, NeubauerIssueStatus.NEW.getName())
    }
}

data class TestSupportStatusResponse(
    val enabled: Boolean
) {
    companion object {
        val ON = TestSupportStatusResponse(true)
        val OFF = TestSupportStatusResponse(false)
    }
}

class TestSupportDisabledException() : NeubauerException() {
    override val statusCode = HttpStatus.NOT_FOUND.value()
}
