package at.derneubauer.backend.rest

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.ImageType
import at.derneubauer.backend.service.ImageService
import at.derneubauer.backend.service.IssueService
import at.derneubauer.backend.web.error.NeubauerException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping("/api/v1/images")
class ImageController(
        private val imageService: ImageService,
        private val loggedInUserResolver: LoggedInUserResolver,
        private val permissionService: PermissionService,
        private val issueService: IssueService
) {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("user-image-upload")
    fun uploadUserImage(@RequestParam("file") file: MultipartFile, @RequestParam("crc32Checksum") expectedChecksum: Long, @RequestParam("issueId") issueId: Long?) {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.USE_APP, user)

        imageService.uploadFile(file, user, expectedChecksum, issueId)
    }

    @GetMapping("thumbnail/{imageId}")
    @ResponseBody
    fun thumbnail(@PathVariable("imageId") imageId: Long): ByteArray {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.USE_APP, user)

        return imageService.retrieveThumbnail(imageId, ImageType.ISSUE)
    }

    @GetMapping("image/{imageId}")
    @ResponseBody
    fun image(@PathVariable("imageId") imageId: Long): ByteArray {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.USE_APP, user)

        return imageService.retrieveImage(imageId, ImageType.ISSUE)
    }
}

open class ChecksumMismatchException(expectedChecksum: Long, actualChecksum: Long) : NeubauerException("Transmitted CRC32 checksum ${expectedChecksum} of file does not match locally calculated CRC32 checksum ${actualChecksum}") {
    override val statusCode = HttpStatus.BAD_REQUEST.value()
    override val localizationKey = "errors.upload.checksumMismatch"
}
