package at.derneubauer.backend.rest.version

import org.springframework.boot.info.BuildProperties
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset

@RestController
@RequestMapping("/api/v1")
class VersionController(
    private val buildInfo: BuildProperties
) {
    @GetMapping("/version")
    fun getVersion(): ResponseEntity<VersionInfoResponse> =
        ResponseEntity.ok(
            VersionInfoResponse(
                version = buildInfo.version,
                rawVersion = buildInfo.get("raw.version"),
                gitBranch = buildInfo.get("git.branch"),
                buildTimestamp = getBuildTimeAsOffsetDateTime()
            ),
        )

    private fun getBuildTimeAsOffsetDateTime(): OffsetDateTime {
        val buildTime = buildInfo.get("time")?.toLong() ?: return OffsetDateTime.now()
        return OffsetDateTime.ofInstant(
            Instant.ofEpochMilli(buildTime),
            ZoneOffset.systemDefault(),
        )
    }
}

