package at.derneubauer.backend.rest.error

import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.http.HttpStatus

class MissingPermissionException(val permission: NeubauerPermission): NeubauerException() {
    override val statusCode = HttpStatus.FORBIDDEN.value()
    override val localizationKey = "errors.missingPermission"
}
