package at.derneubauer.backend.rest.error

import at.derneubauer.backend.web.error.NeubauerException
import org.slf4j.LoggerFactory
import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.context.support.MessageSourceAccessor
import org.springframework.core.annotation.Order
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.DisabledException
import org.springframework.security.core.AuthenticationException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler

@ControllerAdvice(annotations = [RestController::class])
@Order(1)
class NeubauerRestExceptionHandler(private val messageSource: MessageSource) :
    ResponseEntityExceptionHandler() {

    companion object {
        val log = LoggerFactory.getLogger(NeubauerRestExceptionHandler::class.java)
    }

    @ExceptionHandler(Exception::class)
    fun handleAnyException(e: Exception, request: WebRequest): ResponseEntity<Any> {
        log.error("Exception occurred", e)
        return handleException(e, request) ?: ResponseEntity.internalServerError().body(e)
    }

    @ExceptionHandler(NeubauerException::class)
    fun handleNeubauerException(e: NeubauerException, request: WebRequest): ResponseEntity<Any> {
        log.error("Exception occurred", e)
        return handleNeubauerException(
            e.message ?: "",
            e.localizationKey,
            e,
            e.statusCode,
            request
        )
    }

    @ExceptionHandler(BadCredentialsException::class)
    fun handleBadCredentials(e: RuntimeException, request: WebRequest): ResponseEntity<Any> {
        return handleNeubauerException(
            "The credentials you provided are invalid!",
            "errors.login.invalidCredentials",
            e,
            HttpStatus.UNAUTHORIZED.value(),
            request
        )
    }

    @ExceptionHandler(DisabledException::class)
    fun handleAccountLocked(e: RuntimeException, request: WebRequest): ResponseEntity<Any> {
        return handleNeubauerException(
            "Your account is locked!",
            "errors.login.accountLocked",
            e,
            HttpStatus.UNAUTHORIZED.value(),
            request
        )
    }

    @ExceptionHandler(AuthenticationException::class)
    fun handleAuthenticationException(e: RuntimeException, request: WebRequest): ResponseEntity<Any> {
        return handleNeubauerException(
            "Authentication failed!",
            "errors.login.authFailed",
            e,
            HttpStatus.UNAUTHORIZED.value(),
            request
        )
    }

    private fun handleNeubauerException(
        message: String,
        localizationKey: String,
        e: RuntimeException,
        httpStatusCode: Int,
        request: WebRequest
    ): ResponseEntity<Any> {
        val messages = MessageSourceAccessor(messageSource, LocaleContextHolder.getLocale())
        val bodyOfResponse = ErrorResponse(message, messages.getMessage(localizationKey))
        return handleExceptionInternal(e, bodyOfResponse, HttpHeaders(), HttpStatus.valueOf(httpStatusCode), request)
            ?: createResponseEntity(bodyOfResponse, HttpHeaders(), HttpStatus.valueOf(httpStatusCode), request)
    }

    override fun handleMethodArgumentNotValid(
        ex: MethodArgumentNotValidException,
        headers: HttpHeaders,
        status: HttpStatusCode,
        request: WebRequest
    ): ResponseEntity<Any>? {
        return validationRestError(ex, request)
    }

    override fun handleHttpMessageNotReadable(
        ex: HttpMessageNotReadableException,
        headers: HttpHeaders,
        status: HttpStatusCode,
        request: WebRequest
    ): ResponseEntity<Any>? {
        return handleNeubauerException(
            ex.message ?: "",
            "errors.unableToDeserialize",
            ex,
            HttpStatus.BAD_REQUEST.value(),
            request,
        )
    }

    private fun validationRestError(ex: MethodArgumentNotValidException, request: WebRequest): ResponseEntity<Any> {
        val fieldErrors = ex.bindingResult.fieldErrors
        val messages = MessageSourceAccessor(messageSource, LocaleContextHolder.getLocale())
        val validationErrors = fieldErrors.map { fieldError ->
            val localizedFieldName = messages.getMessage("json.field.${fieldError.field}", fieldError.field)
            val messagePart = fieldError.defaultMessage
            val localizedMessage = "${localizedFieldName}: ${messagePart}"
            ValidationError(
                localizedMessage,
                messagePart,
                fieldError.field,
                localizedFieldName,
                fieldError.rejectedValue
            )
        }
        val localizedMessageOfFirstValidationError = validationErrors.firstOrNull()?.message ?: ""
        val bodyOfResponse = ErrorResponse(
            "The request did not pass validation because of ${fieldErrors.size} error(s).",
            localizedMessageOfFirstValidationError,
            validationErrors
        )
//        log.info("Returning from REST API with error {} and HTTP status 400", ex)
        return handleExceptionInternal(ex, bodyOfResponse, HttpHeaders(), HttpStatus.BAD_REQUEST, request)
            ?: createResponseEntity(bodyOfResponse, HttpHeaders(), HttpStatus.BAD_REQUEST, request)
    }
}

data class ErrorResponse(
    val message: String,
    val localizedMessage: String,
    val validationErrors: Collection<ValidationError>? = emptyList()
)

data class ValidationError(
    val message: String?,
    val messagePart: String?,
    val field: String?,
    val localizedFieldName: String?,
    val rejectedValue: Any?
)
