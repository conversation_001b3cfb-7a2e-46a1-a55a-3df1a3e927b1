package at.derneubauer.backend.rest.app

import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.AcceptedIssueService
import at.derneubauer.backend.service.IssueService
import at.derneubauer.backend.util.NeubauerDateFormatter
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.*
import java.time.ZonedDateTime

@RestController
@RequestMapping("/api/v1/app")
class AppCommandsController(
        private val loggedInUserResolver: LoggedInUserResolver,
        private val permissionService: PermissionService,
        private val issueService: IssueService,
        private val acceptedIssueService: AcceptedIssueService
) {

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("issue/{issueId}/claim")
    fun claimIssue(@PathVariable("issueId") issueId: Long) {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.ACCEPT_ISSUE, user)

        acceptedIssueService.acceptIssue(user.id, issueId)
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("issue/{issueId}/reopen")
    fun reopenIssue(@PathVariable("issueId") issueId: Long) {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.REOPEN_ISSUE, user)
        permissionService.ensurePermission(NeubauerPermission.ACCEPT_ISSUE, user)

        issueService.reopenIssue(user, issueId)
        acceptedIssueService.acceptIssue(user.id, issueId)
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("issue/{issueId}/mark-done")
    fun markIssueDone(@PathVariable("issueId") issueId: Long) {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.SET_ISSUE_TECHNICIAN_DONE, user)

        issueService.setIssueToTechnicianDone(issueId, user.id)
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("issue/{issueId}/put-back")
    fun putIssueBack(@PathVariable("issueId") issueId: Long) {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.PUT_BACK_ISSUE, user)

        acceptedIssueService.putBackIssue(user.id, issueId)
    }

    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PostMapping("issue/{issueId}/add-note")
    fun addNote(@PathVariable("issueId") issueId: Long, @RequestBody note: NoteRequest) {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.EDIT_ISSUE_DETAIL_NOTE, user)

        issueService.appendNote(issueId, note, user.username)
    }

}

data class NoteRequest(
        val note: String,
        val date: ZonedDateTime
)