package at.derneubauer.backend.rest.app

import at.derneubauer.backend.db.image.ImageDo
import at.derneubauer.backend.db.issue.AcceptedIssueDo
import at.derneubauer.backend.db.issue.IssueDo
import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.security.LoggedInUserResolver
import at.derneubauer.backend.security.NeubauerPermission
import at.derneubauer.backend.security.PermissionService
import at.derneubauer.backend.service.*
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime

@RestController
@RequestMapping("/api/v1/app")
class AppSyncController(
        private val loggedInUserResolver: LoggedInUserResolver,
        private val permissionService: PermissionService,
        private val issueService: IssueService,
        private val userService: UserService,
        private val acceptedIssueService: AcceptedIssueService,
        private val resourcePlanService: ResourcePlanService,
        private val imageService: ImageService
) {

    @GetMapping("sync")
    fun sync(@RequestParam(name = "sync-from", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) syncFrom: ZonedDateTime?): SyncResponse {
        val user = loggedInUserResolver.resolveLoggedInUser()
        permissionService.ensurePermission(NeubauerPermission.USE_APP, user)

        val since = syncFrom ?: ZonedDateTime.ofInstant(Instant.ofEpochMilli(0), ZoneOffset.UTC)

        val issues = issues(since)
        val technicians = technicians(since)
        val acceptedIssues = acceptedIssues(since)
        val images = images(since)

        val latestTimestamp = latestTimestampFor(issues, technicians, acceptedIssues, images)

        return SyncResponse(issues, technicians, acceptedIssues, images, latestTimestamp)
    }

    private fun issues(since: ZonedDateTime): List<IssueWrapperSyncDto> {
        val newlyCreatedIssues = issueService.findOpenIssuesCreatedSince(since)
        val updatedIssues = issueService.findOpenIssuesUpdatedSince(since)
        val recentlyClosedButReopenedIssues = issueService.findStatusHistoryForIssuesThatHaveBeenReopenedAndAreStillOpen(since)

        val updatedIssuesWithoutDuplicates = updatedIssues
                .minus(newlyCreatedIssues)
                .minus(recentlyClosedButReopenedIssues)

        val closedIssues = issueService.findIssuesThatHaveBeenClosedSince(since)

        return newlyCreatedIssues.map {
            IssueWrapperSyncDto(SyncStatus.INSERTED, IssueSyncDto(it))
        }.plus(recentlyClosedButReopenedIssues.map {
            IssueWrapperSyncDto(SyncStatus.INSERTED, IssueSyncDto(it))
        }).plus(updatedIssuesWithoutDuplicates.map {
            IssueWrapperSyncDto(SyncStatus.UPDATED, IssueSyncDto(it))
        }).plus(closedIssues.map {
            IssueWrapperSyncDto(SyncStatus.DELETED, IssueSyncDto(it))
        }).also {
            it.forEach {
                it.issue.latestAssignmentDate = latestAssignmentDateForIssue(it.issue.id)
            }
        }.sortedBy { it.issue.updatedAt }
    }

    private fun latestAssignmentDateForIssue(issueId: Long): ZonedDateTime? {
        return resourcePlanService.getLatestAssignmentDateForIssue(issueId)
    }

    private fun technicians(since: ZonedDateTime): List<TechnicianWrapperSyncDto> {
        val newlyCreatedTechnicians = userService.findTechniciansCreatedSince(since)
        val updatedTechnicians = userService.findTechniciansUpdatedSince(since)

        val updatedIssuesWithoutDuplicates = updatedTechnicians.minus(newlyCreatedTechnicians)

        return newlyCreatedTechnicians.map {
            TechnicianWrapperSyncDto(SyncStatus.INSERTED, TechnicianSyncDto(it))
        }.plus(updatedIssuesWithoutDuplicates.map {
            TechnicianWrapperSyncDto(SyncStatus.UPDATED, TechnicianSyncDto(it))
        }).sortedBy { it.technician.updatedAt }
    }

    private fun acceptedIssues(since: ZonedDateTime): List<AcceptedIssueWrapperSyncDto> {
        return acceptedIssueService.allAcceptedIssuesSince(since).map {
            val syncStatus = if (it.accepted) SyncStatus.INSERTED else SyncStatus.DELETED
            AcceptedIssueWrapperSyncDto(syncStatus, AcceptedIssueSyncDto(it))
        }.sortedBy { it.acceptedIssue.createdAt }
    }

    private fun images(since: ZonedDateTime): List<ImageWrapperSyncDto> {
        return imageService.allImagesSince(since).map {
            ImageWrapperSyncDto(
                    syncStatus = if (it.status == ImageStatus.ADDED.name) SyncStatus.INSERTED else SyncStatus.DELETED,
                    image = ImageSyncDto(it)
            )
        }.sortedBy { it.image.updatedAt }
    }

    private fun latestTimestampFor(issues: List<IssueWrapperSyncDto>, technicians: List<TechnicianWrapperSyncDto>,
                                   acceptedIssues: List<AcceptedIssueWrapperSyncDto>, images: List<ImageWrapperSyncDto>): ZonedDateTime? {
        val latestIssueTimestamp = issues.maxOfOrNull { it.issue.updatedAt }
        val latestTechnicianTimestamp = technicians.maxOfOrNull { it.technician.updatedAt }
        val latestAcceptedIssueTimestamp = acceptedIssues.maxOfOrNull { it.acceptedIssue.createdAt }
        val latestImageTimestamp = images.maxOfOrNull { it.image.updatedAt }

        return listOfNotNull(latestIssueTimestamp, latestTechnicianTimestamp, latestAcceptedIssueTimestamp, latestImageTimestamp).maxOrNull()
    }
}

data class SyncResponse(
        val issues: List<IssueWrapperSyncDto>,
        val technicians: List<TechnicianWrapperSyncDto>,
        val acceptedIssues: List<AcceptedIssueWrapperSyncDto>,
        val images: List<ImageWrapperSyncDto>,
        val latestTimestamp: ZonedDateTime?
)

enum class SyncStatus {
    INSERTED,
    UPDATED,
    DELETED
}

data class IssueWrapperSyncDto(
        val syncStatus: SyncStatus,
        val issue: IssueSyncDto
)

data class IssueSyncDto(
        val id: Long,
        val externalId: String,
        val contactPerson: String?,
        val address: String?,
        val description: String?,
        val note: String?,
        val status: String,
        val updatedAt: ZonedDateTime,
        val createdAt: ZonedDateTime,
        var latestAssignmentDate: ZonedDateTime?
) {
    constructor(issue: IssueDo) :
            this(
                    issue.id,
                    issue.externalId,
                    issue.contactPerson,
                    issue.address,
                    issue.description,
                    issue.note,
                    issue.status,
                    issue.updatedAt,
                    issue.createdAt,
                    null
            )
}

data class TechnicianWrapperSyncDto(
        val syncStatus: SyncStatus,
        val technician: TechnicianSyncDto
)

data class TechnicianSyncDto(
        val id: Long,
        val firstName: String,
        val lastName: String,
        val phoneNumber: String?,
        val updatedAt: ZonedDateTime,
        val createdAt: ZonedDateTime
) {
    constructor(technician: UserDo) : this(technician.id, technician.firstName, technician.lastName, technician.phoneNumber, technician.updatedAt, technician.createdAt)
}

data class AcceptedIssueWrapperSyncDto(
        val syncStatus: SyncStatus,
        val acceptedIssue: AcceptedIssueSyncDto
)

data class AcceptedIssueSyncDto(
        val id: Long,
        val issueId: Long,
        val technicianId: Long,
        val createdAt: ZonedDateTime
) {
    constructor(acceptedIssue: AcceptedIssueDo) : this(acceptedIssue.id, acceptedIssue.issueId, acceptedIssue.technicianId, acceptedIssue.createdAt)
}

data class ImageWrapperSyncDto(
        val syncStatus: SyncStatus = SyncStatus.INSERTED,
        val image: ImageSyncDto
)

data class ImageSyncDto(
        val id: Long,
        val type: String,
        val refId: Long,
        val width: Int,
        val height: Int,
        val uploadedAt: ZonedDateTime,
        val updatedAt: ZonedDateTime
) {
    constructor(image: ImageDo) : this(image.id, image.refType, image.refId, image.width, image.height, image.uploadedAt, image.updatedAt)
}