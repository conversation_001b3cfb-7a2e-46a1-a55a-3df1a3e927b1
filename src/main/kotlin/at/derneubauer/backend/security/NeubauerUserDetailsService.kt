package at.derneubauer.backend.security

import at.derneubauer.backend.db.user.UserMapper
import org.slf4j.LoggerFactory
import org.springframework.dao.DataAccessException
import org.springframework.security.core.userdetails.User
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.stereotype.Service

@Service
class NeubauerUserDetailsService(val userMapper: UserMapper, val permissionService: PermissionService) : UserDetailsService {

    companion object {
        val log = LoggerFactory.getLogger(NeubauerUserDetailsService::class.java)
    }

    override fun loadUserByUsername(username: String): UserDetails {
        try {
            val user = userMapper.findByUsername(username) ?: throw UsernameNotFoundException(username)
            permissionService.ensurePermission(NeubauerPermission.LOGIN, user)
            return Neu<PERSON>uer<PERSON>ser(user.username, user.password, user.disabled)
        } catch (e: DataAccessException) {
            log.error("Invalid credentials", e)
            throw RuntimeException("Unable to find user")
        }
    }
}
