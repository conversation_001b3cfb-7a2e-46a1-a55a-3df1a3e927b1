package at.derneubauer.backend.security

import at.derneubauer.backend.config.NeubauerConfig
import at.derneubauer.backend.util.NeubauerDateFormatter
import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.apache.http.entity.ContentType
import org.springframework.context.MessageSource
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.AuthenticationProvider
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.DisabledException
import org.springframework.security.authentication.dao.DaoAuthenticationProvider
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.invoke
import org.springframework.security.core.AuthenticationException
import org.springframework.security.core.userdetails.User
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.provisioning.InMemoryUserDetailsManager
import org.springframework.security.web.AuthenticationEntryPoint
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.AuthenticationFailureHandler
import org.springframework.security.web.session.SessionInformationExpiredEvent
import org.springframework.security.web.session.SessionInformationExpiredStrategy
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.session.web.http.CookieHttpSessionIdResolver
import org.springframework.session.web.http.HeaderHttpSessionIdResolver
import org.springframework.session.web.http.HttpSessionIdResolver
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Configuration
@Order(1)
@EnableWebSecurity
class RestSecurityConfiguration(
    val restAuthenticationEntryPoint: RestAuthenticationEntryPoint,
    val userDetailsService: NeubauerUserDetailsService,
    val sessionExpiredStrategy: UnauthorizedSessionInformationExpiredStrategy
) {
    @Bean
    fun apiFilterChain(http: HttpSecurity): SecurityFilterChain {
        http.authenticationProvider(authenticationProvider())

        http.invoke {
            securityMatcher(AntPathRequestMatcher.antMatcher("/api/**"))

            securityContext {
                requireExplicitSave = false
            }

            authorizeHttpRequests {
                authorize("/api/v1/login", permitAll)
                authorize("/api/v1/version", permitAll)
                authorize("/api/v1/test-support/status", permitAll)
                authorize("/api/**", authenticated)
            }

            csrf {
                disable()
            }

            exceptionHandling {
                defaultAuthenticationEntryPointFor(restAuthenticationEntryPoint, AntPathRequestMatcher("/api/**"))
            }

            sessionManagement {
                sessionConcurrency {
                    maximumSessions = Int.MAX_VALUE
                    expiredSessionStrategy = sessionExpiredStrategy
                }
            }
        }

        return http.build()
    }

    @Bean
    fun restPasswordEncoder(): PasswordEncoder {
        return DefaultPasswordEncoderFactories.createDelegatingPasswordEncoder()
    }

    @Bean("restAuthenticationProvider")
    fun authenticationProvider(): DaoAuthenticationProvider {
        val authenticationProvider = DaoAuthenticationProvider()
        authenticationProvider.setUserDetailsService(userDetailsService)
        authenticationProvider.setPasswordEncoder(restPasswordEncoder())
        return authenticationProvider
    }
}

@Configuration
@EnableWebSecurity
class OffaConnectorSecurityConfiguration(
    val neubauerConfig: NeubauerConfig
) {
    @Bean("offaApiFilterChain")
    fun apiFilterChain(http: HttpSecurity): SecurityFilterChain {
        http.authenticationProvider(authenticationProvider())

        http.invoke {
            securityMatcher(AntPathRequestMatcher.antMatcher("/offa/**"))

            authorizeHttpRequests {
                authorize("/offa/**", authenticated)
            }

            csrf {
                disable()
            }

            httpBasic {  }
        }

        return http.build()
    }

    @Bean
    fun offaPasswordEncoder(): PasswordEncoder {
        return DefaultPasswordEncoderFactories.createDelegatingPasswordEncoder()
    }

    @Bean
    fun offaUserDetailsService(): InMemoryUserDetailsManager? {
        val offaConfig = neubauerConfig.offa ?: return null

        val user = User.withUsername(offaConfig.user)
                .password(offaConfig.password)
                .authorities("ROLE_OFFA")
                .build()

        return InMemoryUserDetailsManager(user)
    }

    @Bean("offaApiAuthenticationProvider")
    fun authenticationProvider(): AuthenticationProvider {
        val authenticationProvider = DaoAuthenticationProvider()
        authenticationProvider.setUserDetailsService(offaUserDetailsService())
        authenticationProvider.setPasswordEncoder(offaPasswordEncoder())
        return authenticationProvider
    }
}

@Configuration
@Order(3)
class AppApiSecurityConfiguration(val userDetailsService: NeubauerUserDetailsService) {
    @Bean("appApiFilterChain")
    fun apiFilterChain(http: HttpSecurity): SecurityFilterChain {
        http.authenticationProvider(authenticationProvider())

        http.invoke {
            securityMatcher(AntPathRequestMatcher.antMatcher("/neubauer-web/**"))

            securityContext {
                requireExplicitSave = false
            }

            authorizeHttpRequests {
                authorize("/neubauer-web/**", authenticated)
            }

            csrf {
                disable()
            }

            httpBasic { }
        }

        return http.build()
    }

    @Bean
    fun appApiPasswordEncoder(): PasswordEncoder {
        return DefaultPasswordEncoderFactories.createDelegatingPasswordEncoder()
    }

    @Bean("appApiAuthenticationProvider")
    fun authenticationProvider(): DaoAuthenticationProvider {
        val authenticationProvider = DaoAuthenticationProvider()
        authenticationProvider.setUserDetailsService(userDetailsService)
        authenticationProvider.setPasswordEncoder(appApiPasswordEncoder())
        return authenticationProvider
    }
}


@Configuration
@Order(4)
class FrontendApiSecurityConfiguration(
    val restAuthenticationEntryPoint: RestAuthenticationEntryPoint,
    val userDetailsService: NeubauerUserDetailsService,
    val sessionExpiredStrategy: UnauthorizedSessionInformationExpiredStrategy
) {

    @Bean("frontendApiFilterChain")
    fun apiFilterChain(http: HttpSecurity): SecurityFilterChain {
        http.authenticationProvider(authenticationProvider())

        http.invoke {
            securityMatcher(AntPathRequestMatcher.antMatcher("/admin/api/**"))

            securityContext {
                requireExplicitSave = false
            }

            authorizeHttpRequests {
                authorize("/admin/api/**", authenticated)
            }

            csrf {
                disable()
            }

            exceptionHandling {
                defaultAuthenticationEntryPointFor(restAuthenticationEntryPoint, AntPathRequestMatcher("/admin/api/**"))
            }

            sessionManagement {
                sessionConcurrency {
                    maximumSessions = Int.MAX_VALUE
                    expiredSessionStrategy = sessionExpiredStrategy
                }
            }
        }

        return http.build()
    }

    @Bean
    fun frontendApiPasswordEncoder(): PasswordEncoder {
        return DefaultPasswordEncoderFactories.createDelegatingPasswordEncoder()
    }

    @Bean("frontendApiAuthenticationProvider")
    fun authenticationProvider(): DaoAuthenticationProvider {
        val authenticationProvider = DaoAuthenticationProvider()
        authenticationProvider.setUserDetailsService(userDetailsService)
        authenticationProvider.setPasswordEncoder(frontendApiPasswordEncoder())
        return authenticationProvider
    }
}

@Configuration
@Order(5)
class WebSecurityConfiguration(
    val userDetailsService: NeubauerUserDetailsService,
    val webAuthenticationFailureHandler: WebAuthenticationFailureHandler
) {
    @Bean("webApiFilterChain")
    fun apiFilterChain(http: HttpSecurity): SecurityFilterChain {
        http.authenticationProvider(authenticationProvider())

        http.invoke {
            securityMatcher(AntPathRequestMatcher.antMatcher("/**"))

            securityContext {
                requireExplicitSave = false
            }

            authorizeHttpRequests {
                authorize("/error", permitAll)
                authorize("/assets/**", permitAll)
                authorize(anyRequest, authenticated)
            }

            formLogin {
                loginPage = "/login"
                permitAll()
                defaultSuccessUrl("/dashboard", true)
            }

            logout {
                logoutUrl = "/logout"
                logoutSuccessUrl = "/login"
                permitAll()
            }

            sessionManagement {
                sessionConcurrency {
                    maximumSessions = Int.MAX_VALUE
                    expiredUrl = "/login"
                }
            }

            headers {
                frameOptions {
                    sameOrigin = true
                }
            }
        }

        return http.build()
    }

    @Bean
    fun httpSessionIdResolver() = EndpointAwareHttpSessionIdResolver()

    @Bean
    fun passwordEncoder(): PasswordEncoder {
        return DefaultPasswordEncoderFactories.createDelegatingPasswordEncoder()
    }

    @Bean("webAuthenticationProvider")
    fun authenticationProvider(): DaoAuthenticationProvider {
        val authenticationProvider = DaoAuthenticationProvider()
        authenticationProvider.setUserDetailsService(userDetailsService)
        authenticationProvider.setPasswordEncoder(passwordEncoder())
        return authenticationProvider
    }
}

class EndpointAwareHttpSessionIdResolver : HttpSessionIdResolver {
    private val webResolver: HttpSessionIdResolver
    private val apiResolver: HttpSessionIdResolver
    private val apiRequestMatcher: AntPathRequestMatcher

    constructor() {
        webResolver = CookieHttpSessionIdResolver()
        apiResolver = HeaderHttpSessionIdResolver("x-auth-token")
        apiRequestMatcher = AntPathRequestMatcher("/api/**")
    }

    override fun setSessionId(request: HttpServletRequest?, response: HttpServletResponse?, sessionId: String?) {
        findMatchingResolver(request).setSessionId(request, response, sessionId)
    }

    override fun expireSession(request: HttpServletRequest?, response: HttpServletResponse?) {
        findMatchingResolver(request).expireSession(request, response)
    }

    override fun resolveSessionIds(request: HttpServletRequest?): MutableList<String> {
        return findMatchingResolver(request).resolveSessionIds(request)
    }

    private fun findMatchingResolver(request: HttpServletRequest?): HttpSessionIdResolver {
        if (apiRequestMatcher.matches(request)) {
            return apiResolver
        }

        return webResolver
    }
}

@Service
class RestAuthenticationEntryPoint : AuthenticationEntryPoint {
    override fun commence(
        request: HttpServletRequest,
        response: HttpServletResponse,
        authenticationException: AuthenticationException
    ) {
        when (authenticationException) {
            is DisabledException -> response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Account disabled")
            is BadCredentialsException -> response.sendError(
                HttpServletResponse.SC_UNAUTHORIZED,
                "Username or password is incorrect"
            )

            else -> response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Auth token missing or invalid")
        }
    }
}

@Service
class WebAuthenticationFailureHandler : AuthenticationFailureHandler {
    override fun onAuthenticationFailure(
        request: HttpServletRequest,
        response: HttpServletResponse,
        exception: AuthenticationException
    ) {
        when (exception) {
            is DisabledException -> response.sendRedirect("/login?error=disabled")
            else -> response.sendRedirect("/login?error=bad_credentials")
        }
    }

}

@Service
class UnauthorizedSessionInformationExpiredStrategy(private val messageSource: MessageSource) :
    SessionInformationExpiredStrategy {

    override fun onExpiredSessionDetected(event: SessionInformationExpiredEvent) {
        val response = event.response

        val httpStatus = HttpStatus.UNAUTHORIZED
        response.status = httpStatus.value()
        response.contentType = ContentType.APPLICATION_JSON.mimeType

        val objectMapper = ObjectMapper()

        val sessionExpiredResponse = SessionExpiredResponse(
            NeubauerDateFormatter.dateTimeWithTzFormatter.format(ZonedDateTime.now()),
            httpStatus.value(),
            httpStatus.reasonPhrase,
            messageSource.getMessage("errors.session.expired", arrayOf(), LocaleContextHolder.getLocale()),
            event.request.servletPath
        )

        response.writer.write(objectMapper.writeValueAsString(sessionExpiredResponse))
        response.flushBuffer()
    }
}

data class SessionExpiredResponse(
    val timestamp: String,
    val status: Int,
    val error: String,
    val message: String,
    val path: String
)
