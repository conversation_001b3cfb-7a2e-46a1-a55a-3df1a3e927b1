package at.derneubauer.backend.security

import at.derneubauer.backend.db.user.UserDo
import org.springframework.stereotype.Service

@Service
class PermissionResolver() {

    private fun noPermissions(): List<NeubauerPermission> = listOf()

    private fun adminPermissions(): List<NeubauerPermission> = listOf(
            NeubauerPermission.CREATE_USER,
            NeubauerPermission.EDIT_USER,
            *supervisorPermissions().toTypedArray()
    )
    private fun supervisorPermissions(): List<NeubauerPermission> = listOf(
            NeubauerPermission.LOGIN,
            NeubauerPermission.VIEW_RESOURCE_PLAN_TECHNICIAN,
            NeubauerPermission.VIEW_RESOURCE_PLAN_ASSISTANT,
            NeubauerPermission.VIEW_TECHNICIAN_LIST,
            NeubauerPermission.VIEW_TECHNICIAN_ASSIGNMENT_LIST,
            NeubauerPermission.VIEW_ASSISTANT_LIST,
            NeubauerPermission.VIEW_ASSISTANT_ASSIGNMENT_LIST,
            NeubauerPermission.VIEW_USER_LIST,
            NeubauerPermission.VIEW_USER_DETAIL,
            NeubauerPermission.VIEW_ISSUE_LIST,
            NeubauerPermission.VIEW_ISSUE_DETAIL,
            NeubauerPermission.VIEW_ISSUE_DOCUMENTS,
            NeubauerPermission.EDIT_ISSUE_DETAIL_NOTE,
            NeubauerPermission.CREATE_ISSUE_TECHNICIAN_ASSIGNMENT,
            NeubauerPermission.EDIT_ISSUE_TECHNICIAN_ASSIGNMENT,
            NeubauerPermission.DELETE_ISSUE_TECHNICIAN_ASSIGNMENT,
            NeubauerPermission.LIST_USER_IMAGES,
            NeubauerPermission.LIST_ISSUE_IMAGES,
            NeubauerPermission.VIEW_USER_IMAGE,
            NeubauerPermission.VIEW_ISSUE_IMAGE,
            NeubauerPermission.DELETE_ISSUE_IMAGE,
            NeubauerPermission.DELETE_USER_IMAGE,
            NeubauerPermission.CREATE_ASSISTANT_TECHNICIAN_ASSIGNMENT,
            NeubauerPermission.EDIT_ASSISTANT_TECHNICIAN_ASSIGNMENT,
            NeubauerPermission.DELETE_ASSISTANT_TECHNICIAN_ASSIGNMENT,
            NeubauerPermission.DOWNLOAD_ISSUE_IMAGES,
            NeubauerPermission.DOWNLOAD_USER_IMAGES,
            NeubauerPermission.DOWNLOAD_ISSUE_DOCUMENTS,
            NeubauerPermission.VIEW_TECHNICIAN_ABSENCES,
            NeubauerPermission.VIEW_RECURRING_ISSUE_PLACEHOLDERS,
            NeubauerPermission.EDIT_RECURRING_ISSUE_PLACEHOLDERS,
            NeubauerPermission.DELETE_RECURRING_ISSUE_PLACEHOLDERS,
            NeubauerPermission.CREATE_RECURRING_ISSUE_PLACEHOLDERS,
            NeubauerPermission.VIEW_ASSISTANT_ABSENCES,
            NeubauerPermission.VIEW_ABSENCE_DETAIL,
            NeubauerPermission.EDIT_ABSENCE,
            NeubauerPermission.CREATE_ABSENCE,
            NeubauerPermission.DELETE_ABSENCE,
            NeubauerPermission.REQUEST_USER_IMAGES_FROM_APP,
            NeubauerPermission.REQUEST_ISSUE_IMAGES_FROM_APP,
            NeubauerPermission.PRINT_RESOURCE_PLAN,
            NeubauerPermission.PRINT_ISSUE_DETAILS,
            NeubauerPermission.VIEW_ISSUE_HISTORY,
            NeubauerPermission.CLOSE_ISSUE,
            NeubauerPermission.REOPEN_ISSUE,
            NeubauerPermission.REOPEN_ISSUE_FROM_DONE_TECHNICIAN,
            NeubauerPermission.REOPEN_ISSUE_FROM_CLOSED,
            NeubauerPermission.MOVE_ISSUE_IMAGE,
            NeubauerPermission.MOVE_USER_IMAGE
    )

    private fun technicianPermissions(): List<NeubauerPermission> = listOf(
            NeubauerPermission.LOGIN,
            NeubauerPermission.USE_APP,
            NeubauerPermission.SET_ISSUE_TECHNICIAN_DONE,
            NeubauerPermission.ACCEPT_ISSUE,
            NeubauerPermission.PUT_BACK_ISSUE,
            NeubauerPermission.REOPEN_ISSUE,
            NeubauerPermission.REOPEN_ISSUE_FROM_DONE_TECHNICIAN,
            NeubauerPermission.EDIT_ISSUE_DETAIL_NOTE
    )

    private fun assistantPermissions(): List<NeubauerPermission> = listOf()


    fun resolvePermissionsFor(user: UserDo): List<NeubauerPermission> {
        if (user.disabled) {
            return noPermissions()
        }

        return when (user.role) {
            NeubauerRole.ADMIN.name -> adminPermissions()
            NeubauerRole.SUPERVISOR.name -> supervisorPermissions()
            NeubauerRole.TECHNICIAN.name -> technicianPermissions()
            NeubauerRole.ASSISTANT.name -> assistantPermissions()
            else -> noPermissions()
        }
    }
}
