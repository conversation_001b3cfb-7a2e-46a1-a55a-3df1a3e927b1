package at.derneubauer.backend.security

enum class NeubauerRole(val localizationKey: String, val sortOrder: Int) {
    ADMIN("role.admin", 4),
    SUPERVISOR("role.supervisor", 3),
    TECHNICIAN("role.technician", 2),
    ASSISTANT("role.assistant", 1);

    fun getName() = name

    companion object {
        fun sortedValues(): List<NeubauerRole> {
            return values().sortedBy { it.sortOrder }
        }
    }
}

