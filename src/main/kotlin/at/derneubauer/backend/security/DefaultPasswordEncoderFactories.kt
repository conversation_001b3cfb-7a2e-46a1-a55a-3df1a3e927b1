package at.derneubauer.backend.security

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.DelegatingPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder

class DefaultPasswordEncoderFactories {
    companion object {
        fun createDelegatingPasswordEncoder(): PasswordEncoder {
            val encodingId = "bcrypt"
            val encoders = HashMap<String, PasswordEncoder>()

            encoders[encodingId] = BCryptPasswordEncoder()

            val delegatingPasswordEncoder = DelegatingPasswordEncoder(encodingId, encoders)
            delegatingPasswordEncoder.setDefaultPasswordEncoderForMatches(BCryptPasswordEncoder())

            return delegatingPasswordEncoder
        }
    }
}