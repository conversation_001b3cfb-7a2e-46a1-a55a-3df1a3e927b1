package at.derneubauer.backend.security

import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.db.user.UserMapper
import at.derneubauer.backend.rest.error.NoLoggedInUserFoundException
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import java.util.*
import jakarta.servlet.*

@Service
class LoggedInUserResolver(val userMapper: UserMapper) {

    companion object {
        val log = LoggerFactory.getLogger(LoggedInUserResolver::class.java)
    }

    fun resolveLoggedInUser(): UserDo {
        log.debug("Resolving logged in user ...")
        val principal = SecurityContextHolder.getContext().authentication.principal
        if (principal is UserDetails) {
            val user = userMapper.findByUsername(principal.username)
            if (user != null) {
                MDC.put("neubauer_user__secret_id", "${user.secretId}")
                return user
            }
        }

        throw NoLoggedInUserFoundException()
    }
}

@Component
class LogContextCleanupFilter : Filter {
    override fun doFilter(request: ServletRequest?, response: ServletResponse?, chain: FilterChain?) {
        try {
            MDC.put("neubauer__request__id", UUID.randomUUID().toString())
            chain?.doFilter(request, response)
        } finally {
            MDC.clear();
        }
    }

    override fun destroy() {}

    override fun init(filterConfig: FilterConfig?) {}
}
