package at.derneubauer.backend.security

import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.userdetails.UserDetails

class NeubauerUser(val username_: String, val password_: String, val isDisabled: Boolean) : UserDetails {

    override fun getUsername() = username_

    override fun getPassword() = password_

    override fun isEnabled() = !isDisabled

    override fun getAuthorities() = mutableListOf<GrantedAuthority>()

    override fun isCredentialsNonExpired() = true

    override fun isAccountNonExpired() = true

    override fun isAccountNonLocked() = true

}
