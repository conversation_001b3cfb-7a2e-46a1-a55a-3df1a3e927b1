package at.derneubauer.backend.security

import at.derneubauer.backend.db.user.UserDo
import at.derneubauer.backend.rest.error.MissingPermissionException
import org.springframework.stereotype.Service

@Service
class PermissionService(val permissionResolver: PermissionResolver, val loggedInUserResolver: LoggedInUserResolver) {

    fun loggedInUserHasPermission(permission: NeubauerPermission): Boolean {
        val loggedInUser = loggedInUserResolver.resolveLoggedInUser()
        return hasPermission(permission, loggedInUser)
    }

    fun hasPermission(permission: NeubauerPermission, user: UserDo): Boolean {
        return permissionResolver.resolvePermissionsFor(user).contains(permission)
    }

    fun ensurePermission(permission: NeubauerPermission, user: UserDo) {
        if (!hasPermission(permission, user)) {
            throw MissingPermissionException(permission)
        }
    }
}
