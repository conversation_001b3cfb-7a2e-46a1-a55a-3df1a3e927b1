package at.derneubauer.backend.store

import at.derneubauer.backend.db.documents.IssueDocumentMapper
import at.derneubauer.backend.db.documents.model.DocumentDo
import at.derneubauer.backend.web.error.NeubauerException
import org.springframework.stereotype.Service

@Service
class IssueDocumentStore(
    private val issueDocumentMapper: IssueDocumentMapper
) {
    fun getDocumentsForIssueId(issueId: Long): List<DocumentDo> =
        try {
            issueDocumentMapper.getDocumentsForIssueId(issueId)
        } catch (e: Exception) {
            throw NeubauerException("Could not get documents for issue id", e)
        }

    fun getDocumentById(documentId: Long): DocumentDo? =
        try {
            issueDocumentMapper.getDocumentById(documentId)
        } catch (e: Exception) {
            throw NeubauerException("Could not get document by id", e)
        }
}
