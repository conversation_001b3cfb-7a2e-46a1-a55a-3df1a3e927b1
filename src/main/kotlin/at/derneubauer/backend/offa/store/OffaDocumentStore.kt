package at.derneubauer.backend.offa.store

import at.derneubauer.backend.offa.db.OffaDocumentMapper
import at.derneubauer.backend.offa.service.OffaCreateDocumentException
import at.derneubauer.backend.offa.service.OffaDocumentMappingException
import at.derneubauer.backend.offa.unmarshal.OffaDocumentType
import org.springframework.stereotype.Service

@Service
class OffaDocumentStore(
    private val offaDocumentsMapper: OffaDocumentMapper
) {
    fun createDocument(
        fileStorageKey: String,
        fileSize: Long,
        documentType: OffaDocumentType,
        documentNumber: String,
        documentText: String?,
        documentFilename: String,
        projectNumber: String,
        projectText: String?,
    ): Long = try {
        offaDocumentsMapper.createDocument(
            fileStorageKey = fileStorageKey,
            fileSize = fileSize,
            documentType = documentType,
            documentNumber = documentNumber,
            documentText = documentText,
            documentFilename = documentFilename,
            projectNumber = projectNumber,
            projectText = projectText,
        )
    } catch (e: Exception) {
        throw OffaCreateDocumentException(e)
    }

    fun mapDocumentToIssue(
        documentId: Long,
        issueId: Long,
    ) = try {
        offaDocumentsMapper.mapDocumentToIssue(
            documentId = documentId,
            issueId = issueId,
        )
    } catch (e: Exception) {
        throw OffaDocumentMappingException(e)
    }

    fun getIssueIdsForWorksheetNumbers(
        worksheetNumbers: List<String>,
    ) = offaDocumentsMapper.findIssueIdsForWorksheetNumbers(
        worksheetNumbers = worksheetNumbers,
    )

    fun deleteIssueDocumentsByDocumentId(
        documentId: Long,
    ) = offaDocumentsMapper.deleteIssueDocumentsByDocumentId(documentId)
}
