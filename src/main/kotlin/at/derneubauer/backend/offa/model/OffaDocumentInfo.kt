package at.derneubauer.backend.offa.model

import at.derneubauer.backend.offa.unmarshal.OffaDocumentType

data class OffaDocumentInfo(
    val documentType: OffaDocumentType,
    val documentNumber: String,
    val documentText: String? = null,
    val documentFilename: String,
    val project: OffaProject,
    val worksheets: List<OffaWorksheet>
)

data class OffaProject(
    val projectNumber: String,
    val projectText: String? = null,
)

data class OffaWorksheet(
    val worksheetNumber: String
)
