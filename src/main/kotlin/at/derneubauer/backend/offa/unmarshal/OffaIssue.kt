package at.derneubauer.backend.offa.unmarshal

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.XmlSchemaType
import jakarta.xml.bind.annotation.XmlType
import javax.xml.datatype.XMLGregorianCalendar

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = [
    "externalId",
    "customer",
    "billing",
    "technician",
    "appointmentDate",
    "appointmentTime",
    "customerNumber",
    "contactPerson",
    "workload"
])
@XmlRootElement(name="issue")
class OffaIssue {

    @XmlElement(name = "number", required = true)
    var externalId: String? = null

    @XmlElement(name = "object")
    var customer: OffaIssueAddress? = null

    var billing: OffaIssueAddress? = null

    var technician: String? = null

    @XmlSchemaType(name = "date")
    var appointmentDate: XMLGregorianCalendar? = null

    @XmlSchemaType(name = "time")
    var appointmentTime: XMLGregorianCalendar? = null

    var customerNumber: String? = null

    var contactPerson: String? = null

    var workload: String? = null
}
