package at.derneubauer.backend.offa.unmarshal

import at.derneubauer.backend.offa.model.OffaDocumentInfo
import at.derneubauer.backend.offa.model.OffaProject
import at.derneubauer.backend.offa.model.OffaWorksheet
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlElementWrapper
import jakarta.xml.bind.annotation.XmlEnum
import jakarta.xml.bind.annotation.XmlEnumValue
import jakarta.xml.bind.annotation.XmlRootElement

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "document")
data class OffaDocumentInfoDto(
    @XmlElement(name = "documentType")
    val documentType: OffaDocumentType? = null,

    @XmlElement(name = "documentNumber")
    val documentNumber: String? = null,

    @XmlElement(name = "documentText")
    val documentText: String? = null,

    @XmlElement(name = "documentFilename")
    val documentFilename: String? = null,

    @XmlElement(name = "project")
    val project: OffaProjectDto? = null,

    @field:XmlElementWrapper(name = "worksheets")
    @field:XmlElement(name = "worksheet")
    val worksheets: List<OffaWorksheetDto>? = null
)

@XmlAccessorType(XmlAccessType.FIELD)
data class OffaProjectDto(
    @XmlElement(name = "projectNumber")
    val projectNumber: String? = null,

    @XmlElement(name = "projectText")
    val projectText: String? = null
)

@XmlAccessorType(XmlAccessType.FIELD)
data class OffaWorksheetDto(
    @XmlElement(name = "worksheetNumber")
    val worksheetNumber: String? = null
)

@XmlEnum
enum class OffaDocumentType {
    @XmlEnumValue("Angebot")
    OFFER,

    @XmlEnumValue("Rechnung")
    RECEIPT
}

fun OffaDocumentInfoDto.toOffaDocumentInfo(): OffaDocumentInfo {
    requireNotNull(documentType) { "documentType must not be null." }
    require(!documentNumber.isNullOrEmpty()) { "documentNumber must not be null or empty." }
    require(!documentFilename.isNullOrEmpty()) { "documentFilename must not be null or empty." }

    requireNotNull(project) { "project must not be null." }
    require(!project.projectNumber.isNullOrEmpty()) { "projectNumber must not be null or empty." }
    val nullSafeProject = OffaProject(
        projectNumber = project.projectNumber,
        projectText = project.projectText.takeIf { !it.isNullOrEmpty() },
    )

    require(!worksheets.isNullOrEmpty()) { "worksheets must not be null or empty." }
    val nullSafeWorksheets = worksheets.map {
        require(!it.worksheetNumber.isNullOrEmpty()) { "worksheetNumber must not be null or empty." }
        OffaWorksheet(
            worksheetNumber = it.worksheetNumber,
        )
    }

    return OffaDocumentInfo(
        documentType = documentType,
        documentNumber = documentNumber,
        documentText = documentText.takeIf { !it.isNullOrEmpty() },
        documentFilename = documentFilename,
        project = nullSafeProject,
        worksheets = nullSafeWorksheets,
    )
}
