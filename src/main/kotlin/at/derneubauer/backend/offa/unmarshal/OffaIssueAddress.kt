package at.derneubauer.backend.offa.unmarshal

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.XmlType

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = [
    "name",
    "street",
    "zipCode",
    "city",
    "telephone"
])
@XmlRootElement(name="object")
class OffaIssueAddress {
    var name: String? = null
    var street: String? = null
    var zipCode: String? = null
    var city: String? = null
    var telephone: String? = null
}
