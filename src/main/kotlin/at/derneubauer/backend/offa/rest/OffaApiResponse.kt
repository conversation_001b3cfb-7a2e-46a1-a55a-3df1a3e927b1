package at.derneubauer.backend.offa.rest

import at.derneubauer.backend.offa.service.OffaCreateDocumentException
import at.derneubauer.backend.offa.service.OffaDocumentInfoParseException
import at.derneubauer.backend.offa.service.OffaDocumentMappingException
import at.derneubauer.backend.offa.service.OffaDocumentUploadException
import at.derneubauer.backend.offa.service.OffaFilenamesNotMatchingException
import at.derneubauer.backend.offa.service.OffaInvalidDocumentMediaTypeException
import at.derneubauer.backend.offa.service.OffaNoIssuesWithWorksheetNumbersFoundException
import at.derneubauer.backend.offa.service.UnexpectedOffaException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.multipart.support.MissingServletRequestPartException
import java.time.OffsetDateTime

data class OffaApiResponse<T>(
    val data: T? = null,
    val error: OffaApiError? = null
)

data class OffaApiError(
    val message: String,
    val statusCode: Int,
    val timestamp: OffsetDateTime = OffsetDateTime.now(),
)

fun Exception.toOffaApiResponse(): ResponseEntity<OffaApiResponse<Any>> {
    return when (this) {
        is MissingServletRequestPartException,
        is OffaInvalidDocumentMediaTypeException,
        is OffaDocumentInfoParseException,
        is OffaFilenamesNotMatchingException,
        is OffaNoIssuesWithWorksheetNumbersFoundException
        -> ResponseEntity.badRequest().body(
            OffaApiResponse(
                error = OffaApiError(
                    message = toString(),
                    statusCode = HttpStatus.BAD_REQUEST.value(),
                )
            )
        )

        is OffaDocumentUploadException,
        is OffaCreateDocumentException,
        is OffaDocumentMappingException
        -> ResponseEntity.internalServerError().body(
            OffaApiResponse(
                error = OffaApiError(
                    message = toString(),
                    statusCode = HttpStatus.INTERNAL_SERVER_ERROR.value(),
                )
            )
        )

        else -> ResponseEntity.internalServerError().body(
            OffaApiResponse(
                error = OffaApiError(
                    message = UnexpectedOffaException(this).toString(),
                    statusCode = HttpStatus.INTERNAL_SERVER_ERROR.value(),
                )
            )
        )
    }
}
