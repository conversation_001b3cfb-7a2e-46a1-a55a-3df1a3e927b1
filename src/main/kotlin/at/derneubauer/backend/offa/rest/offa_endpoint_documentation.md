### Endpoint: Store Worksheet Document

#### Description:
Endpoint to store a document attached to one or more issues/worksheets.

This endpoint accepts two multipart files:
- `documentInfo`: Information about the document, is expected to be an XML-file.
- `document`: The actual document (offer or receipt), is expected to be an PDF.

#### Endpoint:
`POST /offa/issue/documents`

#### Authorization:
The endpoint is secured with the same Basic Auth as the already used endpoint (/offa/issue/connector).
You will need to add the `Authorization` Header with the Base64-encoded combination of username and password.

- Example:
```
Authorization: Basic dXNlcm5hbWU6cGFzc3dvcmQ=
```

Whereas `dXNlcm5hbWU6cGFzc3dvcmQ=` equals to the Base64-encoded version of: `username:password`

#### Parameters:
- `documentInfo` (multipart file): The xml file containing information about the document.
- `document` (multipart file): The actual document (offer or receipt) as PDF.

##### Important information about the `documentInfo`:
The xml file has to have the following structure.

```xml
<?xml version="1.0" encoding="utf-8"?>
<document>
    <documentType>Angebot</documentType>
    <documentNumber>AN211061</documentNumber>
    <documentText>Das ist der Angebot Betreff</documentText>
    <documentFilename>AN211061.pdf</documentFilename>
    <project>
        <projectNumber>PA211599</projectNumber>
        <projectText>Das ist der Projekt Betreff</projectText>
    </project>
    <worksheets>
        <worksheet>
            <worksheetNumber>S100055</worksheetNumber>
        </worksheet>
        <worksheet>
            <worksheetNumber>S100056</worksheetNumber>
        </worksheet>
    </worksheets>
</document>
```

Apart from it being a valid xml file there are some requirements it has to match in order to be accepted:
- `documentType` has to be either `Angebot` or `Rechnung`
- the value of `documentFilename` has to match with the actual filename of the PDF file
- there has to be at least one `worksheet` element within the `worksheets` list
- only `documentText` and `projectText` are allowed to be null/empty, all other fields are required

#### Response:
- **Success:** 
  - Status Code: 200 (OK)
- **Failure:** 
  - Status Code: 
    - 400 (Bad Request) for client-side errors
    - 500 (Internal Server Error) for server-side errors
  - Body: Error message describing the issue encountered.

##### Example Responses:
- Success:
```json
{
  "data": "Document stored successfully",
  "error": null
}
```

- Failure:
```json
  {
    "data": null,
    "error": {
      "message": "at.derneubauer.backend.offa.service.OffaDocumentInfoParseException: Error trying to parse the document information with root cause: java.lang.IllegalArgumentException: documentType must not be null.",
      "statusCode": 400,
      "timestamp": "2024-02-20T07:49:39.758148632Z"
    }
  }
```

#### Possible Errors:
- `MissingServletRequestPartException`: If either `documentInfo` or `document` is missing.
- `OffaInvalidDocumentMediaTypeException`: If either one of the two multipart files has an invalid media type.
- `OffaDocumentInfoParseException`: If there is an error parsing the document information either because the xml is malformed or a required field is not present.
- `OffaFilenamesNotMatchingException`: If the filename in the document info and filename of the actual document don't match.
- `OffaNoIssuesWithWorksheetNumbersFoundException`: If no issues/worksheets with the provided numbers within the documentInfo are found in the database.
- `OffaDocumentUploadException`: If there is an error uploading the document into the S3 bucket.
- `OffaCreateDocumentException`: If there is an error creating the document entry in the database.
- `OffaDocumentMappingException`: If there is an error creating the issue/worksheet to document mapping table entry in the database.
- `UnexpectedOffaException`: If an unexpected exception occurs.

#### Example Usage (cURL):
```bash
curl --location 'https://example.com/offa/issue/documents' \
--header 'Authorization: Basic dXNlcm5hbWU6cGFzc3dvcmQ=' \
--form 'documentInfo=@"/path/to/documentInfo/AN211061.xml"' \
--form 'document=@"/path/to/document/AN211061.pdf"'
```
