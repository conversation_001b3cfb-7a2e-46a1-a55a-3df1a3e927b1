package at.derneubauer.backend.offa.rest

import at.derneubauer.backend.offa.service.OffaCreateDocumentException
import at.derneubauer.backend.offa.service.OffaDocumentInfoParseException
import at.derneubauer.backend.offa.service.OffaDocumentMappingException
import at.derneubauer.backend.offa.service.OffaDocumentService
import at.derneubauer.backend.offa.service.OffaDocumentUploadException
import at.derneubauer.backend.offa.service.OffaFilenamesNotMatchingException
import at.derneubauer.backend.offa.service.OffaInvalidDocumentMediaTypeException
import at.derneubauer.backend.offa.service.OffaNoIssuesWithWorksheetNumbersFoundException
import at.derneubauer.backend.offa.service.UnexpectedOffaException
import jakarta.validation.Valid
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.support.MissingServletRequestPartException

@RestController
class OffaIssueDocumentsController(
    private val offaDocumentService: OffaDocumentService
) {
    /**
     * Endpoint to store a document attached to one or more issues/worksheets.
     * This endpoint accepts two multipart files:
     * - documentInfo: Information about the document, is expected to be an XML-file.
     * - document: The actual document (offer or receipt), is expected to be an PDF.
     *
     * @param documentInfo The xml file containing information about the document.
     * @param document The actual document (offer or receipt) as PDF.
     *
     * @return ResponseEntity indicating the success or failure of the operation.
     *         - If the operation is successful, returns ResponseEntity with status code 200 (OK).
     *         - If any error occurs during the operation, returns ResponseEntity with an appropriate status code
     *           along with an error message in the body.
     *
     * @throws MissingServletRequestPartException
     *      if either documentInfo or document is missing.
     * @throws OffaInvalidDocumentMediaTypeException
     *      if either one of the two multipart files has an invalid media type.
     * @throws OffaDocumentInfoParseException
     *      if there is an error parsing the document information either because
     *      the xml is malformed or a required field is not present.
     * @throws OffaFilenamesNotMatchingException
     *      if the filename in the document info and filename of the actual document don't match.
     * @throws OffaNoIssuesWithWorksheetNumbersFoundException
     *      if no issues/worksheets with the provided numbers within the documentInfo are found in the database.
     * @throws OffaDocumentUploadException
     *      if there is an error uploading the document into the S3 bucket.
     * @throws OffaCreateDocumentException
     *      if there is an error creating the document entry in the database.
     * @throws OffaDocumentMappingException
     *      if there is an error creating the issue/worksheet to document mapping table entry in the database.
     * @throws UnexpectedOffaException
     *      if an unexpected exception occurs.
     */
    @PostMapping("/offa/issue/documents")
    fun storeIssueDocument(
        @Valid @RequestParam("documentInfo") documentInfo: MultipartFile?,
        @Valid @RequestParam("document") document: MultipartFile?
    ): ResponseEntity<OffaApiResponse<Any>> {
        return try {
            if (documentInfo == null || documentInfo.size == 0L) throw MissingServletRequestPartException("documentInfo")
            if (document == null || document.size == 0L) throw MissingServletRequestPartException("document")

            offaDocumentService.validateAndStore(
                documentInfo = documentInfo,
                document = document,
            )

            ResponseEntity.ok().body(
                OffaApiResponse(
                    data = "Document stored successfully",
                ),
            )
        } catch (e: Exception) {
            e.toOffaApiResponse()
        }
    }
}
