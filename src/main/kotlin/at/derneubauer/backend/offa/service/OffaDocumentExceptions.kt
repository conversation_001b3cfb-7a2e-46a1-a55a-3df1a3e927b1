package at.derneubauer.backend.offa.service

class OffaInvalidDocumentMediaTypeException(paramName: String, expectedMediaType: String, actualMediaType: String?) :
    RuntimeException("'$paramName' is expected to be of type '$expectedMediaType' but was '$actualMediaType'.")

class OffaDocumentInfoParseException(throwable: Throwable) :
    RuntimeException("Error trying to parse the document information with root cause: $throwable", throwable)

class OffaFilenamesNotMatchingException(expectedFilename: String, actualFilename: String?) :
    RuntimeException(
        "The expected filename defined in the documentInfo is '$expectedFilename' but the " +
                "received document has the filename '$actualFilename'"
    )

class OffaDocumentUploadException(throwable: Throwable) :
    RuntimeException("Error trying to upload the document to the S3 bucket with root cause $throwable", throwable)

class OffaCreateDocumentException(throwable: Throwable) :
    RuntimeException("Could not store document in database with root cause: $throwable", throwable)

class OffaDocumentMappingException(throwable: Throwable) :
    RuntimeException(
        "Could not create mapping table entry for document to issue with root cause: $throwable",
        throwable,
    )

class OffaNoIssuesWithWorksheetNumbersFoundException :
    RuntimeException("There are no worksheets within database matching the worksheet numbers in the documentInfo.")

class UnexpectedOffaException(throwable: Throwable) :
    RuntimeException("An unexpected error occurred. Exception: $throwable")
