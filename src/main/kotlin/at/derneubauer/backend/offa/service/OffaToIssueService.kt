package at.derneubauer.backend.offa.service

import at.derneubauer.backend.offa.db.OffaIssueCountDo
import at.derneubauer.backend.offa.db.OffaIssueInsertDto
import at.derneubauer.backend.offa.db.OffaToIssueMapper
import at.derneubauer.backend.offa.unmarshal.OffaIssue
import at.derneubauer.backend.offa.unmarshal.OffaIssueAddress
import at.derneubauer.backend.service.NeubauerIssueStatus
import at.derneubauer.backend.web.error.NeubauerException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import javax.xml.datatype.XMLGregorianCalendar

@Service
class OffaToIssueService(private val offaToIssueMapper: OffaToIssueMapper) {

    companion object {
        private val log = LoggerFactory.getLogger(this::class.java)
    }

    fun insertOrUpdate(offaIssue: OffaIssue) {
        val externalId: String =
            offaIssue.externalId ?: throw ClientErrorOffaToIssueServiceException("External id not provided.")
        val address = addressToString(offaIssue.customer)
        val appointmentDateTime = dateTimeToString(offaIssue.appointmentDate, offaIssue.appointmentTime)

        if (offaToIssueMapper.isExternalIdKnown(externalId).issueCount == 0) {
            val insertDto = OffaIssueInsertDto(
                externalId,
                offaIssue.contactPerson,
                address,
                appointmentDateTime,
                offaIssue.workload
            )
            offaToIssueMapper.insert(insertDto)
            val issueId = insertDto.id ?: return

            offaToIssueMapper.insertNewStatusForIssue(issueId, NeubauerIssueStatus.NEW.getName())

            val smartbricksProjectId = createInSmartbricks(offaIssue)
            if (smartbricksProjectId != null) {
                offaToIssueMapper.linkWithSmartbricksProject(issueId, smartbricksProjectId)
            }
        } else {
            // maybe update in smartbricks, depends on what we need to send in the first place
            offaToIssueMapper.update(
                externalId,
                offaIssue.contactPerson,
                address,
                appointmentDateTime,
                offaIssue.workload
            )
        }
    }

    private fun createInSmartbricks(offaIssue: OffaIssue): Long? {
        return try {
            // TODO: api call to smartbricks
            val response = OffaIssueCountDo(
                issueCount = 1
            )
            response.issueCount.toLong()
        } catch (ex: Exception) {
            log.error("Error creating project for issue (${offaIssue.externalId}) in smartbricks", ex)
            null
        }
    }

    private fun addressToString(address: OffaIssueAddress?): String {
        val sb = StringBuilder()
        if (address?.name != null) sb.append(address.name).append(" ")
        if (address?.street != null) sb.append(address.street).append(" ")
        if (address?.zipCode != null) sb.append(address.zipCode).append(" ")
        if (address?.city != null) sb.append(address.city)
        if (address?.telephone != null) sb.append(" ").append(address.telephone)
        return sb.toString()
    }

    private fun dateTimeToString(date: XMLGregorianCalendar?, time: XMLGregorianCalendar?): String {
        val sb = StringBuilder()
        if (date != null) {
            val day = date.day.toString().padStart(2, '0')
            val month = date.month.toString().padStart(2, '0')
            val year = date.year
            sb.append("$day.$month.$year").append(" ")
        }
        if (time != null) {
            val hour = time.hour.toString().padStart(2, '0')
            val minute = time.minute.toString().padStart(2, '0')
            sb.append("$hour:$minute")
        }
        return sb.toString()
    }

}

open class OffaToIssueServiceException : NeubauerException {
    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}

class ClientErrorOffaToIssueServiceException : OffaToIssueServiceException {
    override val statusCode = HttpStatus.BAD_REQUEST.value()

    constructor(message: String) : super(message)
    constructor(message: String, t: Throwable) : super(message, t)
}