package at.derneubauer.backend.offa.service

import at.derneubauer.backend.offa.model.OffaDocumentInfo
import at.derneubauer.backend.offa.store.OffaDocumentStore
import at.derneubauer.backend.offa.unmarshal.OffaDocumentInfoDto
import at.derneubauer.backend.offa.unmarshal.toOffaDocumentInfo
import at.derneubauer.backend.service.DocumentFileService
import com.amazonaws.AmazonServiceException
import com.amazonaws.SdkClientException
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ObjectMetadata
import jakarta.annotation.PostConstruct
import jakarta.xml.bind.JAXBContext
import org.apache.commons.io.IOUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@Service
class OffaDocumentService(
    private val offaDocumentStore: OffaDocumentStore,
    private val documentFileService: DocumentFileService,
) {
    companion object {
        const val DOCUMENT_INFO_EXPECTED_MEDIA_TYPE = MediaType.APPLICATION_XML_VALUE
        const val DOCUMENT_EXPECTED_MEDIA_TYPE = MediaType.APPLICATION_PDF_VALUE
    }

    fun validateAndStore(documentInfo: MultipartFile, document: MultipartFile) {
        ensureCorrectMediaTypes(
            documentInfo = documentInfo,
            document = document,
        )

        val offaDocumentInfo = unmarshalDocumentInfo(documentInfo)
        ensureFilenamesMatch(
            offaDocumentInfo = offaDocumentInfo,
            document = document,
        )

        val issueIds = retrieveIssueIdsForWorksheetNumbers(
            offaDocumentInfo.worksheets.map {
                it.worksheetNumber
            }
        )

        val fileKey = documentFileService.uploadDocumentToS3(document)
        val documentId = createDocumentInDatabase(
            fileKey = fileKey,
            offaDocumentInfo = offaDocumentInfo,
            document = document,
        )

        mapIssuesToDocument(
            documentId = documentId,
            issueIds = issueIds,
        )
    }

    private fun unmarshalDocumentInfo(document: MultipartFile): OffaDocumentInfo {
        val context: JAXBContext = JAXBContext.newInstance(OffaDocumentInfoDto::class.java)
        val unmarshaller = context.createUnmarshaller()

        val offaDocumentInfo = try {
            (unmarshaller.unmarshal(document.inputStream) as OffaDocumentInfoDto).toOffaDocumentInfo()
        } catch (e: Exception) {
            throw OffaDocumentInfoParseException(e)
        }

        return offaDocumentInfo
    }

    private fun createDocumentInDatabase(
        fileKey: String,
        offaDocumentInfo: OffaDocumentInfo,
        document: MultipartFile,
    ): Long = offaDocumentStore.createDocument(
        fileStorageKey = fileKey,
        fileSize = document.size,
        documentType = offaDocumentInfo.documentType,
        documentNumber = offaDocumentInfo.documentNumber,
        documentText = offaDocumentInfo.documentText,
        documentFilename = offaDocumentInfo.documentFilename,
        projectNumber = offaDocumentInfo.project.projectNumber,
        projectText = offaDocumentInfo.project.projectText,
    )

    private fun retrieveIssueIdsForWorksheetNumbers(worksheetNumbers: List<String>): List<Long> {
        val issueIds = offaDocumentStore.getIssueIdsForWorksheetNumbers(
            worksheetNumbers = worksheetNumbers,
        )

        if (issueIds.isEmpty()) {
            throw OffaNoIssuesWithWorksheetNumbersFoundException()
        }

        return issueIds
    }

    private fun mapIssuesToDocument(
        documentId: Long,
        issueIds: List<Long>,
    ) {
        offaDocumentStore.deleteIssueDocumentsByDocumentId(
            documentId = documentId,
        )

        issueIds.forEach {
            offaDocumentStore.mapDocumentToIssue(
                documentId = documentId,
                issueId = it
            )
        }
    }

    private fun ensureCorrectMediaTypes(documentInfo: MultipartFile, document: MultipartFile) {
        if (documentInfo.contentType != DOCUMENT_INFO_EXPECTED_MEDIA_TYPE) {
            throw OffaInvalidDocumentMediaTypeException(
                paramName = documentInfo.name,
                expectedMediaType = DOCUMENT_INFO_EXPECTED_MEDIA_TYPE,
                actualMediaType = documentInfo.contentType,
            )
        }

        if (document.contentType != DOCUMENT_EXPECTED_MEDIA_TYPE) {
            throw OffaInvalidDocumentMediaTypeException(
                paramName = document.name,
                expectedMediaType = DOCUMENT_EXPECTED_MEDIA_TYPE,
                actualMediaType = document.contentType,
            )
        }
    }

    private fun ensureFilenamesMatch(offaDocumentInfo: OffaDocumentInfo, document: MultipartFile) =
        document.originalFilename?.let {
            if (it != offaDocumentInfo.documentFilename) {
                throw OffaFilenamesNotMatchingException(
                    expectedFilename = offaDocumentInfo.documentFilename,
                    actualFilename = document.originalFilename,
                )
            }
        }
}
