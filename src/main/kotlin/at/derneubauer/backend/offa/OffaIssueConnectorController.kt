package at.derneubauer.backend.offa

import at.derneubauer.backend.offa.service.OffaToIssueService
import at.derneubauer.backend.offa.unmarshal.OffaIssue
import org.apache.tomcat.util.http.fileupload.FileUploadException
import org.slf4j.LoggerFactory
import org.springframework.http.MediaType
import org.springframework.stereotype.Controller
import org.springframework.web.HttpRequestHandler
import org.springframework.web.bind.annotation.RequestMapping
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jakarta.servlet.http.Part
import jakarta.xml.bind.JAXBContext

@Controller
class OffaIssueConnectorController(private val offaToIssueService: OffaToIssueService): HttpRequestHandler {

    companion object {
        val log = LoggerFactory.getLogger(this::class.java)
    }

    /**
     * Store or update issue
     *
     * @throws IOException
     *     if an I/O error occurs
     * @throws IllegalStateException
     *     if size limits are exceeded or no multipart configuration is
     *     provided
     * @throws ServletException
     *     if the request is not multipart/form-data
     * @throws FileUploadException
     *     if  file is not application/xml
     * @throws JAXBException
     *     if any unexpected errors occur while unmarshalling
     * @throws UnmarshalException
     *     if the {@link ValidationEventHandler ValidationEventHandler}
     *     returns false from its <tt>handleEvent</tt> method or the
     *     <tt>Unmarshaller</tt> is unable to perform the XML to Java
     *     binding.  See <a href="#unmarshalEx">Unmarshalling XML Data</a>
     * @throws IllegalArgumentException
     *     if the InputStream parameter is null
     * @throws IbatisException
     *     if database error
     */
    @RequestMapping("/offa/issue/connector")
    override fun handleRequest(request: HttpServletRequest, response: HttpServletResponse) {
        val files: Collection<Part>  = request.getParts()
        for (file in files) {
            if (file.contentType != MediaType.APPLICATION_XML_VALUE) {
                throw RuntimeException("Filetype not allowed")
            }
            val context: JAXBContext = JAXBContext.newInstance(OffaIssue::class.java)
            val unmarshaller = context.createUnmarshaller()

            file.inputStream.use { inStream ->
                var offaIssue = unmarshaller.unmarshal(inStream) as OffaIssue
                offaToIssueService.insertOrUpdate(offaIssue)
            }
        }
    }
}
