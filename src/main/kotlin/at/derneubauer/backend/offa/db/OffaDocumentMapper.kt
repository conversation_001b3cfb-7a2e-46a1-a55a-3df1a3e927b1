package at.derneubauer.backend.offa.db

import at.derneubauer.backend.db.utils.MyBatisInClauseExtensionDriver
import at.derneubauer.backend.offa.unmarshal.OffaDocumentType
import org.apache.ibatis.annotations.Delete
import org.apache.ibatis.annotations.Lang
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

@Mapper
interface OffaDocumentMapper {
    @Select(
        """
            INSERT INTO documents
                (
                    file_storage_key, 
                    file_size, 
                    document_type, 
                    document_number, 
                    document_text, 
                    document_file_name, 
                    project_number, 
                    project_text    
                ) 
            VALUES
                (
                    #{fileStorageKey},
                    #{fileSize},
                    #{documentType},
                    #{documentNumber},
                    #{documentText},
                    #{documentFilename},
                    #{projectNumber},
                    #{projectText}
                )
            ON DUPLICATE KEY UPDATE
                file_storage_key = VALUES(file_storage_key),
                file_size = VALUES(file_size),
                document_text = VALUES(document_text),
                document_file_name = VALUES(document_file_name),
                project_text = VALUES(project_text)
            RETURNING id;
        """
    )
    fun createDocument(
        @Param("fileStorageKey") fileStorageKey: String,
        @Param("fileSize") fileSize: Long,
        @Param("documentType") documentType: OffaDocumentType,
        @Param("documentNumber") documentNumber: String,
        @Param("documentText") documentText: String?,
        @Param("documentFilename") documentFilename: String,
        @Param("projectNumber") projectNumber: String,
        @Param("projectText") projectText: String?,
    ): Long

    @Select(
        """
            INSERT INTO issue_documents
                (
                    issue_id,
                    document_id
                ) 
            VALUES
                (
                    #{issueId},
                    #{documentId}
                )
        """
    )
    fun mapDocumentToIssue(
        @Param("issueId") issueId: Long,
        @Param("documentId") documentId: Long,
    )

    @Select(
        """
            SELECT id
            FROM issues
            WHERE external_id IN (#{worksheetNumbers})
        """
    )
    @Lang(MyBatisInClauseExtensionDriver::class)
    fun findIssueIdsForWorksheetNumbers(
        @Param("worksheetNumbers") worksheetNumbers: List<String>
    ): List<Long>

    @Delete("""
       DELETE 
       FROM issue_documents
       WHERE document_id = #{documentId}
    """)
    fun deleteIssueDocumentsByDocumentId(documentId: Long)
}
