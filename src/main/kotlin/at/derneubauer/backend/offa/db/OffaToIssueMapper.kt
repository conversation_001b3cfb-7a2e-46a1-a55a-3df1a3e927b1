package at.derneubauer.backend.offa.db

import org.apache.ibatis.annotations.*
import java.io.Serializable

@Mapper
interface OffaToIssueMapper {

    @Results(id = "issueResult")
    @ConstructorArgs(
        Arg(column = "issueCount", javaType = Int::class)
    )
    @Select("""
        SELECT COUNT(*) as issueCount FROM issues WHERE external_id = #{externalId};
    """)
    fun isExternalIdKnown(@Param("externalId") externalId: String): OffaIssueCountDo

    @Update("""
        UPDATE issues
        SET
            contact_person = #{contactPerson},
            address = #{address},
            suggested_date = #{suggestedDate},
            description = #{description}
        WHERE external_id = #{externalId}
    """)
    fun update(
        @Param("externalId") externalId: String,
        @Param("contactPerson") contactPerson: String?,
        @Param("address") address: String,
        @Param("suggestedDate") suggestedDate: String,
        @Param("description") description: String?
    )

    @Insert("""
        INSERT INTO issues
        SET
            contact_person = #{contactPerson},
            address = #{address},
            suggested_date = #{suggestedDate},
            description = #{description},
            external_id = #{externalId}
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    fun insert(insertDto: OffaIssueInsertDto): Long

    @Insert("""
        INSERT INTO issue_status (issue_id, status) values (#{issueId}, #{status})
    """)
    fun insertNewStatusForIssue(@Param("issueId") issueId: Long, @Param("status") to: String)

    @Update("""
        UPDATE issues
        SET
            smartbricks_project_id = #{smartbricksProjectId}
        WHERE id = #{issueId}
    """)
    fun linkWithSmartbricksProject(@Param("issueId") issueId: Long, @Param("smartbricksProjectId") smartbricksProjectId: Long)
}

data class OffaIssueCountDo(
    var issueCount: Int
) : Serializable

data class OffaIssueInsertDto(
    var externalId: String,
    var contactPerson: String?,
    var address: String,
    var suggestedDate: String,
    var description: String?,
    var id: Long? = null
) : Serializable
