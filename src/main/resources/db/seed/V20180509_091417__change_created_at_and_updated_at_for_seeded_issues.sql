UPDATE issues SET created_at = "2018-05-01 18:30:59.001" WHERE external_id = 'AS170223';
UPDATE issues SET created_at = "2018-05-01 18:30:59.002" WHERE external_id = 'AS170224';
UPDATE issues SET created_at = "2018-05-01 18:30:59.003" WHERE external_id = 'AS170225';
UPDATE issues SET created_at = "2018-05-01 18:30:59.004" WHERE external_id = 'AS170226';
UPDATE issues SET created_at = "2018-05-01 18:30:59.005" WHERE external_id = 'AS170227';
UPDATE issues SET created_at = "2018-05-01 18:30:59.006" WHERE external_id = 'AS170228';
UPDATE issues SET created_at = "2018-05-01 18:30:59.007" WHERE external_id = 'AS170229';
UPDATE issues SET created_at = "2018-05-01 18:30:59.008" WHERE external_id = 'AS170230';
