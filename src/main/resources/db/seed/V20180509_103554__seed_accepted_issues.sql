INSERT INTO accepted_issues
    (
        technician_id,
        issue_id,
        accepted,
        created_at
    )
    VALUES


    (
        (SELECT id FROM users WHERE role = 'TECHNICIAN' LIMIT 1),
        (SELECT id FROM issues WHERE external_id = 'AS170223' LIMIT 1),
        true,
        "2018-05-01 18:31:30.015"
    ),
    (
        (SELECT id FROM users WHERE role = 'TECHNICIAN' LIMIT 1),
        (SELECT id FROM issues WHERE external_id = 'AS170223' LIMIT 1),
        false,
        "2018-05-01 18:32:30.015"
    ),
    (
        (SELECT id FROM users WHERE role = 'TECHNICIAN' LIMIT 1),
        (SELECT id FROM issues WHERE external_id = 'AS170223' LIMIT 1),
        true,
        "2018-05-01 18:33:30.015"
    ),


    (
        (SELECT id FROM users WHERE role = 'TECHNICIAN' LIMIT 1),
        (SELECT id FROM issues WHERE external_id = 'AS170224' LIMIT 1),
        true,
        "2018-05-01 18:31:30.015"
    ),
    (
        (SELECT id FROM users WHERE role = 'TECHNICIAN' LIMIT 1),
        (SELECT id FROM issues WHERE external_id = 'AS170224' LIMIT 1),
        false,
        "2018-05-01 18:32:30.015"
    ),


    (
        (SELECT id FROM users WHERE role = 'TECHNICIAN' LIMIT 1),
        (SELECT id FROM issues WHERE external_id = 'AS170228' LIMIT 1),
        true,
        "2018-05-01 18:35:30.027"
    ),


    (
        (SELECT id FROM users WHERE role = 'TECHNICIAN' LIMIT 1),
        (SELECT id FROM issues WHERE external_id = 'AS170229' LIMIT 1),
        false,
        "2018-05-01 18:34:30.039"
    )
;
