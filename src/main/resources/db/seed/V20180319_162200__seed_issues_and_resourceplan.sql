INSERT INTO issues
	(
	    external_id,
		contact_person,
		address,
		suggested_date,
		description,
		note,
		status
	)
	VALUES
	(
	    'AS170223',
		'<PERSON>',
		'Oeverseegasse 1, 8010 Graz',
		'02.04.2018 08:00',
		'<PERSON>ari<PERSON> das Badezimmer',
		'Repariere es',
		'PLANNED'
	),
	(
	    'AS170224',
		'<PERSON>',
		'Elisabethiergasse 22, 8020 Graz',
		'02.04.2018 09:00',
		'<PERSON> in der Kü<PERSON> ist kaputt',
		'Dr<PERSON>d',
		'REOPENED'
	),
    (
	    'AS170225',
		'<PERSON>',
		'Gadollaplatz 1 8010 Graz',
		'02.04.2018 10:30',
		'<PERSON><PERSON><PERSON> rinn<PERSON>',
		NULL,
		'ACCEPTED'
	),
    (
        'AS170226',
        '<PERSON>',
        'Herrengasse 15 8010 Graz',
        '02.04.2018 10:30',
        '<PERSON><PERSON><PERSON> Rohr ben<PERSON>ti<PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        'DONE_TECHNICIAN'
    ),
    (
        'AS170227',
        '<PERSON><PERSON>',
        'Hirtengasse 13 8020 Graz',
        '02.04.2018 10:30',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>NE<PERSON>'
    ),
    (
        'AS170228',
        '<PERSON> <PERSON>rot<PERSON>ger',
        '<PERSON><PERSON><PERSON><PERSON> 13 8020 Graz',
        '02.04.2018 10:30',
        'Neu<PERSON> <PERSON>ohr benötigt',
        'Was<PERSON><PERSON>hrbruch',
        '<PERSON>LANNE<PERSON>'
    ),
    (
        '<PERSON>170229',
        '<PERSON> <PERSON><PERSON>',
        '<PERSON><PERSON><PERSON><PERSON> 13 8020 <PERSON>raz',
        '02.04.2018 10:30',
        'Neu<PERSON> <PERSON><PERSON>r benötigt',
        '<PERSON>ser<PERSON>hrbruch',
        'PLANNED'
    ),
    (
	    'AS170230',
		NULL,
		NULL,
		NULL,
		NULL,
		NULL,
		'PLANNED'
	);



INSERT INTO issues_technicians_assignment
	(
		user_id,
		issue_id,
		start_date,
		end_date
	)
	VALUES
	(
		(SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170223' LIMIT 1),
		TIMESTAMP("2018-04-02 04:00:00"),
		TIMESTAMP("2018-04-02 10:00:00")
	),
    (
		(SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170224' LIMIT 1),
		TIMESTAMP("2018-04-02 06:00:00"),
        TIMESTAMP("2018-04-02 10:45:00")
	),
	(
        (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170225' LIMIT 1),
		TIMESTAMP("2018-04-02 09:30:00"),
        TIMESTAMP("2018-04-02 11:00:00")
    ),
	(
        (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170226' LIMIT 1),
		TIMESTAMP("2018-04-02 13:30:00"),
        TIMESTAMP("2018-04-02 15:00:00")
    ),
	(
        (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170226' LIMIT 1),
		TIMESTAMP("2018-04-02 06:30:00"),
        TIMESTAMP("2018-04-02 11:00:00")
    ),
	(
        (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170227' LIMIT 1),
		TIMESTAMP("2018-04-02 08:30:00"),
        TIMESTAMP("2018-04-02 15:30:00")
    ),
	(
        (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170228' LIMIT 1),
		TIMESTAMP("2018-04-02 11:00:00"),
        TIMESTAMP("2018-04-02 14:00:00")
    ),
	(
        (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170229' LIMIT 1),
		TIMESTAMP("2018-04-02 14:30:00"),
        TIMESTAMP("2018-04-02 16:00:00")
    ),
	(
        (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
		(SELECT id FROM issues WHERE external_id = 'AS170230' LIMIT 1),
		TIMESTAMP("2018-03-23 07:00:00"),
        TIMESTAMP("2018-04-04 11:00:00")
    );

