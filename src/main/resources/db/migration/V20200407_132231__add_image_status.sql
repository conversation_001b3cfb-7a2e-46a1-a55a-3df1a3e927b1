CREATE TABLE image_status (
  id                 BIGINT             AUTO_INCREMENT PRIMARY KEY,
  image_id           BIGINT             NOT NULL,
  ref_type           varchar(10)        NOT NULL,
  ref_id             BIGINT             NOT NULL,
  status             varchar(64)        NOT NULL,
  created_at         DATETIME(3)        NOT NULL DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (image_id) REFERENCES images (id)
);

-- Create an entry of image_status for every existing image
INSERT INTO image_status (image_id, ref_id, ref_type, status)
SELECT images.id, images.ref_id, images.ref_type, 'ADDED'
FROM images;

-- remove columns from images that are now in image_status
ALTER TABLE images
  DROP COLUMN ref_id,
  DROP COLUMN ref_type;

-- create view that returns the image with the latest status
CREATE OR REPLACE VIEW v_images_latest_status AS
    SELECT
            img.id,
            imgStat.ref_id,
            imgStat.ref_type,
            img.image,
            img.thumbnail,
            img.width,
            img.height,
            imgStat.status,
            img.uploaded_at,
            imgStat.created_at as updated_at
        FROM images img
        JOIN (SELECT *
			  FROM image_status is1
			  WHERE is1.created_at = (SELECT MAX(created_at)
			  						  FROM image_status is2
			  						  WHERE is1.image_id = is2.image_id)
			  						) imgStat
            ON (img.id = imgStat.image_id);

-- create view that returns only images that have the status 'added'
CREATE OR REPLACE VIEW v_images_added AS
    SELECT *
        FROM v_images_latest_status
        WHERE
            status = 'ADDED';
