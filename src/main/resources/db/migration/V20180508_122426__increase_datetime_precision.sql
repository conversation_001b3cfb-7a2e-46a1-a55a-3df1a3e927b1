ALTER TABLE issues
    M<PERSON><PERSON><PERSON> COLUMN updated_at        DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    MODIFY COLUMN created_at        DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE images
    MODIFY COLUMN uploaded_at       DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE users
    MODIFY COLUMN created_at        DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE issues_technicians_assignment
    MODIFY COLUMN updated_at        DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    MODIFY COLUMN created_at        DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP;

ALTER TABLE technicians_assistants_assignment
    MODIFY COLUMN updated_at        DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON><PERSON><PERSON><PERSON> COLUMN created_at        DATETIME(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP;
