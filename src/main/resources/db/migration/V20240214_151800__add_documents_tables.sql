CREATE TABLE documents
(
    id                 BIGINT AUTO_INCREMENT PRIMARY KEY,

    file_storage_key   VARCHAR(255)  NOT NULL UNIQUE,
    file_size          BIGINT        NOT NULL,

    document_type      VARCHAR(32)   NOT NULL,
    document_number    VARCHAR(1024) NOT NULL,
    document_text      VARCHAR(1024),
    document_file_name VA<PERSON>HAR(1024) NOT NULL,

    project_number     VARCHAR(1024) NOT NULL,
    project_text       VARCHAR(1024),

    created_at         TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE issue_documents
(
    id          BIGINT AUTO_INCREMENT PRIMARY KEY,

    issue_id    BIGINT    NOT NULL,
    document_id BIGINT    NOT NULL,

    created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    UNIQUE (issue_id, document_id),
    FOREIG<PERSON> KEY (issue_id) REFERENCES issues (id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON>EY (document_id) REFERENCES documents (id) ON DELETE CASCADE
);
