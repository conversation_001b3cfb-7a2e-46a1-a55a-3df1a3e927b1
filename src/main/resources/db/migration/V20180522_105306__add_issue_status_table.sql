CREATE TABLE issue_status (
    id                      BIGINT            AUTO_INCREMENT PRIMARY KEY,
    status                  VARCHAR(64)       NOT NULL,
    issue_id                BIGINT            NOT NULL,
    requester_id            BIGINT            NOT NULL,
    created_at              DATETIME(3)       NOT NULL DEFAULT CURRENT_TIMESTAMP,

    FOREIG<PERSON> KEY (requester_id) REFERENCES users (id),
    FOREIG<PERSON> KEY (issue_id) REFERENCES issues (id)
)
CHARACTER SET utf8 COMMENT 'Status of a given issue and the user who moved it into this status';

INSERT INTO issue_status (status, issue_id, created_at, requester_id) SELECT status, id, created_at, (SELECT id FROM users WHERE role IN ('ADMIN', 'SUPERVISOR') ORDER BY id ASC LIMIT 1) from issues;

ALTER TABLE issues DROP COLUMN status;

CREATE OR REPLACE VIEW v_issues_with_status AS
    SELECT i.*, is1.status
    	FROM issues i
        INNER JOIN issue_status is1 ON is1.issue_id = i.id
        LEFT OUTER JOIN issue_status is2
            ON (
				is1.issue_id = is2.issue_id
                AND (
                    is1.created_at < is2.created_at
				    OR (
                        is1.created_at = is2.created_at
                        AND is1.id < is2.id
                    )
                )
            )
        WHERE
            is2.id IS NULL;