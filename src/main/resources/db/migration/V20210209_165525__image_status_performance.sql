CREATE OR REPLACE VIEW v_images_latest_status AS
WITH image_status_grouped as (
SELECT id, image_id, ref_id, ref_type, status, created_at,
       ROW_NUMBER() OVER (PARTITION BY image_id ORDER BY id DESC) rank
FROM image_status
)
SELECT
        img.id,
        imgStat.ref_id,
        imgStat.ref_type,
        img.image,
        img.thumbnail,
        img.width,
        img.height,
        imgStat.status,
        img.uploaded_at,
        imgStat.created_at as updated_at
    FROM images img
    JOIN image_status_grouped as imgStat ON img.id = imgStat.image_id
    WHERE imgStat.rank = 1