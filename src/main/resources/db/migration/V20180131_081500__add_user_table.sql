CREATE TABLE users (
  id                      BIGINT       AUTO_INCREMENT PRIMARY KEY,

  email                   VARCHAR(255) NOT NULL UNIQUE,
  password                CHAR(60) NOT NULL,
  disabled                NUMERIC(1)   NOT NULL DEFAULT 0,
  locale                  VARCHAR(255) NOT NULL,
  secret_id               CHAR(36) NOT NULL DEFAULT UUID(),

  created                 TIMES<PERSON>MP    NOT NULL DEFAULT CURRENT_TIMESTAMP
)
  CHARACTER SET utf8 COMMENT 'Users';

CREATE INDEX users_email_idx
  ON users (email);
