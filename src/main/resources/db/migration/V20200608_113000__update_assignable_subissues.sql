CREATE OR REPLACE VIEW v_assignable_subissues AS
 SELECT
        i.id,
        i.external_id,
        i.contact_person,
        i.address,
        i.suggested_date,
        i.description,
        i.note,
        i.status,
        i.updated_at,
        i.created_at
    FROM v_issues_with_status i
    WHERE i.status = 'NEW'
    AND i.id NOT IN (
      SELECT issue_id
      FROM recurring_issues
    UNION DISTINCT
      SELECT DISTINCT sub_issue_id
      FROM recurring_issues
      WHERE sub_issue_id IS NOT NULL
    )
