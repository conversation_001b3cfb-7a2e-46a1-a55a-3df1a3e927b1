CREATE OR REPLACE VIEW v_issues_with_status AS
WITH issue_status_partitioned as (
SELECT id, issue_id, status, created_at,
       ROW_NUMBER() OVER (PARTITION BY issue_id ORDER BY id DESC) rank
FROM issue_status
)
SELECT i.id,
       i.external_id,
       i.contact_person,
       i.address,
       i.suggested_date,
       i.description,
       i.note,
       ist.status,
       ist.created_at AS in_status_since,
       GREATEST(i.updated_at, ist.created_at) AS updated_at,
       i.created_at
FROM issues i INNER JOIN issue_status_partitioned as ist ON i.id = ist.issue_id
WHERE ist.rank = 1;