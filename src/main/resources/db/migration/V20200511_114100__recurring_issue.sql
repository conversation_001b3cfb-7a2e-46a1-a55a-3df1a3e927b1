CREATE TABLE recurring_issues(
    id                      BIGINT      AUTO_INCREMENT PRIMARY KEY,
    issue_id                BIGINT      NOT NULL,
    sub_issue_id            BIGINT,
    placeholder_date_from   DATETIME    NOT NULL,
    placeholder_date_to     DATETIME    NOT NULL,
    user_id                 BIGINT      NOT NULL,

    FOREIG<PERSON> KEY (issue_id)      REFERENCES issues (id),
    FOREIGN KEY (sub_issue_id)  REFERENCES issues (id),
    FOREIGN KEY (user_id)       REFERENCES users (id)
)
