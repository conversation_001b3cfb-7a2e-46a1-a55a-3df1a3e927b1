CREATE TABLE issues_technicians_assignment (
  id                      BIGINT       AUTO_INCREMENT PRIMARY KEY,

  user_id                 BIGINT       NOT NULL,
  issue_id                BIGINT       NOT NULL,
  start_date              DATETIME     NOT NULL,
  end_date                DATETIME     NOT NULL,

  updated_at              DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at              DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users (id),
  FOREIGN KEY (issue_id) REFERENCES issues (id)
)
  CHARACTER SET utf8 COMMENT 'Issues assigned to technicians';
