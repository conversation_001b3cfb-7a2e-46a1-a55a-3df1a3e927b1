CREATE TABLE absences_assignment (
  id                      BIGINT            AUTO_INCREMENT PRIMARY KEY,

  user_id                 BIGINT            NOT NULL,
  absence_type            VARCHAR(64)       NOT NULL,
  note                    VARCHAR(8192),
  start_date              DATETIME(3)       NOT NULL,
  end_date                DATETIME(3)       NOT NULL,

  updated_at              DATETIME(3)       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at              DATETIME(3)       NOT NULL DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (user_id) REFERENCES users (id)
)
  CHARACTER SET utf8 COMMENT 'Absences assigned to technicians and assistants';
