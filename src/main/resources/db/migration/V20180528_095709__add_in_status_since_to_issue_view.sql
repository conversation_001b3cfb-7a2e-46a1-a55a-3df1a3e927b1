CREATE OR REPLACE VIEW v_issues_with_status AS
    SELECT
            i.id,
            i.external_id,
            i.contact_person,
            i.address,
            i.suggested_date,
            i.description,
            i.note,
            is1.status,
            is1.created_at AS in_status_since,
            GREATEST(i.updated_at, is1.created_at) AS updated_at,
            i.created_at
    	FROM issues i
        INNER JOIN issue_status is1 ON is1.issue_id = i.id
        LEFT OUTER JOIN issue_status is2
            ON (
				is1.issue_id = is2.issue_id
                AND (
                    is1.created_at < is2.created_at
				    OR (
                        is1.created_at = is2.created_at
                        AND is1.id < is2.id
                    )
                )
            )
        WHERE
            is2.id IS NULL;
