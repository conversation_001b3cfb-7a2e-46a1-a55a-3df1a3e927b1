CREATE TABLE accepted_issues (
  id                      BIGINT       AUTO_INCREMENT PRIMARY KEY,

  technician_id           BIGINT       NOT NULL,
  issue_id                BIGINT       NOT NULL,

  accepted                BOOLEAN      NOT NULL,

  created_at              DATETIME(3)  NOT NULL DEFAULT CURRENT_TIMESTAMP,

  FOREIG<PERSON> KEY (technician_id) REFERENCES users (id),
  FOREIG<PERSON> KEY (issue_id) REFERENCES issues (id)
)
  CHARACTER SET utf8 COMMENT 'Issues accepted or put back by a technician';


CREATE OR REPLACE VIEW v_accepted_issues AS
    SELECT
            ai1.id,
            ai1.technician_id,
            ai1.issue_id,
            ai1.accepted,
            ai1.created_at
        FROM accepted_issues ai1
        LEFT OUTER JOIN accepted_issues ai2
            ON (
                ai1.technician_id = ai2.technician_id
                AND ai1.issue_id = ai2.issue_id
                AND ai1.created_at < ai2.created_at
                OR (
                    ai1.created_at = ai2.created_at
                    AND ai1.id < ai2.id
                )
            )
        WHERE
            ai2.id IS NULL;
