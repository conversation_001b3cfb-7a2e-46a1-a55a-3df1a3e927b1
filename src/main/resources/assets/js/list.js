function List($element, $pager, config, listName) {
    this.list = {
        $element: null,

        $pager: null,

        config: null,

        dataSource: null,

        listName: null,

        __constructor: function($element, $pager, config, listName) {
            kendo.culture("de-AT");
            this.$element = $element
            this.$pager = $pager
            this.config = config
            this.dataSource = this.setupDataSource()
            this.listName = listName
            this.setupListView()
            this.setupPagination()
            this.setupSearch()
        },

        setupDataSource: function() {
            if (typeof(this.config.endpoint) == 'undefined') {
                return new kendo.data.DataSource({
                    data: this.config.data
                });
            } else {
                return new kendo.data.DataSource({
                    serverPaging: false,
                    transport: {
                        read: {
                            url: this.config.endpoint.READ,
                            dataType: "json",
                            data: {

                            }
                        }
                    },
                    pageSize: 10
                });
            }
        },

        setupListView: function() {
            this.$element.kendoListView({
                selectable: true,
                dataSource: this.dataSource,
                template: kendo.template($(this.config.listTemplate).html()),
                dataBound: this.config.dataBound,
                title: this.config.listtitle
            });

            this.$element.kendoDraggable({
                filter: this.config.listFilter,
                cursorOffset: { top: -10, left: 0 },
                dragstart: function (e) {
                    window.dropDataItem = this.$element.data("kendoListView").dataItem(e.currentTarget)
                    window.dropDataItem.listName = this.listName
                }.bind(this),
                hint: this.config.hint
            });
        },

        setupPagination: function() {
            if (this.$pager.attr("id") === "issue-pager" || this.$pager.attr("id") === "assistant-pager") {
                 this.$pager.kendoPager({
                     dataSource: this.dataSource,
                     info: false,
                     numeric: false,
                 });
             } else {
                 this.$pager.kendoPager({
                     dataSource: this.dataSource,
                     info: false
                 });
             }
        },

        setupSearch: function() {
            $(this.config.searchId).keyup(function(e) {
                 this.dataSource.filter({
                     logic: 'or',
                     filters: this.config.filters(e.target.value)
                 });
            }.bind(this));
        }

    }

    this.list.__constructor($element, $pager, config, listName)
}