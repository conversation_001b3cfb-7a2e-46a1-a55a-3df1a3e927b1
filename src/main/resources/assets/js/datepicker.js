$(document).ready(function() {
    if ($(".datePicker").length > 0) {
        $(".datePicker").kendoDatePicker({
            culture: "de-AT",
            min: new Date(1950, 0, 1, 7, 0, 0),
            max: new Date(2049, 11, 31, 17, 0, 0),
            disableDates: ["sa", "su"],
            format: "dd.MM.yyyy",
            interval: 15
        });
    }

    if ($(".datePicker").length > 0) {
        $(".timePicker").kendoTimePicker({
            min: new Date(1950, 0, 1, 7, 0, 0),
            max: new Date(2049, 11, 31, 17, 0, 0),
            format: "HH:mm",
            parseFormats: ["HH:mm"],
            interval: 15
        });
    }
});
