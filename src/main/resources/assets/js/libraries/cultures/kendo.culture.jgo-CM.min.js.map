{"version": 3, "sources": ["cultures/kendo.culture.jgo-CM.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,4BACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,cAAc,YAAY,UAAU,WAAW,UACzEC,WAAY,SAAS,SAAS,cAAc,YAAY,UAAU,WAAW,UAC7EC,YAAa,SAAS,SAAS,cAAc,YAAY,UAAU,WAAW,WAElFC,QACIH,OAAQ,cAAc,cAAc,eAAe,kBAAkB,cAAc,oBAAoB,eAAe,kBAAkB,oBAAoB,eAAe,kBAAkB,kBAC7LC,WAAY,cAAc,cAAc,eAAe,kBAAkB,cAAc,oBAAoB,eAAe,kBAAkB,oBAAoB,eAAe,kBAAkB,mBAErMG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,8BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.jgo-CM.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"jgo-CM\"] = {\n        name: \"jgo-CM\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Central African CFA Franc\",\n                abbr: \"XAF\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"FCFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"<PERSON>ɔ́ndi\",\"<PERSON>ɔ́ndi\",\"Ápta Mɔ́ndi\",\"Wɛ́nɛsɛdɛ\",\"Tɔ́sɛdɛ\",\"<PERSON>ɛl<PERSON><PERSON>ɛdɛ\",\"<PERSON><PERSON>id<PERSON>\"],\n                    namesAbbr: [\"<PERSON>ɔ́ndi\",\"<PERSON>ɔ́<PERSON>\",\"Ápta Mɔ́ndi\",\"Wɛ́nɛsɛdɛ\",\"Tɔ́sɛdɛ\",\"Fɛlâyɛdɛ\",\"Sásidɛ\"],\n                    namesShort: [\"Sɔ́ndi\",\"Mɔ́ndi\",\"Ápta Mɔ́ndi\",\"Wɛ́nɛsɛdɛ\",\"Tɔ́sɛdɛ\",\"Fɛlâyɛdɛ\",\"Sásidɛ\"]\n                },\n                months: {\n                    names: [\"Nduŋmbi Saŋ\",\"Pɛsaŋ Pɛ́pá\",\"Pɛsaŋ Pɛ́tát\",\"Pɛsaŋ Pɛ́nɛ́kwa\",\"Pɛsaŋ Pataa\",\"Pɛsaŋ Pɛ́nɛ́ntúkú\",\"Pɛsaŋ Saambá\",\"Pɛsaŋ Pɛ́nɛ́fɔm\",\"Pɛsaŋ Pɛ́nɛ́pfúꞋú\",\"Pɛsaŋ Nɛgɛ́m\",\"Pɛsaŋ Ntsɔ̌pmɔ́\",\"Pɛsaŋ Ntsɔ̌ppá\"],\n                    namesAbbr: [\"Nduŋmbi Saŋ\",\"Pɛsaŋ Pɛ́pá\",\"Pɛsaŋ Pɛ́tát\",\"Pɛsaŋ Pɛ́nɛ́kwa\",\"Pɛsaŋ Pataa\",\"Pɛsaŋ Pɛ́nɛ́ntúkú\",\"Pɛsaŋ Saambá\",\"Pɛsaŋ Pɛ́nɛ́fɔm\",\"Pɛsaŋ Pɛ́nɛ́pfúꞋú\",\"Pɛsaŋ Nɛgɛ́m\",\"Pɛsaŋ Ntsɔ̌pmɔ́\",\"Pɛsaŋ Ntsɔ̌ppá\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"dddd, yyyy MMMM dd\",\n                    F: \"dddd, yyyy MMMM dd HH:mm:ss\",\n                    g: \"yyyy-MM-dd HH:mm\",\n                    G: \"yyyy-MM-dd HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}