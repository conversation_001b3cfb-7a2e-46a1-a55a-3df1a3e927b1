{"version": 3, "sources": ["cultures/kendo.culture.tzm-Latn-DZ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,gBACXC,KAAM,cACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,iBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAa,WAAW,SAAS,UAAU,SAAS,UAAU,SACtEC,WAAY,OAAY,MAAM,MAAM,MAAM,MAAM,MAAM,OACtDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,WAAW,QAAQ,UAAU,SAAS,OAAO,QAAQ,QAAQ,QAAQ,WAAW,QAAQ,WAAW,YAC3GC,WAAY,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAEnFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,gBACHC,EAAG,wBACHC,EAAG,kBACHC,EAAG,qBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.tzm-Latn-DZ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"tzm-Latn-DZ\"] = {\n        name: \"tzm-Latn-DZ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Algerian Dinar\",\n                abbr: \"DZD\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"DA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"lh\\u0027ed\",\"letnayen\",\"ttlata\",\"la<PERSON><PERSON><PERSON><PERSON>\",\"lexmis\",\"ldje<PERSON><PERSON><PERSON>\",\"ssebt\"],\n                    namesAbbr: [\"lh\\u0027d\",\"let\",\"ttl\",\"lar\",\"lex\",\"ldj\",\"sse\"],\n                    namesShort: [\"lh\",\"lt\",\"tt\",\"la\",\"lx\",\"ld\",\"ss\"]\n                },\n                months: {\n                    names: [\"Yennayer\",\"Furar\",\"Meghres\",\"Yebrir\",\"Magu\",\"Yunyu\",\"Yulyu\",\"Ghuct\",\"Cutenber\",\"Tuber\",\"Nunember\",\"Dujanbir\"],\n                    namesAbbr: [\"Yen\",\"Fur\",\"Megh\",\"Yeb\",\"May\",\"Yun\",\"Yul\",\"Ghu\",\"Cut\",\"Tub\",\"Nun\",\"Duj\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"dd MMMM, yyyy\",\n                    F: \"dd MMMM, yyyy H:mm:ss\",\n                    g: \"dd-MM-yyyy H:mm\",\n                    G: \"dd-MM-yyyy H:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}