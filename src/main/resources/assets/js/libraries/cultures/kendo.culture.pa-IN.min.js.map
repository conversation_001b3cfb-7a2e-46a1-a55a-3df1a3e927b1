{"version": 3, "sources": ["cultures/kendo.culture.pa-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,SAAS,UAAU,UAAU,SAAS,WAAW,aACjEC,WAAY,MAAM,OAAO,QAAQ,QAAQ,OAAO,QAAQ,WACxDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,MAE1CC,QACIH,OAAQ,QAAQ,QAAQ,OAAO,SAAS,KAAK,MAAM,QAAQ,OAAO,QAAQ,SAAS,QAAQ,SAC3FC,WAAY,QAAQ,QAAQ,OAAO,SAAS,KAAK,MAAM,QAAQ,OAAO,QAAQ,SAAS,QAAQ,UAEnGG,IAAK,OAAO,OAAO,QACnBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,WACHC,EAAG,oBACHC,EAAG,gCACHC,EAAG,oBACHC,EAAG,uBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.pa-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"pa-IN\"] = {\n        name: \"pa-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"$ -n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ਐਤਵਾਰ\",\"ਸੋਮਵਾਰ\",\"ਮੰਗਲਵਾਰ\",\"ਬੁੱਧਵਾਰ\",\"ਵੀਰਵਾਰ\",\"ਸ਼ੁੱਕਰਵਾਰ\",\"ਸ਼ਨਿੱਚਰਵਾਰ\"],\n                    namesAbbr: [\"ਐਤ.\",\"ਸੋਮ.\",\"ਮੰਗਲ.\",\"ਬੁੱਧ.\",\"ਵੀਰ.\",\"ਸ਼ੁਕਰ.\",\"ਸ਼ਨਿੱਚਰ.\"],\n                    namesShort: [\"ਐ\",\"ਸ\",\"ਮ\",\"ਬ\",\"ਵ\",\"ਸ਼ੁ\",\"ਸ਼\"]\n                },\n                months: {\n                    names: [\"ਜਨਵਰੀ\",\"ਫ਼ਰਵਰੀ\",\"ਮਾਰਚ\",\"ਅਪ੍ਰੈਲ\",\"ਮਈ\",\"ਜੂਨ\",\"ਜੁਲਾਈ\",\"ਅਗਸਤ\",\"ਸਤੰਬਰ\",\"ਅਕਤੂਬਰ\",\"ਨਵੰਬਰ\",\"ਦਸੰਬਰ\"],\n                    namesAbbr: [\"ਜਨਵਰੀ\",\"ਫ਼ਰਵਰੀ\",\"ਮਾਰਚ\",\"ਅਪ੍ਰੈਲ\",\"ਮਈ\",\"ਜੂਨ\",\"ਜੁਲਾਈ\",\"ਅਗਸਤ\",\"ਸਤੰਬਰ\",\"ਅਕਤੂਬਰ\",\"ਨਵੰਬਰ\",\"ਦਸੰਬਰ\"]\n                },\n                AM: [\"ਸਵੇਰ\",\"ਸਵੇਰ\",\"ਸਵੇਰ\"],\n                PM: [\"ਸ਼ਾਮ\",\"ਸ਼ਾਮ\",\"ਸ਼ਾਮ\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy dddd\",\n                    F: \"dd MMMM yyyy dddd tt hh:mm:ss\",\n                    g: \"dd-MM-yy tt hh:mm\",\n                    G: \"dd-MM-yy tt hh:mm:ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"tt hh:mm\",\n                    T: \"tt hh:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}