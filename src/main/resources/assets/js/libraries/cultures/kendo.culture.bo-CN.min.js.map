{"version": 3, "sources": ["cultures/kendo.culture.bo-CN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,YAAY,eAAe,aAAa,cAAc,aAAa,eACvFC,WAAY,QAAQ,QAAQ,WAAW,SAAS,UAAU,SAAS,WACnEC,YAAa,MAAM,MAAM,OAAO,MAAM,OAAO,OAAO,UAExDC,QACIH,OAAQ,iBAAiB,kBAAkB,kBAAkB,iBAAiB,gBAAgB,kBAAkB,kBAAkB,mBAAmB,iBAAiB,iBAAiB,sBAAsB,uBAC7MC,WAAY,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,WAE1GG,IAAK,SAAS,SAAS,UACvBC,IAAK,UAAU,UAAU,WACzBC,UACIC,EAAG,WACHC,EAAG,yBACHC,EAAG,kCACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,YACHC,EAAG,YACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,mBACHC,EAAG,oBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.bo-CN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"bo-CN\"] = {\n        name: \"bo-CN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Chinese Yuan\",\n                abbr: \"CNY\",\n                pattern: [\"$-n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,0],\n                symbol: \"¥\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"གཟའ་ཉི་མ།\",\"གཟའ་ཟླ་བ།\",\"གཟའ་མིག་དམར།\",\"གཟའ་ལྷག་པ།\",\"གཟའ་ཕུར་བུ།\",\"གཟའ་པ་སངས།\",\"གཟའ་སྤེན་པ།\"],\n                    namesAbbr: [\"ཉི་མ།\",\"ཟླ་བ།\",\"མིག་དམར།\",\"ལྷག་པ།\",\"ཕུར་བུ།\",\"པ་སངས།\",\"སྤེན་པ།\"],\n                    namesShort: [\"ཉི།\",\"ཟླ།\",\"དམར།\",\"ལྷག\",\"ཕུར།\",\"སངས།\",\"སྤེན།\"]\n                },\n                months: {\n                    names: [\"སྤྱི་ཟླ་དང་པོ།\",\"སྤྱི་ཟླ་གཉིས་པ།\",\"སྤྱི་ཟླ་གསུམ་པ།\",\"སྤྱི་ཟླ་བཞི་པ།\",\"སྤྱི་ཟླ་ལྔ་པ།\",\"སྤྱི་ཟླ་དྲུག་པ།\",\"སྤྱི་ཟླ་བདུན་པ།\",\"སྤྱི་ཟླ་བརྒྱད་པ།\",\"སྤྱི་ཟླ་དགུ་པ།\",\"སྤྱི་ཟླ་བཅུ་པ།\",\"སྤྱི་ཟླ་བཅུ་གཅིག་པ།\",\"སྤྱི་ཟླ་བཅུ་གཉིས་པ།\"],\n                    namesAbbr: [\"ཟླ་ ༡\",\"ཟླ་ ༢\",\"ཟླ་ ༣\",\"ཟླ་ ༤\",\"ཟླ་ ༥\",\"ཟླ་ ༦\",\"ཟླ་ ༧\",\"ཟླ་ ༨\",\"ཟླ་ ༩\",\"ཟླ་ ༡༠\",\"ཟླ་ ༡༡\",\"ཟླ་ ༡༢\"]\n                },\n                AM: [\"སྔ་དྲོ\",\"སྔ་དྲོ\",\"སྔ་དྲོ\"],\n                PM: [\"ཕྱི་དྲོ\",\"ཕྱི་དྲོ\",\"ཕྱི་དྲོ\"],\n                patterns: {\n                    d: \"yyyy/M/d\",\n                    D: \"yyyy'ལོའི་ཟླ' M'ཚེས' d\",\n                    F: \"yyyy'ལོའི་ཟླ' M'ཚེས' d HH:mm:ss\",\n                    g: \"yyyy/M/d HH:mm\",\n                    G: \"yyyy/M/d HH:mm:ss\",\n                    m: \"ཟླ་Mཚེས་d\",\n                    M: \"ཟླ་Mཚེས་d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy'ལོའི་ཟླ་' M\",\n                    Y: \"yyyy'ལོའི་ཟླ་' M\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}