{"version": 3, "sources": ["cultures/kendo.culture.kn-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,MACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,SAAS,UAAU,SAAS,UAAU,WAAW,UACnEC,WAAY,QAAQ,OAAO,QAAQ,OAAO,QAAQ,SAAS,QAC3DC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,QAAQ,WAAW,SAAS,UAAU,KAAK,OAAO,OAAO,SAAS,YAAY,WAAW,UAAU,YAC3GC,WAAY,QAAQ,WAAW,SAAS,UAAU,KAAK,OAAO,OAAO,SAAS,YAAY,WAAW,UAAU,aAEnHG,IAAK,YAAY,YAAY,aAC7BC,IAAK,UAAU,UAAU,WACzBC,UACIC,EAAG,WACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.kn-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"kn-IN\"] = {\n        name: \"kn-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"$ -n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ಭಾನುವಾರ\",\"ಸೋಮವಾರ\",\"ಮಂಗಳವಾರ\",\"ಬುಧವಾರ\",\"ಗುರುವಾರ\",\"ಶುಕ್ರವಾರ\",\"ಶನಿವಾರ\"],\n                    namesAbbr: [\"ಭಾನು.\",\"ಸೋಮ.\",\"ಮಂಗಳ.\",\"ಬುಧ.\",\"ಗುರು.\",\"ಶುಕ್ರ.\",\"ಶನಿ.\"],\n                    namesShort: [\"ರ\",\"ಸ\",\"ಮ\",\"ಬ\",\"ಗ\",\"ಶ\",\"ಶ\"]\n                },\n                months: {\n                    names: [\"ಜನವರಿ\",\"ಫೆಬ್ರವರಿ\",\"ಮಾರ್ಚ್\",\"ಏಪ್ರೀಲ್\",\"ಮೇ\",\"ಜೂನ್\",\"ಜುಲೈ\",\"ಆಗಸ್ಟ್\",\"ಸೆಪ್ಟಂಬರ್\",\"ಅಕ್ಟೋಬರ್\",\"ನವೆಂಬರ್\",\"ಡಿಸೆಂಬರ್\"],\n                    namesAbbr: [\"ಜನವರಿ\",\"ಫೆಬ್ರವರಿ\",\"ಮಾರ್ಚ್\",\"ಎಪ್ರಿಲ್\",\"ಮೇ\",\"ಜೂನ್\",\"ಜುಲೈ\",\"ಆಗಸ್ಟ್\",\"ಸೆಪ್ಟಂಬರ್\",\"ಅಕ್ಟೋಬರ್\",\"ನವೆಂಬರ್\",\"ಡಿಸೆಂಬರ್\"]\n                },\n                AM: [\"ಪೂರ್ವಾಹ್ನ\",\"ಪೂರ್ವಾಹ್ನ\",\"ಪೂರ್ವಾಹ್ನ\"],\n                PM: [\"ಅಪರಾಹ್ನ\",\"ಅಪರಾಹ್ನ\",\"ಅಪರಾಹ್ನ\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd-MM-yy HH:mm\",\n                    G: \"dd-MM-yy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}