{"version": 3, "sources": ["cultures/kendo.culture.kkj.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,QAAQ,QAAQ,YAAY,OAAO,YAAY,cAC/DC,WAAY,QAAQ,QAAQ,QAAQ,YAAY,OAAO,YAAY,cACnEC,YAAa,QAAQ,QAAQ,QAAQ,YAAY,OAAO,YAAY,eAExEC,QACIH,OAAQ,QAAQ,QAAQ,iBAAiB,cAAc,eAAe,kBAAkB,SAAS,KAAK,QAAQ,SAAS,KAAK,WAC5HC,WAAY,QAAQ,QAAQ,iBAAiB,cAAc,eAAe,kBAAkB,SAAS,KAAK,QAAQ,SAAS,KAAK,YAEpIG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,6BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.kkj.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"kkj\"] = {\n        name: \"kkj\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"FCFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"sɔndi\",\"lundi\",\"mardi\",\"mɛrkɛrɛdi\",\"yedi\",\"vaŋdɛrɛdi\",\"mɔnɔ sɔndi\"],\n                    namesAbbr: [\"sɔndi\",\"lundi\",\"mardi\",\"mɛrkɛrɛdi\",\"yedi\",\"vaŋdɛrɛdi\",\"mɔnɔ sɔndi\"],\n                    namesShort: [\"sɔndi\",\"lundi\",\"mardi\",\"mɛrkɛrɛdi\",\"yedi\",\"vaŋdɛrɛdi\",\"mɔnɔ sɔndi\"]\n                },\n                months: {\n                    names: [\"pamba\",\"wanja\",\"mbiyɔ mɛndoŋgɔ\",\"Nyɔlɔmbɔŋgɔ\",\"Mɔnɔ ŋgbanja\",\"Nyaŋgwɛ ŋgbanja\",\"kuŋgwɛ\",\"fɛ\",\"njapi\",\"nyukul\",\"11\",\"ɓulɓusɛ\"],\n                    namesAbbr: [\"pamba\",\"wanja\",\"mbiyɔ mɛndoŋgɔ\",\"Nyɔlɔmbɔŋgɔ\",\"Mɔnɔ ŋgbanja\",\"Nyaŋgwɛ ŋgbanja\",\"kuŋgwɛ\",\"fɛ\",\"njapi\",\"nyukul\",\"11\",\"ɓulɓusɛ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM yyyy\",\n                    D: \"dddd dd MMMM yyyy\",\n                    F: \"dddd dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd/MM yyyy HH:mm\",\n                    G: \"dd/MM yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}