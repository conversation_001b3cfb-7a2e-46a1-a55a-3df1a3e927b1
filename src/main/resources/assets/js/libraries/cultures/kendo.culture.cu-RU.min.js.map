{"version": 3, "sources": ["cultures/kendo.culture.cu-RU.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,gBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,gBAAgB,YAAY,SAAS,cAAc,UAAU,YAC/EC,WAAY,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,QACxDC,YAAa,QAAQ,OAAO,QAAQ,OAAO,OAAO,OAAO,SAE7DC,QACIH,OAAQ,cAAc,aAAa,SAAS,aAAa,QAAQ,UAAU,UAAU,YAAY,cAAc,aAAa,YAAY,cACxIC,WAAY,QAAQ,OAAO,OAAO,QAAQ,MAAM,QAAQ,QAAQ,SAAS,OAAO,OAAO,OAAO,SAElGG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,0BACHC,EAAG,mCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.cu-RU.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"cu-RU\"] = {\n        name: \"cu-RU\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Russian Ruble\",\n                abbr: \"RUB\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"₽\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"недѣ́лѧ\",\"понедѣ́льникъ\",\"вто́рникъ\",\"среда̀\",\"четверто́къ\",\"пѧто́къ\",\"сꙋббѡ́та\"],\n                    namesAbbr: [\"ндⷧ҇ѧ\",\"пнⷣе\",\"втоⷬ҇\",\"срⷣе\",\"чеⷦ҇\",\"пѧⷦ҇\",\"сꙋⷠ҇\"],\n                    namesShort: [\"ндⷧ҇ѧ\",\"пнⷣе\",\"втоⷬ҇\",\"срⷣе\",\"чеⷦ҇\",\"пѧⷦ҇\",\"сꙋⷠ҇\"]\n                },\n                months: {\n                    names: [\"і҆аннꙋа́рїй\",\"феврꙋа́рїй\",\"ма́ртъ\",\"а҆прі́ллїй\",\"ма́їй\",\"і҆ꙋ́нїй\",\"і҆ꙋ́лїй\",\"а҆́ѵгꙋстъ\",\"септе́мврїй\",\"ѻ҆ктѡ́врїй\",\"ное́мврїй\",\"деке́мврїй\"],\n                    namesAbbr: [\"і҆аⷩ҇\",\"феⷡ҇\",\"маⷬ҇\",\"а҆пⷬ҇\",\"маꙵ\",\"і҆ꙋⷩ҇\",\"і҆ꙋⷧ҇\",\"а҆́ѵⷢ҇\",\"сеⷫ҇\",\"ѻ҆кⷮ\",\"ноеⷨ\",\"деⷦ҇\"]\n                },\n                AM: [\"ДП\",\"дп\",\"ДП\"],\n                PM: [\"ПП\",\"пп\",\"ПП\"],\n                patterns: {\n                    d: \"yyyy.MM.dd\",\n                    D: \"dddd, d MMMM 'л'. yyyy.\",\n                    F: \"dddd, d MMMM 'л'. yyyy. HH:mm:ss\",\n                    g: \"yyyy.MM.dd HH:mm\",\n                    G: \"yyyy.MM.dd HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}