{"version": 3, "sources": ["cultures/kendo.culture.kl.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,MACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,iBAAiB,gBAAgB,kBAAkB,iBAAiB,kBAAkB,kBACvGC,WAAY,OAAO,MAAM,QAAQ,QAAQ,OAAO,QAAQ,QACxDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,WAAW,YAAY,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,UAAU,aAAa,WAAW,YAAY,aACvHC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,8BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,eACHC,EAAG,eACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.kl.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"kl\"] = {\n        name: \"kl\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"$ -n\",\"$n\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3,0],\n                symbol: \"kr.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"sapaat\",\"ataasinngorneq\",\"marlunngorneq\",\"pingasunngorneq\",\"sisamanngorneq\",\"tallimanngorneq\",\"arfininngorneq\"],\n                    namesAbbr: [\"sap.\",\"at.\",\"marl.\",\"ping.\",\"sis.\",\"tall.\",\"arf.\"],\n                    namesShort: [\"sa\",\"at\",\"ma\",\"pi\",\"si\",\"ta\",\"ar\"]\n                },\n                months: {\n                    names: [\"januaari\",\"februaari\",\"marsi\",\"apriili\",\"maaji\",\"juuni\",\"juuli\",\"aggusti\",\"septembari\",\"oktobari\",\"novembari\",\"decembari\"],\n                    namesAbbr: [\"jan\",\"feb\",\"mar\",\"apr\",\"mai\",\"jun\",\"jul\",\"aug\",\"sep\",\"okt\",\"nov\",\"dec\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"MMMM d'.-at, 'yyyy\",\n                    F: \"MMMM d'.-at, 'yyyy HH:mm:ss\",\n                    g: \"dd-MM-yyyy HH:mm\",\n                    G: \"dd-MM-yyyy HH:mm:ss\",\n                    m: \"MMMM d'.-at'\",\n                    M: \"MMMM d'.-at'\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}