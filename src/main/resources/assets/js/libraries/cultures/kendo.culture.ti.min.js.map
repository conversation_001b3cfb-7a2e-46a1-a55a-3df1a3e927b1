{"version": 3, "sources": ["cultures/kendo.culture.ti.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OAC7CC,WAAY,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OACjDC,YAAa,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,QAEtDC,QACIH,OAAQ,KAAK,OAAO,OAAO,OAAO,OAAO,KAAK,MAAM,MAAM,QAAQ,OAAO,MAAM,QAC/EC,WAAY,KAAK,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,QAEhFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,6BACHC,EAAG,wCACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ti.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ti\"] = {\n        name: \"ti\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Nfk\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ሰንበት\",\"ሰኑይ\",\"ሰሉስ\",\"ረቡዕ\",\"ሓሙስ\",\"ዓርቢ\",\"ቀዳም\"],\n                    namesAbbr: [\"ሰንበት\",\"ሰኑይ\",\"ሰሉስ\",\"ረቡዕ\",\"ሓሙስ\",\"ዓርቢ\",\"ቀዳም\"],\n                    namesShort: [\"ሰንበት\",\"ሰኑይ\",\"ሰሉስ\",\"ረቡዕ\",\"ሓሙስ\",\"ዓርቢ\",\"ቀዳም\"]\n                },\n                months: {\n                    names: [\"ጥሪ\",\"ለካቲት\",\"መጋቢት\",\"ሚያዝያ\",\"ግንቦት\",\"ሰነ\",\"ሓምለ\",\"ነሓሰ\",\"መስከረም\",\"ጥቅምቲ\",\"ሕዳር\",\"ታሕሳስ\"],\n                    namesAbbr: [\"ጥሪ\",\"ለካቲ\",\"መጋቢ\",\"ሚያዝ\",\"ግንቦ\",\"ሰነ\",\"ሓምለ\",\"ነሓሰ\",\"መስከ\",\"ጥቅም\",\"ሕዳር\",\"ታሕሳ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd፡ dd MMMM መዓልቲ yyyy gg\",\n                    F: \"dddd፡ dd MMMM መዓልቲ yyyy gg h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}