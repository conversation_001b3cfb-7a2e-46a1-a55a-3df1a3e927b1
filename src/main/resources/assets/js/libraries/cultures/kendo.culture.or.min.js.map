{"version": 3, "sources": ["cultures/kendo.culture.or.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,UAAU,WAAW,SAAS,UAAU,WAAW,UACpEC,WAAY,OAAO,QAAQ,SAAS,OAAO,QAAQ,SAAS,QAC5DC,YAAa,IAAI,MAAM,IAAI,KAAK,KAAK,KAAK,MAE9CC,QACIH,OAAQ,WAAW,UAAU,UAAU,WAAW,KAAK,QAAQ,QAAQ,QAAQ,aAAa,UAAU,UAAU,YAChHC,WAAY,WAAW,UAAU,UAAU,WAAW,KAAK,QAAQ,QAAQ,QAAQ,aAAa,UAAU,UAAU,aAExHG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.or.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"or\"] = {\n        name: \"or\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"$ -n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ରବିବାର\",\"ସୋମବାର\",\"ମଙ୍ଗଳବାର\",\"ବୁଧବାର\",\"ଗୁରୁବାର\",\"ଶୁକ୍ରବାର\",\"ଶନିବାର\"],\n                    namesAbbr: [\"ରବି.\",\"ସୋମ.\",\"ମଙ୍ଗଳ.\",\"ବୁଧ.\",\"ଗୁରୁ.\",\"ଶୁକ୍ର.\",\"ଶନି.\"],\n                    namesShort: [\"ର\",\"ସୋ\",\"ମ\",\"ବୁ\",\"ଗୁ\",\"ଶୁ\",\"ଶ\"]\n                },\n                months: {\n                    names: [\"ଜାନୁୟାରୀ\",\"ଫେବୃଆରୀ\",\"ମାର୍ଚ୍ଚ\",\"ଏପ୍ରିଲ୍‌\",\"ମେ\",\"ଜୁନ୍‌\",\"ଜୁଲାଇ\",\"ଅଗଷ୍ଟ\",\"ସେପ୍ଟେମ୍ବର\",\"ଅକ୍ଟୋବର\",\"ନଭେମ୍ବର\",\"ଡିସେମ୍ବର\"],\n                    namesAbbr: [\"ଜାନୁୟାରୀ\",\"ଫେବୃଆରୀ\",\"ମାର୍ଚ୍ଚ\",\"ଏପ୍ରିଲ୍‌\",\"ମେ\",\"ଜୁନ୍‌\",\"ଜୁଲାଇ\",\"ଅଗଷ୍ଟ\",\"ସେପ୍ଟେମ୍ବର\",\"ଅକ୍ଟୋବର\",\"ନଭେମ୍ବର\",\"ଡିସେମ୍ବର\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd-MM-yy HH:mm\",\n                    G: \"dd-MM-yy HH:mm:ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}