{"version": 3, "sources": ["cultures/kendo.culture.tk.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,UAAU,UAAU,WAAW,WAAW,OAAO,SACpEC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAC1CC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,SAAS,SAAS,OAAO,QAAQ,MAAM,OAAO,OAAO,SAAS,WAAW,UAAU,SAAS,UACpGC,WAAY,MAAM,MAAM,OAAO,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,QAErFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,gBACHC,EAAG,gCACHC,EAAG,yCACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.tk.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"tk\"] = {\n        name: \"tk\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n$\",\"n$\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"m.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Ýekşenbe\",\"Duşenbe\",\"<PERSON>şenbe\",\"Çarşenbe\",\"Penşenbe\",\"<PERSON>\",\"<PERSON><PERSON><PERSON>\"],\n                    namesAbbr: [\"Ýb\",\"Db\",\"Sb\",\"Çb\",\"Pb\",\"An\",\"Şb\"],\n                    namesShort: [\"Ý\",\"D\",\"S\",\"Ç\",\"P\",\"A\",\"<PERSON>\"]\n                },\n                months: {\n                    names: [\"Ýanwar\",\"Fewral\",\"<PERSON>\",\"Aprel\",\"Maý\",\"lýun\",\"lýul\",\"Awgust\",\"Sentýabr\",\"Oktýabr\",\"Noýabr\",\"Dekabr\"],\n                    namesAbbr: [\"Ýan\",\"Few\",\"<PERSON>\",\"Apr\",\"Maý\",\"lýun\",\"lýul\",\"Awg\",\"Sen\",\"Okt\",\"Noý\",\"Dek\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd.MM.yy 'ý.'\",\n                    D: \"yyyy'-nji ýylyň 'd'-nji 'MMMM\",\n                    F: \"yyyy'-nji ýylyň 'd'-nji 'MMMM HH:mm:ss\",\n                    g: \"dd.MM.yy 'ý.' HH:mm\",\n                    G: \"dd.MM.yy 'ý.' HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy 'ý.' MMMM\",\n                    Y: \"yyyy 'ý.' MMMM\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}