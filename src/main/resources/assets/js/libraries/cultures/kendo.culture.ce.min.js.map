{"version": 3, "sources": ["cultures/kendo.culture.ce.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,cAAc,aAAa,aAAa,aAAa,WAAW,eAAe,UACvFC,WAAY,cAAc,aAAa,aAAa,aAAa,WAAW,eAAe,UAC3FC,YAAa,cAAc,aAAa,aAAa,aAAa,WAAW,eAAe,WAEhGC,QACIH,OAAQ,SAAS,UAAU,OAAO,SAAS,MAAM,OAAO,OAAO,SAAS,WAAW,UAAU,SAAS,WACtGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,6BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ce.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ce\"] = {\n        name: \"ce\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₽\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"кӀиранан де\",\"оршотан де\",\"шинарин де\",\"кхаарин де\",\"еарин де\",\"пӀераскан де\",\"шот де\"],\n                    namesAbbr: [\"кӀиранан де\",\"оршотан де\",\"шинарин де\",\"кхаарин де\",\"еарин де\",\"пӀераскан де\",\"шот де\"],\n                    namesShort: [\"кӀиранан де\",\"оршотан де\",\"шинарин де\",\"кхаарин де\",\"еарин де\",\"пӀераскан де\",\"шот де\"]\n                },\n                months: {\n                    names: [\"январь\",\"февраль\",\"март\",\"апрель\",\"май\",\"июнь\",\"июль\",\"август\",\"сентябрь\",\"октябрь\",\"ноябрь\",\"декабрь\"],\n                    namesAbbr: [\"янв\",\"фев\",\"мар\",\"апр\",\"май\",\"июн\",\"июл\",\"авг\",\"сен\",\"окт\",\"ноя\",\"дек\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"yyyy MMMM d, dddd\",\n                    F: \"yyyy MMMM d, dddd HH:mm:ss\",\n                    g: \"yyyy-MM-dd HH:mm\",\n                    G: \"yyyy-MM-dd HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}