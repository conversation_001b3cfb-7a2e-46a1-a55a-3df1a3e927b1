{"version": 3, "sources": ["cultures/kendo.culture.ne.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,WAAW,SAAS,UAAU,WAAW,UACnEC,WAAY,MAAM,MAAM,QAAQ,MAAM,OAAO,QAAQ,OACrDC,YAAa,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,MAE7CC,QACIH,OAAQ,QAAQ,YAAY,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,aAAa,UAAU,WAAW,YAC1GC,WAAY,KAAK,MAAM,QAAQ,SAAS,KAAK,MAAM,QAAQ,KAAK,QAAQ,OAAO,MAAM,QAEzFG,IAAK,YAAY,YAAY,aAC7BC,IAAK,UAAU,UAAU,WACzBC,UACIC,EAAG,WACHC,EAAG,sBACHC,EAAG,iCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ne.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ne\"] = {\n        name: \"ne\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"रु\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"आइतवार\",\"सोमवार\",\"मङ्गलवार\",\"बुधवार\",\"बिहीवार\",\"शुक्रवार\",\"शनिवार\"],\n                    namesAbbr: [\"आइत\",\"सोम\",\"मङ्गल\",\"बुध\",\"बिही\",\"शुक्र\",\"शनि\"],\n                    namesShort: [\"आ\",\"सो\",\"म\",\"बु\",\"बि\",\"शु\",\"श\"]\n                },\n                months: {\n                    names: [\"जनवरी\",\"फेब्रुअरी\",\"मार्च\",\"अप्रिल\",\"मे\",\"जून\",\"जुलाई\",\"अगस्त\",\"सेप्टेम्बर\",\"अक्टोबर\",\"नोभेम्बर\",\"डिसेम्बर\"],\n                    namesAbbr: [\"जन\",\"फेब\",\"मार्च\",\"अप्रिल\",\"मे\",\"जून\",\"जुलाई\",\"अग\",\"सेप्ट\",\"अक्ट\",\"नोभ\",\"डिस\"]\n                },\n                AM: [\"पूर्वाह्न\",\"पूर्वाह्न\",\"पूर्वाह्न\"],\n                PM: [\"अपराह्न\",\"अपराह्न\",\"अपराह्न\"],\n                patterns: {\n                    d: \"M/d/yyyy\",\n                    D: \"dddd, MMMM dd, yyyy\",\n                    F: \"dddd, MMMM dd, yyyy h:mm:ss tt\",\n                    g: \"M/d/yyyy h:mm tt\",\n                    G: \"M/d/yyyy h:mm:ss tt\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM,yyyy\",\n                    Y: \"MMMM,yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}