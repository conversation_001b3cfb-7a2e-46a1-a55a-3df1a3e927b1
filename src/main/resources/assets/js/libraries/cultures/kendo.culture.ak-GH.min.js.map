{"version": 3, "sources": ["cultures/kendo.culture.ak-GH.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,gBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,SAAS,SAAS,SAAS,QAAQ,OAAO,YAC5DC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,eAAe,kBAAkB,cAAc,mBAAmB,6BAA6B,sBAAsB,oBAAoB,gBAAgB,aAAa,gBAAgB,kBAAkB,iBAChNC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ak-GH.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ak-GH\"] = {\n        name: \"ak-GH\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Ghana<PERSON>\",\n                abbr: \"GHS\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"GH₵\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"<PERSON>wesida\",\"<PERSON>wow<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>w<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"],\n                    namesAbbr: [\"<PERSON>we\",\"<PERSON>wo\",\"<PERSON>\",\"Wuk\",\"Yaw\",\"<PERSON>a\",\"Mem\"],\n                    namesShort: [\"Kwe\",\"Dwo\",\"<PERSON>\",\"Wuk\",\"Yaw\",\"Fia\",\"Mem\"]\n                },\n                months: {\n                    names: [\"Sanda-Ɔpɛpɔn\",\"Kwakwar-Ɔgyefuo\",\"Ebɔw-Ɔbenem\",\"Ebɔbira-Oforisuo\",\"Esusow Aketseaba-Kɔtɔnimba\",\"Obirade-Ayɛwohomumu\",\"Ayɛwoho-Kitawonsa\",\"Difuu-Ɔsandaa\",\"Fankwa-Ɛbɔ\",\"Ɔbɛsɛ-Ahinime\",\"Ɔberɛfɛw-Obubuo\",\"Mumu-Ɔpɛnimba\"],\n                    namesAbbr: [\"S-Ɔ\",\"K-Ɔ\",\"E-Ɔ\",\"E-O\",\"E-K\",\"O-A\",\"A-K\",\"D-Ɔ\",\"F-Ɛ\",\"Ɔ-A\",\"Ɔ-O\",\"M-Ɔ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy/MM/dd\",\n                    D: \"dddd, yyyy MMMM dd\",\n                    F: \"dddd, yyyy MMMM dd h:mm:ss tt\",\n                    g: \"yyyy/MM/dd h:mm tt\",\n                    G: \"yyyy/MM/dd h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}