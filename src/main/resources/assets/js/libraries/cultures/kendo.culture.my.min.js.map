{"version": 3, "sources": ["cultures/kendo.culture.my.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,UAAU,SAAS,WAAW,WAAW,SAAS,OACtEC,WAAY,YAAY,UAAU,SAAS,WAAW,WAAW,SAAS,OAC1EC,YAAa,YAAY,UAAU,SAAS,WAAW,WAAW,SAAS,QAE/EC,QACIH,OAAQ,WAAW,aAAa,MAAM,OAAO,KAAK,OAAO,UAAU,QAAQ,WAAW,aAAa,WAAW,WAC9GC,WAAY,MAAM,KAAK,MAAM,OAAO,KAAK,OAAO,KAAK,IAAI,MAAM,QAAQ,MAAM,OAEjFG,IAAK,QAAQ,QAAQ,SACrBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,8BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.my.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"my\"] = {\n        name: \"my\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"K\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"တနင်္ဂနွေ\",\"တနင်္လာ\",\"အင်္ဂါ\",\"ဗုဒ္ဓဟူး\",\"ကြာသပတေး\",\"သောကြာ\",\"စနေ\"],\n                    namesAbbr: [\"တနင်္ဂနွေ\",\"တနင်္လာ\",\"အင်္ဂါ\",\"ဗုဒ္ဓဟူး\",\"ကြာသပတေး\",\"သောကြာ\",\"စနေ\"],\n                    namesShort: [\"တနင်္ဂနွေ\",\"တနင်္လာ\",\"အင်္ဂါ\",\"ဗုဒ္ဓဟူး\",\"ကြာသပတေး\",\"သောကြာ\",\"စနေ\"]\n                },\n                months: {\n                    names: [\"ဇန်နဝါရီ\",\"ဖေဖော်ဝါရီ\",\"မတ်\",\"ဧပြီ\",\"မေ\",\"ဇွန်\",\"ဇူလိုင်\",\"ဩဂုတ်\",\"စက်တင်ဘာ\",\"အောက်တိုဘာ\",\"နိုဝင်ဘာ\",\"ဒီဇင်ဘာ\"],\n                    namesAbbr: [\"ဇန်\",\"ဖေ\",\"မတ်\",\"ဧပြီ\",\"မေ\",\"ဇွန်\",\"ဇူ\",\"ဩ\",\"စက်\",\"အောက်\",\"နို\",\"ဒီ\"]\n                },\n                AM: [\"နံနက်\",\"နံနက်\",\"နံနက်\"],\n                PM: [\"ညနေ\",\"ညနေ\",\"ညနေ\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"dddd၊ dd MMMM yyyy\",\n                    F: \"dddd၊ dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd-MM-yyyy HH:mm\",\n                    G: \"dd-MM-yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}