{"version": 3, "sources": ["cultures/kendo.culture.byn-ER.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,iBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,KAAK,MAAM,YAAY,MAAM,MAAM,YACtDC,WAAY,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM,OAC9CC,YAAa,MAAM,KAAK,MAAM,KAAK,MAAM,MAAM,QAEnDC,QACIH,OAAQ,OAAO,QAAQ,MAAM,OAAO,OAAO,YAAY,MAAM,UAAU,YAAY,MAAM,YAAY,SACrGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,QAEjFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,4BACHC,EAAG,uCACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.byn-ER.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"byn-ER\"] = {\n        name: \"byn-ER\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Eritrean Nakfa\",\n                abbr: \"ERN\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Nfk\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ሰንበር ቅዳዅ\",\"ሰኑ\",\"ሰሊጝ\",\"ለጓ ወሪ ለብዋ\",\"ኣምድ\",\"ኣርብ\",\"ሰንበር ሽጓዅ\"],\n                    namesAbbr: [\"ሰ/ቅ\",\"ሰኑ\",\"ሰሊጝ\",\"ለጓ\",\"ኣምድ\",\"ኣርብ\",\"ሰ/ሽ\"],\n                    namesShort: [\"ሰ/ቅ\",\"ሰኑ\",\"ሰሊጝ\",\"ለጓ\",\"ኣምድ\",\"ኣርብ\",\"ሰ/ሽ\"]\n                },\n                months: {\n                    names: [\"ልደትሪ\",\"ካብኽብቲ\",\"ክብላ\",\"ፋጅኺሪ\",\"ክቢቅሪ\",\"ምኪኤል ትጟኒሪ\",\"ኰርኩ\",\"ማርያም ትሪ\",\"ያኸኒ መሳቅለሪ\",\"መተሉ\",\"ምኪኤል መሽወሪ\",\"ተሕሳስሪ\"],\n                    namesAbbr: [\"ልደት\",\"ካብኽ\",\"ክብላ\",\"ፋጅኺ\",\"ክቢቅ\",\"ም/ት\",\"ኰር\",\"ማርያ\",\"ያኸኒ\",\"መተሉ\",\"ም/ም\",\"ተሕሳ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd፡ dd MMMM ግርጋ yyyy gg\",\n                    F: \"dddd፡ dd MMMM ግርጋ yyyy gg h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}