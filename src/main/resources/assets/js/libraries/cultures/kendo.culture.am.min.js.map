{"version": 3, "sources": ["cultures/kendo.culture.am.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,KAAK,OAAO,MAAM,MAAM,MAAM,OAC5CC,WAAY,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,OAC/CC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,QAAQ,QAAQ,MAAM,OAAO,KAAK,KAAK,MAAM,OAAO,SAAS,QAAQ,QAAQ,SACrFC,WAAY,MAAM,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,QAEhFG,IAAK,MAAM,MAAM,OACjBC,IAAK,OAAO,OAAO,QACnBC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,+BACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.am.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"am\"] = {\n        name: \"am\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"ብር\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"እሑድ\",\"ሰኞ\",\"ማክሰኞ\",\"ረቡዕ\",\"ሐሙስ\",\"ዓርብ\",\"ቅዳሜ\"],\n                    namesAbbr: [\"እሑድ\",\"ሰኞ\",\"ማክሰ\",\"ረቡዕ\",\"ሐሙስ\",\"ዓርብ\",\"ቅዳሜ\"],\n                    namesShort: [\"እ\",\"ሰ\",\"ማ\",\"ረ\",\"ሐ\",\"ዓ\",\"ቅ\"]\n                },\n                months: {\n                    names: [\"ጃንዩወሪ\",\"ፌብሩወሪ\",\"ማርች\",\"ኤፕሪል\",\"ሜይ\",\"ጁን\",\"ጁላይ\",\"ኦገስት\",\"ሴፕቴምበር\",\"ኦክቶበር\",\"ኖቬምበር\",\"ዲሴምበር\"],\n                    namesAbbr: [\"ጃንዩ\",\"ፌብሩ\",\"ማርች\",\"ኤፕሪ\",\"ሜይ\",\"ጁን\",\"ጁላይ\",\"ኦገስ\",\"ሴፕቴ\",\"ኦክቶ\",\"ኖቬም\",\"ዲሴም\"]\n                },\n                AM: [\"ጥዋት\",\"ጥዋት\",\"ጥዋት\"],\n                PM: [\"ከሰዓት\",\"ከሰዓት\",\"ከሰዓት\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd ፣d MMMM yyyy\",\n                    F: \"dddd ፣d MMMM yyyy h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}