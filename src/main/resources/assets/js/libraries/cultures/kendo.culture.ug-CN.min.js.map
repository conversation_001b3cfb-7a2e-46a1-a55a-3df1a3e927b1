{"version": 3, "sources": ["cultures/kendo.culture.ug-CN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,UAAU,WAAW,WAAW,WAAW,OAAO,SACrEC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAC1CC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,SAAS,SAAS,OAAO,SAAS,MAAM,QAAQ,QAAQ,UAAU,WAAW,WAAW,UAAU,WAC1GC,WAAY,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,SAAS,WAE1GG,IAAK,eAAe,eAAe,gBACnCC,IAAK,eAAe,eAAe,gBACnCC,UACIC,EAAG,WACHC,EAAG,oBACHC,EAAG,4BACHC,EAAG,gBACHC,EAAG,mBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,mBACHC,EAAG,oBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ug-CN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ug-CN\"] = {\n        name: \"ug-CN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Chinese Yuan\",\n                abbr: \"CNY\",\n                pattern: [\"$-n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"¥\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"يەكشەنبە\",\"دۈشەنبە\",\"سەيشەنبە\",\"چارشەنبە\",\"پەيشەنبە\",\"جۈمە\",\"شەنبە\"],\n                    namesAbbr: [\"يە\",\"دۈ\",\"سە\",\"چا\",\"پە\",\"جۈ\",\"شە\"],\n                    namesShort: [\"ي\",\"د\",\"س\",\"چ\",\"پ\",\"ج\",\"ش\"]\n                },\n                months: {\n                    names: [\"يانۋار\",\"فېۋرال\",\"مارت\",\"ئاپرېل\",\"ماي\",\"ئىيۇن\",\"ئىيۇل\",\"ئاۋغۇست\",\"سېنتەبىر\",\"ئۆكتەبىر\",\"نويابىر\",\"دېكابىر\"],\n                    namesAbbr: [\"1-ئاي\",\"2-ئاي\",\"3-ئاي\",\"4-ئاي\",\"5-ئاي\",\"6-ئاي\",\"7-ئاي\",\"8-ئاي\",\"9-ئاي\",\"10-ئاي\",\"11-ئاي\",\"12-ئاي\"]\n                },\n                AM: [\"چۈشتىن بۇرۇن\",\"چۈشتىن بۇرۇن\",\"چۈشتىن بۇرۇن\"],\n                PM: [\"چۈشتىن كېيىن\",\"چۈشتىن كېيىن\",\"چۈشتىن كېيىن\"],\n                patterns: {\n                    d: \"yyyy-M-d\",\n                    D: \"yyyy-'يىل' d-MMMM\",\n                    F: \"yyyy-'يىل' d-MMMM H:mm:ss\",\n                    g: \"yyyy-M-d H:mm\",\n                    G: \"yyyy-M-d H:mm:ss\",\n                    m: \"d-MMMM\",\n                    M: \"d-MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy-'يىلى' MMMM\",\n                    Y: \"yyyy-'يىلى' MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}