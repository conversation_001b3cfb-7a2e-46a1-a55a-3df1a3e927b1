{"version": 3, "sources": ["cultures/kendo.culture.hu.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,QAAQ,OAAO,SAAS,YAAY,SAAS,WAChEC,WAAY,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,OACvCC,YAAa,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,QAE5CC,QACIH,OAAQ,SAAS,UAAU,UAAU,UAAU,QAAQ,SAAS,SAAS,YAAY,aAAa,UAAU,WAAW,YACvHC,WAAY,OAAO,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,SAAS,OAAO,OAAO,SAEjGG,IAAK,MAAM,MAAM,OACjBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,gBACHC,EAAG,sBACHC,EAAG,8BACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,KACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.hu.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"hu\"] = {\n        name: \"hu\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"Ft\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"vasárnap\",\"hétfő\",\"kedd\",\"szerda\",\"cs<PERSON>t<PERSON>rtök\",\"péntek\",\"szombat\"],\n                    namesAbbr: [\"V\",\"H\",\"K\",\"Sze\",\"Cs\",\"P\",\"Szo\"],\n                    namesShort: [\"V\",\"H\",\"K\",\"Sze\",\"Cs\",\"P\",\"Szo\"]\n                },\n                months: {\n                    names: [\"január\",\"február\",\"m<PERSON>rcius\",\"április\",\"m<PERSON>jus\",\"június\",\"július\",\"augusztus\",\"szeptember\",\"október\",\"november\",\"december\"],\n                    namesAbbr: [\"jan.\",\"febr.\",\"m<PERSON>rc.\",\"ápr.\",\"máj.\",\"jún.\",\"júl.\",\"aug.\",\"szept.\",\"okt.\",\"nov.\",\"dec.\"]\n                },\n                AM: [\"de.\",\"de.\",\"DE.\"],\n                PM: [\"du.\",\"du.\",\"DU.\"],\n                patterns: {\n                    d: \"yyyy. MM. dd.\",\n                    D: \"yyyy. MMMM d., dddd\",\n                    F: \"yyyy. MMMM d., dddd H:mm:ss\",\n                    g: \"yyyy. MM. dd. H:mm\",\n                    G: \"yyyy. MM. dd. H:mm:ss\",\n                    m: \"MMMM d.\",\n                    M: \"MMMM d.\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy. MMMM\",\n                    Y: \"yyyy. MMMM\"\n                },\n                \"/\": \". \",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}