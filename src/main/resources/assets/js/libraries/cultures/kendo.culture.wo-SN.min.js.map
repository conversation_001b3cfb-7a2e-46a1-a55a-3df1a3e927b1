{"version": 3, "sources": ["cultures/kendo.culture.wo-SN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,MACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,UAAU,UAAU,UAAU,SAAS,SACjEC,WAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QACtDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,UAAU,UAAU,QAAQ,QAAQ,KAAK,OAAO,SAAS,KAAK,YAAY,WAAW,YAAY,YACzGC,WAAY,OAAO,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,QAAQ,OAAO,OAAO,SAExFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,mBACHC,EAAG,4BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.wo-SN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"wo-SN\"] = {\n        name: \"wo-SN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"CFA\",\n                abbr: \"XOF\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"CFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"<PERSON>b<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>aa<PERSON>\"],\n                    namesAbbr: [\"Dib.\",\"Alt.\",\"Tal.\",\"Àll.\",\"Alx.\",\"Àjj.\",\"Gaa.\"],\n                    namesShort: [\"Di\",\"Al\",\"Ta\",\"Àl\",\"Ax\",\"Àj\",\"Ga\"]\n                },\n                months: {\n                    names: [\"Samwiye\",\"Fewriye\",\"Maars\",\"Awril\",\"Me\",\"Suwe\",\"<PERSON>let\",\"Ut\",\"Septàmbar\",\"<PERSON>toobar\",\"Noowàmbar\",\"<PERSON>àmbar\"],\n                    namesAbbr: [\"Sam.\",\"Few.\",\"Maa\",\"Awr.\",\"Me\",\"Suw\",\"Sul.\",\"Ut\",\"Sept.\",\"Okt.\",\"Now.\",\"Des.\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy HH:mm:ss\",\n                    g: \"dd/MM/yyyy HH:mm\",\n                    G: \"dd/MM/yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}