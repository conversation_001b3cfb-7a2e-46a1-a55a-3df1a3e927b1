{"version": 3, "sources": ["cultures/kendo.culture.ar-QA.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,cACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,UAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,UAAU,WAAW,WAAW,SAAS,SAAS,SAClEC,WAAY,QAAQ,UAAU,WAAW,WAAW,SAAS,SAAS,SACtEC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,QAAQ,SAAS,OAAO,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,UAClGC,WAAY,QAAQ,SAAS,OAAO,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,SAAS,SAAS,WAE1GG,IAAK,IAAI,IAAI,KACbC,IAAK,IAAI,IAAI,KACbC,UACIC,EAAG,aACHC,EAAG,gBACHC,EAAG,4BACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ar-QA.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ar-QA\"] = {\n        name: \"ar-QA\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Qatari Rial\",\n                abbr: \"QAR\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"ر.ق.‏\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"الأحد\",\"الإثنين\",\"الثلاثاء\",\"الأربعاء\",\"الخميس\",\"الجمعة\",\"السبت\"],\n                    namesAbbr: [\"الأحد\",\"الإثنين\",\"الثلاثاء\",\"الأربعاء\",\"الخميس\",\"الجمعة\",\"السبت\"],\n                    namesShort: [\"ح\",\"ن\",\"ث\",\"ر\",\"خ\",\"ج\",\"س\"]\n                },\n                months: {\n                    names: [\"يناير\",\"فبراير\",\"مارس\",\"أبريل\",\"مايو\",\"يونيو\",\"يوليه\",\"أغسطس\",\"سبتمبر\",\"أكتوبر\",\"نوفمبر\",\"ديسمبر\"],\n                    namesAbbr: [\"يناير\",\"فبراير\",\"مارس\",\"أبريل\",\"مايو\",\"يونيو\",\"يوليه\",\"أغسطس\",\"سبتمبر\",\"أكتوبر\",\"نوفمبر\",\"ديسمبر\"]\n                },\n                AM: [\"ص\",\"ص\",\"ص\"],\n                PM: [\"م\",\"م\",\"م\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dd MMMM, yyyy\",\n                    F: \"dd MMMM, yyyy hh:mm:ss tt\",\n                    g: \"dd/MM/yyyy hh:mm tt\",\n                    G: \"dd/MM/yyyy hh:mm:ss tt\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"hh:mm tt\",\n                    T: \"hh:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}