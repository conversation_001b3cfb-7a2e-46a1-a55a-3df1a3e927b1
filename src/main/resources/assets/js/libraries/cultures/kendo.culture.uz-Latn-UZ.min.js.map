{"version": 3, "sources": ["cultures/kendo.culture.uz-Latn-UZ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,eACXC,KAAM,aACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,kBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,WAAW,WAAW,aAAa,YAAY,OAAO,UAC1EC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAC1CC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,SAAS,SAAS,OAAO,QAAQ,MAAM,OAAO,OAAO,SAAS,UAAU,SAAS,SAAS,UAClGC,WAAY,OAAO,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,QAEtFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,8BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.uz-Latn-UZ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"uz-Latn-UZ\"] = {\n        name: \"uz-Latn-UZ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Uzbekistani Som\",\n                abbr: \"UZS\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 0,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"soʻm\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"yaks<PERSON><PERSON>\",\"dushan<PERSON>\",\"se<PERSON><PERSON>\",\"ch<PERSON><PERSON><PERSON>\",\"payshan<PERSON>\",\"juma\",\"shanba\"],\n                    namesAbbr: [\"Ya\",\"Du\",\"Se\",\"Ch\",\"<PERSON>\",\"Ju\",\"Sh\"],\n                    namesShort: [\"Ya\",\"Du\",\"Se\",\"Ch\",\"Pa\",\"Ju\",\"Sh\"]\n                },\n                months: {\n                    names: [\"Yanvar\",\"Fevral\",\"Mart\",\"Aprel\",\"May\",\"Iyun\",\"Iyul\",\"Avgust\",\"Sentabr\",\"Oktabr\",\"Noyabr\",\"Dekabr\"],\n                    names<PERSON>bbr: [\"<PERSON>v\",\"Fev\",\"<PERSON>\",\"Apr\",\"May\",\"Iyun\",\"Iyul\",\"Avg\",\"Sen\",\"Okt\",\"Noya\",\"Dek\"]\n                },\n                AM: [\"TO\",\"to\",\"TO\"],\n                PM: [\"TK\",\"tk\",\"TK\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, yyyy MMMM dd\",\n                    F: \"dddd, yyyy MMMM dd HH:mm:ss\",\n                    g: \"dd/MM/yyyy HH:mm\",\n                    G: \"dd/MM/yyyy HH:mm:ss\",\n                    m: \"d-MMMM\",\n                    M: \"d-MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}