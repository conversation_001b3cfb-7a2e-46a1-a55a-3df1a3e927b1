{"version": 3, "sources": ["cultures/kendo.culture.hy.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,aAAa,YAAY,aAAa,YAAY,SAAS,SAC5EC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,UAAU,UAAU,OAAO,QAAQ,QAAQ,SAAS,SAAS,UAAU,YAAY,YAAY,WAAW,aAClHC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.hy.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"hy\"] = {\n        name: \"hy\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"֏\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Կիրակի\",\"Երկուշաբթի\",\"Երեքշաբթի\",\"Չորեքշաբթի\",\"Հինգշաբթի\",\"Ուրբաթ\",\"Շաբաթ\"],\n                    namesAbbr: [\"Կիր\",\"Երկ\",\"Երք\",\"Չրք\",\"Հնգ\",\"Ուր\",\"Շբթ\"],\n                    namesShort: [\"Կ\",\"Ե\",\"Ե\",\"Չ\",\"Հ\",\"Ո\",\"Շ\"]\n                },\n                months: {\n                    names: [\"Հունվար\",\"Փետրվար\",\"Մարտ\",\"Ապրիլ\",\"Մայիս\",\"Հունիս\",\"Հուլիս\",\"Օգոստոս\",\"Սեպտեմբեր\",\"Հոկտեմբեր\",\"Նոյեմբեր\",\"Դեկտեմբեր\"],\n                    namesAbbr: [\"Հնվ\",\"Փտվ\",\"Մրտ\",\"Ապր\",\"Մյս\",\"Հնս\",\"Հլս\",\"Օգս\",\"Սպտ\",\"Հկտ\",\"Նյմ\",\"Դկտ\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd.MM.yyyy\",\n                    D: \"d MMMM, yyyy\",\n                    F: \"d MMMM, yyyy HH:mm:ss\",\n                    g: \"dd.MM.yyyy HH:mm\",\n                    G: \"dd.MM.yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}