{"version": 3, "sources": ["cultures/kendo.culture.oc-FR.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,OACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,SAAS,UAAU,WAAW,SAAS,YAAY,YACrEC,WAAY,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,OAClDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,SAAS,UAAU,OAAO,QAAQ,MAAM,OAAO,SAAS,QAAQ,WAAW,UAAU,WAAW,YACxGC,WAAY,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,SAE3FG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,wBACHC,EAAG,iCACHC,EAAG,uBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,YACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.oc-FR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"oc-FR\"] = {\n        name: \"oc-FR\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Euro\",\n                abbr: \"EUR\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"€\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"dimenge\",\"diluns\",\"dimarts\",\"dimècres\",\"dijòus\",\"divendres\",\"dissabte\"],\n                    namesAbbr: [\"dg.\",\"dl.\",\"dma.\",\"dmc.\",\"dj.\",\"dv.\",\"ds.\"],\n                    namesShort: [\"dg\",\"dl\",\"da\",\"dc\",\"dj\",\"dv\",\"ds\"]\n                },\n                months: {\n                    names: [\"genièr\",\"febrièr\",\"març\",\"abril\",\"mai\",\"junh\",\"julhet\",\"agost\",\"setembre\",\"octobre\",\"novembre\",\"decembre\"],\n                    namesAbbr: [\"gen.\",\"feb.\",\"març\",\"abr.\",\"mai\",\"junh\",\"julh\",\"ag.\",\"set.\",\"oct.\",\"nov.\",\"dec.\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd d MMMM' de 'yyyy\",\n                    F: \"dddd d MMMM' de 'yyyy HH.mm.ss\",\n                    g: \"dd/MM/yyyy HH' h 'mm\",\n                    G: \"dd/MM/yyyy HH.mm.ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH' h 'mm\",\n                    T: \"HH.mm.ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM' de 'yyyy\",\n                    Y: \"MMMM' de 'yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \".\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}