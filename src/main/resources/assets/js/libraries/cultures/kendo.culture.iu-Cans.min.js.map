{"version": 3, "sources": ["cultures/kendo.culture.iu-Cans.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,YACXC,KAAM,UACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,SAAS,UAC1DC,WAAY,MAAM,MAAM,OAAO,OAAO,KAAK,MAAM,UACjDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,QAAQ,QAAQ,MAAM,MAAM,KAAK,KAAK,MAAM,OAAO,OAAO,OAAO,OAAO,QAChFC,WAAY,MAAM,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,QAEhFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,oBACHC,EAAG,+BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.iu-Cans.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"iu-Cans\"] = {\n        name: \"iu-Cans\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"$\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ᓈᑦᑏᖑᔭ\",\"ᓇᒡᒐᔾᔭᐅ\",\"ᐊᐃᑉᐱᖅ\",\"ᐱᖓᑦᓯᖅ\",\"ᓯᑕᒻᒥᖅ\",\"ᑕᓪᓕᕐᒥᖅ\",\"ᓯᕙᑖᕐᕕᒃ\"],\n                    namesAbbr: [\"ᓈᑦᑏ\",\"ᓇᒡᒐ\",\"ᐊᐃᑉᐱ\",\"ᐱᖓᑦᓯ\",\"ᓯᑕ\",\"ᑕᓪᓕ\",\"ᓯᕙᑖᕐᕕᒃ\"],\n                    namesShort: [\"ᓈ\",\"ᓇ\",\"ᐊ\",\"ᐱ\",\"ᓯ\",\"ᑕ\",\"ᓯ\"]\n                },\n                months: {\n                    names: [\"ᔮᓐᓄᐊᕆ\",\"ᕖᕝᕗᐊᕆ\",\"ᒫᑦᓯ\",\"ᐄᐳᕆ\",\"ᒪᐃ\",\"ᔫᓂ\",\"ᔪᓚᐃ\",\"ᐋᒡᒌᓯ\",\"ᓯᑎᐱᕆ\",\"ᐅᑐᐱᕆ\",\"ᓄᕕᐱᕆ\",\"ᑎᓯᐱᕆ\"],\n                    namesAbbr: [\"ᔮᓐᓄ\",\"ᕖᕝᕗ\",\"ᒫᑦᓯ\",\"ᐄᐳᕆ\",\"ᒪᐃ\",\"ᔫᓂ\",\"ᔪᓚᐃ\",\"ᐋᒡᒌ\",\"ᓯᑎᐱ\",\"ᐅᑐᐱ\",\"ᓄᕕᐱ\",\"ᑎᓯᐱ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd,MMMM dd,yyyy\",\n                    F: \"dddd,MMMM dd,yyyy h:mm:ss tt\",\n                    g: \"d/M/yyyy h:mm tt\",\n                    G: \"d/M/yyyy h:mm:ss tt\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM,yyyy\",\n                    Y: \"MMMM,yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}