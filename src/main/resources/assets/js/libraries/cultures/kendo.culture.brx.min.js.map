{"version": 3, "sources": ["cultures/kendo.culture.brx.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,QAAQ,UAAU,SAAS,WAAW,WAAW,WAClEC,WAAY,MAAM,KAAK,OAAO,MAAM,QAAQ,QAAQ,QACpDC,YAAa,MAAM,KAAK,OAAO,MAAM,QAAQ,QAAQ,SAEzDC,QACIH,OAAQ,WAAW,aAAa,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,aAAa,QAAQ,WAAW,aAC5GC,WAAY,WAAW,aAAa,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,aAAa,QAAQ,WAAW,cAEpHG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.brx.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"brx\"] = {\n        name: \"brx\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"रबिबार\",\"समबार\",\"मंगलबार\",\"बुदबार\",\"बिसथिबार\",\"सुखुरबार\",\"सुनिबार\"],\n                    namesAbbr: [\"रबि\",\"सम\",\"मंगल\",\"बुद\",\"बिसथि\",\"सुखुर\",\"सुनि\"],\n                    namesShort: [\"रबि\",\"सम\",\"मंगल\",\"बुद\",\"बिसथि\",\"सुखुर\",\"सुनि\"]\n                },\n                months: {\n                    names: [\"जानुवारी\",\"फेब्रुवारी\",\"मार्स\",\"एफ्रिल\",\"मे\",\"जुन\",\"जुलाइ\",\"आगस्थ\",\"सेबथेज्ब़र\",\"अखथबर\",\"नबेज्ब़र\",\"दिसेज्ब़र\"],\n                    namesAbbr: [\"जानुवारी\",\"फेब्रुवारी\",\"मार्स\",\"एफ्रिल\",\"मे\",\"जुन\",\"जुलाइ\",\"आगस्थ\",\"सेबथेज्ब़र\",\"अखथबर\",\"नबेज्ब़र\",\"दिसेज्ब़र\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"M/d/yyyy\",\n                    D: \"dddd, MMMM d, yyyy\",\n                    F: \"dddd, MMMM d, yyyy h:mm:ss tt\",\n                    g: \"M/d/yyyy h:mm tt\",\n                    G: \"M/d/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}