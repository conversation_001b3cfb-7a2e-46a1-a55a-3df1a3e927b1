{"version": 3, "sources": ["cultures/kendo.culture.mg-MG.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,kBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,cAAc,SAAS,WAAW,YAAY,OAAO,YACvEC,WAAY,OAAO,QAAQ,MAAM,OAAO,OAAO,MAAM,QACrDC,YAAa,OAAO,QAAQ,MAAM,OAAO,OAAO,MAAM,SAE1DC,QACIH,OAAQ,UAAU,WAAW,SAAS,SAAS,MAAM,OAAO,QAAQ,YAAY,YAAY,UAAU,WAAW,YACjHC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,mBACHC,EAAG,4BACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.mg-MG.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"mg-MG\"] = {\n        name: \"mg-MG\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Malagasy Ariary\",\n                abbr: \"MGA\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 0,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Ar\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"<PERSON>ah<PERSON>\",\"Alatsinainy\",\"<PERSON>lat<PERSON>\",\"Alaro<PERSON>\",\"Alakamisy\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\"],\n                    namesAbbr: [\"<PERSON>ah\",\"Alats\",\"Tal\",\"<PERSON>ar\",\"<PERSON>ak\",\"Zom\",\"<PERSON>ab\"],\n                    namesShort: [\"Alah\",\"Alats\",\"Tal\",\"<PERSON>ar\",\"<PERSON>ak\",\"Zom\",\"Asab\"]\n                },\n                months: {\n                    names: [\"<PERSON>oary\",\"Febroary\",\"<PERSON>sa\",\"Aprily\",\"Mey\",\"Jona\",\"Jolay\",\"Aogositra\",\"Septambra\",\"<PERSON>to<PERSON>\",\"Novambra\",\"<PERSON>am<PERSON>\"],\n                    namesAbbr: [\"Jan\",\"<PERSON>\",\"<PERSON>\",\"Apr\",\"Mey\",\"Jon\",\"Jol\",\"Aog\",\"Sep\",\"Okt\",\"Nov\",\"Des\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy HH:mm:ss\",\n                    g: \"d/M/yyyy HH:mm\",\n                    G: \"d/M/yyyy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}