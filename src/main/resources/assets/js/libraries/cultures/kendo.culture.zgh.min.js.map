{"version": 3, "sources": ["cultures/kendo.culture.zgh.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,QAAQ,SAAS,QAAQ,QAAQ,UAAU,WAC5DC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,QACjDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,SAEtDC,QACIH,OAAQ,SAAS,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,SAAS,OAAO,WAAW,QAAQ,WAAW,YACtGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,mBACHC,EAAG,4BACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.zgh.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"zgh\"] = {\n        name: \"zgh\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n$\",\"n$\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"MAD\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ⴰⵙⴰⵎⴰⵙ\",\"ⴰⵢⵏⴰⵙ\",\"ⴰⵙⵉⵏⴰⵙ\",\"ⴰⴽⵕⴰⵙ\",\"ⴰⴽⵡⴰⵙ\",\"ⴰⵙⵉⵎⵡⴰⵙ\",\"ⴰⵙⵉⴹⵢⴰⵙ\"],\n                    namesAbbr: [\"ⴰⵙⴰ\",\"ⴰⵢⵏ\",\"ⴰⵙⵉ\",\"ⴰⴽⵕ\",\"ⴰⴽⵡ\",\"ⴰⵙⵉⵎ\",\"ⴰⵙⵉⴹ\"],\n                    namesShort: [\"ⴰⵙⴰ\",\"ⴰⵢⵏ\",\"ⴰⵙⵉ\",\"ⴰⴽⵕ\",\"ⴰⴽⵡ\",\"ⴰⵙⵉⵎ\",\"ⴰⵙⵉⴹ\"]\n                },\n                months: {\n                    names: [\"ⵉⵏⵏⴰⵢⵔ\",\"ⴱⵕⴰⵢⵕ\",\"ⵎⴰⵕⵚ\",\"ⵉⴱⵔⵉⵔ\",\"ⵎⴰⵢⵢⵓ\",\"ⵢⵓⵏⵢⵓ\",\"ⵢⵓⵍⵢⵓⵣ\",\"ⵖⵓⵛⵜ\",\"ⵛⵓⵜⴰⵏⴱⵉⵔ\",\"ⴽⵜⵓⴱⵔ\",\"ⵏⵓⵡⴰⵏⴱⵉⵔ\",\"ⴷⵓⵊⴰⵏⴱⵉⵔ\"],\n                    namesAbbr: [\"ⵉⵏⵏ\",\"ⴱⵕⴰ\",\"ⵎⴰⵕ\",\"ⵉⴱⵔ\",\"ⵎⴰⵢ\",\"ⵢⵓⵏ\",\"ⵢⵓⵍ\",\"ⵖⵓⵛ\",\"ⵛⵓⵜ\",\"ⴽⵜⵓ\",\"ⵏⵓⵡ\",\"ⴷⵓⵊ\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy HH:mm:ss\",\n                    g: \"d/M/yyyy HH:mm\",\n                    G: \"d/M/yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}