{"version": 3, "sources": ["cultures/kendo.culture.as-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,WAAW,SAAS,cAAc,WAAW,UACvEC,WAAY,OAAO,OAAO,SAAS,OAAO,OAAO,SAAS,QAC1DC,YAAa,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,MAE7CC,QACIH,OAAQ,WAAW,aAAa,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,aAAa,UAAU,UAAU,YAC7GC,WAAY,OAAO,SAAS,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,SAAS,QAAQ,MAAM,SAEnGG,IAAK,SAAS,SAAS,UACvBC,IAAK,QAAQ,QAAQ,SACrBC,UACIC,EAAG,aACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,UACHC,EAAG,WAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.as-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"as-IN\"] = {\n        name: \"as-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"$ -n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ৰবিবাৰ\",\"সোমবাৰ\",\"মঙ্গলবাৰ\",\"বুধবাৰ\",\"বৃহস্পতিবাৰ\",\"শুক্রবাৰ\",\"শনিবাৰ\"],\n                    namesAbbr: [\"ৰবি.\",\"সোম.\",\"মঙ্গল.\",\"বুধ.\",\"বৃহ.\",\"শুক্র.\",\"শনি.\"],\n                    namesShort: [\"ৰ\",\"সো\",\"ম\",\"বু\",\"বৃ\",\"শু\",\"শ\"]\n                },\n                months: {\n                    names: [\"জানুৱাৰী\",\"ফেব্রুৱাৰী\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগষ্ট\",\"চেপ্টেম্বৰ\",\"অক্টোবৰ\",\"নবেম্বৰ\",\"ডিচেম্বৰ\"],\n                    namesAbbr: [\"জানু\",\"ফেব্রু\",\"মার্চ\",\"এপ্রিল\",\"মে\",\"জুন\",\"জুলাই\",\"আগষ্ট\",\"চেপ্টে\",\"অক্টো\",\"নবে\",\"ডিচে\"]\n                },\n                AM: [\"ৰাতিপু\",\"ৰাতিপু\",\"ৰাতিপু\"],\n                PM: [\"আবেলি\",\"আবেলি\",\"আবেলি\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"yyyy,MMMM dd, dddd\",\n                    F: \"yyyy,MMMM dd, dddd tt h:mm:ss\",\n                    g: \"dd-MM-yyyy tt h:mm\",\n                    G: \"dd-MM-yyyy tt h:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"tt h:mm\",\n                    T: \"tt h:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM,yy\",\n                    Y: \"MMMM,yy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}