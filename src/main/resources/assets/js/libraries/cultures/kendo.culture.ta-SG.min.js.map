{"version": 3, "sources": ["cultures/kendo.culture.ta-SG.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,mBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,UAAU,WAAW,QAAQ,UAAU,SAAS,OACjEC,WAAY,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,OAC3DC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAE/CC,QACIH,OAAQ,QAAQ,WAAW,SAAS,SAAS,KAAK,OAAO,OAAO,SAAS,aAAa,WAAW,UAAU,YAC3GC,WAAY,MAAM,QAAQ,QAAQ,OAAO,KAAK,OAAO,OAAO,MAAM,QAAQ,OAAO,MAAM,SAE3FG,IAAK,WAAW,WAAW,YAC3BC,IAAK,WAAW,WAAW,YAC3BC,UACIC,EAAG,WACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ta-SG.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ta-SG\"] = {\n        name: \"ta-SG\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Singapore Dollar\",\n                abbr: \"SGD\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"$\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ஞாயிறு\",\"திங்கள்\",\"செவ்வாய்\",\"புதன்\",\"வியாழன்\",\"வெள்ளி\",\"சனி\"],\n                    namesAbbr: [\"ஞாயி.\",\"திங்.\",\"செவ்.\",\"புத.\",\"வியா.\",\"வெள்.\",\"சனி\"],\n                    namesShort: [\"ஞா\",\"தி\",\"செ\",\"பு\",\"வி\",\"வெ\",\"ச\"]\n                },\n                months: {\n                    names: [\"ஜனவரி\",\"பிப்ரவரி\",\"மார்ச்\",\"ஏப்ரல்\",\"மே\",\"ஜூன்\",\"ஜூலை\",\"ஆகஸ்டு\",\"செப்டம்பர்\",\"அக்டோபர்\",\"நவம்பர்\",\"டிசம்பர்\"],\n                    namesAbbr: [\"ஜன.\",\"பிப்.\",\"மார்.\",\"ஏப்.\",\"மே\",\"ஜூன்\",\"ஜூலை\",\"ஆக.\",\"செப்.\",\"அக்.\",\"நவ.\",\"டிச.\"]\n                },\n                AM: [\"முற்பகல்\",\"முற்பகல்\",\"முற்பகல்\"],\n                PM: [\"பிற்பகல்\",\"பிற்பகல்\",\"பிற்பகல்\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd, d MMMM, yyyy\",\n                    F: \"dddd, d MMMM, yyyy tt h:mm:ss\",\n                    g: \"d/M/yyyy tt h:mm\",\n                    G: \"d/M/yyyy tt h:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"tt h:mm\",\n                    T: \"tt h:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}