{"version": 3, "sources": ["cultures/kendo.culture.ko-KP.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,mBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAC5CC,WAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KACpCC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,OACjEC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,QAEzEG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,cACHC,EAAG,mBACHC,EAAG,8BACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,KACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ko-KP.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ko-KP\"] = {\n        name: \"ko-KP\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"North Korean Won\",\n                abbr: \"KPW\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 0,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₩\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"일요일\",\"월요일\",\"화요일\",\"수요일\",\"목요일\",\"금요일\",\"토요일\"],\n                    namesAbbr: [\"일\",\"월\",\"화\",\"수\",\"목\",\"금\",\"토\"],\n                    namesShort: [\"일\",\"월\",\"화\",\"수\",\"목\",\"금\",\"토\"]\n                },\n                months: {\n                    names: [\"1월\",\"2월\",\"3월\",\"4월\",\"5월\",\"6월\",\"7월\",\"8월\",\"9월\",\"10월\",\"11월\",\"12월\"],\n                    namesAbbr: [\"1월\",\"2월\",\"3월\",\"4월\",\"5월\",\"6월\",\"7월\",\"8월\",\"9월\",\"10월\",\"11월\",\"12월\"]\n                },\n                AM: [\"오전\",\"오전\",\"오전\"],\n                PM: [\"오후\",\"오후\",\"오후\"],\n                patterns: {\n                    d: \"yyyy. M. d.\",\n                    D: \"yyyy년 M월 d일 dddd\",\n                    F: \"yyyy년 M월 d일 dddd tt h:mm:ss\",\n                    g: \"yyyy. M. d. tt h:mm\",\n                    G: \"yyyy. M. d. tt h:mm:ss\",\n                    m: \"MMMM d일\",\n                    M: \"MMMM d일\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"tt h:mm\",\n                    T: \"tt h:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy년 MMMM\",\n                    Y: \"yyyy년 MMMM\"\n                },\n                \"/\": \". \",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}