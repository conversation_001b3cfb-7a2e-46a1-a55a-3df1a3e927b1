{"version": 3, "sources": ["cultures/kendo.culture.kln-KE.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,kBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,SAAS,UAAU,UAAU,YAAY,SAAS,QACpEC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,SAAS,aAAa,WAAW,YAAY,SAAS,QAAQ,YAAY,UAAU,SAAS,SAAS,oBAAoB,wBAClIC,WAAY,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAEnFG,IAAK,MAAM,MAAM,OACjBC,IAAK,QAAQ,QAAQ,SACrBC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,6BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.kln-KE.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"kln-KE\"] = {\n        name: \"kln-KE\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Kenyan Shilling\",\n                abbr: \"KES\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Ksh\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"<PERSON><PERSON><PERSON>\",\"Kota<PERSON>\",\"Koaeng’\",\"Ko<PERSON>mo<PERSON>\",\"Ko<PERSON>’wan\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>\"],\n                    namesAbbr: [\"Kts\",\"Kot\",\"Koo\",\"Ko<PERSON>\",\"Koa\",\"Kom\",\"Kol\"],\n                    namesShort: [\"Kts\",\"Kot\",\"Koo\",\"Kos\",\"Koa\",\"Kom\",\"Kol\"]\n                },\n                months: {\n                    names: [\"Mulgul\",\"Ng’atyaato\",\"Kiptaamo\",\"Iwootkuut\",\"Mamuut\",\"Paagi\",\"Ng’eiyeet\",\"Rooptui\",\"Bureet\",\"Epeeso\",\"Kipsuunde ne taai\",\"Kipsuunde nebo aeng’\"],\n                    namesAbbr: [\"Mul\",\"Ngat\",\"Taa\",\"Iwo\",\"Mam\",\"Paa\",\"Nge\",\"Roo\",\"Bur\",\"Epe\",\"Kpt\",\"Kpa\"]\n                },\n                AM: [\"krn\",\"krn\",\"KRN\"],\n                PM: [\"koosk\",\"koosk\",\"KOOSK\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, d MMMM yyyy\",\n                    F: \"dddd, d MMMM yyyy HH:mm:ss\",\n                    g: \"dd/MM/yyyy HH:mm\",\n                    G: \"dd/MM/yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}