{"version": 3, "sources": ["cultures/kendo.culture.quc.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAc,KAChBC,KAAM,MACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAc,SAAc,SAAc,UAAe,SAAc,UAAe,WAC9FC,WAAY,OAAY,OAAY,OAAY,QAAa,OAAY,QAAa,SACtFC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,YAAsB,YAAsB,WAAgB,WAAgB,UAAe,WAAgB,WAAgB,cAAmB,cAAwB,WAAgB,aAAkB,gBAChNC,WAAY,QAAa,QAAa,OAAO,OAAO,MAAM,OAAO,OAAO,UAAU,UAAe,OAAO,SAAS,aAErHG,IAAK,OAAO,OAAO,QACnBC,IAAK,OAAO,OAAO,QACnBC,UACIC,EAAG,aACHC,EAAG,mCACHC,EAAG,8CACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,gBACHC,EAAG,gBACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,mBACHC,EAAG,oBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.quc.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"quc\"] = {\n        name: \"quc\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"Q\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"juq\\u0027ij\",\"kaq\\u0027ij\",\"oxq\\u0027ij\",\"kajq\\u0027ij\",\"joq\\u0027ij\",\"waqq\\u0027ij\",\"wuqq\\u0027ij\"],\n                    namesAbbr: [\"juq\\u0027\",\"kaq\\u0027\",\"oxq\\u0027\",\"kajq\\u0027\",\"joq\\u0027\",\"waqq\\u0027\",\"wuqq\\u0027\"],\n                    namesShort: [\"ju\",\"ka\",\"ox\",\"kj\",\"jo\",\"wa\",\"wu\"]\n                },\n                months: {\n                    names: [\"nab\\u0027e ik\\u0027\",\"ukab\\u0027 ik\\u0027\",\"urox ik\\u0027\",\"ukaj ik\\u0027\",\"uro ik\\u0027\",\"uwaq ik\\u0027\",\"uwuq ik\\u0027\",\"uwajxaq ik\\u0027\",\"ub\\u0027elej ik\\u0027\",\"ulaj ik\\u0027\",\"ujulaj ik\\u0027\",\"ukab\\u0027laj ik\\u0027\"],\n                    namesAbbr: [\"nab\\u0027e\",\"ukab\\u0027\",\"urox\",\"ukaj\",\"uro\",\"uwaq\",\"uwuq\",\"uwajxaq\",\"ub\\u0027elej\",\"ulaj\",\"ujulaj\",\"ukab\\u0027laj\"]\n                },\n                AM: [\"a.m.\",\"a.m.\",\"A.M.\"],\n                PM: [\"p.m.\",\"p.m.\",\"P.M.\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, dd' rech 'MMMM' rech 'yyyy\",\n                    F: \"dddd, dd' rech 'MMMM' rech 'yyyy h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"d' rech 'MMMM\",\n                    M: \"d' rech 'MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM' rech 'yyyy\",\n                    Y: \"MMMM' rech 'yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}