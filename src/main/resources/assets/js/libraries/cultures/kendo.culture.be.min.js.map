{"version": 3, "sources": ["cultures/kendo.culture.be.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,aAAa,UAAU,SAAS,SAAS,UAAU,UACrEC,WAAY,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAC3CC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,WAAW,OAAO,UAAU,WAAW,MAAM,UAAU,SAAS,UAAU,WAAW,aAAa,WAAW,WACrHC,WAAY,QAAQ,MAAM,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQ,OAAO,SAExFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,WACHC,EAAG,cACHC,EAAG,uBACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,eACHC,EAAG,gBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.be.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"be\"] = {\n        name: \"be\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"Br\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"нядзеля\",\"панядзелак\",\"аўторак\",\"серада\",\"чацвер\",\"пятніца\",\"субота\"],\n                    namesAbbr: [\"нд\",\"пн\",\"аўт\",\"ср\",\"чц\",\"пт\",\"сб\"],\n                    namesShort: [\"нд\",\"пн\",\"аў\",\"ср\",\"чц\",\"пт\",\"сб\"]\n                },\n                months: {\n                    names: [\"студзень\",\"люты\",\"сакавік\",\"красавік\",\"май\",\"чэрвень\",\"ліпень\",\"жнівень\",\"верасень\",\"кастрычнік\",\"лістапад\",\"снежань\"],\n                    namesAbbr: [\"студз\",\"лют\",\"сак\",\"крас\",\"май\",\"чэрв\",\"ліп\",\"жн\",\"вер\",\"кастр\",\"ліст\",\"снеж\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd.MM.yy\",\n                    D: \"d MMMM yyyy\",\n                    F: \"d MMMM yyyy HH:mm:ss\",\n                    g: \"dd.MM.yy HH:mm\",\n                    G: \"dd.MM.yy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy г.\",\n                    Y: \"MMMM yyyy г.\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}