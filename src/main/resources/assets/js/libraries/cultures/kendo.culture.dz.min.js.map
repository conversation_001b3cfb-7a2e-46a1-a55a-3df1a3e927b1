{"version": 3, "sources": ["cultures/kendo.culture.dz.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,eAAe,aAAa,cAAc,aAAa,cAAc,aACzFC,WAAY,MAAM,OAAO,OAAO,OAAO,OAAO,QAAQ,OACtDC,YAAa,MAAM,OAAO,OAAO,OAAO,OAAO,QAAQ,QAE3DC,QACIH,OAAQ,eAAe,kBAAkB,kBAAkB,gBAAgB,gBAAgB,iBAAiB,kBAAkB,mBAAmB,iBAAiB,iBAAiB,sBAAsB,uBACzMC,WAAY,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,QAAQ,UAE/FG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,iCACHC,EAAG,mDACHC,EAAG,oCACHC,EAAG,+BACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,yBACHC,EAAG,oBACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.dz.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"dz\"] = {\n        name: \"dz\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"Nu.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"གཟའ་ཟླ་བ་\",\"གཟའ་མིག་དམར་\",\"གཟའ་ལྷག་པ་\",\"གཟའ་ཕུར་བུ་\",\"གཟའ་པ་སངས་\",\"གཟའ་སྤེན་པ་\",\"གཟའ་ཉི་མ་\"],\n                    namesAbbr: [\"ཟླ་\",\"མིར་\",\"ལྷག་\",\"ཕུར་\",\"སངས་\",\"སྤེན་\",\"ཉི་\"],\n                    namesShort: [\"ཟླ་\",\"མིར་\",\"ལྷག་\",\"ཕུར་\",\"སངས་\",\"སྤེན་\",\"ཉི་\"]\n                },\n                months: {\n                    names: [\"སྤྱི་ཟླ་དངཔ་\",\"སྤྱི་ཟླ་གཉིས་པ་\",\"སྤྱི་ཟླ་གསུམ་པ་\",\"སྤྱི་ཟླ་བཞི་པ\",\"སྤྱི་ཟླ་ལྔ་པ་\",\"སྤྱི་ཟླ་དྲུག་པ\",\"སྤྱི་ཟླ་བདུན་པ་\",\"སྤྱི་ཟླ་བརྒྱད་པ་\",\"སྤྱི་ཟླ་དགུ་པ་\",\"སྤྱི་ཟླ་བཅུ་པ་\",\"སྤྱི་ཟླ་བཅུ་གཅིག་པ་\",\"སྤྱི་ཟླ་བཅུ་གཉིས་པ་\"],\n                    namesAbbr: [\"ཟླ་༡\",\"ཟླ་༢\",\"ཟླ་༣\",\"ཟླ་༤\",\"ཟླ་༥\",\"ཟླ་༦\",\"ཟླ་༧\",\"ཟླ་༨\",\"ཟླ་༩\",\"ཟླ་༡༠\",\"ཟླ་༡༡\",\"ཟླ་༡༢\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"dddd, སྤྱི་ལོ་yyyy MMMM ཚེས་dd\",\n                    F: \"dddd, སྤྱི་ལོ་yyyy MMMM ཚེས་dd ཆུ་ཚོད་h:mm:ss tt\",\n                    g: \"yyyy-MM-dd ཆུ་ཚོད་ h སྐར་མ་ mm tt\",\n                    G: \"yyyy-MM-dd ཆུ་ཚོད་h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"ཆུ་ཚོད་ h སྐར་མ་ mm tt\",\n                    T: \"ཆུ་ཚོད་h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}