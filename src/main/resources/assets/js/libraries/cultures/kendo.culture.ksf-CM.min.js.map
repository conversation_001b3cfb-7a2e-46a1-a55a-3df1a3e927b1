{"version": 3, "sources": ["cultures/kendo.culture.ksf-CM.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,4BACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,SAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,SAC3DC,WAAY,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,OACjDC,YAAa,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,QAEtDC,QACIH,OAAQ,iBAAiB,gBAAgB,eAAe,eAAe,gBAAgB,kBAAkB,kBAAkB,kBAAkB,kBAAkB,gBAAgB,wBAAwB,yBACvMC,WAAY,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,QAEzEG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,WACHC,EAAG,mBACHC,EAAG,4BACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ksf-CM.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ksf-CM\"] = {\n        name: \"ksf-CM\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Central African CFA Franc\",\n                abbr: \"XAF\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 0,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"FCFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"sɔ́ndǝ\",\"lǝndí\",\"maadí\",\"mɛkrɛdí\",\"jǝǝdí\",\"júmb<PERSON>\",\"samdí\"],\n                    namesAbbr: [\"sɔ́n\",\"lǝn\",\"maa\",\"mɛk\",\"jǝǝ\",\"júm\",\"sam\"],\n                    namesShort: [\"sɔ́n\",\"lǝn\",\"maa\",\"mɛk\",\"jǝǝ\",\"júm\",\"sam\"]\n                },\n                months: {\n                    names: [\"ŋwíí a ntɔ́ntɔ\",\"ŋwíí akǝ bɛ́ɛ\",\"ŋwíí akǝ ráá\",\"ŋwíí akǝ nin\",\"ŋwíí akǝ táan\",\"ŋwíí akǝ táafɔk\",\"ŋwíí akǝ táabɛɛ\",\"ŋwíí akǝ táaraa\",\"ŋwíí akǝ táanin\",\"ŋwíí akǝ ntɛk\",\"ŋwíí akǝ ntɛk di bɔ́k\",\"ŋwíí akǝ ntɛk di bɛ́ɛ\"],\n                    namesAbbr: [\"ŋ1\",\"ŋ2\",\"ŋ3\",\"ŋ4\",\"ŋ5\",\"ŋ6\",\"ŋ7\",\"ŋ8\",\"ŋ9\",\"ŋ10\",\"ŋ11\",\"ŋ12\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd d MMMM yyyy\",\n                    F: \"dddd d MMMM yyyy HH:mm:ss\",\n                    g: \"d/M/yyyy HH:mm\",\n                    G: \"d/M/yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}