{"version": 3, "sources": ["cultures/kendo.culture.yo-BJ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,yBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,WAAW,eAAe,SAAS,SAAS,WAAW,iBAC3EC,WAAY,OAAO,MAAM,UAAU,SAAS,SAAS,MAAM,YAC3DC,YAAa,OAAO,MAAM,UAAU,SAAS,SAAS,MAAM,aAEhEC,QACIH,OAAQ,eAAe,aAAa,cAAc,YAAY,cAAc,aAAa,aAAa,YAAY,aAAa,cAAc,YAAY,cACzJC,WAAY,UAAU,QAAQ,SAAS,OAAO,SAAS,QAAQ,QAAQ,OAAO,QAAQ,SAAS,OAAO,UAE1GG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,6BACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.yo-BJ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"yo-BJ\"] = {\n        name: \"yo-BJ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"West African CFA Franc\",\n                abbr: \"XOF\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 0,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"CFA\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Ɔjɔ́ Àìkú\",\"Ɔjɔ́ Ajé\",\"Ɔjɔ́ Ìsɛ́gun\",\"Ɔjɔ́rú\",\"Ɔjɔ́bɔ\",\"Ɔjɔ́ Ɛtì\",\"Ɔjɔ́ Àbámɛ́ta\"],\n                    namesAbbr: [\"Àìkú\",\"Ajé\",\"Ìsɛ́gun\",\"Ɔjɔ́rú\",\"Ɔjɔ́bɔ\",\"Ɛtì\",\"Àbámɛ́ta\"],\n                    namesShort: [\"Àìkú\",\"Ajé\",\"Ìsɛ́gun\",\"Ɔjɔ́rú\",\"Ɔjɔ́bɔ\",\"Ɛtì\",\"Àbámɛ́ta\"]\n                },\n                months: {\n                    names: [\"Oshù Shɛ́rɛ́\",\"Oshù Èrèlè\",\"Oshù Ɛrɛ̀nà\",\"Oshù Ìgbé\",\"Oshù Ɛ̀bibi\",\"Oshù Òkúdu\",\"Oshù Agɛmɔ\",\"Oshù Ògún\",\"Oshù Owewe\",\"Oshù Ɔ̀wàrà\",\"Oshù Bélú\",\"Oshù Ɔ̀pɛ̀\"],\n                    namesAbbr: [\"Shɛ́rɛ́\",\"Èrèlè\",\"Ɛrɛ̀nà\",\"Ìgbé\",\"Ɛ̀bibi\",\"Òkúdu\",\"Agɛmɔ\",\"Ògún\",\"Owewe\",\"Ɔ̀wàrà\",\"Bélú\",\"Ɔ̀pɛ̀\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, d MMMM yyyy\",\n                    F: \"dddd, d MMMM yyyy HH:mm:ss\",\n                    g: \"dd/MM/yyyy HH:mm\",\n                    G: \"dd/MM/yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}