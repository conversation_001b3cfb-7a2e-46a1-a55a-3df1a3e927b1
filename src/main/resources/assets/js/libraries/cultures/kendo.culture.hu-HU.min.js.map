{"version": 3, "sources": ["cultures/kendo.culture.hu-HU.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,mBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,QAAQ,OAAO,SAAS,YAAY,SAAS,WAChEC,WAAY,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,OACvCC,YAAa,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,QAE5CC,QACIH,OAAQ,SAAS,UAAU,UAAU,UAAU,QAAQ,SAAS,SAAS,YAAY,aAAa,UAAU,WAAW,YACvHC,WAAY,OAAO,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,SAAS,OAAO,OAAO,SAEjGG,IAAK,MAAM,MAAM,OACjBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,gBACHC,EAAG,sBACHC,EAAG,8BACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,UACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,KACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.hu-HU.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"hu-HU\"] = {\n        name: \"hu-HU\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Hungarian Forint\",\n                abbr: \"HUF\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"Ft\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"vasárnap\",\"hétf<PERSON>\",\"kedd\",\"szerda\",\"cs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"péntek\",\"szombat\"],\n                    namesAbbr: [\"V\",\"H\",\"K\",\"<PERSON>ze\",\"Cs\",\"P\",\"Szo\"],\n                    namesShort: [\"V\",\"H\",\"K\",\"Sze\",\"Cs\",\"P\",\"Szo\"]\n                },\n                months: {\n                    names: [\"január\",\"február\",\"március\",\"április\",\"m<PERSON>jus\",\"június\",\"július\",\"augusztus\",\"szeptember\",\"október\",\"november\",\"december\"],\n                    namesAbbr: [\"jan.\",\"febr.\",\"márc.\",\"ápr.\",\"máj.\",\"jún.\",\"júl.\",\"aug.\",\"szept.\",\"okt.\",\"nov.\",\"dec.\"]\n                },\n                AM: [\"de.\",\"de.\",\"DE.\"],\n                PM: [\"du.\",\"du.\",\"DU.\"],\n                patterns: {\n                    d: \"yyyy. MM. dd.\",\n                    D: \"yyyy. MMMM d., dddd\",\n                    F: \"yyyy. MMMM d., dddd H:mm:ss\",\n                    g: \"yyyy. MM. dd. H:mm\",\n                    G: \"yyyy. MM. dd. H:mm:ss\",\n                    m: \"MMMM d.\",\n                    M: \"MMMM d.\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"H:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy. MMMM\",\n                    Y: \"yyyy. MMMM\"\n                },\n                \"/\": \". \",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}