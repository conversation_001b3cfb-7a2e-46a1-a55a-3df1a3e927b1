{"version": 3, "sources": ["cultures/kendo.culture.nqo-GN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,gBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,YAAY,SAAS,WAAW,QAAQ,aAAa,YACrEC,WAAY,OAAO,OAAO,OAAO,MAAM,MAAM,OAAO,QACpDC,YAAa,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAE/CC,QACIH,OAAQ,aAAa,UAAU,QAAQ,aAAa,SAAS,aAAa,WAAW,WAAW,UAAU,WAAW,SAAS,gBAC9HC,WAAY,OAAO,OAAO,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,MAAM,SAEzFG,IAAK,IAAI,IAAI,KACbC,IAAK,IAAI,IAAI,KACbC,UACIC,EAAG,aACHC,EAAG,sBACHC,EAAG,kCACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,eACHC,EAAG,eACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.nqo-GN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"nqo-GN\"] = {\n        name: \"nqo-GN\",\n        numberFormat: {\n            pattern: [\"n-\"],\n            decimals: 3,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"%-n\",\"%n\"],\n                decimals: 3,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Guinea Francs\",\n                abbr: \"GNF\",\n                pattern: [\"n- $\",\"$ n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"ߖߕ.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ߞߊ߯ߙߌ\",\"ߞߐ߬ߓߊ߬ߟߏ߲\",\"ߞߐ߬ߟߏ߲\",\"ߞߎߣߎ߲ߟߏ߲\",\"ߓߌߟߏ߲\",\"ߛߌ߬ߣߌ߲߬ߟߏ߲\",\"ߞߍ߲ߘߍߟߏ߲\"],\n                    namesAbbr: [\"ߞߊ߯ߙ\",\"ߞߐ߬ߓ\",\"ߞߐ߬ߟ\",\"ߞߎߣ\",\"ߓߌߟ\",\"ߛߌ߬ߣ\",\"ߞߍ߲ߘ\"],\n                    namesShort: [\"ߞߊ\",\"ߞߐ\",\"ߞߟ\",\"ߞߎ\",\"ߓߌ\",\"ߛߌ\",\"ߞߍ\"]\n                },\n                months: {\n                    names: [\"ߓߌ߲ߠߊߥߎߟߋ߲\",\"ߞߏ߲ߞߏߜߍ\",\"ߕߙߊߓߊ\",\"ߞߏ߲ߞߏߘߌ߬ߓߌ\",\"ߘߓߊ߬ߕߊ\",\"ߥߊ߬ߛߌߥߊ߬ߙߊ\",\"ߞߊ߬ߙߌߝߐ߭\",\"ߘߓߊ߬ߓߌߟߊ\",\"ߕߎߟߊߝߌ߲\",\"ߞߏ߲ߓߌߕߌ߮\",\"ߣߍߣߍߓߊ\",\"ߞߏ߬ߟߌ߲߬ߞߏߟߌ߲\"],\n                    namesAbbr: [\"ߓߌ߲ߠ\",\"ߞߏ߲ߞ\",\"ߕߙߊ\",\"ߞߏ߲ߘ\",\"ߘߓߕ\",\"ߥߊ߬ߛ\",\"ߞߊ߬ߙ\",\"ߘߓߊ߬\",\"ߕߎߟ\",\"ߞߏ߲ߓ\",\"ߣߍߣ\",\"ߞߏ߬ߟ\"]\n                },\n                AM: [\"ߛ\",\"ߛ\",\"ߛ\"],\n                PM: [\"ߥ\",\"ߥ\",\"ߥ\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, MMMM dd, yyyy\",\n                    F: \"dddd, MMMM dd, yyyy tt hh:mm:ss\",\n                    g: \"dd/MM/yyyy tt hh:mm\",\n                    G: \"dd/MM/yyyy tt hh:mm:ss\",\n                    m: \"MMMM ߕߟߋ߬ dd\",\n                    M: \"MMMM ߕߟߋ߬ dd\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"tt hh:mm\",\n                    T: \"tt hh:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}