{"version": 3, "sources": ["cultures/kendo.culture.yo.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,YAAY,WAAW,eAAe,SAAS,SAAS,WAAW,iBAC3EC,WAAY,OAAO,MAAM,UAAU,SAAS,SAAS,MAAM,YAC3DC,YAAa,OAAO,MAAM,UAAU,SAAS,SAAS,MAAM,aAEhEC,QACIH,OAAQ,aAAa,YAAY,aAAa,WAAW,aAAa,YAAY,YAAY,WAAW,YAAY,aAAa,WAAW,aAC7IC,WAAY,SAAS,QAAQ,SAAS,OAAO,SAAS,QAAQ,QAAQ,OAAO,QAAQ,SAAS,OAAO,UAEzGG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,+BACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.yo.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"yo\"] = {\n        name: \"yo\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"-$n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"₦\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Ọjọ́ Àìkú\",\"Ọjọ́ Ajé\",\"Ọjọ́ Ìsẹ́gun\",\"Ọjọ́rú\",\"Ọjọ́bọ\",\"Ọjọ́ Ẹtì\",\"Ọjọ́ Àbámẹ́ta\"],\n                    namesAbbr: [\"Àìkú\",\"Ajé\",\"Ìsẹ́gun\",\"Ọjọ́rú\",\"Ọjọ́bọ\",\"Ẹtì\",\"Àbámẹ́ta\"],\n                    namesShort: [\"Àìkú\",\"Ajé\",\"Ìsẹ́gun\",\"Ọjọ́rú\",\"Ọjọ́bọ\",\"Ẹtì\",\"Àbámẹ́ta\"]\n                },\n                months: {\n                    names: [\"Oṣù Ṣẹ́rẹ́\",\"Oṣù Èrèlè\",\"Oṣù Ẹrẹ̀nà\",\"Oṣù Ìgbé\",\"Oṣù Ẹ̀bibi\",\"Oṣù Òkúdu\",\"Oṣù Agẹmọ\",\"Oṣù Ògún\",\"Oṣù Owewe\",\"Oṣù Ọ̀wàrà\",\"Oṣù Bélú\",\"Oṣù Ọ̀pẹ̀\"],\n                    namesAbbr: [\"Ṣẹ́rẹ́\",\"Èrèlè\",\"Ẹrẹ̀nà\",\"Ìgbé\",\"Ẹ̀bibi\",\"Òkúdu\",\"Agẹmọ\",\"Ògún\",\"Owewe\",\"Ọ̀wàrà\",\"Bélú\",\"Ọ̀pẹ̀\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, d MMMM yyyy\",\n                    F: \"dddd, d MMMM yyyy h:mm:ss tt\",\n                    g: \"dd/MM/yyyy h:mm tt\",\n                    G: \"dd/MM/yyyy h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}