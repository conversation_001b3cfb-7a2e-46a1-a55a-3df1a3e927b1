{"version": 3, "sources": ["cultures/kendo.culture.quz-BO.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,qBACNU,KAAM,MACNR,SAAU,QAAQ,OAClBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,YAAY,YAAY,cAAc,eAAoB,aAAa,eAC1FC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAW,MAAM,OACrDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,cAAc,cAAc,eAAe,SAAS,UAAU,aAAa,aAAa,eAAe,YAAY,WAAW,YAAiB,eACvJC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAElFG,IAAK,OAAO,OAAO,QACnBC,IAAK,OAAO,OAAO,QACnBC,UACIC,EAAG,aACHC,EAAG,+BACHC,EAAG,2CACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,cACHC,EAAG,cACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,iBACHC,EAAG,kBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.quz-BO.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"quz-BO\"] = {\n        name: \"quz-B<PERSON>\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Bolivian Boliviano\",\n                abbr: \"BOB\",\n                pattern: [\"($ n)\",\"$ n\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"Bs.\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"intichaw\",\"killachaw\",\"atipachaw\",\"quyllurchaw\",\"Ch\\u0027 askachaw\",\"<PERSON><PERSON><PERSON>aw\",\"k\\u0027uychichaw\"],\n                    namesAbbr: [\"int\",\"kil\",\"ati\",\"quy\",\"Ch\\u0027\",\"Ill\",\"k\\u0027u\"],\n                    namesShort: [\"d\",\"k\",\"a\",\"m\",\"h\",\"b\",\"k\"]\n                },\n                months: {\n                    names: [\"Qulla puquy\",\"Hatun puquy\",\"Pauqar waray\",\"ayriwa\",\"Aymuray\",\"Inti raymi\",\"Anta Sitwa\",\"Qhapaq Sitwa\",\"Uma raymi\",\"Kantaray\",\"Ayamarq\\u0027a\",\"Kapaq Raymi\"],\n                    namesAbbr: [\"Qul\",\"Hat\",\"Pau\",\"ayr\",\"Aym\",\"Int\",\"Ant\",\"Qha\",\"Uma\",\"Kan\",\"Aya\",\"Kap\"]\n                },\n                AM: [\"a.m.\",\"a.m.\",\"A.M.\"],\n                PM: [\"p.m.\",\"p.m.\",\"P.M.\"],\n                patterns: {\n                    d: \"dd/MM/yyyy\",\n                    D: \"dddd, dd' de 'MMMM' de 'yyyy\",\n                    F: \"dddd, dd' de 'MMMM' de 'yyyy hh:mm:ss tt\",\n                    g: \"dd/MM/yyyy hh:mm tt\",\n                    G: \"dd/MM/yyyy hh:mm:ss tt\",\n                    m: \"d 'de' MMMM\",\n                    M: \"d 'de' MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"hh:mm tt\",\n                    T: \"hh:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM' de 'yyyy\",\n                    Y: \"MMMM' de 'yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}