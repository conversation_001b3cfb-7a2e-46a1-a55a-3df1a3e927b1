{"version": 3, "sources": ["cultures/kendo.culture.lrc-IQ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,cACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,QAGhBG,WACIC,UACIC,MACIC,OAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAC5CC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,OAChDC,YAAa,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAErDC,QACIH,OAAQ,SAAS,SAAS,OAAO,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,UAAU,UAAU,SAAS,UACpGC,WAAY,SAAS,SAAS,OAAO,QAAQ,MAAM,QAAQ,QAAQ,QAAQ,UAAU,UAAU,SAAS,WAE5GG,IAAK,KAAK,KAAK,MACfC,IAAK,KAAK,KAAK,MACfC,UACIC,EAAG,aACHC,EAAG,oBACHC,EAAG,+BACHC,EAAG,qBACHC,EAAG,wBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.lrc-IQ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"lrc-IQ\"] = {\n        name: \"lrc-IQ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Iraqi Dinar\",\n                abbr: \"IQD\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"IQD\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"Sun\",\"Mon\",\"<PERSON>e\",\"Wed\",\"<PERSON>hu\",\"<PERSON><PERSON>\",\"Sat\"],\n                    namesAbbr: [\"<PERSON>\",\"<PERSON>\",\"<PERSON>e\",\"Wed\",\"Thu\",\"Fri\",\"Sat\"],\n                    namesShort: [\"Sun\",\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\"]\n                },\n                months: {\n                    names: [\"جانڤیە\",\"فئڤریە\",\"مارس\",\"آڤریل\",\"مئی\",\"جوٙأن\",\"جوٙلا\",\"آگوست\",\"سئپتامر\",\"ئوکتوڤر\",\"نوڤامر\",\"دئسامر\"],\n                    namesAbbr: [\"جانڤیە\",\"فئڤریە\",\"مارس\",\"آڤریل\",\"مئی\",\"جوٙأن\",\"جوٙلا\",\"آگوست\",\"سئپتامر\",\"ئوکتوڤر\",\"نوڤامر\",\"دئسامر\"]\n                },\n                AM: [\"AM\",\"am\",\"AM\"],\n                PM: [\"PM\",\"pm\",\"PM\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"yyyy MMMM d, dddd\",\n                    F: \"yyyy MMMM d, dddd h:mm:ss tt\",\n                    g: \"yyyy-MM-dd h:mm tt\",\n                    G: \"yyyy-MM-dd h:mm:ss tt\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"yyyy MMMM\",\n                    Y: \"yyyy MMMM\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 6\n            }\n        }\n    }\n})(this);\n}));"]}