{"version": 3, "sources": ["cultures/kendo.culture.hi-IN.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,eACNU,KAAM,MACNR,SAAU,OAAO,MACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,UAAU,SAAS,UAAU,WAAW,UAClEC,WAAY,OAAO,OAAO,QAAQ,OAAO,QAAQ,SAAS,QAC1DC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,QAAQ,QAAQ,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,UAAU,UAAU,SAAS,WACjGC,WAAY,QAAQ,QAAQ,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,UAAU,UAAU,SAAS,YAEzGG,IAAK,YAAY,YAAY,aAC7BC,IAAK,UAAU,UAAU,WACzBC,UACIC,EAAG,aACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.hi-IN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"hi-IN\"] = {\n        name: \"hi-IN\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Indian Rupee\",\n                abbr: \"INR\",\n                pattern: [\"$ -n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"रविवार\",\"सोमवार\",\"मंगलवार\",\"बुधवार\",\"गुरुवार\",\"शुक्रवार\",\"शनिवार\"],\n                    namesAbbr: [\"रवि.\",\"सोम.\",\"मंगल.\",\"बुध.\",\"गुरु.\",\"शुक्र.\",\"शनि.\"],\n                    namesShort: [\"र\",\"स\",\"म\",\"ब\",\"ग\",\"श\",\"श\"]\n                },\n                months: {\n                    names: [\"जनवरी\",\"फरवरी\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुलाई\",\"अगस्त\",\"सितम्बर\",\"अक्तूबर\",\"नवम्बर\",\"दिसम्बर\"],\n                    namesAbbr: [\"जनवरी\",\"फरवरी\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुलाई\",\"अगस्त\",\"सितम्बर\",\"अक्तूबर\",\"नवम्बर\",\"दिसम्बर\"]\n                },\n                AM: [\"पूर्वाह्न\",\"पूर्वाह्न\",\"पूर्वाह्न\"],\n                PM: [\"अपराह्न\",\"अपराह्न\",\"अपराह्न\"],\n                patterns: {\n                    d: \"dd-MM-yyyy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd-MM-yyyy HH:mm\",\n                    G: \"dd-MM-yyyy HH:mm:ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}