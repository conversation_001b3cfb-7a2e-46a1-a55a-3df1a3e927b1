{"version": 3, "sources": ["cultures/kendo.culture.ar-MR.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,sBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,UAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,UAAU,WAAW,WAAW,SAAS,SAAS,SAClEC,WAAY,QAAQ,UAAU,WAAW,WAAW,SAAS,SAAS,SACtEC,YAAa,QAAQ,UAAU,WAAW,WAAW,SAAS,SAAS,UAE3EC,QACIH,OAAQ,QAAQ,SAAS,OAAO,QAAQ,OAAO,QAAQ,QAAQ,OAAO,QAAQ,SAAS,SAAS,SAChGC,WAAY,QAAQ,SAAS,OAAO,QAAQ,OAAO,QAAQ,QAAQ,OAAO,QAAQ,SAAS,SAAS,UAExGG,IAAK,IAAI,IAAI,KACbC,IAAK,IAAI,IAAI,KACbC,UACIC,EAAG,WACHC,EAAG,qBACHC,EAAG,gCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,UACHC,EAAG,aACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ar-MR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ar-MR\"] = {\n        name: \"ar-MR\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \".\",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"٪\"\n            },\n            currency: {\n                name: \"Mauritanian Ouguiya\",\n                abbr: \"MRO\",\n                pattern: [\"-$ n\",\"$ n\"],\n                decimals: 0,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"أ.م.‏\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"الأحد\",\"الاثنين\",\"الثلاثاء\",\"الأربعاء\",\"الخميس\",\"الجمعة\",\"السبت\"],\n                    namesAbbr: [\"الأحد\",\"الاثنين\",\"الثلاثاء\",\"الأربعاء\",\"الخميس\",\"الجمعة\",\"السبت\"],\n                    namesShort: [\"الأحد\",\"الاثنين\",\"الثلاثاء\",\"الأربعاء\",\"الخميس\",\"الجمعة\",\"السبت\"]\n                },\n                months: {\n                    names: [\"يناير\",\"فبراير\",\"مارس\",\"إبريل\",\"مايو\",\"يونيو\",\"يوليو\",\"أغشت\",\"شتمبر\",\"أكتوبر\",\"نوفمبر\",\"دجمبر\"],\n                    namesAbbr: [\"يناير\",\"فبراير\",\"مارس\",\"إبريل\",\"مايو\",\"يونيو\",\"يوليو\",\"أغشت\",\"شتمبر\",\"أكتوبر\",\"نوفمبر\",\"دجمبر\"]\n                },\n                AM: [\"ص\",\"ص\",\"ص\"],\n                PM: [\"م\",\"م\",\"م\"],\n                patterns: {\n                    d: \"d/M/yyyy\",\n                    D: \"dddd، d MMMM، yyyy\",\n                    F: \"dddd، d MMMM، yyyy h:mm:ss tt\",\n                    g: \"d/M/yyyy h:mm tt\",\n                    G: \"d/M/yyyy h:mm:ss tt\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"h:mm tt\",\n                    T: \"h:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}