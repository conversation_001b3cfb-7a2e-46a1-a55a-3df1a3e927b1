{"version": 3, "sources": ["cultures/kendo.culture.sma-SE.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,WACXC,KAAM,SACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,gBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,OAGhBG,WACIC,UACIC,MACIC,OAAQ,UAAU,SAAS,SAAS,cAAc,UAAU,cAAc,gBAC1EC,WAAY,MAAM,MAAM,MAAM,OAAO,OAAO,QAAQ,QACpDC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,YAAY,SAAS,UAAU,WAAW,WAAW,SAAS,WAAW,UAAU,WAAW,QAAQ,QAAQ,SACtHC,WAAY,QAAQ,QAAQ,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,SAEhGG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,0BACHC,EAAG,mCACHC,EAAG,mBACHC,EAAG,sBACHC,EAAG,eACHC,EAAG,eACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.sma-SE.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"sma-SE\"] = {\n        name: \"sma-SE\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n %\",\"n %\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Swedish Krona\",\n                abbr: \"SEK\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \".\",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"kr\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"aejlege\",\"må<PERSON>a\",\"dæj<PERSON>\",\"gaskev<PERSON><PERSON><PERSON><PERSON>\",\"du<PERSON><PERSON>\",\"bearja<PERSON><PERSON>\",\"la<PERSON><PERSON><PERSON><PERSON>\"],\n                    namesAbbr: [\"aej\",\"måa\",\"dæj\",\"gask\",\"duar\",\"bearj\",\"laav\"],\n                    namesShort: [\"a\",\"m\",\"d\",\"g\",\"d\",\"b\",\"l\"]\n                },\n                months: {\n                    names: [\"ts<PERSON>engele\",\"goevte\",\"njoktje\",\"voerhtje\",\"suehpede\",\"ruffie\",\"snjaltje\",\"m<PERSON>etske\",\"sk<PERSON>erede\",\"golke\",\"rahka\",\"goeve\"],\n                    names<PERSON>bbr: [\"ts<PERSON>en\",\"goevt\",\"njok\",\"voer\",\"sueh\",\"ruff\",\"snja\",\"mïet\",\"skïer\",\"golk\",\"rahk\",\"goev\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"yyyy-MM-dd\",\n                    D: \"dddd, MMMM d'. b. 'yyyy\",\n                    F: \"dddd, MMMM d'. b. 'yyyy HH:mm:ss\",\n                    g: \"yyyy-MM-dd HH:mm\",\n                    G: \"yyyy-MM-dd HH:mm:ss\",\n                    m: \"MMMM d'. b.'\",\n                    M: \"MMMM d'. b.'\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}