{"version": 3, "sources": ["cultures/kendo.culture.gu.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAa,IACfC,KAAM,KACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,KAEZC,UACIT,KAAM,GACNU,KAAM,GACNR,SAAU,OAAO,MACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,EAAE,GACdE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,SAAS,SAAS,UAAU,SAAS,UAAU,WAAW,UAClEC,WAAY,MAAM,MAAM,OAAO,MAAM,OAAO,QAAQ,OACpDC,YAAa,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAE9CC,QACIH,OAAQ,YAAY,YAAY,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,YAAY,UAAU,UAAU,YAC5GC,WAAY,SAAS,SAAS,QAAQ,SAAS,KAAK,MAAM,QAAQ,KAAK,QAAQ,QAAQ,MAAM,SAEjGG,IAAK,iBAAiB,iBAAiB,kBACvCC,IAAK,iBAAiB,iBAAiB,kBACvCC,UACIC,EAAG,WACHC,EAAG,eACHC,EAAG,wBACHC,EAAG,iBACHC,EAAG,oBACHC,EAAG,UACHC,EAAG,UACHC,EAAG,gCACHC,EAAG,QACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.gu.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"gu\"] = {\n        name: \"gu\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3,2],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"\",\n                abbr: \"\",\n                pattern: [\"$ -n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3,2],\n                symbol: \"₹\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"રવિવાર\",\"સોમવાર\",\"મંગળવાર\",\"બુધવાર\",\"ગુરુવાર\",\"શુક્રવાર\",\"શનિવાર\"],\n                    namesAbbr: [\"રવિ\",\"સોમ\",\"મંગળ\",\"બુધ\",\"ગુરુ\",\"શુક્ર\",\"શનિ\"],\n                    namesShort: [\"ર\",\"સો\",\"મં\",\"બુ\",\"ગુ\",\"શુ\",\"શ\"]\n                },\n                months: {\n                    names: [\"જાન્યુઆરી\",\"ફેબ્રુઆરી\",\"માર્ચ\",\"એપ્રિલ\",\"મે\",\"જૂન\",\"જુલાઈ\",\"ઑગસ્ટ\",\"સપ્ટેમ્બર\",\"ઑક્ટોબર\",\"નવેમ્બર\",\"ડિસેમ્બર\"],\n                    namesAbbr: [\"જાન્યુ\",\"ફેબ્રુ\",\"માર્ચ\",\"એપ્રિલ\",\"મે\",\"જૂન\",\"જુલાઈ\",\"ઑગ\",\"સપ્ટે\",\"ઑક્ટો\",\"નવે\",\"ડિસે\"]\n                },\n                AM: [\"પૂર્વ મધ્યાહ્ન\",\"પૂર્વ મધ્યાહ્ન\",\"પૂર્વ મધ્યાહ્ન\"],\n                PM: [\"ઉત્તર મધ્યાહ્ન\",\"ઉત્તર મધ્યાહ્ન\",\"ઉત્તર મધ્યાહ્ન\"],\n                patterns: {\n                    d: \"dd-MM-yy\",\n                    D: \"dd MMMM yyyy\",\n                    F: \"dd MMMM yyyy HH:mm:ss\",\n                    g: \"dd-MM-yy HH:mm\",\n                    G: \"dd-MM-yy HH:mm:ss\",\n                    m: \"dd MMMM\",\n                    M: \"dd MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"HH:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"-\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}