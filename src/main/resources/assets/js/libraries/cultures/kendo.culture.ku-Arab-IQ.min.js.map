{"version": 3, "sources": ["cultures/kendo.culture.ku-Arab-IQ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,eACXC,KAAM,aACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,cACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,UAGhBG,WACIC,UACIC,MACIC,OAAQ,WAAW,WAAW,UAAU,YAAY,YAAY,QAAQ,SACxEC,WAAY,WAAW,WAAW,UAAU,YAAY,YAAY,QAAQ,SAC5EC,YAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAEzCC,QACIH,OAAQ,gBAAgB,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,SAAS,MAAM,UAAU,eAAe,eAAe,gBAC1HC,WAAY,gBAAgB,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,SAAS,MAAM,UAAU,eAAe,eAAe,iBAElIG,IAAK,MAAM,MAAM,OACjBC,IAAK,MAAM,MAAM,OACjBC,UACIC,EAAG,aACHC,EAAG,sBACHC,EAAG,kCACHC,EAAG,sBACHC,EAAG,yBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,WACHC,EAAG,cACHC,EAAG,iCACHC,EAAG,aACHC,EAAG,cAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.ku-Arab-IQ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"ku-Arab-IQ\"] = {\n        name: \"ku-Arab-IQ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"٪\"\n            },\n            currency: {\n                name: \"Iraqi Dinar\",\n                abbr: \"IQD\",\n                pattern: [\"$-n\",\"$n\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"د.ع.‏\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"یەکشەممە\",\"دووشەممە\",\"سێشەممە\",\"چوارشەممە\",\"پێنجشەممە\",\"ھەینی\",\"شەممە\"],\n                    namesAbbr: [\"یەکشەممە\",\"دووشەممە\",\"سێشەممە\",\"چوارشەممە\",\"پێنجشەممە\",\"ھەینی\",\"شەممە\"],\n                    namesShort: [\"ی\",\"د\",\"س\",\"چ\",\"پ\",\"ھ\",\"ش\"]\n                },\n                months: {\n                    names: [\"کانوونی دووەم\",\"شوبات\",\"ئازار\",\"نیسان\",\"ئایار\",\"حوزەیران\",\"تەمووز\",\"ئاب\",\"ئەیلوول\",\"تشرینی یەکەم\",\"تشرینی دووەم\",\"کانونی یەکەم\"],\n                    namesAbbr: [\"کانوونی دووەم\",\"شوبات\",\"ئازار\",\"نیسان\",\"ئایار\",\"حوزەیران\",\"تەمووز\",\"ئاب\",\"ئەیلوول\",\"تشرینی یەکەم\",\"تشرینی دووەم\",\"کانونی یەکەم\"]\n                },\n                AM: [\"پ.ن\",\"پ.ن\",\"پ.ن\"],\n                PM: [\"د.ن\",\"د.ن\",\"د.ن\"],\n                patterns: {\n                    d: \"yyyy/MM/dd\",\n                    D: \"dddd, dd MMMM, yyyy\",\n                    F: \"dddd, dd MMMM, yyyy hh:mm:ss tt\",\n                    g: \"yyyy/MM/dd hh:mm tt\",\n                    G: \"yyyy/MM/dd hh:mm:ss tt\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"hh:mm tt\",\n                    T: \"hh:mm:ss tt\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM, yyyy\",\n                    Y: \"MMMM, yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}