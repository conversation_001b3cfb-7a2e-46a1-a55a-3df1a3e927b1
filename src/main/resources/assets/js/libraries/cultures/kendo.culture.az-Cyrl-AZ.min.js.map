{"version": 3, "sources": ["cultures/kendo.culture.az-Cyrl-AZ.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,eACXC,KAAM,aACNC,cACIC,SAAU,MACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,oBACNU,KAAM,MACNR,SAAU,OAAO,OACjBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,QAAQ,eAAe,kBAAkB,WAAW,cAAc,OAAO,SACjFC,WAAY,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KACvCC,YAAa,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,MAE5CC,QACIH,OAAQ,SAAS,SAAS,OAAO,QAAQ,MAAM,OAAO,OAAO,SAAS,WAAW,UAAU,SAAS,UACpGC,WAAY,MAAM,MAAM,MAAM,MAAM,MAAM,OAAO,OAAO,MAAM,MAAM,MAAM,MAAM,QAEpFG,IAAK,IACLC,IAAK,IACLC,UACIC,EAAG,aACHC,EAAG,cACHC,EAAG,uBACHC,EAAG,kBACHC,EAAG,sBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,YACHC,EAAG,aAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.az-Cyrl-AZ.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"az-Cyrl-AZ\"] = {\n        name: \"az-Cyrl-AZ\",\n        numberFormat: {\n            pattern: [\"-n\"],\n            decimals: 2,\n            \",\": \" \",\n            \".\": \",\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Azerbaijani Manat\",\n                abbr: \"AZN\",\n                pattern: [\"-n $\",\"n $\"],\n                decimals: 2,\n                \",\": \" \",\n                \".\": \",\",\n                groupSize: [3],\n                symbol: \"₼\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"базар\",\"базар ертәси\",\"чәршәнбә ахшамы\",\"чәршәнбә\",\"ҹүмә ахшамы\",\"ҹүмә\",\"шәнбә\"],\n                    namesAbbr: [\"Б\",\"Бе\",\"Ча\",\"Ч\",\"Ҹа\",\"Ҹ\",\"Ш\"],\n                    namesShort: [\"Б\",\"Бе\",\"Ча\",\"Ч\",\"Ҹа\",\"Ҹ\",\"Ш\"]\n                },\n                months: {\n                    names: [\"jанвар\",\"феврал\",\"март\",\"апрел\",\"мај\",\"ијун\",\"ијул\",\"август\",\"сентјабр\",\"октјабр\",\"нојабр\",\"декабр\"],\n                    namesAbbr: [\"Јан\",\"Фев\",\"Мар\",\"Апр\",\"Мај\",\"Ијун\",\"Ијул\",\"Авг\",\"Сен\",\"Окт\",\"Ноя\",\"Дек\"]\n                },\n                AM: [\"\"],\n                PM: [\"\"],\n                patterns: {\n                    d: \"dd.MM.yyyy\",\n                    D: \"d MMMM yyyy\",\n                    F: \"d MMMM yyyy HH:mm:ss\",\n                    g: \"dd.MM.yyyy H:mm\",\n                    G: \"dd.MM.yyyy HH:mm:ss\",\n                    m: \"MMMM d\",\n                    M: \"MMMM d\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"MMMM yyyy\",\n                    Y: \"MMMM yyyy\"\n                },\n                \"/\": \".\",\n                \":\": \":\",\n                firstDay: 1\n            }\n        }\n    }\n})(this);\n}));"]}