{"version": 3, "sources": ["cultures/kendo.culture.km-KH.js"], "names": ["f", "define", "amd", "window", "undefined", "kendo", "cultures", "name", "numberFormat", "pattern", "decimals", ",", ".", "groupSize", "percent", "symbol", "currency", "abbr", "calendars", "standard", "days", "names", "namesAbbr", "namesShort", "months", "AM", "PM", "patterns", "d", "D", "F", "g", "G", "m", "M", "s", "t", "T", "u", "y", "Y", "/", ":", "firstDay", "this"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAQC,GACfC,MAAMC,SAAS,UACXC,KAAM,QACNC,cACIC,SAAU,OACVC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZC,SACIL,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,KAEZC,UACIT,KAAM,iBACNU,KAAM,MACNR,SAAU,MAAM,MAChBC,SAAU,EACVC,IAAK,IACLC,IAAK,IACLC,WAAY,GACZE,OAAQ,MAGhBG,WACIC,UACIC,MACIC,OAAQ,cAAc,YAAY,aAAa,UAAU,iBAAiB,YAAY,YACtFC,WAAY,QAAQ,KAAK,KAAK,KAAK,QAAQ,MAAM,MACjDC,YAAa,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAE5CC,QACIH,OAAQ,OAAO,SAAS,OAAO,OAAO,OAAO,SAAS,SAAS,OAAO,QAAQ,OAAO,WAAW,QAChGC,WAAY,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,OAE9DG,IAAK,QAAQ,QAAQ,SACrBC,IAAK,QAAQ,QAAQ,SACrBC,UACIC,EAAG,WACHC,EAAG,cACHC,EAAG,uBACHC,EAAG,gBACHC,EAAG,oBACHC,EAAG,SACHC,EAAG,SACHC,EAAG,gCACHC,EAAG,OACHC,EAAG,WACHC,EAAG,iCACHC,EAAG,uBACHC,EAAG,wBAEPC,IAAK,IACLC,IAAK,IACLC,SAAU,MAIvBC", "file": "kendo.culture.km-KH.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function( window, undefined ) {\n    kendo.cultures[\"km-KH\"] = {\n        name: \"km-KH\",\n        numberFormat: {\n            pattern: [\"- n\"],\n            decimals: 2,\n            \",\": \",\",\n            \".\": \".\",\n            groupSize: [3],\n            percent: {\n                pattern: [\"-n%\",\"n%\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"%\"\n            },\n            currency: {\n                name: \"Cambodian Riel\",\n                abbr: \"KHR\",\n                pattern: [\"-n$\",\"n$\"],\n                decimals: 2,\n                \",\": \",\",\n                \".\": \".\",\n                groupSize: [3],\n                symbol: \"៛\"\n            }\n        },\n        calendars: {\n            standard: {\n                days: {\n                    names: [\"ថ្ងៃអាទិត្យ\",\"ថ្ងៃច័ន្ទ\",\"ថ្ងៃអង្គារ\",\"ថ្ងៃពុធ\",\"ថ្ងៃព្រហស្បតិ៍\",\"ថ្ងៃសុក្រ\",\"ថ្ងៃសៅរ៍\"],\n                    namesAbbr: [\"អាទិ.\",\"ច.\",\"អ.\",\"ពុ\",\"ព្រហ.\",\"សុ.\",\"ស.\"],\n                    namesShort: [\"អា\",\"ច\",\"អ\",\"ពុ\",\"ព\",\"សុ\",\"ស\"]\n                },\n                months: {\n                    names: [\"មករា\",\"កុម្ភៈ\",\"មិនា\",\"មេសា\",\"ឧសភា\",\"មិថុនា\",\"កក្កដា\",\"សីហា\",\"កញ្ញា\",\"តុលា\",\"វិច្ឆិកា\",\"ធ្នូ\"],\n                    namesAbbr: [\"១\",\"២\",\"៣\",\"៤\",\"៥\",\"៦\",\"៧\",\"៨\",\"៩\",\"១០\",\"១១\",\"១២\"]\n                },\n                AM: [\"ព្រឹក\",\"ព្រឹក\",\"ព្រឹក\"],\n                PM: [\"ល្ងាច\",\"ល្ងាច\",\"ល្ងាច\"],\n                patterns: {\n                    d: \"dd/MM/yy\",\n                    D: \"d MMMM yyyy\",\n                    F: \"d MMMM yyyy HH:mm:ss\",\n                    g: \"dd/MM/yy H:mm\",\n                    G: \"dd/MM/yy HH:mm:ss\",\n                    m: \"d MMMM\",\n                    M: \"d MMMM\",\n                    s: \"yyyy'-'MM'-'dd'T'HH':'mm':'ss\",\n                    t: \"H:mm\",\n                    T: \"HH:mm:ss\",\n                    u: \"yyyy'-'MM'-'dd HH':'mm':'ss'Z'\",\n                    y: \"'ខែ' MM 'ឆ្នាំ' yyyy\",\n                    Y: \"'ខែ' MM 'ឆ្នាំ' yyyy\"\n                },\n                \"/\": \"/\",\n                \":\": \":\",\n                firstDay: 0\n            }\n        }\n    }\n})(this);\n}));"]}