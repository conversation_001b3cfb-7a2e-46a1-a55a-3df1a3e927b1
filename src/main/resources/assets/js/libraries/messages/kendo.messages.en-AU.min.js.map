{"version": 3, "sources": ["messages/kendo.messages.en-AU.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "overflowAnchor", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "units", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "and", "or", "selectValue", "value", "isnull", "isnotnull", "isempty", "isnotempty", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "columnMenu", "TreeView", "Upload", "localization", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,QACTC,OAAU,SACVC,QAAW,WACXC,WAAc,iBAMZV,MAAMC,GAAGU,cACbX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,QACTC,OAAU,SACVC,QAAW,WACXC,WAAc,iBAMZV,MAAMC,GAAGW,aACbZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,iBACjBC,eAAkB,kBAClBC,OAAU,SACVC,QAAW,UACXC,KAAQ,OACRC,SAAY,kBACZC,KAAQ,OACRC,OAAU,YAMRpB,MAAMC,GAAGoB,SACbrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,OACRC,OAAU,SACVC,UAAa,YACbC,cAAiB,gBACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,cACjBC,YAAe,kBACfC,aAAgB,mBAChBC,YAAe,UACfC,oBAAuB,wBACvBC,kBAAqB,sBACrBC,OAAU,SACVC,QAAW,UACXC,WAAc,mBACdC,OAAU,mBACVC,YAAe,eACfC,WAAc,cACdC,WAAc,cACdC,SAAY,YACZC,SAAY,qBACZC,gBAAmB,mBACnBC,SAAY,mBACZC,gBAAmB,mBACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,QACbC,UAAa,mBACbC,MAAS,SACTC,YAAe,eACfC,WAAc,SACdC,eAAkB,aAClBC,QAAW,cACXC,YAAe,OACfC,YAAe,OACfC,gBAAmB,sEACnBC,WAAc,yCACdC,cAAiB,+FACjBC,kBAAqB,4CACrBC,gBAAmB,cACnBC,aAAgB,iBAChBC,WAAc,aACdC,YAAe,cACfC,eAAkB,cAClBC,UAAa,QACbC,eAAkB,cAClBC,SAAY,OACZC,YAAe,UACfC,oBAAuB,0BACvBC,aAAgB,SAChBC,aAAgB,SAChBC,sBAAyB,KACzBC,aAAgB,SAChBC,gBAAmB,mBACnBC,YAAe,eACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,UAAa,aACbC,aAAgB,gBAChBC,SAAY,KACZC,YAAe,eACfC,SAAY,QACZC,QAAW,OACXC,iBAAoB,gBACpBC,QAAW,UACXC,QAAW,UACXC,MAAS,QACTC,OAAU,SACVC,MAAS,QACTC,YAAe,eACfC,YAAe,eACfC,WAAc,cACdC,UAAa,YACbC,WAAc,aACdC,SAAY,YACZC,GAAM,KACNC,OAAU,SACVC,YAAe,eACfC,gBAAmB,mBACnBC,SAAY,YACZC,0BAA6B,+BAC7BC,UAAa,aACbC,YAAe,eACfC,WAAc,cACdC,aAAgB,iBAChBC,eAAkB,mBAClBC,cAAiB,kBACjBC,gBAAmB,oBACnBC,kBAAqB,sBACrBC,iBAAoB,qBACpBC,gBAAmB,oBACnBC,kBAAqB,sBACrBC,iBAAoB,qBACpBC,YAAe,mBACfrG,QAAW,UACXsG,KAAQ,OACRC,eAAkB,sBAMhBvH,MAAMC,GAAGuH,cACbxH,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuH,YAAYrH,UAAUC,QAAQC,UACpD+C,WAAc,SACdE,QAAW,aACXE,YAAe,OACfD,YAAe,OACfK,kBAAqB,4CACrBT,YAAe,eACfO,WAAc,yCACdD,gBAAmB,sEACnBE,cAAiB,+FACjB8D,cAAiB,2BACjBC,OAAU,YAMR1H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQC,UACnDuH,OAAU,UACVC,QAAW,WACX9G,OAAU,SACV+G,MAAS,QACTC,SAAY,cAMV/H,MAAMC,GAAG0H,aACb3H,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0H,WAAWxH,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,cACNC,IAAO,kBACPC,WAAc,cACdC,SAAY,WACZC,eAAkB,mBAClBC,SAAY,aAEdC,QACEN,GAAM,cACNC,IAAO,kBACPM,IAAO,8BACPC,GAAM,kBACNC,IAAO,2BACPC,GAAM,gBAERC,MACEX,GAAM,cACNC,IAAO,kBACPM,IAAO,uBACPC,GAAM,WACNC,IAAO,wBACPC,GAAM,aAERE,OACEZ,GAAM,cACNC,IAAO,sBAOPnI,MAAMC,GAAG8I,aACb/I,MAAMC,GAAG8I,WAAW5I,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8I,WAAW5I,UAAUC,QAAQC,UACnD2I,KAAQ,8BACRpB,OAAU,UACVC,QAAW,WACX9G,OAAU,SACV+G,MAAS,QACTmB,IAAO,MACPC,GAAM,KACNC,YAAe,iBACfpB,SAAY,WACZqB,MAAS,QACT5I,OAAU,YAMRR,MAAMC,GAAG8I,aACb/I,MAAMC,GAAG8I,WAAW5I,UAAUC,QAAQ4H,UACtClI,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8I,WAAW5I,UAAUC,QAAQ4H,WACnDC,QACEC,GAAM,cACNC,IAAO,kBACPC,WAAc,cACdC,SAAY,WACZC,eAAkB,mBAClBC,SAAY,YACZc,OAAU,UACVC,UAAa,cACbC,QAAW,WACXC,WAAc,gBAEhBhB,QACEN,GAAM,cACNC,IAAO,kBACPM,IAAO,8BACPC,GAAM,kBACNC,IAAO,2BACPC,GAAM,eACNS,OAAU,UACVC,UAAa,eAEfT,MACEX,GAAM,cACNC,IAAO,kBACPM,IAAO,uBACPC,GAAM,WACNC,IAAO,wBACPC,GAAM,YACNS,OAAU,UACVC,UAAa,eAEfR,OACEZ,GAAM,cACNC,IAAO,kBACPkB,OAAU,UACVC,UAAa,kBAObtJ,MAAMC,GAAGwJ,mBACbzJ,MAAMC,GAAGwJ,iBAAiBtJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwJ,iBAAiBtJ,UAAUC,QAAQC,UACzDqJ,SAAY,aACZ5B,MAAS,QACT/G,OAAU,SACV2G,OAAU,YAMR1H,MAAMC,GAAG0J,QACb3J,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,UAC9CuJ,SACEC,SAAY,YACZC,OAAU,WACVC,YAAe,YACfC,aAAgB,YAChBC,IAAO,iBAETzJ,OAAU,SACV0J,4BAA+B,oBAC/BC,sBAAyB,cACzBC,QAAW,SACXC,QACEC,aAAgB,SAChBC,YAAe,OACfC,IAAO,MACPC,gBAAmB,WACnBC,UAAa,YACbC,qBAAwB,YACxBC,gBAAmB,YACnBC,MAAS,QACTC,MAAS,QACTC,YAAe,SAEjBC,KAAQ,OACRC,OACEC,IAAO,MACPV,IAAO,MACPW,MAAS,QACTN,MAAS,QACTO,KAAQ,OACRC,KAAQ,WAORrL,MAAMC,GAAGqL,OACbtL,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqL,KAAKnL,UAAUC,QAAQC,UAC7CkL,UACE/K,OAAU,iBACVgL,WAAc,SACdC,OAAU,iBACVrB,QAAW,SACXsB,KAAQ,OACRC,MAAS,kBACT1B,IAAO,gBACPe,KAAQ,eACRY,OAAU,SACVC,OAAU,UAEZC,UACEC,aAAgB,SAChBC,aAAgB,+CAChBC,cAAiB,UAEnBC,UAAa,2BAMXlM,MAAMC,GAAGkM,YACbnM,MAAMC,GAAGkM,UAAUhM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkM,UAAUhM,UAAUC,QAAQC,UAClD+L,MAAS,mEAMPpM,MAAMC,GAAGoM,iBACbrM,MAAMC,GAAGoM,eAAelM,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoM,eAAelM,UAAUC,SAC/CkM,YAAe,iBACfC,cAAiB,oBAMfvM,MAAMC,GAAGuM,QACbxM,MAAMC,GAAGuM,MAAMrM,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuM,MAAMrM,UAAUC,QAAQC,UAC9CoM,SAAY,MACZC,QAAW,yBACXN,MAAS,sBACTO,KAAQ,OACRC,GAAM,SACNC,aAAgB,iBAChBC,MAAS,uBACTC,SAAY,0BACZC,KAAQ,sBACRC,KAAQ,sBACRC,QAAW,UACXC,UAAa,gBAMXnN,MAAMC,GAAGmN,YACbpN,MAAMC,GAAGmN,UAAUjN,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmN,UAAUjN,UAAUC,QAAQC,UAClDgN,cAAiB,wBACjBC,aAAgB,0BAChBC,UAAa,2BAMXvN,MAAMC,GAAGuN,iBACbxN,MAAMC,GAAGuN,eAAerN,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuN,eAAerN,UAAUC,QAAQC,UACvD2I,KAAQ,8BACRyE,aAAgB,gBAChB1M,OAAU,SACV2M,QAAW,oBACX5C,MAAS,oBACThD,MAAS,QACT6F,GAAM,KACNnN,OAAU,SACVwH,WACEK,SAAY,WACZC,eAAkB,mBAClBF,WAAc,cACdG,SAAY,YACZL,GAAM,cACNC,IAAO,sBAOPnI,MAAMC,GAAG2N,mBACb5N,MAAMC,GAAG2N,iBAAiBzN,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2N,iBAAiBzN,UAAUC,QAAQC,UACzDwN,aACEC,MAAS,QACTC,OAAU,SACVC,MAAS,QACTC,OAAU,SACVC,QAAW,UACXC,OAAU,UAEZJ,QACEK,YAAe,iBACfC,SAAY,YAEdL,OACEI,YAAe,iBACfC,SAAY,WAEdJ,QACEI,SAAY,WACZD,YAAe,iBACfE,SAAY,eAEdJ,SACEE,YAAe,iBACfE,SAAY,cACZD,SAAY,YACZnD,IAAO,QAETiD,QACEC,YAAe,iBACfE,SAAY,cACZD,SAAY,WACZzB,GAAM,QAERpC,KACE+D,MAAS,OACTC,YAAe,OACfV,MAAS,QACTW,MAAS,SACTC,WAAc,iBACdC,GAAM,OAERC,iBACE9B,MAAS,QACT+B,OAAU,SACVC,MAAS,QACTC,OAAU,SACV9B,KAAQ,QAEV+B,UACE9D,IAAO,MACP+D,QAAW,UACXC,QAAW,kBAOXlP,MAAMC,GAAGkP,YACbnP,MAAMC,GAAGkP,UAAUhP,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkP,UAAUhP,UAAUC,QAAQC,UAClD+O,OAAU,UACVvG,KAAQ,OACRwG,MAAS,QACTC,KAAQ,OACRC,YAAe,gBACfC,YAAe,sBACfC,MAAS,QACTzE,KAAQ,OACRxK,OAAU,SACV4J,QAAW,SACXsF,kBAAqB,eACrBC,cAAiB,+BACjBC,eAAkB,wBAClB9D,UACEE,aAAgB,+CAElBf,OACEC,IAAO,MACPE,KAAQ,OACRyE,SAAY,YACZC,OAAU,SACV3E,MAAS,SAEX4E,oBACEL,kBAAqB,wBACrBM,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,gBAAmB,sBACnBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,wEACnBC,cAAiB,uEAEnBjG,QACES,MAAS,QACTD,MAAS,QACTL,IAAO,MACP+F,YAAe,gBACfC,YAAe,cACfC,OAAU,SACVC,SAAY,IACZC,cAAiB,iBACjBC,YAAe,eACfC,kBAAqB,wCACrBC,oBAAuB,YACvBC,qBAAwB,YACxBC,cAAiB,aACjBC,WAAc,cACd1G,YAAe,YAOfvK,MAAMkR,aAAelR,MAAMkR,YAAY7Q,SAAS8Q,gBACpDnR,MAAMkR,YAAY7Q,SAAS8Q,cAC3BrR,EAAEQ,QAAO,EAAMN,MAAMkR,YAAY7Q,SAAS8Q,eACxCC,WAAc,cACdC,cAAiB,iBACjBC,wBAA2B,4BAC3BC,sBAAyB,0BACzBC,eAAkB,kBAClBC,WAAc,cACdC,UAAa,aACbC,YAAe,eACfC,aAAgB,gBAChBC,UAAa,YACbC,MAAS,cACTC,YAAe,kBACfxR,MAAS,QACTC,OAAU,YAIRR,MAAMkR,aAAelR,MAAMkR,YAAY7Q,SAAS2R,UACpDhS,MAAMkR,YAAY7Q,SAAS2R,QAC3BlS,EAAEQ,QAAO,EAAMN,MAAMkR,YAAY7Q,SAAS2R,SACxCzR,MAAS,QACTyK,KAAQ,OACRxK,OAAU,SACVyR,OAAU,SACVC,OAAU,KACVC,mBACErH,MAAS,SACTsH,YACE5J,OAAU,SACV6J,SAAY,WACZxJ,KAAQ,SAGZyJ,kBACExH,MAAS,QAEXyH,gBACEzH,MAAS,aAEX0H,eACE1H,MAAS,WAEX2H,iBACE3H,MAAS,YACT4H,SACCC,aAAgB,aAChB/Q,cAAiB,SACjBE,aAAgB,cAChBC,YAAe,UACf6Q,SAAY,YACZC,YAAe,eACfC,YAAe,iBAGlBC,aACEjI,MAAS,cACT4H,SACEM,WAAc,YACdC,kBAAqB,qBACrBC,gBAAmB,mBACnBC,QAAW,YAGfC,cACEtI,MAAS,eACT4H,SACEW,YAAe,eACfC,WAAc,cACdC,cAAiB,iBACjBC,SAAY,mBAGhBC,kBACE3I,MAAS,kBACT4I,YAAe,sCACfC,UAAa,iBACbC,UACEC,IAAO,YACPrL,OAAU,SACVsL,KAAQ,OACRjL,KAAQ,OACRkL,OAAU,iBACVC,KAAQ,QAEVC,WACEC,YAAe,eACfC,SAAY,YACZC,QAAW,UACXC,WAAc,cACdC,QAAW,WACXC,WAAc,eACdC,qBAAwB,2BACxBC,kBAAqB,yBAEvBC,kBACER,YAAe,mBACfC,SAAY,gBACZC,QAAW,sBACXC,WAAc,0BACdC,QAAW,eACXC,WAAc,mBACdC,qBAAwB,+BACxBC,kBAAqB,4BACrBV,OAAU,mCAEZY,QACEf,SAAY,WACZgB,SAAY,WACZC,IAAO,MACPC,IAAO,MACP1L,MAAS,QACTyB,MAAS,QACTL,IAAO,MACPuK,cAAiB,kBACjBC,YAAe,eACfC,YAAe,eACfC,SAAY,YACZvB,UAAa,aACbD,YAAe,eACfyB,YAAe,gBAEjBC,cACEC,UAAa,aACbC,YAAe,iBAGnBC,gBACEzK,MAAS,YACT6J,QACEa,SAAY,YACZC,WAAc,eACdC,WAAc,SACdC,UAAa,aACbC,QAAW,UACXC,YAAe,cACfC,MAAS,QACTC,WAAc,aACdC,OAAU,SACVC,aAAgB,eAChBC,WAAc,eAGlBC,oBACEC,aAAgB,wCAElBC,mBACEvL,MAAS,sBACTsL,aAAgB,+FAChBzB,QACE2B,QAAW,WACXC,OAAU,UACVC,SAAY,cAGhBC,4BACEL,aAAgB,6DAKhBpW,MAAMkR,aAAelR,MAAMkR,YAAY7Q,SAASqW,aACpD1W,MAAMkR,YAAY7Q,SAASqW,WAC3B5W,EAAEQ,QAAO,EAAMN,MAAMkR,YAAY7Q,SAASqW,YACxC7V,cAAiB,oBACjBC,eAAkB,oBAClB6V,cAAiB,kBACjBC,kBAAqB,sBACrBrW,MAAS,QACTmH,OAAU,SACVmP,aAAgB,2BAChB/O,MAAS,QACTgP,OAAU,WACVC,aAAgB,OAChB9N,IAAO,MACPC,GAAM,KACNlB,WACEC,QACEI,SAAY,gBACZC,eAAkB,wBAClBF,WAAc,mBACdG,SAAY,kBAEdM,MACEX,GAAO,UACPC,IAAO,cACPS,GAAO,iBACPF,GAAO,iBAETF,QACEN,GAAM,cACNC,IAAO,kBACPM,IAAO,8BACPC,GAAM,kBACNC,IAAO,2BACPC,GAAM,oBAMR5I,MAAMkR,aAAelR,MAAMkR,YAAY7Q,SAAS2W,UACpDhX,MAAMkR,YAAY7Q,SAAS2W,QAC3BlX,EAAEQ,QAAO,EAAMN,MAAMkR,YAAY7Q,SAAS2W,SACxCnS,cAAiB,kBACjBC,eAAkB,mBAClBC,YAAe,gBACfC,YAAe,gBACfgB,UAAa,YACbiR,kBACEtE,aAAgB,aAChB/Q,cAAiB,SACjBE,aAAgB,cAChBC,YAAe,UACf6Q,SAAY,YACZC,YAAe,eACfC,YAAe,gBAEjBoE,gBAAmB,aACnB5V,KAAQ,OACR6V,QAAW,UACXC,aACEtF,MAAS,cACTC,YAAe,mBAEjBsF,KAAQ,OACRC,IAAO,MACPpS,aAAgB,gBAChBD,UAAa,aACbsS,YAAe,uBACfxW,OAAU,SACVyW,WAAc,OACd5U,SAAY,YACZ6U,OAAU,mBACVC,aACEC,UAAa,YACbnP,OAAU,SACVoP,QAAW,UACXC,UAAa,YACbxF,SAAY,WACZxJ,KAAQ,OACRyG,KAAQ,OACRwI,SAAY,YACZC,SAAY,WACZC,YAAe,mBAEjBC,sBAAyB,mBACzBC,sBAAyB,mBACzBC,OAAU,eACVC,eACE/E,YAAe,eACfC,WAAc,cACdC,cAAiB,iBACjBC,SAAY,kBAEdjS,OAAU,SACV8W,MAAS,cACTC,cACEtF,WAAc,YACdC,kBAAqB,qBACrBC,gBAAmB,mBACnBC,QAAW,WAEboF,KAAQ,UACRC,MAAS,QACTC,aACEC,KAAQ,OACRC,KAAQ,QAEVC,OAAU,aACVC,QAAW,iBACXC,SAAY,kBACZC,aACEC,aAAgB,oBAChBC,cAAiB,oBACjBC,aAAgB,oBAChBC,cAAiB,qBAEnBC,UAAa,aACbC,SAAY,YACZ7X,UAAa,YACb8X,WAAc,wBAIZtZ,MAAMkR,aAAelR,MAAMkR,YAAY7Q,SAASkZ,OACpDvZ,MAAMkR,YAAY7Q,SAASkZ,KAC3BzZ,EAAEQ,QAAO,EAAMN,MAAMkR,YAAY7Q,SAASkZ,MACxCC,QACEC,sBAAyB,sIACzBC,4BAA+B,0DAC/BC,gBAAmB,6EAErBC,MACEC,KAAQ,OACRC,OAAU,SACVC,KAAQ,WAOR/Z,MAAMC,GAAG+Z,SACbha,MAAMC,GAAG+Z,OAAO7Z,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+Z,OAAO7Z,UAAUC,SACvC6Z,oBAAuB,WACvBC,oBAAuB,cAMrBla,MAAMC,GAAGka,WACbna,MAAMC,GAAGka,SAASha,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGka,SAASha,UAAUC,QAAQC,UACjD+Z,OAAU,wBACVC,QAAW,aACXC,cAAiB,kBACjBC,MAAS,QACThP,UACIG,KAAQ,OACRG,OAAU,SACVL,WAAc,SACdC,OAAU,iBACV+O,YAAe,mBACfpQ,QAAW,SACXuB,MAAS,kBACT1B,IAAO,oBAKTjK,MAAMC,GAAGka,WACbna,MAAMC,GAAGka,SAASha,UAAUC,QAAQqa,WACpC3a,EAAEQ,QAAO,EAAMN,MAAMC,GAAGka,SAASha,UAAUC,QAAQqa,YAC/Cpa,UACIW,QAAW,iBACXD,OAAU,eACVF,cAAiB,aACjBC,eAAkB,kBAOtBd,MAAMC,GAAGya,WACb1a,MAAMC,GAAGya,SAASva,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGya,SAASva,UAAUC,QAAQC,UACjDga,QAAW,aACXC,cAAiB,kBACjBC,MAAS,WAMPva,MAAMC,GAAG0a,SACb3a,MAAMC,GAAG0a,OAAOxa,UAAUC,QAAQwa,aAClC9a,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0a,OAAOxa,UAAUC,QAAQwa,cAC/ChP,OAAU,kBACVpL,OAAU,SACV+Z,MAAS,QACTtI,OAAU,SACV4I,oBAAuB,eACvBpT,cAAiB,4BACjBqT,gBAAmB,YACnBC,eAAkB,WAClBC,cAAiB,UACjBC,aAAgB,SAChBC,sBAAyB,eACzBC,qBAAwB,UAMtBnb,MAAMC,GAAGmb,YACbpb,MAAMC,GAAGmb,UAAUjb,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmb,UAAUjb,UAAUC,QAAQC,UAClDgb,SAAY,kBACZC,QAAW,mBACXzG,IAAO,6CACPC,IAAO,6CACPyG,KAAQ,mBACRC,MAAS,yBACTC,IAAO,uBACP5S,KAAQ,wBACR6S,YAAe,gEAMb1b,MAAMC,GAAG0b,SACb3b,MAAMC,GAAG0b,OAAOxb,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0b,OAAOxb,UAAUC,QAAQwa,cAC/CgB,MAAS,WAMP5b,MAAMC,GAAG4b,QACb7b,MAAMC,GAAG4b,MAAM1b,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4b,MAAM1b,UAAUC,QAAQwa,cAC9C1I,OAAU,QAMRlS,MAAMC,GAAG6b,UACb9b,MAAMC,GAAG6b,QAAQ3b,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6b,QAAQ3b,UAAUC,QAAQwa,cAChD1I,OAAU,KACV1R,OAAU,YAKRR,MAAMC,GAAG8b,SACb/b,MAAMC,GAAG8b,OAAO5b,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8b,OAAO5b,UAAUC,QAAQwa,cAC/C1I,OAAU,KACV1R,OAAU,aAITwb,OAAOhc,MAAMic", "file": "kendo.messages.en-AU.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Apply\",\n  \"cancel\": \"Cancel\",\n  \"noColor\": \"no color\",\n  \"clearColor\": \"Clear color\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Apply\",\n  \"cancel\": \"Cancel\",\n  \"noColor\": \"no color\",\n  \"clearColor\": \"Clear color\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Sort Ascending\",\n  \"sortDescending\": \"Sort Descending\",\n  \"filter\": \"Filter\",\n  \"columns\": \"Columns\",\n  \"done\": \"Done\",\n  \"settings\": \"Column Settings\",\n  \"lock\": \"Lock\",\n  \"unlock\": \"Unlock\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Bold\",\n  \"italic\": \"Italic\",\n  \"underline\": \"Underline\",\n  \"strikethrough\": \"Strikethrough\",\n  \"superscript\": \"Superscript\",\n  \"subscript\": \"Subscript\",\n  \"justifyCenter\": \"Center text\",\n  \"justifyLeft\": \"Align text left\",\n  \"justifyRight\": \"Align text right\",\n  \"justifyFull\": \"Justify\",\n  \"insertUnorderedList\": \"Insert unordered list\",\n  \"insertOrderedList\": \"Insert ordered list\",\n  \"indent\": \"Indent\",\n  \"outdent\": \"Outdent\",\n  \"createLink\": \"Insert hyperlink\",\n  \"unlink\": \"Remove hyperlink\",\n  \"insertImage\": \"Insert image\",\n  \"insertFile\": \"Insert file\",\n  \"insertHtml\": \"Insert HTML\",\n  \"viewHtml\": \"View HTML\",\n  \"fontName\": \"Select font family\",\n  \"fontNameInherit\": \"(inherited font)\",\n  \"fontSize\": \"Select font size\",\n  \"fontSizeInherit\": \"(inherited size)\",\n  \"formatBlock\": \"Format\",\n  \"formatting\": \"Format\",\n  \"foreColor\": \"Color\",\n  \"backColor\": \"Background color\",\n  \"style\": \"Styles\",\n  \"emptyFolder\": \"Empty Folder\",\n  \"uploadFile\": \"Upload\",\n  \"overflowAnchor\": \"More tools\",\n  \"orderBy\": \"Arrange by:\",\n  \"orderBySize\": \"Size\",\n  \"orderByName\": \"Name\",\n  \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\n  \"deleteFile\": 'Are you sure you want to delete \"{0}\"?',\n  \"overwriteFile\": 'A file with name \"{0}\" already exists in the current directory. Do you want to overwrite it?',\n  \"directoryNotFound\": \"A directory with this name was not found.\",\n  \"imageWebAddress\": \"Web address\",\n  \"imageAltText\": \"Alternate text\",\n  \"imageWidth\": \"Width (px)\",\n  \"imageHeight\": \"Height (px)\",\n  \"fileWebAddress\": \"Web address\",\n  \"fileTitle\": \"Title\",\n  \"linkWebAddress\": \"Web address\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkOpenInNewWindow\": \"Open link in new window\",\n  \"dialogUpdate\": \"Update\",\n  \"dialogInsert\": \"Insert\",\n  \"dialogButtonSeparator\": \"or\",\n  \"dialogCancel\": \"Cancel\",\n  \"cleanFormatting\": \"Clean formatting\",\n  \"createTable\": \"Create table\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteRow\": \"Delete row\",\n  \"deleteColumn\": \"Delete column\",\n  \"dialogOk\": \"Ok\",\n  \"tableWizard\": \"Table Wizard\",\n  \"tableTab\": \"Table\",\n  \"cellTab\": \"Cell\",\n  \"accessibilityTab\": \"Accessibility\",\n  \"caption\": \"Caption\",\n  \"summary\": \"Summary\",\n  \"width\": \"Width\",\n  \"height\": \"Height\",\n  \"units\": \"Units\",\n  \"cellSpacing\": \"Cell Spacing\",\n  \"cellPadding\": \"Cell Padding\",\n  \"cellMargin\": \"Cell Margin\",\n  \"alignment\": \"Alignment\",\n  \"background\": \"Background\",\n  \"cssClass\": \"CSS Class\",\n  \"id\": \"ID\",\n  \"border\": \"Border\",\n  \"borderStyle\": \"Border Style\",\n  \"collapseBorders\": \"Collapse borders\",\n  \"wrapText\": \"Wrap text\",\n  \"associateCellsWithHeaders\": \"Associate cells with headers\",\n  \"alignLeft\": \"Align Left\",\n  \"alignCenter\": \"Align Center\",\n  \"alignRight\": \"Align Right\",\n  \"alignLeftTop\": \"Align Left Top\",\n  \"alignCenterTop\": \"Align Center Top\",\n  \"alignRightTop\": \"Align Right Top\",\n  \"alignLeftMiddle\": \"Align Left Middle\",\n  \"alignCenterMiddle\": \"Align Center Middle\",\n  \"alignRightMiddle\": \"Align Right Middle\",\n  \"alignLeftBottom\": \"Align Left Bottom\",\n  \"alignCenterBottom\": \"Align Center Bottom\",\n  \"alignRightBottom\": \"Align Right Bottom\",\n  \"alignRemove\": \"Remove Alignment\",\n  \"columns\": \"Columns\",\n  \"rows\": \"Rows\",\n  \"selectAllCells\": \"Select All Cells\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Upload\",\n  \"orderBy\": \"Arrange by\",\n  \"orderByName\": \"Name\",\n  \"orderBySize\": \"Size\",\n  \"directoryNotFound\": \"A directory with this name was not found.\",\n  \"emptyFolder\": \"Empty Folder\",\n  \"deleteFile\": 'Are you sure you want to delete \"{0}\"?',\n  \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\n  \"overwriteFile\": \"A file with name \\\"{0}\\\" already exists in the current directory. Do you want to overwrite it?\",\n  \"dropFilesHere\": \"drop file here to upload\",\n  \"search\": \"Search\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"is true\",\n  \"isFalse\": \"is false\",\n  \"filter\": \"Filter\",\n  \"clear\": \"Clear\",\n  \"operator\": \"Operator\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"startswith\": \"Starts with\",\n    \"contains\": \"Contains\",\n    \"doesnotcontain\": \"Does not contain\",\n    \"endswith\": \"Ends with\"\n  },\n  \"number\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is greater than or equal to\",\n    \"gt\": \"Is greater than\",\n    \"lte\": \"Is less than or equal to\",\n    \"lt\": \"Is less than\"\n  },\n  \"date\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is after or equal to\",\n    \"gt\": \"Is after\",\n    \"lte\": \"Is before or equal to\",\n    \"lt\": \"Is before\"\n  },\n  \"enums\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Show items with value that:\",\n  \"isTrue\": \"is true\",\n  \"isFalse\": \"is false\",\n  \"filter\": \"Filter\",\n  \"clear\": \"Clear\",\n  \"and\": \"And\",\n  \"or\": \"Or\",\n  \"selectValue\": \"-Select value-\",\n  \"operator\": \"Operator\",\n  \"value\": \"Value\",\n  \"cancel\": \"Cancel\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"startswith\": \"Starts with\",\n    \"contains\": \"Contains\",\n    \"doesnotcontain\": \"Does not contain\",\n    \"endswith\": \"Ends with\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\",\n    \"isempty\": \"Is empty\",\n    \"isnotempty\": \"Is not empty\"\n  },\n  \"number\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is greater than or equal to\",\n    \"gt\": \"Is greater than\",\n    \"lte\": \"Is less than or equal to\",\n    \"lt\": \"Is less than\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  },\n  \"date\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"gte\": \"Is after or equal to\",\n    \"gt\": \"Is after\",\n    \"lte\": \"Is before or equal to\",\n    \"lt\": \"Is before\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  },\n  \"enums\": {\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\",\n    \"isnull\": \"Is null\",\n    \"isnotnull\": \"Is not null\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Select All\",\n  \"clear\": \"Clear\",\n  \"filter\": \"Filter\",\n  \"search\": \"Search\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Add Child\",\n    \"append\": \"Add Task\",\n    \"insertAfter\": \"Add Below\",\n    \"insertBefore\": \"Add Above\",\n    \"pdf\": \"Export to PDF\"\n  },\n  \"cancel\": \"Cancel\",\n  \"deleteDependencyWindowTitle\": \"Delete dependency\",\n  \"deleteTaskWindowTitle\": \"Delete task\",\n  \"destroy\": \"Delete\",\n  \"editor\": {\n    \"assingButton\": \"Assign\",\n    \"editorTitle\": \"Task\",\n    \"end\": \"End\",\n    \"percentComplete\": \"Complete\",\n    \"resources\": \"Resources\",\n    \"resourcesEditorTitle\": \"Resources\",\n    \"resourcesHeader\": \"Resources\",\n    \"start\": \"Start\",\n    \"title\": \"Title\",\n    \"unitsHeader\": \"Units\"\n  },\n  \"save\": \"Save\",\n  \"views\": {\n    \"day\": \"Day\",\n    \"end\": \"End\",\n    \"month\": \"Month\",\n    \"start\": \"Start\",\n    \"week\": \"Week\",\n    \"year\": \"Year\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Cancel changes\",\n    \"canceledit\": \"Cancel\",\n    \"create\": \"Add new record\",\n    \"destroy\": \"Delete\",\n    \"edit\": \"Edit\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Save changes\",\n    \"select\": \"Select\",\n    \"update\": \"Update\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Cancel\",\n    \"confirmation\": \"Are you sure you want to delete this record?\",\n    \"confirmDelete\": \"Delete\"\n  },\n  \"noRecords\": \"No records available.\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Drag a column header and drop it here to group by that column\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Increase value\",\n  \"downArrowText\": \"Decrease value\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} of {2} items\",\n  \"empty\": \"No items to display\",\n  \"page\": \"Page\",\n  \"of\": \"of {0}\",\n  \"itemsPerPage\": \"items per page\",\n  \"first\": \"Go to the first page\",\n  \"previous\": \"Go to the previous page\",\n  \"next\": \"Go to the next page\",\n  \"last\": \"Go to the last page\",\n  \"refresh\": \"Refresh\",\n  \"morePages\": \"More pages\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Drop Data Fields Here\",\n  \"columnFields\": \"Drop Column Fields Here\",\n  \"rowFields\": \"Drop Rows Fields Here\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Show items with value that:\",\n  \"filterFields\": \"Fields Filter\",\n  \"filter\": \"Filter\",\n  \"include\": \"Include Fields...\",\n  \"title\": \"Fields to include\",\n  \"clear\": \"Clear\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Cancel\",\n  \"operators\": {\n    \"contains\": \"Contains\",\n    \"doesnotcontain\": \"Does not contain\",\n    \"startswith\": \"Starts with\",\n    \"endswith\": \"Ends with\",\n    \"eq\": \"Is equal to\",\n    \"neq\": \"Is not equal to\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Never\",\n    \"hourly\": \"Hourly\",\n    \"daily\": \"Daily\",\n    \"weekly\": \"Weekly\",\n    \"monthly\": \"Monthly\",\n    \"yearly\": \"Yearly\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"interval\": \" hour(s)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"interval\": \" day(s)\"\n  },\n  \"weekly\": {\n    \"interval\": \" week(s)\",\n    \"repeatEvery\": \"Repeat every: \",\n    \"repeatOn\": \"Repeat on: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"repeatOn\": \"Repeat on: \",\n    \"interval\": \" month(s)\",\n    \"day\": \"Day \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Repeat every: \",\n    \"repeatOn\": \"Repeat on: \",\n    \"interval\": \" year(s)\",\n    \"of\": \" of \"\n  },\n  \"end\": {\n    \"label\": \"End:\",\n    \"mobileLabel\": \"Ends\",\n    \"never\": \"Never\",\n    \"after\": \"After \",\n    \"occurrence\": \" occurrence(s)\",\n    \"on\": \"On \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"first\",\n    \"second\": \"second\",\n    \"third\": \"third\",\n    \"fourth\": \"fourth\",\n    \"last\": \"last\"\n  },\n  \"weekdays\": {\n    \"day\": \"day\",\n    \"weekday\": \"weekday\",\n    \"weekend\": \"weekend day\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"all day\",\n  \"date\": \"Date\",\n  \"event\": \"Event\",\n  \"time\": \"Time\",\n  \"showFullDay\": \"Show full day\",\n  \"showWorkDay\": \"Show business hours\",\n  \"today\": \"Today\",\n  \"save\": \"Save\",\n  \"cancel\": \"Cancel\",\n  \"destroy\": \"Delete\",\n  \"deleteWindowTitle\": \"Delete event\",\n  \"ariaSlotLabel\": \"Selected from {0:t} to {1:t}\",\n  \"ariaEventLabel\": \"{0} on {1:D} at {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Are you sure you want to delete this event?\"\n  },\n  \"views\": {\n    \"day\": \"Day\",\n    \"week\": \"Week\",\n    \"workWeek\": \"Work Week\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Month\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Delete Recurring Item\",\n    \"deleteWindowOccurrence\": \"Delete current occurrence\",\n    \"deleteWindowSeries\": \"Delete the series\",\n    \"editWindowTitle\": \"Edit Recurring Item\",\n    \"editWindowOccurrence\": \"Edit current occurrence\",\n    \"editWindowSeries\": \"Edit the series\",\n    \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\n    \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\"\n  },\n  \"editor\": {\n    \"title\": \"Title\",\n    \"start\": \"Start\",\n    \"end\": \"End\",\n    \"allDayEvent\": \"All day event\",\n    \"description\": \"Description\",\n    \"repeat\": \"Repeat\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Start timezone\",\n    \"endTimezone\": \"End timezone\",\n    \"separateTimezones\": \"Use separate start and end time zones\",\n    \"timezoneEditorTitle\": \"Timezones\",\n    \"timezoneEditorButton\": \"Time zone\",\n    \"timezoneTitle\": \"Time zones\",\n    \"noTimezone\": \"No timezone\",\n    \"editorTitle\": \"Event\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"All borders\",\n  \"insideBorders\": \"Inside borders\",\n  \"insideHorizontalBorders\": \"Inside horizontal borders\",\n  \"insideVerticalBorders\": \"Inside vertical borders\",\n  \"outsideBorders\": \"Outside borders\",\n  \"leftBorder\": \"Left border\",\n  \"topBorder\": \"Top border\",\n  \"rightBorder\": \"Right border\",\n  \"bottomBorder\": \"Bottom border\",\n  \"noBorders\": \"No border\",\n  \"reset\": \"Reset color\",\n  \"customColor\": \"Custom color...\",\n  \"apply\": \"Apply\",\n  \"cancel\": \"Cancel\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Apply\",\n  \"save\": \"Save\",\n  \"cancel\": \"Cancel\",\n  \"remove\": \"Remove\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Format\",\n    \"categories\": {\n      \"number\": \"Number\",\n      \"currency\": \"Currency\",\n      \"date\": \"Date\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Font\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Font size\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Borders\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Alignment\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Align left\",\n     \"justifyCenter\": \"Center\",\n     \"justifyRight\": \"Align right\",\n     \"justifyFull\": \"Justify\",\n     \"alignTop\": \"Align top\",\n     \"alignMiddle\": \"Align middle\",\n     \"alignBottom\": \"Align bottom\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Merge cells\",\n    \"buttons\": {\n      \"mergeCells\": \"Merge all\",\n      \"mergeHorizontally\": \"Merge horizontally\",\n      \"mergeVertically\": \"Merge vertically\",\n      \"unmerge\": \"Unmerge\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Freeze panes\",\n    \"buttons\": {\n      \"freezePanes\": \"Freeze panes\",\n      \"freezeRows\": \"Freeze rows\",\n      \"freezeColumns\": \"Freeze columns\",\n      \"unfreeze\": \"Unfreeze panes\"\n    }\n  },\n  \"validationDialog\": {\n    \"title\": \"Data Validation\",\n    \"hintMessage\": \"Please enter a valid {0} value {1}.\",\n    \"hintTitle\": \"Validation {0}\",\n    \"criteria\": {\n      \"any\": \"Any value\",\n      \"number\": \"Number\",\n      \"text\": \"Text\",\n      \"date\": \"Date\",\n      \"custom\": \"Custom Formula\",\n      \"list\": \"List\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"greater than\",\n      \"lessThan\": \"less than\",\n      \"between\": \"between\",\n      \"notBetween\": \"not between\",\n      \"equalTo\": \"equal to\",\n      \"notEqualTo\": \"not equal to\",\n      \"greaterThanOrEqualTo\": \"greater than or equal to\",\n      \"lessThanOrEqualTo\": \"less than or equal to\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"greater than {0}\",\n      \"lessThan\": \"less than {0}\",\n      \"between\": \"between {0} and {1}\",\n      \"notBetween\": \"not between {0} and {1}\",\n      \"equalTo\": \"equal to {0}\",\n      \"notEqualTo\": \"not equal to {0}\",\n      \"greaterThanOrEqualTo\": \"greater than or equal to {0}\",\n      \"lessThanOrEqualTo\": \"less than or equal to {0}\",\n      \"custom\": \"that satisfies the formula: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Criteria\",\n      \"comparer\": \"Comparer\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Value\",\n      \"start\": \"Start\",\n      \"end\": \"End\",\n      \"onInvalidData\": \"On invalid data\",\n      \"rejectInput\": \"Reject input\",\n      \"showWarning\": \"Show warning\",\n      \"showHint\": \"Show hint\",\n      \"hintTitle\": \"Hint title\",\n      \"hintMessage\": \"Hint message\",\n      \"ignoreBlank\": \"Ignore blank\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Type title\",\n      \"typeMessage\": \"Type message\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Export...\",\n    \"labels\": {\n      \"fileName\": \"File name\",\n      \"saveAsType\": \"Save as type\",\n      \"exportArea\": \"Export\",\n      \"paperSize\": \"Paper size\",\n      \"margins\": \"Margins\",\n      \"orientation\": \"Orientation\",\n      \"print\": \"Print\",\n      \"guidelines\": \"Guidelines\",\n      \"center\": \"Center\",\n      \"horizontally\": \"Horizontally\",\n      \"vertically\": \"Vertically\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Cannot change part of a merged cell.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Copying and pasting\",\n    \"errorMessage\": \"These actions cannot be invoked through the menu. Please use the keyboard shortcuts instead:\",\n    \"labels\": {\n      \"forCopy\": \"for copy\",\n      \"forCut\": \"for cut\",\n      \"forPaste\": \"for paste\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"That action cannot be performed on multiple selection.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Sort range A to Z\",\n  \"sortDescending\": \"Sort range Z to A\",\n  \"filterByValue\": \"Filter by value\",\n  \"filterByCondition\": \"Filter by condition\",\n  \"apply\": \"Apply\",\n  \"search\": \"Search\",\n  \"addToCurrent\": \"Add to current selection\",\n  \"clear\": \"Clear\",\n  \"blanks\": \"(Blanks)\",\n  \"operatorNone\": \"None\",\n  \"and\": \"AND\",\n  \"or\": \"OR\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Text contains\",\n      \"doesnotcontain\": \"Text does not contain\",\n      \"startswith\": \"Text starts with\",\n      \"endswith\": \"Text ends with\"\n    },\n    \"date\": {\n      \"eq\":  \"Date is\",\n      \"neq\": \"Date is not\",\n      \"lt\":  \"Date is before\",\n      \"gt\":  \"Date is after\"\n    },\n    \"number\": {\n      \"eq\": \"Is equal to\",\n      \"neq\": \"Is not equal to\",\n      \"gte\": \"Is greater than or equal to\",\n      \"gt\": \"Is greater than\",\n      \"lte\": \"Is less than or equal to\",\n      \"lt\": \"Is less than\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Add column left\",\n  \"addColumnRight\": \"Add column right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"alignment\": \"Alignment\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Align left\",\n    \"justifyCenter\": \"Center\",\n    \"justifyRight\": \"Align right\",\n    \"justifyFull\": \"Justify\",\n    \"alignTop\": \"Align top\",\n    \"alignMiddle\": \"Align middle\",\n    \"alignBottom\": \"Align bottom\"\n  },\n  \"backgroundColor\": \"Background\",\n  \"bold\": \"Bold\",\n  \"borders\": \"Borders\",\n  \"colorPicker\": {\n    \"reset\": \"Reset color\",\n    \"customColor\": \"Custom color...\"\n  },\n  \"copy\": \"Copy\",\n  \"cut\": \"Cut\",\n  \"deleteColumn\": \"Delete column\",\n  \"deleteRow\": \"Delete row\",\n  \"excelImport\": \"Import from Excel...\",\n  \"filter\": \"Filter\",\n  \"fontFamily\": \"Font\",\n  \"fontSize\": \"Font size\",\n  \"format\": \"Custom format...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automatic\",\n    \"number\": \"Number\",\n    \"percent\": \"Percent\",\n    \"financial\": \"Financial\",\n    \"currency\": \"Currency\",\n    \"date\": \"Date\",\n    \"time\": \"Time\",\n    \"dateTime\": \"Date time\",\n    \"duration\": \"Duration\",\n    \"moreFormats\": \"More formats...\"\n  },\n  \"formatDecreaseDecimal\": \"Decrease decimal\",\n  \"formatIncreaseDecimal\": \"Increase decimal\",\n  \"freeze\": \"Freeze panes\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Freeze panes\",\n    \"freezeRows\": \"Freeze rows\",\n    \"freezeColumns\": \"Freeze columns\",\n    \"unfreeze\": \"Unfreeze panes\"\n  },\n  \"italic\": \"Italic\",\n  \"merge\": \"Merge cells\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Merge all\",\n    \"mergeHorizontally\": \"Merge horizontally\",\n    \"mergeVertically\": \"Merge vertically\",\n    \"unmerge\": \"Unmerge\"\n  },\n  \"open\": \"Open...\",\n  \"paste\": \"Paste\",\n  \"quickAccess\": {\n    \"redo\": \"Redo\",\n    \"undo\": \"Undo\"\n  },\n  \"saveAs\": \"Save As...\",\n  \"sortAsc\": \"Sort ascending\",\n  \"sortDesc\": \"Sort descending\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Sort sheet A to Z\",\n    \"sortSheetDesc\": \"Sort sheet Z to A\",\n    \"sortRangeAsc\": \"Sort range A to Z\",\n    \"sortRangeDesc\": \"Sort range Z to A\"\n  },\n  \"textColor\": \"Text Color\",\n  \"textWrap\": \"Wrap text\",\n  \"underline\": \"Underline\",\n  \"validation\": \"Data validation...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Cannot insert cells due to data loss possibility. Select another insert location or delete the data from the end of your worksheet.\",\n    \"filterRangeContainingMerges\": \"Cannot create a filter within a range containing merges\",\n    \"validationError\": \"The value that you entered violates the validation rules set on the cell.\"\n  },\n  \"tabs\": {\n    \"home\": \"Home\",\n    \"insert\": \"Insert\",\n    \"data\": \"Data\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Increase\",\n  \"decreaseButtonTitle\": \"Decrease\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"No records to display\",\n  \"loading\": \"Loading...\",\n  \"requestFailed\": \"Request failed.\",\n  \"retry\": \"Retry\",\n  \"commands\": {\n      \"edit\": \"Edit\",\n      \"update\": \"Update\",\n      \"canceledit\": \"Cancel\",\n      \"create\": \"Add new record\",\n      \"createchild\": \"Add child record\",\n      \"destroy\": \"Delete\",\n      \"excel\": \"Export to Excel\",\n      \"pdf\": \"Export to PDF\"\n  }\n});\n}\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.columnMenu =\n$.extend(true, kendo.ui.TreeList.prototype.options.columnMenu, {\n    \"messages\": {\n        \"columns\": \"Choose columns\",\n        \"filter\": \"Apply filter\",\n        \"sortAscending\": \"Sort (asc)\",\n        \"sortDescending\": \"Sort (desc)\"\n    }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Loading...\",\n  \"requestFailed\": \"Request failed.\",\n  \"retry\": \"Retry\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Select files...\",\n  \"cancel\": \"Cancel\",\n  \"retry\": \"Retry\",\n  \"remove\": \"Remove\",\n  \"uploadSelectedFiles\": \"Upload files\",\n  \"dropFilesHere\": \"drop files here to upload\",\n  \"statusUploading\": \"uploading\",\n  \"statusUploaded\": \"uploaded\",\n  \"statusWarning\": \"warning\",\n  \"statusFailed\": \"failed\",\n  \"headerStatusUploading\": \"Uploading...\",\n  \"headerStatusUploaded\": \"Done\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} is required\",\n  \"pattern\": \"{0} is not valid\",\n  \"min\": \"{0} should be greater than or equal to {1}\",\n  \"max\": \"{0} should be smaller than or equal to {1}\",\n  \"step\": \"{0} is not valid\",\n  \"email\": \"{0} is not valid email\",\n  \"url\": \"{0} is not valid URL\",\n  \"date\": \"{0} is not valid date\",\n  \"dateCompare\": \"End date should be greater than or equal to the start date\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Close\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancel\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancel\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}