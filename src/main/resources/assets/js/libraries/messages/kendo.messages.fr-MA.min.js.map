{"version": 3, "sources": ["messages/kendo.messages.fr-MA.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gte", "gt", "lte", "lt", "neq", "isnull", "isnotnull", "number", "string", "endswith", "startswith", "contains", "doesnotcontain", "isempty", "isnotempty", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "create", "destroy", "canceledit", "update", "edit", "excel", "pdf", "select", "cancel", "save", "editable", "confirmation", "cancelDelete", "confirmDelete", "noRecords", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "filter", "clear", "isFalse", "isTrue", "operator", "and", "info", "selectValue", "or", "value", "FilterMultiCheck", "checkAll", "search", "Groupable", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "imageWidth", "imageHeight", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "dropFilesHere", "formatting", "viewHtml", "dialogUpdate", "insertFile", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "browserMessages", "FileBrowser", "ImageBrowser", "Upload", "localization", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "timeline", "showFullDay", "showWorkDay", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,IAAO,2BACPC,GAAM,iBACNC,IAAO,0BACPC,GAAM,gBACNC,IAAO,mBACPC,OAAU,YACVC,UAAa,mBAEfC,QACER,GAAM,aACNC,IAAO,0BACPC,GAAM,kBACNC,IAAO,0BACPC,GAAM,kBACNC,IAAO,mBACPC,OAAU,YACVC,UAAa,mBAEfE,QACEC,SAAY,iBACZV,GAAM,aACNK,IAAO,mBACPM,WAAc,eACdC,SAAY,WACZC,eAAkB,kBAClBP,OAAU,YACVC,UAAa,kBACbO,QAAW,WACXC,WAAc,kBAEhBC,OACEhB,GAAM,aACNK,IAAO,mBACPC,OAAU,YACVC,UAAa,sBAObf,MAAMC,GAAGwB,aACbzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,aACNC,IAAO,2BACPC,GAAM,iBACNC,IAAO,0BACPC,GAAM,gBACNC,IAAO,mBACPC,OAAU,YACVC,UAAa,mBAEfC,QACER,GAAM,aACNC,IAAO,0BACPC,GAAM,kBACNC,IAAO,0BACPC,GAAM,kBACNC,IAAO,mBACPC,OAAU,YACVC,UAAa,mBAEfE,QACEC,SAAY,iBACZV,GAAM,aACNK,IAAO,mBACPM,WAAc,eACdC,SAAY,WACZC,eAAkB,kBAClBP,OAAU,YACVC,UAAa,kBACbO,QAAW,WACXC,WAAc,kBAEhBC,OACEhB,GAAM,aACNK,IAAO,mBACPC,OAAU,YACVC,UAAa,sBAObf,MAAMC,GAAGyB,aACb1B,MAAMC,GAAGyB,WAAWvB,UAAUC,QAAQuB,SACtC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyB,WAAWvB,UAAUC,QAAQuB,UACnDC,QAAW,WACXC,cAAiB,gBACjBC,eAAkB,kBAClBC,SAAY,wBACZC,KAAQ,OACRC,KAAQ,UACRC,OAAU,YAMRlC,MAAMC,GAAGkC,mBACbnC,MAAMC,GAAGkC,iBAAiBhC,UAAUC,QAAQuB,SAC5C7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkC,iBAAiBhC,UAAUC,QAAQuB,UACzDS,OACEC,SAAY,UACZC,YAAe,mBAEjBC,KACEC,MAAS,SACTC,WAAc,gBACdC,MAAS,SACTC,MAAS,SACTC,GAAM,MACNC,YAAe,QAEjBC,aACEV,MAAS,oBACTW,QAAW,oBACXJ,MAAS,SACTK,OAAU,uBACVC,OAAU,mBAEZF,SACEG,IAAO,OACPb,SAAY,OACZC,YAAe,kBACfa,SAAY,4BAEdC,iBACEC,MAAS,UACTC,OAAU,YACVC,KAAQ,UACRC,OAAU,SACVC,MAAS,aAEXT,QACEV,YAAe,kBACfa,SAAY,2BACZd,SAAY,cAEdY,QACES,GAAM,KACNpB,YAAe,kBACfa,SAAY,2BACZd,SAAY,cAEdsB,UACET,IAAO,OACPU,QAAW,qBACXC,QAAW,uBAOX7D,MAAMC,GAAG6D,OACb9D,MAAMC,GAAG6D,KAAK3D,UAAUC,QAAQuB,SAChC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6D,KAAK3D,UAAUC,QAAQuB,UAC7CoC,UACEC,OAAU,UACVC,QAAW,UACXC,WAAc,UACdC,OAAU,gBACVC,KAAQ,SACRC,MAAS,kBACTC,IAAO,gBACPC,OAAU,eACVC,OAAU,4BACVC,KAAQ,iCAEVC,UACEC,aAAgB,yDAChBC,aAAgB,UAChBC,cAAiB,WAEnBC,UAAa,sCAMX9E,MAAMC,GAAG8E,QACb/E,MAAMC,GAAG8E,MAAM5E,UAAUC,QAAQuB,SACjC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,MAAM5E,UAAUC,QAAQuB,UAC9CqD,SAAY,OACZC,KAAQ,OACRC,QAAW,sCACXxB,GAAM,SACNyB,MAAS,mCACTC,QAAW,aACX/B,MAAS,2BACTgC,aAAgB,oBAChB9B,KAAQ,2BACR+B,KAAQ,2BACRC,SAAY,6BACZC,UAAa,qBAMXxF,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQuB,SACtC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQuB,UACnD8D,OAAU,UACVC,MAAS,iBACTC,QAAW,aACXC,OAAU,WACVC,SAAY,eAMV7F,MAAMC,GAAGwB,aACbzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQuB,SACtC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQuB,UACnD8D,OAAU,UACVK,IAAO,KACPJ,MAAS,iBACTK,KAAQ,yCACRC,YAAe,iBACfL,QAAW,aACXC,OAAU,WACVK,GAAM,KACNzB,OAAU,UACVqB,SAAY,YACZK,MAAS,YAMPlG,MAAMC,GAAGkG,mBACbnG,MAAMC,GAAGkG,iBAAiBhG,UAAUC,QAAQuB,SAC5C7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkG,iBAAiBhG,UAAUC,QAAQuB,UACzDyE,SAAY,iBACZV,MAAS,iBACTD,OAAU,UACVY,OAAU,eAMRrG,MAAMC,GAAGqG,YACbtG,MAAMC,GAAGqG,UAAUnG,UAAUC,QAAQuB,SACrC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,UAAUnG,UAAUC,QAAQuB,UAClDwD,MAAS,yFAMPnF,MAAMC,GAAGsG,SACbvG,MAAMC,GAAGsG,OAAOpG,UAAUC,QAAQuB,SAClC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsG,OAAOpG,UAAUC,QAAQuB,UAC/C6E,KAAQ,OACRC,WAAc,6BACdC,SAAY,SACZC,gBAAmB,mBACnBC,SAAY,mBACZC,gBAAmB,mBACnBC,YAAe,sBACfC,OAAU,uBACVC,WAAc,eACdC,YAAe,gBACfC,kBAAqB,kBACrBC,oBAAuB,gBACvBC,OAAU,WACVC,cAAiB,UACjBC,YAAe,YACfC,YAAe,4BACfC,aAAgB,4BAChBC,QAAW,sBACXC,cAAiB,QACjBC,OAAU,SACVC,UAAa,YACbC,YAAe,cACfC,UAAa,WACbC,OAAU,+BACVC,WAAc,4CACdC,kBAAqB,gDACrBC,YAAe,mBACfC,gBAAmB,4FACnBC,QAAW,iBACXC,YAAe,MACfC,YAAe,SACfC,cAAiB,iGACjBC,WAAc,cACdC,UAAa,kBACbC,UAAa,UACbC,sBAAyB,KACzBC,aAAgB,SAChBC,aAAgB,UAChBC,aAAgB,2BAChBC,gBAAmB,cACnBC,WAAc,eACdC,YAAe,eACfC,oBAAuB,mCACvBC,SAAY,OACZC,YAAe,aACfC,eAAkB,cAClBhD,OAAU,SACViD,YAAe,qBACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,aAAgB,uBAChBC,UAAa,kBACbC,cAAiB,4BACjBC,WAAc,SACdC,SAAY,YACZC,aAAgB,SAChBC,WAAc,cACdC,SAAY,KACZC,YAAe,uBACfC,SAAY,QACZC,QAAW,UACXC,iBAAoB,gBACpBC,QAAW,aACXC,QAAW,WACXC,MAAS,UACTC,OAAU,UACVC,YAAe,0BACfC,YAAe,2BACfC,WAAc,qBACdC,UAAa,aACbC,WAAc,OACdC,SAAY,aACZC,GAAM,KACNC,OAAU,UACVC,YAAe,mBACfC,gBAAmB,qBACnBC,SAAY,oBACZC,0BAA6B,iCAC7BC,UAAa,mBACbC,YAAe,oBACfC,WAAc,mBACdC,aAAgB,2BAChBC,eAAkB,4BAClBC,cAAiB,2BACjBC,gBAAmB,6BACnBC,kBAAqB,8BACrBC,iBAAoB,6BACpBC,gBAAmB,0BACnBC,kBAAqB,2BACrBC,iBAAoB,0BACpBC,YAAe,qBACfvK,QAAW,WACXwK,KAAQ,SACRC,eAAkB,qCAMpB,IAAIC,IACF9D,WAAe,UACfJ,QAAY,YACZC,YAAgB,MAChBC,YAAgB,SAChBL,kBAAsB,8BACtBC,YAAgB,kBAChBF,WAAe,4CACfG,gBAAoB,wFACpBI,cAAkB,oFAClBsB,cAAkB,4CAClBxD,OAAW,YAGTrG,OAAMC,GAAGsM,cACbvM,MAAMC,GAAGsM,YAAYpM,UAAUC,QAAQuB,SACvC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,YAAYpM,UAAUC,QAAQuB,SAAU2K,IAG5DtM,MAAMC,GAAGuM,eACbxM,MAAMC,GAAGuM,aAAarM,UAAUC,QAAQuB,SACxC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuM,aAAarM,UAAUC,QAAQuB,SAAU2K,IAM7DtM,MAAMC,GAAGwM,SACbzM,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQsM,aAClC5M,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,OAAOtM,UAAUC,QAAQsM,cAC/ClI,OAAU,UACVqF,cAAiB,yCACjB8C,OAAU,UACVC,MAAS,YACTrI,OAAU,kBACVsI,aAAgB,SAChBC,eAAkB,aAClBC,gBAAmB,iBACnBC,oBAAuB,2BACvBC,qBAAwB,OACxBC,sBAAyB,kBAMvBlN,MAAMC,GAAGkN,YACbnN,MAAMC,GAAGkN,UAAUhN,UAAUC,QAAQuB,SACrC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkN,UAAUhN,UAAUC,QAAQuB,UAClDyL,OAAU,mBACV5I,OAAU,UACVE,UACEC,aAAgB,mDAElBpE,KAAQ,OACR0D,QAAW,UACXoJ,QACEC,YAAe,mBACfC,YAAe,cACfC,YAAe,YACfjL,IAAO,MACPkL,YAAe,eACfC,OAAU,UACVC,kBAAqB,wCACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,IACZC,qBAAwB,iBACxBC,oBAAuB,mBACvBC,MAAS,QACTC,WAAc,yBAEhBC,MAAS,YACTC,oBACEC,gBAAmB,mEACnBC,uBAA0B,mCAC1BC,mBAAsB,gCACtBC,kBAAqB,qCACrBC,cAAiB,kEACjBC,qBAAwB,iCACxBC,iBAAoB,oBACpBC,gBAAmB,qCAErBnK,KAAQ,cACRoK,KAAQ,OACRC,MAAS,cACTC,OACEC,OAAU,SACV9L,IAAO,OACP+L,MAAS,OACTC,KAAQ,UACRC,SAAY,qBACZC,SAAY,eAEdZ,kBAAqB,2BACrBa,YAAe,2BACfC,YAAe,kCAObtP,MAAMC,GAAGsP,YACbvP,MAAMC,GAAGsP,UAAUpP,UAAUC,QAAQuB,SACrC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsP,UAAUpP,UAAUC,QAAQuB,UAClD6N,SAAY,iBACZC,QAAW,uBACXC,IAAO,yCACPC,IAAO,yCACPC,KAAQ,uBACRC,MAAS,mCACTC,IAAO,uCACPvP,KAAQ,gCACRwP,YAAe,6DAMb/P,MAAMC,GAAG+P,SACbhQ,MAAMC,GAAG+P,OAAO7P,UAAUC,QAAQuB,SAClC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+P,OAAO7P,UAAUC,QAAQsM,cAC/CuD,MAAS,YAMPjQ,MAAMC,GAAGiQ,QACblQ,MAAMC,GAAGiQ,MAAM/P,UAAUC,QAAQuB,SACjC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiQ,MAAM/P,UAAUC,QAAQsM,cAC9CyD,OAAU,QAMRnQ,MAAMC,GAAGmQ,UACbpQ,MAAMC,GAAGmQ,QAAQjQ,UAAUC,QAAQuB,SACnC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmQ,QAAQjQ,UAAUC,QAAQsM,cAChDyD,OAAU,KACV3L,OAAU,aAKRxE,MAAMC,GAAGoQ,SACbrQ,MAAMC,GAAGoQ,OAAOlQ,UAAUC,QAAQuB,SAClC7B,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoQ,OAAOlQ,UAAUC,QAAQsM,cAC/CyD,OAAU,KACV3L,OAAU,cAIT8L,OAAOtQ,MAAMuQ", "file": "kendo.messages.fr-MA.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Est égal à\",\n    \"gte\": \"Est postérieur ou égal à\",\n    \"gt\": \"Est postérieur\",\n    \"lte\": \"Est antérieur ou égal à\",\n    \"lt\": \"Est antérieur\",\n    \"neq\": \"N’est pas égal à\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\"\n  },\n  \"number\": {\n    \"eq\": \"Est égal à\",\n    \"gte\": \"Est supérieur ou égal à\",\n    \"gt\": \"Est supérieur à\",\n    \"lte\": \"Est inférieur ou égal à\",\n    \"lt\": \"Est inférieur à\",\n    \"neq\": \"N’est pas égal à\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\"\n  },\n  \"string\": {\n    \"endswith\": \"Se termine par\",\n    \"eq\": \"Est égal à\",\n    \"neq\": \"N’est pas égal à\",\n    \"startswith\": \"Commence par\",\n    \"contains\": \"Contient\",\n    \"doesnotcontain\": \"Ne contient pas\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\",\n    \"isempty\": \"Est vide\",\n    \"isnotempty\": \"N’est pas vide\"\n  },\n  \"enums\": {\n    \"eq\": \"Est égal à\",\n    \"neq\": \"N’est pas égal à\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Est égal à\",\n    \"gte\": \"Est postérieur ou égal à\",\n    \"gt\": \"Est postérieur\",\n    \"lte\": \"Est antérieur ou égal à\",\n    \"lt\": \"Est antérieur\",\n    \"neq\": \"N’est pas égal à\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\"\n  },\n  \"number\": {\n    \"eq\": \"Est égal à\",\n    \"gte\": \"Est supérieur ou égal à\",\n    \"gt\": \"Est supérieur à\",\n    \"lte\": \"Est inférieur ou égal à\",\n    \"lt\": \"Est inférieur à\",\n    \"neq\": \"N’est pas égal à\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\"\n  },\n  \"string\": {\n    \"endswith\": \"Se termine par\",\n    \"eq\": \"Est égal à\",\n    \"neq\": \"N’est pas égal à\",\n    \"startswith\": \"Commence par\",\n    \"contains\": \"Contient\",\n    \"doesnotcontain\": \"Ne contient pas\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\",\n    \"isempty\": \"Est vide\",\n    \"isnotempty\": \"N’est pas vide\"\n  },\n  \"enums\": {\n    \"eq\": \"Est égal à\",\n    \"neq\": \"N’est pas égal à\",\n    \"isnull\": \"Est nulle\",\n    \"isnotnull\": \"N’est pas nulle\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Colonnes\",\n  \"sortAscending\": \"Tri croissant\",\n  \"sortDescending\": \"Tri décroissant\",\n  \"settings\": \"Paramètres de colonne\",\n  \"done\": \"Fini\",\n  \"lock\": \"Bloquer\",\n  \"unlock\": \"Ouvrir\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"jour(s)\",\n    \"repeatEvery\": \"Répéter chaque:\"\n  },\n  \"end\": {\n    \"after\": \" Après\",\n    \"occurrence\": \"occurrence(s)\",\n    \"label\": \"Finir:\",\n    \"never\": \"Jamais\",\n    \"on\": \"Sur\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Une fois par jour\",\n    \"monthly\": \"Une fois par mois\",\n    \"never\": \"Jamais\",\n    \"weekly\": \"Une fois par semaine\",\n    \"yearly\": \"Une fois par an\"\n  },\n  \"monthly\": {\n    \"day\": \"Jour\",\n    \"interval\": \"mois\",\n    \"repeatEvery\": \"Répéter chaque:\",\n    \"repeatOn\": \"Répéter l'opération sur:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"premier\",\n    \"fourth\": \"quatrième\",\n    \"last\": \"dernier\",\n    \"second\": \"second\",\n    \"third\": \"troisième\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Répéter chaque:\",\n    \"repeatOn\": \"Répéter l'opération sur:\",\n    \"interval\": \"semaine(s)\"\n  },\n  \"yearly\": {\n    \"of\": \"de\",\n    \"repeatEvery\": \"Répéter chaque:\",\n    \"repeatOn\": \"Répéter l'opération sur:\",\n    \"interval\": \"année(ans)\"\n  },\n  \"weekdays\": {\n    \"day\": \"jour\",\n    \"weekday\": \"jour de la semaine\",\n    \"weekend\": \"jour de week-end\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"create\": \"Insérer\",\n    \"destroy\": \"Effacer\",\n    \"canceledit\": \"Annuler\",\n    \"update\": \"Mettre à jour\",\n    \"edit\": \"Éditer\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"select\": \"Sélectionner\",\n    \"cancel\": \"Annuler les modifications\",\n    \"save\": \"Enregistrer les modifications\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Êtes-vous sûr de vouloir supprimer cet enregistrement?\",\n    \"cancelDelete\": \"Annuler\",\n    \"confirmDelete\": \"Effacer\"\n  },\n  \"noRecords\": \"Aucun enregistrement disponible.\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Tous\",\n  \"page\": \"Page\",\n  \"display\": \"Afficher les items {0} - {1} de {2}\",\n  \"of\": \"de {0}\",\n  \"empty\": \"Aucun enregistrement à afficher.\",\n  \"refresh\": \"Actualiser\",\n  \"first\": \"Aller à la première page\",\n  \"itemsPerPage\": \"articles par page\",\n  \"last\": \"Aller à la dernière page\",\n  \"next\": \"Aller à la page suivante\",\n  \"previous\": \"Aller à la page précédente\",\n  \"morePages\": \"Plusieurs pages\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"Filtrer\",\n  \"clear\": \"Effacer filtre\",\n  \"isFalse\": \"est fausse\",\n  \"isTrue\": \"est vrai\",\n  \"operator\": \"Opérateur\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"Filtrer\",\n  \"and\": \"Et\",\n  \"clear\": \"Effacer filtre\",\n  \"info\": \"Afficher les lignes avec la valeur qui\",\n  \"selectValue\": \"-Sélectionner-\",\n  \"isFalse\": \"est fausse\",\n  \"isTrue\": \"est vrai\",\n  \"or\": \"Ou\",\n  \"cancel\": \"Annuler\",\n  \"operator\": \"Opérateur\",\n  \"value\": \"Valeur\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Choisir toutes\",\n  \"clear\": \"Effacer filtre\",\n  \"filter\": \"Filtrer\",\n  \"search\": \"Recherche\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Faites glisser un en-tête de colonne et déposer ici pour grouper par cette colonne.\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Gras\",\n  \"createLink\": \"Insérer un lien hypertexte\",\n  \"fontName\": \"Police\",\n  \"fontNameInherit\": \"(police héritée)\",\n  \"fontSize\": \"Taille de police\",\n  \"fontSizeInherit\": \"(taille héritée)\",\n  \"formatBlock\": \"Style du paragraphe\",\n  \"indent\": \"Augmenter le retrait\",\n  \"insertHtml\": \"Insérer HTML\",\n  \"insertImage\": \"Insérer image\",\n  \"insertOrderedList\": \"Liste numérotée\",\n  \"insertUnorderedList\": \"Liste à puces\",\n  \"italic\": \"Italique\",\n  \"justifyCenter\": \"Centrer\",\n  \"justifyFull\": \"Justifier\",\n  \"justifyLeft\": \"Aligner le texte à gauche\",\n  \"justifyRight\": \"Aligner le texte à droite\",\n  \"outdent\": \"Diminuer le retrait\",\n  \"strikethrough\": \"Barré\",\n  \"styles\": \"Styles\",\n  \"subscript\": \"Subscript\",\n  \"superscript\": \"Superscript\",\n  \"underline\": \"Souligné\",\n  \"unlink\": \"Supprimer le lien hypertexte\",\n  \"deleteFile\": \"Êtes-vous sûr de vouloir supprimer \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"Un répertoire avec ce nom n'a pas été trouvé.\",\n  \"emptyFolder\": \"Vider le dossier\",\n  \"invalidFileType\": \"Le fichier sélectionné \\\"{0}\\\" n'est pas valide. Les types de fichiers supportés sont {1}.\",\n  \"orderBy\": \"Organiser par:\",\n  \"orderByName\": \"Nom\",\n  \"orderBySize\": \"Taille\",\n  \"overwriteFile\": \"Un fichier avec le nom \\\"{0}\\\" existe déjà dans le répertoire courant. Voulez-vous le remplacer?\",\n  \"uploadFile\": \"Télécharger\",\n  \"backColor\": \"Couleur de fond\",\n  \"foreColor\": \"Couleur\",\n  \"dialogButtonSeparator\": \"Ou\",\n  \"dialogCancel\": \"Fermer\",\n  \"dialogInsert\": \"Insérer\",\n  \"imageAltText\": \"Le texte de remplacement\",\n  \"imageWebAddress\": \"Adresse Web\",\n  \"imageWidth\": \"Largeur (px)\",\n  \"imageHeight\": \"Hauteur (px)\",\n  \"linkOpenInNewWindow\": \"Ouvrir dans une nouvelle fenêtre\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"Info-bulle\",\n  \"linkWebAddress\": \"Adresse Web\",\n  \"search\": \"Search\",\n  \"createTable\": \"Insérer un tableau\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteColumn\": \"Supprimer la colonne\",\n  \"deleteRow\": \"Supprimer ligne\",\n  \"dropFilesHere\": \"drop files here to upload\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Update\",\n  \"insertFile\": \"Insert file\",\n  \"dialogOk\": \"OK\",\n  \"tableWizard\": \"Assistant de tableau\",\n  \"tableTab\": \"Table\",\n  \"cellTab\": \"Cellule\",\n  \"accessibilityTab\": \"Accessibilité\",\n  \"caption\": \"Sous-titre\",\n  \"summary\": \"Sommaire\",\n  \"width\": \"Largeur\",\n  \"height\": \"Hauteur\",\n  \"cellSpacing\": \"Espacement des cellules\",\n  \"cellPadding\": \"Rembourrage des cellules\",\n  \"cellMargin\": \"Marge des cellules\",\n  \"alignment\": \"Alignement\",\n  \"background\": \"Fond\",\n  \"cssClass\": \"CSS Classe\",\n  \"id\": \"Id\",\n  \"border\": \"Bordure\",\n  \"borderStyle\": \"Style de bordure\",\n  \"collapseBorders\": \"Rétracter bordures\",\n  \"wrapText\": \"Renvoi à la ligne\",\n  \"associateCellsWithHeaders\": \"Cellules associées aux entêtes\",\n  \"alignLeft\": \"Aligner à gauche\",\n  \"alignCenter\": \"Aligner le centre\",\n  \"alignRight\": \"Aligner à droite\",\n  \"alignLeftTop\": \"Aligner à gauche et haut\",\n  \"alignCenterTop\": \"Aligner le centre et haut\",\n  \"alignRightTop\": \"Aligner à droite et haut\",\n  \"alignLeftMiddle\": \"Aligner à gauche et milieu\",\n  \"alignCenterMiddle\": \"Aligner le centre et milieu\",\n  \"alignRightMiddle\": \"Aligner à droite et milieu\",\n  \"alignLeftBottom\": \"Aligner à gauche et bas\",\n  \"alignCenterBottom\": \"Aligner le centre et bas\",\n  \"alignRightBottom\": \"Aligner à droite et bas\",\n  \"alignRemove\": \"Retirer alignement\",\n  \"columns\": \"Colonnes\",\n  \"rows\": \"Lignes\",\n  \"selectAllCells\": \"Sélectionner toutes les cellules\"\n});\n}\n\n/* FileBrowser and ImageBrowser messages */\n\nvar browserMessages = {\n  \"uploadFile\" : \"Charger\",\n  \"orderBy\" : \"Trier par\",\n  \"orderByName\" : \"Nom\",\n  \"orderBySize\" : \"Taille\",\n  \"directoryNotFound\" : \"Aucun répértoire de ce nom.\",\n  \"emptyFolder\" : \"Répertoire vide\",\n  \"deleteFile\" : 'Etes-vous sûr de vouloir supprimer \"{0}\"?',\n  \"invalidFileType\" : \"Le fichier sélectionné \\\"{0}\\\" n'est pas valide. Les type fichiers supportés sont {1}.\",\n  \"overwriteFile\" : \"Un fichier du nom \\\"{0}\\\" existe déjà dans ce répertoire. Voulez-vous le remplacer?\",\n  \"dropFilesHere\" : \"glissez les fichiers ici pour les charger\",\n  \"search\" : \"Recherche\"\n};\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages, browserMessages);\n}\n\nif (kendo.ui.ImageBrowser) {\nkendo.ui.ImageBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.ImageBrowser.prototype.options.messages, browserMessages);\n}\n\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Annuler\",\n  \"dropFilesHere\": \"déposer les fichiers à télécharger ici\",\n  \"remove\": \"Retirer\",\n  \"retry\": \"Réessayer\",\n  \"select\": \"Sélectionner...\",\n  \"statusFailed\": \"échoué\",\n  \"statusUploaded\": \"téléchargé\",\n  \"statusUploading\": \"téléchargement\",\n  \"uploadSelectedFiles\": \"Télécharger des fichiers\",\n  \"headerStatusUploaded\": \"Done\",\n  \"headerStatusUploading\": \"Uploading...\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"toute la journée\",\n  \"cancel\": \"Annuler\",\n  \"editable\": {\n    \"confirmation\": \"Etes-vous sûr de vouloir supprimer cet élément?\"\n  },\n  \"date\": \"Date\",\n  \"destroy\": \"Effacer\",\n  \"editor\": {\n    \"allDayEvent\": \"Toute la journée\",\n    \"description\": \"Description\",\n    \"editorTitle\": \"Evènement\",\n    \"end\": \"Fin\",\n    \"endTimezone\": \"End timezone\",\n    \"repeat\": \"Répéter\",\n    \"separateTimezones\": \"Use separate start and end time zones\",\n    \"start\": \"Début\",\n    \"startTimezone\": \"Start timezone\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Fuseau horaire\",\n    \"timezoneEditorTitle\": \"Fuseaux horaires\",\n    \"title\": \"Titre\",\n    \"noTimezone\": \"Pas de fuseau horaire\"\n  },\n  \"event\": \"Evènement\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Voulez-vous supprimer seulement cet évènement ou toute la série?\",\n    \"deleteWindowOccurrence\": \"Suppression de l'élément courant\",\n    \"deleteWindowSeries\": \"Suppression de toute la série\",\n    \"deleteWindowTitle\": \"Suppression d'un élément récurrent\",\n    \"editRecurring\": \"Voulez-vous modifier seulement cet évènement ou toute la série?\",\n    \"editWindowOccurrence\": \"Modifier l'occurrence courante\",\n    \"editWindowSeries\": \"Modifier la série\",\n    \"editWindowTitle\": \"Modification de l'élément courant\"\n  },\n  \"save\": \"Sauvegarder\",\n  \"time\": \"Time\",\n  \"today\": \"Aujourd'hui\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Jour\",\n    \"month\": \"Mois\",\n    \"week\": \"Semaine\",\n    \"workWeek\": \"Semaine de travail\",\n    \"timeline\": \"Chronologie\"\n  },\n  \"deleteWindowTitle\": \"Suppression de l'élément\",\n  \"showFullDay\": \"Montrer toute la journée\",\n  \"showWorkDay\": \"Montrer les heures ouvrables\"\n});\n}\n\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} est requis\",\n  \"pattern\": \"{0} n'est pas valide\",\n  \"min\": \"{0} doit être plus grand ou égal à {1}\",\n  \"max\": \"{0} doit être plus petit ou égal à {1}\",\n  \"step\": \"{0} n'est pas valide\",\n  \"email\": \"{0} n'est pas un courriel valide\",\n  \"url\": \"{0} n'est pas une adresse web valide\",\n  \"date\": \"{0} n'est pas une date valide\",\n  \"dateCompare\": \"La date de fin doit être postérieure à la date de début\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Fermer\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annuler\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Annuler\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}