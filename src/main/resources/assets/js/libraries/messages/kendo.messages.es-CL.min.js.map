{"version": 3, "sources": ["messages/kendo.messages.es-CL.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "noTimezone", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,UACTC,OAAU,cAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,UACTC,OAAU,cAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,mBACjBC,eAAkB,oBAClBC,OAAU,UACVC,QAAW,WACXC,KAAQ,QACRC,SAAY,4BACZC,KAAQ,WACRC,OAAU,iBAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,UACRC,OAAU,UACVC,UAAa,YACbC,cAAiB,UACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,iBACjBC,YAAe,+BACfC,aAAgB,6BAChBC,YAAe,aACfC,oBAAuB,6BACvBC,kBAAqB,0BACrBC,OAAU,gBACVC,QAAW,iBACXC,WAAc,gBACdC,OAAU,kBACVC,YAAe,kBACfC,WAAc,mBACdC,WAAc,gBACdC,SAAY,WACZC,SAAY,iCACZC,gBAAmB,oBACnBC,SAAY,+BACZC,gBAAmB,oBACnBC,YAAe,UACfC,WAAc,UACdC,UAAa,QACbC,UAAa,iBACbC,MAAS,UACTC,YAAe,gBACfC,WAAc,QACdC,QAAW,iBACXC,YAAe,SACfC,YAAe,SACfC,gBAAmB,wFACnBC,WAAc,0CACdC,cAAiB,yFACjBC,kBAAqB,mDACrBC,gBAAmB,gBACnBC,aAAgB,oBAChBC,WAAc,aACdC,YAAe,YACfC,eAAkB,gBAClBC,UAAa,SACbC,eAAkB,gBAClBC,SAAY,QACZC,YAAe,UACfC,oBAAuB,gCACvBC,aAAgB,aAChBC,aAAgB,WAChBC,sBAAyB,IACzBC,aAAgB,WAChBC,YAAe,cACfC,cAAiB,iCACjBC,eAAkB,+BAClBC,YAAe,sBACfC,YAAe,qBACfC,UAAa,cACbC,aAAgB,oBAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,gBACdC,QAAW,cACXE,YAAe,SACfD,YAAe,SACfK,kBAAqB,mDACrBR,YAAe,gBACfM,WAAc,0CACdD,gBAAmB,wFACnBE,cAAiB,yFACjBwB,cAAiB,sCACjBC,OAAU,YAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,KACVC,QAAW,KACXvE,OAAU,UACVwE,MAAS,iBACTC,SAAY,cAMVtF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,aACNC,IAAO,gBACPC,WAAc,eACdC,SAAY,WACZC,eAAkB,cAClBC,SAAY,aACZC,OAAU,UACVC,UAAa,aACbC,QAAW,aACXC,WAAc,iBAEhBC,QACEV,GAAM,aACNC,IAAO,gBACPU,IAAO,uBACPC,GAAM,eACNC,IAAO,uBACPC,GAAM,eACNR,OAAU,UACVC,UAAa,cAEfQ,MACEf,GAAM,aACNC,IAAO,gBACPU,IAAO,yBACPC,GAAM,eACNC,IAAO,wBACPC,GAAM,cACNR,OAAU,UACVC,UAAa,cAEfS,OACEhB,GAAM,aACNC,IAAO,gBACPK,OAAU,UACVC,UAAa,iBAObhG,MAAMC,GAAGyG,aACb1G,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQC,UACnDsG,KAAQ,+BACRxB,OAAU,KACVC,QAAW,KACXvE,OAAU,UACVwE,MAAS,kBACTuB,IAAO,IACPC,GAAM,IACNC,YAAe,uBACfxB,SAAY,WACZyB,MAAS,QACTvG,OAAU,cAMRR,MAAMC,GAAGyG,aACb1G,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyG,WAAWvG,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,aACNC,IAAO,gBACPC,WAAc,eACdC,SAAY,WACZC,eAAkB,cAClBC,SAAY,aACZC,OAAU,UACVC,UAAa,aACbC,QAAW,aACXC,WAAc,iBAEhBC,QACEV,GAAM,aACNC,IAAO,gBACPU,IAAO,uBACPC,GAAM,eACNC,IAAO,uBACPC,GAAM,eACNR,OAAU,UACVC,UAAa,cAEfQ,MACEf,GAAM,aACNC,IAAO,iBACPU,IAAO,yBACPC,GAAM,eACNC,IAAO,wBACPC,GAAM,cACNR,OAAU,UACVC,UAAa,cAEfS,OACEhB,GAAM,aACNC,IAAO,gBACPK,OAAU,UACVC,UAAa,iBAObhG,MAAMC,GAAG+G,mBACbhH,MAAMC,GAAG+G,iBAAiB7G,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+G,iBAAiB7G,UAAUC,QAAQC,UACzD4G,SAAY,mBACZ5B,MAAS,kBACTxE,OAAU,UACVoE,OAAU,YAMRjF,MAAMC,GAAGiH,QACblH,MAAMC,GAAGiH,MAAM/G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiH,MAAM/G,UAAUC,QAAQC,UAC9C8G,SACEC,SAAY,oBACZC,OAAU,gBACVC,YAAe,iBACfC,aAAgB,kBAChBC,IAAO,kBAEThH,OAAU,WACViH,4BAA+B,qBAC/BC,sBAAyB,eACzBC,QAAW,SACXC,QACEC,aAAgB,UAChBC,YAAe,QACfC,IAAO,MACPC,gBAAmB,WACnBC,UAAa,WACbC,qBAAwB,WACxBC,gBAAmB,WACnBC,MAAS,WACTC,MAAS,SACTC,YAAe,YAEjBC,KAAQ,UACRC,OACEC,IAAO,MACPV,IAAO,MACPW,MAAS,MACTN,MAAS,WACTO,KAAQ,SACRC,KAAQ,UAOR5I,MAAMC,GAAG4I,OACb7I,MAAMC,GAAG4I,KAAK1I,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4I,KAAK1I,UAAUC,QAAQC,UAC7CyI,UACEtI,OAAU,mBACVuI,WAAc,WACdC,OAAU,UACVrB,QAAW,WACXsB,KAAQ,SACRC,MAAS,mBACT1B,IAAO,iBACPe,KAAQ,kBACRY,OAAU,cACVC,OAAU,cAEZC,UACEC,aAAgB,WAChBC,aAAgB,6CAChBC,cAAiB,YAEnBC,UAAa,+BAMXzJ,MAAMC,GAAGyJ,YACb1J,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQC,UAClDsJ,MAAS,qFAMP3J,MAAMC,GAAG2J,iBACb5J,MAAMC,GAAG2J,eAAezJ,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,eAAezJ,UAAUC,SAC/CyJ,YAAe,oBACfC,cAAiB,qBAMf9J,MAAMC,GAAG8J,QACb/J,MAAMC,GAAG8J,MAAM5J,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8J,MAAM5J,UAAUC,QAAQC,UAC9C2J,SAAY,QACZC,QAAW,uCACXN,MAAS,oBACTO,KAAQ,SACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,yBACTC,SAAY,0BACZC,KAAQ,2BACRC,KAAQ,wBACRC,QAAW,aACXC,UAAa,iBAMX1K,MAAMC,GAAG0K,mBACb3K,MAAMC,GAAG0K,iBAAiBxK,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0K,iBAAiBxK,UAAUC,QAAQC,UACzDuK,aACEC,MAAS,QACTC,OAAU,WACVC,MAAS,cACTC,OAAU,eACVC,QAAW,eACXC,OAAU,cAEZJ,QACEK,YAAe,iBACfC,SAAY,YAEdL,OACEI,YAAe,iBACfC,SAAY,WAEdJ,QACEI,SAAY,aACZD,YAAe,iBACfE,SAAY,gBAEdJ,SACEE,YAAe,iBACfE,SAAY,eACZD,SAAY,WACZ3C,IAAO,QAETyC,QACEC,YAAe,iBACfE,SAAY,eACZD,SAAY,UACZjB,GAAM,QAERpC,KACEuD,MAAS,OACTC,YAAe,MACfV,MAAS,QACTW,MAAS,UACTC,WAAc,iBACdC,GAAM,OAERC,iBACEtB,MAAS,UACTuB,OAAU,UACVC,MAAS,UACTC,OAAU,SACVtB,KAAQ,UAEVuB,UACEtD,IAAO,MACPuD,QAAW,gBACXC,QAAW,2BAOXjM,MAAMC,GAAGiM,YACblM,MAAMC,GAAGiM,UAAU/L,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiM,UAAU/L,UAAUC,QAAQC,UAClD8L,OAAU,cACV3F,KAAQ,QACR4F,MAAS,SACTC,KAAQ,OACRC,YAAe,uBACfC,YAAe,2BACfC,MAAS,MACTjE,KAAQ,UACR/H,OAAU,WACVmH,QAAW,WACX8E,kBAAqB,kBACrBC,cAAiB,uCACjBC,eAAkB,wBAClBtD,UACEE,aAAgB,iDAElBf,OACEC,IAAO,MACPE,KAAQ,SACRiE,SAAY,iBACZC,OAAU,SACVnE,MAAS,OAEXoE,oBACEL,kBAAqB,+BACrBM,uBAA0B,6BAC1BC,mBAAsB,oBACtBC,gBAAmB,6BACnBC,qBAAwB,2BACxBC,iBAAoB,kBACpBC,gBAAmB,mEACnBC,cAAiB,kEAEnBzF,QACES,MAAS,SACTD,MAAS,SACTL,IAAO,MACPuF,YAAe,cACfC,YAAe,cACfC,OAAU,UACVC,SAAY,IACZC,cAAiB,yBACjBC,YAAe,sBACfC,kBAAqB,wDACrBC,oBAAuB,iBACvBC,qBAAwB,eACxBC,WAAc,mBACdjG,YAAe,aAOf9H,MAAMC,GAAG+N,SACbhO,MAAMC,GAAG+N,OAAO7N,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+N,OAAO7N,UAAUC,SACvC6N,oBAAuB,WACvBC,oBAAuB,eAMrBlO,MAAMC,GAAGkO,WACbnO,MAAMC,GAAGkO,SAAShO,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkO,SAAShO,UAAUC,QAAQC,UACjD+N,QAAW,cACXC,cAAiB,sBACjBC,MAAS,gBAMPtO,MAAMC,GAAGsO,SACbvO,MAAMC,GAAGsO,OAAOpO,UAAUC,QAAQoO,aAClC1O,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsO,OAAOpO,UAAUC,QAAQoO,cAC/CrF,OAAU,gBACV3I,OAAU,WACV8N,MAAS,aACTG,OAAU,SACVC,oBAAuB,iBACvB1J,cAAiB,2CACjB2J,gBAAmB,WACnBC,eAAkB,aAClBC,cAAiB,cACjBC,aAAgB,QAChBC,sBAAyB,cACzBC,qBAAwB,gBAMtBhP,MAAMC,GAAGgP,YACbjP,MAAMC,GAAGgP,UAAU9O,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgP,UAAU9O,UAAUC,QAAQC,UAClD6O,SAAY,mBACZC,QAAW,mBACXC,IAAO,mCACPC,IAAO,mCACPC,KAAQ,mBACRC,MAAS,yCACTC,IAAO,2BACPhJ,KAAQ,6BACRiJ,YAAe,wDAMbzP,MAAMC,GAAGyP,SACb1P,MAAMC,GAAGyP,OAAOvP,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyP,OAAOvP,UAAUC,QAAQoO,cAC/CmB,MAAS,WAMP3P,MAAMC,GAAG2P,QACb5P,MAAMC,GAAG2P,MAAMzP,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2P,MAAMzP,UAAUC,QAAQoO,cAC9CqB,OAAU,QAMR7P,MAAMC,GAAG6P,UACb9P,MAAMC,GAAG6P,QAAQ3P,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6P,QAAQ3P,UAAUC,QAAQoO,cAChDqB,OAAU,KACVrP,OAAU,cAKRR,MAAMC,GAAG8P,SACb/P,MAAMC,GAAG8P,OAAO5P,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8P,OAAO5P,UAAUC,QAAQoO,cAC/CqB,OAAU,KACVrP,OAAU,eAITwP,OAAOhQ,MAAMiQ", "file": "kendo.messages.es-CL.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"Aplicar\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"Orden ascendente\",\n  \"sortDescending\": \"Orden descendente\",\n  \"filter\": \"Filtros\",\n  \"columns\": \"Columnas\",\n  \"done\": \"Hecho\",\n  \"settings\": \"Configuración de columnas\",\n  \"lock\": \"Bloquear\",\n  \"unlock\": \"Desbloquear\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Negrita\",\n  \"italic\": \"Itálica\",\n  \"underline\": \"Subrayado\",\n  \"strikethrough\": \"Tachado\",\n  \"superscript\": \"Superíndice\",\n  \"subscript\": \"Subíndice\",\n  \"justifyCenter\": \"Texto centrado\",\n  \"justifyLeft\": \"Alinear texto a la izquierda\",\n  \"justifyRight\": \"Alinear texto a la derecha\",\n  \"justifyFull\": \"Justificar\",\n  \"insertUnorderedList\": \"Insertar lista desordenada\",\n  \"insertOrderedList\": \"Insertar lista ordenada\",\n  \"indent\": \"Poner sangría\",\n  \"outdent\": \"Quitar sangría\",\n  \"createLink\": \"Insert enlace\",\n  \"unlink\": \"Eliminar enlace\",\n  \"insertImage\": \"Insertar imagen\",\n  \"insertFile\": \"Insertar fichero\",\n  \"insertHtml\": \"Insertar HTML\",\n  \"viewHtml\": \"Ver HTML\",\n  \"fontName\": \"Seleccionar familia de fuentes\",\n  \"fontNameInherit\": \"(fuente heredada)\",\n  \"fontSize\": \"Seleccionar tamaño de fuente\",\n  \"fontSizeInherit\": \"(tamaño heredado)\",\n  \"formatBlock\": \"Formato\",\n  \"formatting\": \"Formato\",\n  \"foreColor\": \"Color\",\n  \"backColor\": \"Color de fondo\",\n  \"style\": \"Estilos\",\n  \"emptyFolder\": \"Carpeta vacía\",\n  \"uploadFile\": \"Subir\",\n  \"orderBy\": \"Ordenados por:\",\n  \"orderBySize\": \"Tamaño\",\n  \"orderByName\": \"Nombre\",\n  \"invalidFileType\": \"El fichero seleccionado \\\"{0}\\\" no es válido. Los tipos de ficheros soportados son {1}.\",\n  \"deleteFile\": '¿Está seguro que quiere eliminar \"{0}\"?',\n  \"overwriteFile\": 'Un fichero con el nombre \"{0}\" ya existe en el directorio actual. ¿Desea reemplazarlo?',\n  \"directoryNotFound\": \"Un directorio con este nombre no fue encontrado.\",\n  \"imageWebAddress\": \"Dirección Web\",\n  \"imageAltText\": \"Texto alternativo\",\n  \"imageWidth\": \"Ancho (px)\",\n  \"imageHeight\": \"Alto (px)\",\n  \"fileWebAddress\": \"Dirección Web\",\n  \"fileTitle\": \"Título\",\n  \"linkWebAddress\": \"Dirección Web\",\n  \"linkText\": \"Texto\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkOpenInNewWindow\": \"Abrir enlace en nueva ventana\",\n  \"dialogUpdate\": \"Actualizar\",\n  \"dialogInsert\": \"Insertar\",\n  \"dialogButtonSeparator\": \"o\",\n  \"dialogCancel\": \"Cancelar\",\n  \"createTable\": \"Crear tabla\",\n  \"addColumnLeft\": \"Agregar columna a la izquierda\",\n  \"addColumnRight\": \"Agregar columna a la derecha\",\n  \"addRowAbove\": \"Agregar fila arriba\",\n  \"addRowBelow\": \"Agregar fila abajo\",\n  \"deleteRow\": \"Borrar fila\",\n  \"deleteColumn\": \"Borrar columna\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Subir fichero\",\n  \"orderBy\": \"Ordenar por\",\n  \"orderByName\": \"Nombre\",\n  \"orderBySize\": \"Tamaño\",\n  \"directoryNotFound\": \"Un directorio con este nombre no fue encontrado.\",\n  \"emptyFolder\": \"Carpeta vacía\",\n  \"deleteFile\": '¿Está seguro que quiere eliminar \"{0}\"?',\n  \"invalidFileType\": \"El fichero seleccionado \\\"{0}\\\" no es válido. Los tipos de ficheros soportados son {1}.\",\n  \"overwriteFile\": \"Un fichero con el nombre \\\"{0}\\\" ya existe en el directorio actual. ¿Desea reemplazarlo?\",\n  \"dropFilesHere\": \"arrastre un fichero aquí para subir\",\n  \"search\": \"Buscar\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"Sí\",\n  \"isFalse\": \"No\",\n  \"filter\": \"Filtrar\",\n  \"clear\": \"Limpiar filtro\",\n  \"operator\": \"Operador\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"No es igual a\",\n    \"startswith\": \"Comienza con\",\n    \"contains\": \"Contiene\",\n    \"doesnotcontain\": \"No contiene\",\n    \"endswith\": \"Termina en\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\",\n    \"isempty\": \"Está vacío\",\n    \"isnotempty\": \"No está vacío\"\n  },\n  \"number\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"No es igual a\",\n    \"gte\": \"Es mayor o igual que\",\n    \"gt\": \"Es mayor que\",\n    \"lte\": \"Es menor o igual que\",\n    \"lt\": \"Es menor que\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\"\n  },\n  \"date\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"No es igual a\",\n    \"gte\": \"Es posterior o igual a\",\n    \"gt\": \"Es posterior\",\n    \"lte\": \"Es anterior o igual a\",\n    \"lt\": \"Es anterior\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\"\n  },\n  \"enums\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"No es igual a\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Mostrar filas con valor que:\",\n  \"isTrue\": \"Sí\",\n  \"isFalse\": \"No\",\n  \"filter\": \"Filtrar\",\n  \"clear\": \"Limpiar filtros\",\n  \"and\": \"Y\",\n  \"or\": \"O\",\n  \"selectValue\": \"-Seleccionar valor -\",\n  \"operator\": \"Operador\",\n  \"value\": \"Valor\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"No es igual a\",\n    \"startswith\": \"Comienza con\",\n    \"contains\": \"Contiene\",\n    \"doesnotcontain\": \"No contiene\",\n    \"endswith\": \"Termina en\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\",\n    \"isempty\": \"Está vacío\",\n    \"isnotempty\": \"No está vacío\"\n  },\n  \"number\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"No es igual a\",\n    \"gte\": \"Es mayor o igual que\",\n    \"gt\": \"Es mayor que\",\n    \"lte\": \"Es menor o igual que\",\n    \"lt\": \"Es menor que\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\"\n  },\n  \"date\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"Es diferente a\",\n    \"gte\": \"Es posterior o igual a\",\n    \"gt\": \"Es posterior\",\n    \"lte\": \"Es anterior o igual a\",\n    \"lt\": \"Es anterior\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\"\n  },\n  \"enums\": {\n    \"eq\": \"Es igual a\",\n    \"neq\": \"No es igual a\",\n    \"isnull\": \"Es nulo\",\n    \"isnotnull\": \"No es nulo\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Seleccionar todo\",\n  \"clear\": \"Limpiar filtros\",\n  \"filter\": \"Filtrar\",\n  \"search\": \"Buscar\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Agregar sub-tarea\",\n    \"append\": \"Agregar tarea\",\n    \"insertAfter\": \"Insertar abajo\",\n    \"insertBefore\": \"Insertar arriba\",\n    \"pdf\": \"Exportar a PDF\"\n  },\n  \"cancel\": \"Cancelar\",\n  \"deleteDependencyWindowTitle\": \"Borrar dependencia\",\n  \"deleteTaskWindowTitle\": \"Borrar tarea\",\n  \"destroy\": \"Borrar\",\n  \"editor\": {\n    \"assingButton\": \"Asignar\",\n    \"editorTitle\": \"Tarea\",\n    \"end\": \"Fin\",\n    \"percentComplete\": \"Completa\",\n    \"resources\": \"Recursos\",\n    \"resourcesEditorTitle\": \"Recursos\",\n    \"resourcesHeader\": \"Recursos\",\n    \"start\": \"Comienzo\",\n    \"title\": \"Título\",\n    \"unitsHeader\": \"Unidades\"\n  },\n  \"save\": \"Guardar\",\n  \"views\": {\n    \"day\": \"Día\",\n    \"end\": \"Fin\",\n    \"month\": \"Mes\",\n    \"start\": \"Comienzo\",\n    \"week\": \"Semana\",\n    \"year\": \"Año\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Cancelar Cambios\",\n    \"canceledit\": \"Cancelar\",\n    \"create\": \"Agregar\",\n    \"destroy\": \"Eliminar\",\n    \"edit\": \"Editar\",\n    \"excel\": \"Exportar a Excel\",\n    \"pdf\": \"Exportar a PDF\",\n    \"save\": \"Guardar Cambios\",\n    \"select\": \"Seleccionar\",\n    \"update\": \"Actualizar\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Cancelar\",\n    \"confirmation\": \"¿Confirma la eliminación de este registro?\",\n    \"confirmDelete\": \"Eliminar\"\n  },\n  \"noRecords\": \"No hay datos disponibles.\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Arrastre el título de una columna y suéltelo aquí para agrupar por ese criterio\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Incrementar valor\",\n  \"downArrowText\": \"Disminuir valor\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Todas\",\n  \"display\": \"Elementos mostrados {0} - {1} de {2}\",\n  \"empty\": \"No hay registros.\",\n  \"page\": \"Página\",\n  \"of\": \"de {0}\",\n  \"itemsPerPage\": \"ítems por página\",\n  \"first\": \"Ir a la primera página\",\n  \"previous\": \"Ir a la página anterior\",\n  \"next\": \"Ir a la página siguiente\",\n  \"last\": \"Ir a la última página\",\n  \"refresh\": \"Actualizar\",\n  \"morePages\": \"Más paginas\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Nunca\",\n    \"hourly\": \"Por hora\",\n    \"daily\": \"Diariamente\",\n    \"weekly\": \"Semanalmente\",\n    \"monthly\": \"Mensualmente\",\n    \"yearly\": \"Anualmente\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Repetir cada: \",\n    \"interval\": \" hora(s)\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Repetir cada: \",\n    \"interval\": \" día(s)\"\n  },\n  \"weekly\": {\n    \"interval\": \" semana(s)\",\n    \"repeatEvery\": \"Repetir cada: \",\n    \"repeatOn\": \"Repetir en: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Repetir cada: \",\n    \"repeatOn\": \"Repetir en: \",\n    \"interval\": \" mes(es)\",\n    \"day\": \"Día \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Repetir cada: \",\n    \"repeatOn\": \"Repetir en: \",\n    \"interval\": \" año(s)\",\n    \"of\": \" de \"\n  },\n  \"end\": {\n    \"label\": \"Fin:\",\n    \"mobileLabel\": \"Fin\",\n    \"never\": \"Nunca\",\n    \"after\": \"Después\",\n    \"occurrence\": \" ocurrencia(s)\",\n    \"on\": \"En \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"primero\",\n    \"second\": \"segundo\",\n    \"third\": \"tercero\",\n    \"fourth\": \"cuarto\",\n    \"last\": \"último\"\n  },\n  \"weekdays\": {\n    \"day\": \"día\",\n    \"weekday\": \"día de semana\",\n    \"weekend\": \"día de fin de semana\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"todo el día\",\n  \"date\": \"Fecha\",\n  \"event\": \"Evento\",\n  \"time\": \"Hora\",\n  \"showFullDay\": \"Mostrar día completo\",\n  \"showWorkDay\": \"Mostrar horas laborables\",\n  \"today\": \"Hoy\",\n  \"save\": \"Guardar\",\n  \"cancel\": \"Cancelar\",\n  \"destroy\": \"Eliminar\",\n  \"deleteWindowTitle\": \"Eliminar evento\",\n  \"ariaSlotLabel\": \"Seleccionado desde {0:t} hasta {1:t}\",\n  \"ariaEventLabel\": \"{0} en {1:D} al {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"¿Está seguro que quiere eliminar este evento?\"\n  },\n  \"views\": {\n    \"day\": \"Día\",\n    \"week\": \"Semana\",\n    \"workWeek\": \"Semana laboral\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Mes\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Eliminar elemento recurrente\",\n    \"deleteWindowOccurrence\": \"Eliminar ocurrencia actual\",\n    \"deleteWindowSeries\": \"Eliminar la serie\",\n    \"editWindowTitle\": \"Editar elemento recurrente\",\n    \"editWindowOccurrence\": \"Editar ocurrencia actual\",\n    \"editWindowSeries\": \"Editar la serie\",\n    \"deleteRecurring\": \"¿Quiere eliminar esta ocurrencia del evento o la serie completa?\",\n    \"editRecurring\": \"¿Quiere editar esta ocurrencia del evento o la serie completa?\"\n  },\n  \"editor\": {\n    \"title\": \"Título\",\n    \"start\": \"Inicio\",\n    \"end\": \"Fin\",\n    \"allDayEvent\": \"Todo el día\",\n    \"description\": \"Descripción\",\n    \"repeat\": \"Repetir\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Zona horaria de inicio\",\n    \"endTimezone\": \"Zona horaria de fin\",\n    \"separateTimezones\": \"Usar zonas horarias separadas para el inicio y el fin\",\n    \"timezoneEditorTitle\": \"Zonas horarias\",\n    \"timezoneEditorButton\": \"Zona horaria\",\n    \"noTimezone\": \"Sin zona horaria\",\n    \"editorTitle\": \"Evento\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Aumentar\",\n  \"decreaseButtonTitle\": \"Disminuir\"\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Cargando...\",\n  \"requestFailed\": \"Fallo en solicitud.\",\n  \"retry\": \"Reintentar\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Seleccione...\",\n  \"cancel\": \"Cancelar\",\n  \"retry\": \"Reintentar\",\n  \"remove\": \"Quitar\",\n  \"uploadSelectedFiles\": \"Subir archivos\",\n  \"dropFilesHere\": \"Arrastre los archivos aquí para subirlos\",\n  \"statusUploading\": \"subiendo\",\n  \"statusUploaded\": \"Completado\",\n  \"statusWarning\": \"advertencia\",\n  \"statusFailed\": \"Error\",\n  \"headerStatusUploading\": \"Subiendo...\",\n  \"headerStatusUploaded\": \"Completado\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} es requerido\",\n  \"pattern\": \"{0} no es válido\",\n  \"min\": \"{0} debe ser mayor o igual a {1}\",\n  \"max\": \"{0} debe ser menor o igual a {1}\",\n  \"step\": \"{0} no es válido\",\n  \"email\": \"{0} no es un correo electrónico válido\",\n  \"url\": \"{0} no es una URL válida\",\n  \"date\": \"{0} no es una fecha válida\",\n  \"dateCompare\": \"Fecha final debe ser mayor o igual a fecha inicial\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Cerca\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Cancelar\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}