{"version": 3, "sources": ["messages/kendo.messages.fa-IR.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "TreeView", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "dateCompare", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGNC,MAAMC,GAAGC,kBACTF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACtDE,MAAS,QACTC,OAAU,YAMdR,MAAMC,GAAGQ,cACTT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UAClDE,MAAS,QACTC,OAAU,YAMdR,MAAMC,GAAGS,aACTV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACjDM,cAAiB,kBACjBC,eAAkB,kBAClBC,OAAU,QACVC,QAAW,UACXC,KAAQ,OACRC,SAAY,kBACZC,KAAQ,OACRC,OAAU,cAMdlB,MAAMC,GAAGkB,SACTnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC7Ce,KAAQ,OACRC,OAAU,SACVC,UAAa,YACbC,cAAiB,gBACjBC,YAAe,cACfC,UAAa,YACbC,cAAiB,cACjBC,YAAe,kBACfC,aAAgB,mBAChBC,YAAe,UACfC,oBAAuB,wBACvBC,kBAAqB,sBACrBC,OAAU,SACVC,QAAW,UACXC,WAAc,mBACdC,OAAU,mBACVC,YAAe,eACfC,WAAc,cACdC,WAAc,gBACdC,SAAY,YACZC,SAAY,qBACZC,gBAAmB,mBACnBC,SAAY,mBACZC,gBAAmB,mBACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,QACbC,UAAa,mBACbC,MAAS,SACTC,YAAe,eACfC,WAAc,SACdC,QAAW,cACXC,YAAe,OACfC,YAAe,OACfC,gBAAmB,sEACnBC,WAAc,yCACdC,cAAiB,+FACjBC,kBAAqB,4CACrBC,gBAAmB,cACnBC,aAAgB,iBAChBC,WAAc,aACdC,YAAe,cACfC,eAAkB,cAClBC,UAAa,QACbC,eAAkB,cAClBC,SAAY,OACZC,YAAe,UACfC,oBAAuB,0BACvBC,aAAgB,SAChBC,aAAgB,SAChBC,sBAAyB,KACzBC,aAAgB,SAChBC,YAAe,eACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,UAAa,aACbC,aAAgB,mBAMpB9E,MAAMC,GAAG8E,cACT/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UAClD6C,WAAc,WACdC,QAAW,oBACXE,YAAe,MACfD,YAAe,SACfK,kBAAqB,0BACrBR,YAAe,aACfM,WAAc,kCACdD,gBAAmB,wEACnBE,cAAiB,mFACjBwB,cAAiB,yBACjBC,OAAU,WAMdjF,MAAMC,GAAGiF,aACTlF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACjD8E,OAAU,YACVC,QAAW,aACXvE,OAAU,QACVwE,MAAS,WACTC,SAAY,WAMhBtF,MAAMC,GAAGiF,aACTlF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACjDC,QACIC,GAAM,gBACNC,IAAO,iBACPC,WAAc,cACdC,SAAY,YACZC,eAAkB,aAClBC,SAAY,iBAEhBC,QACIN,GAAM,gBACNC,IAAO,iBACPM,IAAO,0BACPC,GAAM,iBACNC,IAAO,0BACPC,GAAM,gBAEVC,MACIX,GAAM,gBACNC,IAAO,iBACPM,IAAO,4BACPC,GAAM,SACNC,IAAO,4BACPC,GAAM,UAEVE,OACIZ,GAAM,gBACNC,IAAO,qBAOf1F,MAAMC,GAAGqG,aACTtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,UACjDkG,KAAQ,2BACRpB,OAAU,YACVC,QAAW,aACXvE,OAAU,QACVwE,MAAS,WACTmB,IAAO,IACPC,GAAM,KACNC,YAAe,iBACfpB,SAAY,QACZqB,MAAS,QACTnG,OAAU,YAMdR,MAAMC,GAAGqG,aACTtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,WACjDC,QACIC,GAAM,gBACNC,IAAO,iBACPC,WAAc,cACdC,SAAY,YACZC,eAAkB,aAClBC,SAAY,iBAEhBC,QACIN,GAAM,gBACNC,IAAO,iBACPM,IAAO,0BACPC,GAAM,iBACNC,IAAO,0BACPC,GAAM,gBAEVC,MACIX,GAAM,gBACNC,IAAO,iBACPM,IAAO,4BACPC,GAAM,SACNC,IAAO,4BACPC,GAAM,UAEVE,OACIZ,GAAM,gBACNC,IAAO,qBAOf1F,MAAMC,GAAG2G,mBACT5G,MAAMC,GAAG2G,iBAAiBzG,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,iBAAiBzG,UAAUC,QAAQC,UACvDwG,SAAY,aACZxB,MAAS,WACTxE,OAAU,WAMdb,MAAMC,GAAG6G,QACT9G,MAAMC,GAAG6G,MAAM3G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6G,MAAM3G,UAAUC,QAAQC,UAC5C0G,SACIC,SAAY,mBACZC,OAAU,iBACVC,YAAe,eACfC,aAAgB,iBAChBC,IAAO,mBAEX5G,OAAU,SACV6G,4BAA+B,YAC/BC,sBAAyB,UACzBC,QAAW,MACXC,QACIC,aAAgB,aAChBC,YAAe,MACfC,IAAO,QACPC,gBAAmB,SACnBC,UAAa,QACbC,qBAAwB,QACxBC,gBAAmB,QACnBC,MAAS,OACTC,MAAS,QACTC,YAAe,UAEnBC,KAAQ,QACRC,OACIC,IAAO,MACPV,IAAO,QACPW,MAAS,MACTN,MAAS,OACTO,KAAQ,OACRC,KAAQ,UAOhBxI,MAAMC,GAAGwI,OACTzI,MAAMC,GAAGwI,KAAKtI,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwI,KAAKtI,UAAUC,QAAQC,UAC3CqI,UACIlI,OAAU,SACVmI,WAAc,SACdC,OAAU,uBACVrB,QAAW,MACXsB,KAAQ,SACRC,MAAS,cACT1B,IAAO,YACPe,KAAQ,QACRY,OAAU,SACVC,OAAU,SAEdC,UACIC,aAAgB,SAChBC,aAAgB,+BAChBC,cAAiB,OAErBC,UAAa,yBAMjBrJ,MAAMC,GAAGqJ,YACTtJ,MAAMC,GAAGqJ,UAAUnJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqJ,UAAUnJ,UAAUC,QAAQC,UAChDkJ,MAAS,kEAMbvJ,MAAMC,GAAGuJ,iBACTxJ,MAAMC,GAAGuJ,eAAerJ,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuJ,eAAerJ,UAAUC,SAC7CqJ,YAAe,aACfC,cAAiB,aAMrB1J,MAAMC,GAAG0J,QACT3J,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,UAC5CuJ,SAAY,MACZC,QAAW,8BACXN,MAAS,8BACTO,KAAQ,OACRC,GAAM,SACNC,aAAgB,mBAChBC,MAAS,kBACTC,SAAY,kBACZC,KAAQ,kBACRC,KAAQ,kBACRC,QAAW,gBACXC,UAAa,iBAMjBtK,MAAMC,GAAGsK,YACTvK,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQC,UAChDmK,cAAiB,wBACjBC,aAAgB,0BAChBC,UAAa,2BAMjB1K,MAAMC,GAAG0K,iBACT3K,MAAMC,GAAG0K,eAAexK,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0K,eAAexK,UAAUC,QAAQC,UACrDkG,KAAQ,8BACRqE,aAAgB,gBAChB/J,OAAU,SACVgK,QAAW,oBACX5C,MAAS,oBACT5C,MAAS,QACTyF,GAAM,KACNtK,OAAU,SACV+E,WACIK,SAAY,WACZC,eAAkB,mBAClBF,WAAc,cACdG,SAAY,YACZL,GAAM,cACNC,IAAO,sBAOf1F,MAAMC,GAAG8K,mBACT/K,MAAMC,GAAG8K,iBAAiB5K,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8K,iBAAiB5K,UAAUC,QAAQC,UACvD2K,aACIC,MAAS,UACTC,OAAU,QACVC,MAAS,SACTC,OAAU,QACVC,QAAW,SACXC,OAAU,WAEdJ,QACIK,YAAe,gBACfC,SAAY,SAEhBL,OACII,YAAe,gBACfC,SAAY,QAEhBJ,QACII,SAAY,QACZD,YAAe,gBACfE,SAAY,iBAEhBJ,SACIE,YAAe,gBACfE,SAAY,gBACZD,SAAY,OACZnD,IAAO,QAEXiD,QACIC,YAAe,gBACfE,SAAY,gBACZD,SAAY,OACZzB,GAAM,QAEVpC,KACI+D,MAAS,SACTC,YAAe,QACfV,MAAS,UACTW,MAAS,UACTC,WAAc,cACdC,GAAM,OAEVC,iBACI9B,MAAS,MACT+B,OAAU,MACVC,MAAS,MACTC,OAAU,QACV9B,KAAQ,OAEZ+B,UACI9D,IAAO,MACP+D,QAAW,WACXC,QAAW,iBAOnBrM,MAAMC,GAAGqM,YACTtM,MAAMC,GAAGqM,UAAUnM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,UAAUnM,UAAUC,QAAQC,UAChDkM,OAAU,UACVnG,KAAQ,OACRoG,MAAS,QACTC,KAAQ,OACRC,YAAe,gBACfC,YAAe,sBACfC,MAAS,QACTzE,KAAQ,OACR3H,OAAU,SACV+G,QAAW,SACXsF,kBAAqB,eACrBC,cAAiB,+BACjBC,eAAkB,wBAClB9D,UACIE,aAAgB,+CAEpBf,OACIC,IAAO,MACPE,KAAQ,OACRyE,SAAY,YACZC,OAAU,SACV3E,MAAS,SAEb4E,oBACIL,kBAAqB,wBACrBM,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,gBAAmB,sBACnBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,wEACnBC,cAAiB,uEAErBjG,QACIS,MAAS,QACTD,MAAS,QACTL,IAAO,MACP+F,YAAe,gBACfC,YAAe,cACfC,OAAU,SACVC,SAAY,IACZC,cAAiB,iBACjBC,YAAe,eACfC,kBAAqB,wCACrBC,oBAAuB,YACvBC,qBAAwB,YACxBC,cAAiB,aACjBC,WAAc,cACd1G,YAAe,YAOvB1H,MAAMC,GAAGoO,SACTrO,MAAMC,GAAGoO,OAAOlO,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoO,OAAOlO,UAAUC,SACrCkO,oBAAuB,SACvBC,oBAAuB,UAM3BvO,MAAMC,GAAGuO,WACTxO,MAAMC,GAAGuO,SAASrO,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuO,SAASrO,UAAUC,QAAQC,UAC/CoO,OAAU,8BACVC,QAAW,qBACXC,cAAiB,yBACjBC,MAAS,YACTlG,UACIG,KAAQ,SACRG,OAAU,QACVL,WAAc,SACdC,OAAU,gBACViG,YAAe,eACftH,QAAW,MACXuB,MAAS,cACT1B,IAAO,gBAOfpH,MAAMC,GAAG6O,WACT9O,MAAMC,GAAG6O,SAAS3O,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6O,SAAS3O,UAAUC,QAAQC,UAC/CqO,QAAW,qBACXC,cAAiB,yBACjBC,MAAS,eAMb5O,MAAMC,GAAG8O,SACT/O,MAAMC,GAAG8O,OAAO5O,UAAUC,QAAQ4O,aAClClP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8O,OAAO5O,UAAUC,QAAQ4O,cAC7CjG,OAAU,qBACVvI,OAAU,SACVoO,MAAS,YACTK,OAAU,MACVC,oBAAuB,oBACvBlK,cAAiB,2CACjBmK,gBAAmB,kBACnBC,eAAkB,iBAClBC,cAAiB,QACjBC,aAAgB,kBAChBC,sBAAyB,qBACzBC,qBAAwB,oBAM5BxP,MAAMC,GAAGwP,YACTzP,MAAMC,GAAGwP,UAAUtP,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwP,UAAUtP,UAAUC,QAAQC,UAChDqP,SAAY,iBACZC,QAAW,iBACXC,IAAO,uCACPC,IAAO,uCACPC,KAAQ,iBACRC,MAAS,0BACTC,IAAO,8BACP5J,KAAQ,uBACR6J,YAAe,uDAGxBC,OAAOlQ,MAAMmQ", "file": "kendo.messages.fa-IR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\r\n    /* FlatColorPicker messages */\r\n\r\n    if (kendo.ui.FlatColorPicker) {\r\n        kendo.ui.FlatColorPicker.prototype.options.messages =\r\n        $.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages, {\r\n            \"apply\": \"تایید\",\r\n            \"cancel\": \"انصراف\"\r\n        });\r\n    }\r\n\r\n    /* ColorPicker messages */\r\n\r\n    if (kendo.ui.ColorPicker) {\r\n        kendo.ui.ColorPicker.prototype.options.messages =\r\n        $.extend(true, kendo.ui.ColorPicker.prototype.options.messages, {\r\n            \"apply\": \"تایید\",\r\n            \"cancel\": \"انصراف\"\r\n        });\r\n    }\r\n\r\n    /* ColumnMenu messages */\r\n\r\n    if (kendo.ui.ColumnMenu) {\r\n        kendo.ui.ColumnMenu.prototype.options.messages =\r\n        $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\r\n            \"sortAscending\": \"مرتب سازی صعودی\",\r\n            \"sortDescending\": \"مرتب سازی نزولی\",\r\n            \"filter\": \"فیلتر\",\r\n            \"columns\": \"ستون ها\",\r\n            \"done\": \"تمام\",\r\n            \"settings\": \"تنظیمات ستون ها\",\r\n            \"lock\": \"بستن\",\r\n            \"unlock\": \"باز کردن\"\r\n        });\r\n    }\r\n\r\n    /* Editor messages */\r\n\r\n    if (kendo.ui.Editor) {\r\n        kendo.ui.Editor.prototype.options.messages =\r\n        $.extend(true, kendo.ui.Editor.prototype.options.messages, {\r\n            \"bold\": \"Bold\",\r\n            \"italic\": \"Italic\",\r\n            \"underline\": \"Underline\",\r\n            \"strikethrough\": \"Strikethrough\",\r\n            \"superscript\": \"Superscript\",\r\n            \"subscript\": \"Subscript\",\r\n            \"justifyCenter\": \"Center text\",\r\n            \"justifyLeft\": \"Align text left\",\r\n            \"justifyRight\": \"Align text right\",\r\n            \"justifyFull\": \"Justify\",\r\n            \"insertUnorderedList\": \"Insert unordered list\",\r\n            \"insertOrderedList\": \"Insert ordered list\",\r\n            \"indent\": \"Indent\",\r\n            \"outdent\": \"Outdent\",\r\n            \"createLink\": \"Insert hyperlink\",\r\n            \"unlink\": \"Remove hyperlink\",\r\n            \"insertImage\": \"Insert image\",\r\n            \"insertFile\": \"Insert file\",\r\n            \"insertHtml\": \"درج متن آماده\",\r\n            \"viewHtml\": \"View HTML\",\r\n            \"fontName\": \"Select font family\",\r\n            \"fontNameInherit\": \"(inherited font)\",\r\n            \"fontSize\": \"Select font size\",\r\n            \"fontSizeInherit\": \"(inherited size)\",\r\n            \"formatBlock\": \"Format\",\r\n            \"formatting\": \"Format\",\r\n            \"foreColor\": \"Color\",\r\n            \"backColor\": \"Background color\",\r\n            \"style\": \"Styles\",\r\n            \"emptyFolder\": \"Empty Folder\",\r\n            \"uploadFile\": \"Upload\",\r\n            \"orderBy\": \"Arrange by:\",\r\n            \"orderBySize\": \"Size\",\r\n            \"orderByName\": \"Name\",\r\n            \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\r\n            \"deleteFile\": 'Are you sure you want to delete \"{0}\"?',\r\n            \"overwriteFile\": 'A file with name \"{0}\" already exists in the current directory. Do you want to overwrite it?',\r\n            \"directoryNotFound\": \"A directory with this name was not found.\",\r\n            \"imageWebAddress\": \"Web address\",\r\n            \"imageAltText\": \"Alternate text\",\r\n            \"imageWidth\": \"Width (px)\",\r\n            \"imageHeight\": \"Height (px)\",\r\n            \"fileWebAddress\": \"Web address\",\r\n            \"fileTitle\": \"Title\",\r\n            \"linkWebAddress\": \"Web address\",\r\n            \"linkText\": \"Text\",\r\n            \"linkToolTip\": \"ToolTip\",\r\n            \"linkOpenInNewWindow\": \"Open link in new window\",\r\n            \"dialogUpdate\": \"Update\",\r\n            \"dialogInsert\": \"Insert\",\r\n            \"dialogButtonSeparator\": \"or\",\r\n            \"dialogCancel\": \"Cancel\",\r\n            \"createTable\": \"Create table\",\r\n            \"addColumnLeft\": \"Add column on the left\",\r\n            \"addColumnRight\": \"Add column on the right\",\r\n            \"addRowAbove\": \"Add row above\",\r\n            \"addRowBelow\": \"Add row below\",\r\n            \"deleteRow\": \"Delete row\",\r\n            \"deleteColumn\": \"Delete column\"\r\n        });\r\n    }\r\n\r\n    /* FileBrowser messages */\r\n\r\n    if (kendo.ui.FileBrowser) {\r\n        kendo.ui.FileBrowser.prototype.options.messages =\r\n        $.extend(true, kendo.ui.FileBrowser.prototype.options.messages, {\r\n            \"uploadFile\": \"بارگزاری\",\r\n            \"orderBy\": \"مرتب سازی بر اساس\",\r\n            \"orderByName\": \"نام\",\r\n            \"orderBySize\": \"اندازه\",\r\n            \"directoryNotFound\": \"فولدر مورد نظر پیدا نشد\",\r\n            \"emptyFolder\": \"فولدر خالی\",\r\n            \"deleteFile\": 'آیا از حذف \"{0}\" اطمینان دارید؟',\r\n            \"invalidFileType\": \"انتخاب فایل با پسوند \\\"{0}\\\" امکانپذیر نیست. پسوندهای پشیتبانی شده: {1}\",\r\n            \"overwriteFile\": \"فایل با نام \\\"{0}\\\" در فولدر انتخابی وجود دارد. آیا می خواهید آن را بازنویسی کنید؟\",\r\n            \"dropFilesHere\": \"فایل را به اینجا بکشید\",\r\n            \"search\": \"جستجو\"\r\n        });\r\n    }\r\n\r\n    /* FilterCell messages */\r\n\r\n    if (kendo.ui.FilterCell) {\r\n        kendo.ui.FilterCell.prototype.options.messages =\r\n        $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\r\n            \"isTrue\": \"درست باشد\",\r\n            \"isFalse\": \"درست نباشد\",\r\n            \"filter\": \"فیلتر\",\r\n            \"clear\": \"پاک کردن\",\r\n            \"operator\": \"عملگر\"\r\n        });\r\n    }\r\n\r\n    /* FilterCell operators */\r\n\r\n    if (kendo.ui.FilterCell) {\r\n        kendo.ui.FilterCell.prototype.options.operators =\r\n        $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\r\n            \"string\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\",\r\n                \"startswith\": \"شروع شود با\",\r\n                \"contains\": \"شامل باشد\",\r\n                \"doesnotcontain\": \"شامل نباشد\",\r\n                \"endswith\": \"پایان یابد با\"\r\n            },\r\n            \"number\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\",\r\n                \"gte\": \"برابر یا بزرگتر باشد از\",\r\n                \"gt\": \"بزرگتر باشد از\",\r\n                \"lte\": \"کمتر و یا برابر باشد با\",\r\n                \"lt\": \"کمتر باشد از\"\r\n            },\r\n            \"date\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\",\r\n                \"gte\": \"بعد از یا هم زمان باشد با\",\r\n                \"gt\": \"بعد از\",\r\n                \"lte\": \"قبل از یا هم زمان باشد با\",\r\n                \"lt\": \"قبل از\"\r\n            },\r\n            \"enums\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\"\r\n            }\r\n        });\r\n    }\r\n\r\n    /* FilterMenu messages */\r\n\r\n    if (kendo.ui.FilterMenu) {\r\n        kendo.ui.FilterMenu.prototype.options.messages =\r\n        $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\r\n            \"info\": \"ردیف های را نشان بده که:\",\r\n            \"isTrue\": \"درست باشد\",\r\n            \"isFalse\": \"درست نباشد\",\r\n            \"filter\": \"فیلتر\",\r\n            \"clear\": \"پاک کردن\",\r\n            \"and\": \"و\",\r\n            \"or\": \"یا\",\r\n            \"selectValue\": \"-انتخاب مقدار-\",\r\n            \"operator\": \"عملگر\",\r\n            \"value\": \"مقدار\",\r\n            \"cancel\": \"انصراف\"\r\n        });\r\n    }\r\n\r\n    /* FilterMenu operator messages */\r\n\r\n    if (kendo.ui.FilterMenu) {\r\n        kendo.ui.FilterMenu.prototype.options.operators =\r\n        $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\r\n            \"string\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\",\r\n                \"startswith\": \"شروع شود با\",\r\n                \"contains\": \"شامل باشد\",\r\n                \"doesnotcontain\": \"شامل نباشد\",\r\n                \"endswith\": \"پایان یابد با\"\r\n            },\r\n            \"number\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\",\r\n                \"gte\": \"برابر یا بزرگتر باشد از\",\r\n                \"gt\": \"بزرگتر باشد از\",\r\n                \"lte\": \"کمتر و یا برابر باشد با\",\r\n                \"lt\": \"کمتر باشد از\"\r\n            },\r\n            \"date\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\",\r\n                \"gte\": \"بعد از یا هم زمان باشد با\",\r\n                \"gt\": \"بعد از\",\r\n                \"lte\": \"قبل از یا هم زمان باشد با\",\r\n                \"lt\": \"قبل از\"\r\n            },\r\n            \"enums\": {\r\n                \"eq\": \"برابر باشد با\",\r\n                \"neq\": \"برابر نباشد با\"\r\n            }\r\n        });\r\n    }\r\n\r\n    /* FilterMultiCheck messages */\r\n\r\n    if (kendo.ui.FilterMultiCheck) {\r\n        kendo.ui.FilterMultiCheck.prototype.options.messages =\r\n        $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages, {\r\n            \"checkAll\": \"انتخاب همه\",\r\n            \"clear\": \"پاک کردن\",\r\n            \"filter\": \"فیلتر\"\r\n        });\r\n    }\r\n\r\n    /* Gantt messages */\r\n\r\n    if (kendo.ui.Gantt) {\r\n        kendo.ui.Gantt.prototype.options.messages =\r\n        $.extend(true, kendo.ui.Gantt.prototype.options.messages, {\r\n            \"actions\": {\r\n                \"addChild\": \"اضافه کردن فرزند\",\r\n                \"append\": \"اضافه کردن کار\",\r\n                \"insertAfter\": \"اضافه کن زیر\",\r\n                \"insertBefore\": \"اضافه کن بالای\",\r\n                \"pdf\": \"گرفتن خروجی PDF\"\r\n            },\r\n            \"cancel\": \"انصراف\",\r\n            \"deleteDependencyWindowTitle\": \"حذف رابطه\",\r\n            \"deleteTaskWindowTitle\": \"حذف کار\",\r\n            \"destroy\": \"حذف\",\r\n            \"editor\": {\r\n                \"assingButton\": \"ارجاع دادن\",\r\n                \"editorTitle\": \"کار\",\r\n                \"end\": \"پایان\",\r\n                \"percentComplete\": \"پیشرفت\",\r\n                \"resources\": \"منابع\",\r\n                \"resourcesEditorTitle\": \"منابع\",\r\n                \"resourcesHeader\": \"منابع\",\r\n                \"start\": \"شروع\",\r\n                \"title\": \"عنوان\",\r\n                \"unitsHeader\": \"واحدها\"\r\n            },\r\n            \"save\": \"ذخیره\",\r\n            \"views\": {\r\n                \"day\": \"روز\",\r\n                \"end\": \"پایان\",\r\n                \"month\": \"ماه\",\r\n                \"start\": \"شروع\",\r\n                \"week\": \"هفته\",\r\n                \"year\": \"سال\"\r\n            }\r\n        });\r\n    }\r\n\r\n    /* Grid messages */\r\n\r\n    if (kendo.ui.Grid) {\r\n        kendo.ui.Grid.prototype.options.messages =\r\n        $.extend(true, kendo.ui.Grid.prototype.options.messages, {\r\n            \"commands\": {\r\n                \"cancel\": \"انصراف\",\r\n                \"canceledit\": \"انصراف\",\r\n                \"create\": \"اضافه کردن ردیف جدید\",\r\n                \"destroy\": \"حذف\",\r\n                \"edit\": \"ویرایش\",\r\n                \"excel\": \"خروجی Excel\",\r\n                \"pdf\": \"خروجی PDF\",\r\n                \"save\": \"ذخیره\",\r\n                \"select\": \"انتخاب\",\r\n                \"update\": \"ذخیره\"\r\n            },\r\n            \"editable\": {\r\n                \"cancelDelete\": \"انصراف\",\r\n                \"confirmation\": \"آیا از حذف این ردیف مطمئنید؟\",\r\n                \"confirmDelete\": \"حذف\"\r\n            },\r\n            \"noRecords\": \"اطلاعاتی وجود ندارد\"\r\n        });\r\n    }\r\n\r\n    /* Groupable messages */\r\n\r\n    if (kendo.ui.Groupable) {\r\n        kendo.ui.Groupable.prototype.options.messages =\r\n        $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\r\n            \"empty\": \"برای گروه بندی بر اساس یک ستون، عنوان ستون را به اینجا بکشید\"\r\n        });\r\n    }\r\n\r\n    /* NumericTextBox messages */\r\n\r\n    if (kendo.ui.NumericTextBox) {\r\n        kendo.ui.NumericTextBox.prototype.options =\r\n        $.extend(true, kendo.ui.NumericTextBox.prototype.options, {\r\n            \"upArrowText\": \"اضافه کردن\",\r\n            \"downArrowText\": \"کم کردن\"\r\n        });\r\n    }\r\n\r\n    /* Pager messages */\r\n\r\n    if (kendo.ui.Pager) {\r\n        kendo.ui.Pager.prototype.options.messages =\r\n        $.extend(true, kendo.ui.Pager.prototype.options.messages, {\r\n            \"allPages\": \"همه\",\r\n            \"display\": \"ردیف {0} تا {1} از {2} ردیف\",\r\n            \"empty\": \"ردیفی برای نمایش وجود ندارد\",\r\n            \"page\": \"صفحه\",\r\n            \"of\": \"از {0}\",\r\n            \"itemsPerPage\": \"ردیف های هر صفحه\",\r\n            \"first\": \"برو به صفحه اول\",\r\n            \"previous\": \"برو به صفحه قبل\",\r\n            \"next\": \"برو به صفحه بعد\",\r\n            \"last\": \"برو به صفحه آخر\",\r\n            \"refresh\": \"بارگزاری مجدد\",\r\n            \"morePages\": \"صفحات بیشتر\"\r\n        });\r\n    }\r\n\r\n    /* PivotGrid messages */\r\n\r\n    if (kendo.ui.PivotGrid) {\r\n        kendo.ui.PivotGrid.prototype.options.messages =\r\n        $.extend(true, kendo.ui.PivotGrid.prototype.options.messages, {\r\n            \"measureFields\": \"Drop Data Fields Here\",\r\n            \"columnFields\": \"Drop Column Fields Here\",\r\n            \"rowFields\": \"Drop Rows Fields Here\"\r\n        });\r\n    }\r\n\r\n    /* PivotFieldMenu messages */\r\n\r\n    if (kendo.ui.PivotFieldMenu) {\r\n        kendo.ui.PivotFieldMenu.prototype.options.messages =\r\n        $.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages, {\r\n            \"info\": \"Show items with value that:\",\r\n            \"filterFields\": \"Fields Filter\",\r\n            \"filter\": \"Filter\",\r\n            \"include\": \"Include Fields...\",\r\n            \"title\": \"Fields to include\",\r\n            \"clear\": \"Clear\",\r\n            \"ok\": \"Ok\",\r\n            \"cancel\": \"Cancel\",\r\n            \"operators\": {\r\n                \"contains\": \"Contains\",\r\n                \"doesnotcontain\": \"Does not contain\",\r\n                \"startswith\": \"Starts with\",\r\n                \"endswith\": \"Ends with\",\r\n                \"eq\": \"Is equal to\",\r\n                \"neq\": \"Is not equal to\"\r\n            }\r\n        });\r\n    }\r\n\r\n    /* RecurrenceEditor messages */\r\n\r\n    if (kendo.ui.RecurrenceEditor) {\r\n        kendo.ui.RecurrenceEditor.prototype.options.messages =\r\n        $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\r\n            \"frequencies\": {\r\n                \"never\": \"هیچ وقت\",\r\n                \"hourly\": \"ساعتی\",\r\n                \"daily\": \"روزانه\",\r\n                \"weekly\": \"هفتگی\",\r\n                \"monthly\": \"ماهانه\",\r\n                \"yearly\": \"سالیانه\"\r\n            },\r\n            \"hourly\": {\r\n                \"repeatEvery\": \"تکرار کن هر: \",\r\n                \"interval\": \" ساعت\"\r\n            },\r\n            \"daily\": {\r\n                \"repeatEvery\": \"تکرار کن هر: \",\r\n                \"interval\": \" روز\"\r\n            },\r\n            \"weekly\": {\r\n                \"interval\": \" هفته\",\r\n                \"repeatEvery\": \"تکرار کن هر: \",\r\n                \"repeatOn\": \"تکرار کن در: \"\r\n            },\r\n            \"monthly\": {\r\n                \"repeatEvery\": \"تکرار کن هر: \",\r\n                \"repeatOn\": \"تکرار کن در: \",\r\n                \"interval\": \" ماه\",\r\n                \"day\": \"روز \"\r\n            },\r\n            \"yearly\": {\r\n                \"repeatEvery\": \"تکرار کن هر: \",\r\n                \"repeatOn\": \"تکرار کن در: \",\r\n                \"interval\": \" سال\",\r\n                \"of\": \" از \"\r\n            },\r\n            \"end\": {\r\n                \"label\": \"پایان:\",\r\n                \"mobileLabel\": \"پایان\",\r\n                \"never\": \"هیچ وقت\",\r\n                \"after\": \"بعد از \",\r\n                \"occurrence\": \" دفعات وقوع\",\r\n                \"on\": \"در \"\r\n            },\r\n            \"offsetPositions\": {\r\n                \"first\": \"اول\",\r\n                \"second\": \"دوم\",\r\n                \"third\": \"سوم\",\r\n                \"fourth\": \"چهارم\",\r\n                \"last\": \"آخر\"\r\n            },\r\n            \"weekdays\": {\r\n                \"day\": \"روز\",\r\n                \"weekday\": \"روز هفته\",\r\n                \"weekend\": \"پایان هفته\"\r\n            }\r\n        });\r\n    }\r\n\r\n    /* Scheduler messages */\r\n\r\n    if (kendo.ui.Scheduler) {\r\n        kendo.ui.Scheduler.prototype.options.messages =\r\n        $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\r\n            \"allDay\": \"all day\",\r\n            \"date\": \"Date\",\r\n            \"event\": \"Event\",\r\n            \"time\": \"Time\",\r\n            \"showFullDay\": \"Show full day\",\r\n            \"showWorkDay\": \"Show business hours\",\r\n            \"today\": \"Today\",\r\n            \"save\": \"Save\",\r\n            \"cancel\": \"Cancel\",\r\n            \"destroy\": \"Delete\",\r\n            \"deleteWindowTitle\": \"Delete event\",\r\n            \"ariaSlotLabel\": \"Selected from {0:t} to {1:t}\",\r\n            \"ariaEventLabel\": \"{0} on {1:D} at {2:t}\",\r\n            \"editable\": {\r\n                \"confirmation\": \"Are you sure you want to delete this event?\"\r\n            },\r\n            \"views\": {\r\n                \"day\": \"Day\",\r\n                \"week\": \"Week\",\r\n                \"workWeek\": \"Work Week\",\r\n                \"agenda\": \"Agenda\",\r\n                \"month\": \"Month\"\r\n            },\r\n            \"recurrenceMessages\": {\r\n                \"deleteWindowTitle\": \"Delete Recurring Item\",\r\n                \"deleteWindowOccurrence\": \"Delete current occurrence\",\r\n                \"deleteWindowSeries\": \"Delete the series\",\r\n                \"editWindowTitle\": \"Edit Recurring Item\",\r\n                \"editWindowOccurrence\": \"Edit current occurrence\",\r\n                \"editWindowSeries\": \"Edit the series\",\r\n                \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\r\n                \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\"\r\n            },\r\n            \"editor\": {\r\n                \"title\": \"Title\",\r\n                \"start\": \"Start\",\r\n                \"end\": \"End\",\r\n                \"allDayEvent\": \"All day event\",\r\n                \"description\": \"Description\",\r\n                \"repeat\": \"Repeat\",\r\n                \"timezone\": \" \",\r\n                \"startTimezone\": \"Start timezone\",\r\n                \"endTimezone\": \"End timezone\",\r\n                \"separateTimezones\": \"Use separate start and end time zones\",\r\n                \"timezoneEditorTitle\": \"Timezones\",\r\n                \"timezoneEditorButton\": \"Time zone\",\r\n                \"timezoneTitle\": \"Time zones\",\r\n                \"noTimezone\": \"No timezone\",\r\n                \"editorTitle\": \"Event\"\r\n            }\r\n        });\r\n    }\r\n\r\n    /* Slider messages */\r\n\r\n    if (kendo.ui.Slider) {\r\n        kendo.ui.Slider.prototype.options =\r\n        $.extend(true, kendo.ui.Slider.prototype.options, {\r\n            \"increaseButtonTitle\": \"افزایش\",\r\n            \"decreaseButtonTitle\": \"کاهش\"\r\n        });\r\n    }\r\n\r\n    /* TreeList messages */\r\n\r\n    if (kendo.ui.TreeList) {\r\n        kendo.ui.TreeList.prototype.options.messages =\r\n        $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\r\n            \"noRows\": \"ردیفی برای نمایش موجود نیست\",\r\n            \"loading\": \"در حال بارگزاری...\",\r\n            \"requestFailed\": \"شکست در انجام درخواست.\",\r\n            \"retry\": \"تلاش مجدد\",\r\n            \"commands\": {\r\n                \"edit\": \"ویرایش\",\r\n                \"update\": \"ذخیره\",\r\n                \"canceledit\": \"انصراف\",\r\n                \"create\": \"درج ردیف جدید\",\r\n                \"createchild\": \"درج گره جدید\",\r\n                \"destroy\": \"حذف\",\r\n                \"excel\": \"خروجی Excel\",\r\n                \"pdf\": \"خروجی PDF\"\r\n            }\r\n        });\r\n    }\r\n\r\n    /* TreeView messages */\r\n\r\n    if (kendo.ui.TreeView) {\r\n        kendo.ui.TreeView.prototype.options.messages =\r\n        $.extend(true, kendo.ui.TreeView.prototype.options.messages, {\r\n            \"loading\": \"در حال بارگزاری...\",\r\n            \"requestFailed\": \"شکست در انجام درخواست.\",\r\n            \"retry\": \"تلاش مجدد\"\r\n        });\r\n    }\r\n\r\n    /* Upload messages */\r\n\r\n    if (kendo.ui.Upload) {\r\n        kendo.ui.Upload.prototype.options.localization =\r\n        $.extend(true, kendo.ui.Upload.prototype.options.localization, {\r\n            \"select\": \"انتخاب فایل(ها)...\",\r\n            \"cancel\": \"انصراف\",\r\n            \"retry\": \"تلاش مجدد\",\r\n            \"remove\": \"حذف\",\r\n            \"uploadSelectedFiles\": \"بارگزاری فایل(ها)\",\r\n            \"dropFilesHere\": \"فایل(ها) را برای بارگزاری به اینجا بکشید\",\r\n            \"statusUploading\": \"در حال بارگزاری\",\r\n            \"statusUploaded\": \"پایان بارگزاری\",\r\n            \"statusWarning\": \"هشداد\",\r\n            \"statusFailed\": \"خطا در بارگزاری\",\r\n            \"headerStatusUploading\": \"در حال بارگزاری...\",\r\n            \"headerStatusUploaded\": \"اتمام بارگزاری\"\r\n        });\r\n    }\r\n\r\n    /* Validator messages */\r\n\r\n    if (kendo.ui.Validator) {\r\n        kendo.ui.Validator.prototype.options.messages =\r\n        $.extend(true, kendo.ui.Validator.prototype.options.messages, {\r\n            \"required\": \"{0} اجباری است\",\r\n            \"pattern\": \"{0} معتبر نیست\",\r\n            \"min\": \"{0} باید بزرگتر یا برابر باشد با {1}\",\r\n            \"max\": \"{0} باید کوچکتر یا برابر باشد با {1}\",\r\n            \"step\": \"{0} معتبر نیست\",\r\n            \"email\": \"{0} یک ایمیل معتبر نیست\",\r\n            \"url\": \"{0} آدرس وب سایت معتبر نیست\",\r\n            \"date\": \"{0} تاریخ معتبر نیست\",\r\n            \"dateCompare\": \"تاریخ پایان باید برابر یا بعد از تاریخ آغاز باشد\"\r\n        });\r\n    }\r\n})(window.kendo.jQuery);\r\n\r\n}));"]}