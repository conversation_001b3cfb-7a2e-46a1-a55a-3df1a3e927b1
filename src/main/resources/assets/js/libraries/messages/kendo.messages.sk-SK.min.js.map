{"version": 3, "sources": ["messages/kendo.messages.sk-SK.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "isnullorempty", "isnotnullorempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignment", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "columnMenu", "TreeView", "Upload", "localization", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,SACTC,OAAU,YAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,SACTC,OAAU,YAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,uBACjBC,eAAkB,sBAClBC,OAAU,SACVC,QAAW,SACXC,KAAQ,SACRC,SAAY,oBACZC,KAAQ,UACRC,OAAU,cAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,QACRC,OAAU,UACVC,UAAa,eACbC,cAAiB,cACjBC,YAAe,cACfC,UAAa,cACbC,cAAiB,oBACjBC,YAAe,iBACfC,aAAgB,kBAChBC,YAAe,oBACfC,oBAAuB,0BACvBC,kBAAqB,0BACrBC,OAAU,oBACVC,QAAW,oBACXC,WAAc,eACdC,OAAU,kBACVC,YAAe,iBACfC,WAAc,eACdC,WAAc,cACdC,SAAY,YACZC,SAAY,gBACZC,gBAAmB,qBACnBC,SAAY,wBACZC,gBAAmB,uBACnBC,YAAe,SACfC,WAAc,eACdC,UAAa,QACbC,UAAa,gBACbC,MAAS,QACTC,YAAe,oBACfC,WAAc,SACdC,QAAW,oBACXC,YAAe,WACfC,YAAe,QACfC,gBAAmB,qEACnBC,WAAc,iCACdC,cAAiB,8EACjBC,kBAAqB,wCACrBC,gBAAmB,QACnBC,aAAgB,YAChBC,WAAc,aACdC,YAAe,aACfC,eAAkB,QAClBC,UAAa,QACbC,eAAkB,QAClBC,SAAY,OACZC,YAAe,MACfC,oBAAuB,6BACvBC,aAAgB,SAChBC,aAAgB,SAChBC,sBAAyB,QACzBC,aAAgB,SAChBC,YAAe,iBACfC,cAAiB,sBACjBC,eAAkB,uBAClBC,YAAe,oBACfC,YAAe,oBACfC,UAAa,mBACbC,aAAgB,sBAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,UACdC,QAAW,mBACXE,YAAe,QACfD,YAAe,WACfK,kBAAqB,wCACrBR,YAAe,oBACfM,WAAc,iCACdD,gBAAmB,qEACnBE,cAAiB,8EACjBwB,cAAiB,8CACjBC,OAAU,YAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,YACVC,QAAW,gBACXvE,OAAU,YACVwE,MAAS,WACTC,SAAY,cAMVtF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,KACNC,IAAO,SACPC,WAAc,WACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,UACZC,OAAU,UACVC,UAAa,cACbC,QAAW,aACXC,WAAc,iBACdC,cAAiB,eACjBC,iBAAoB,cAEtBC,QACEZ,GAAM,WACNC,IAAO,aACPY,IAAO,2BACPC,GAAM,gBACNC,IAAO,2BACPC,GAAM,gBACNV,OAAU,UACVC,UAAa,eAEfU,MACEjB,GAAM,KACNC,IAAO,SACPY,IAAO,qBACPC,GAAM,YACNC,IAAO,sBACPC,GAAM,aACNV,OAAU,UACVC,UAAa,eAEfW,OACElB,GAAM,KACNC,IAAO,SACPK,OAAU,UACVC,UAAa,kBAObhG,MAAMC,GAAG2G,aACb5G,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQC,UACnDwG,KAAQ,sCACR1B,OAAU,YACVC,QAAW,gBACXvE,OAAU,YACVwE,MAAS,WACTyB,IAAO,YACPC,GAAM,QACNC,YAAe,oBACf1B,SAAY,WACZ2B,MAAS,UACTzG,OAAU,YAMRR,MAAMC,GAAG2G,aACb5G,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,WAAWzG,UAAUC,QAAQmF,WACnDC,QACIC,GAAM,KACNC,IAAO,SACPC,WAAc,WACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,UACZC,OAAU,UACVC,UAAa,cACbC,QAAW,aACXC,WAAc,iBACdC,cAAiB,eACjBC,iBAAoB,cAExBC,QACIZ,GAAM,WACNC,IAAO,aACPY,IAAO,2BACPC,GAAM,gBACNC,IAAO,2BACPC,GAAM,gBACNV,OAAU,UACVC,UAAa,eAEjBU,MACIjB,GAAM,KACNC,IAAO,SACPY,IAAO,qBACPC,GAAM,YACNC,IAAO,sBACPC,GAAM,aACNV,OAAU,UACVC,UAAa,eAEjBW,OACIlB,GAAM,KACNC,IAAO,SACPK,OAAU,UACVC,UAAa,kBAOfhG,MAAMC,GAAGiH,mBACTlH,MAAMC,GAAGiH,iBAAiB/G,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiH,iBAAiB/G,UAAUC,QAAQC,UACvD8G,SAAY,SACZ9B,MAAS,WACTxE,OAAU,YACVoE,OAAU,YAMdjF,MAAMC,GAAGmH,QACbpH,MAAMC,GAAGmH,MAAMjH,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmH,MAAMjH,UAAUC,QAAQC,UAC9CgH,SACEC,SAAY,kBACZC,OAAU,eACVC,YAAe,YACfC,aAAgB,cAChBC,IAAO,qBAETlH,OAAU,SACVmH,4BAA+B,yBAC/BC,sBAAyB,oBACzBC,QAAW,YACXC,QACEC,aAAgB,WAChBC,YAAe,QACfC,IAAO,SACPC,gBAAmB,SACnBC,UAAa,SACbC,qBAAwB,SACxBC,gBAAmB,SACnBC,MAAS,WACTC,MAAS,QACTC,YAAe,YAEjBC,KAAQ,SACRC,OACEC,IAAO,MACPV,IAAO,SACPW,MAAS,SACTN,MAAS,WACTO,KAAQ,SACRC,KAAQ,UAOR9I,MAAMC,GAAG8I,OACb/I,MAAMC,GAAG8I,KAAK5I,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8I,KAAK5I,UAAUC,QAAQC,UAC7C2I,UACExI,OAAU,gBACVyI,WAAc,SACdC,OAAU,qBACVrB,QAAW,YACXsB,KAAQ,UACRC,MAAS,uBACT1B,IAAO,oBACPe,KAAQ,eACRY,OAAU,SACVC,OAAU,UAEZC,UACEC,aAAgB,SAChBC,aAAgB,wCAChBC,cAAiB,aAEnBC,UAAa,qBAMX3J,MAAMC,GAAG2J,YACb5J,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,UAAUzJ,UAAUC,QAAQC,UAChDwJ,MAAS,6DAMT7J,MAAMC,GAAG6J,iBACb9J,MAAMC,GAAG6J,eAAe3J,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6J,eAAe3J,UAAUC,SAC/C2J,YAAe,iBACfC,cAAiB,oBAMfhK,MAAMC,GAAGgK,QACbjK,MAAMC,GAAGgK,MAAM9J,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgK,MAAM9J,UAAUC,QAAQC,UAC9C6J,SAAY,SACZC,QAAW,2BACXN,MAAS,8BACTO,KAAQ,SACRC,GAAM,QACNC,aAAgB,qBAChBC,MAAS,wBACTC,SAAY,4BACZC,KAAQ,0BACRC,KAAQ,4BACRC,QAAW,UACXC,UAAa,mBAMX5K,MAAMC,GAAG4K,YACb7K,MAAMC,GAAG4K,UAAU1K,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4K,UAAU1K,UAAUC,QAAQC,UAClDyK,cAAiB,8BACjBC,aAAgB,+BAChBC,UAAa,kCAMXhL,MAAMC,GAAGgL,iBACbjL,MAAMC,GAAGgL,eAAe9K,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgL,eAAe9K,UAAUC,QAAQC,UACvDwG,KAAQ,sCACRqE,aAAgB,cAChBrK,OAAU,SACVsK,QAAW,mBACX5C,MAAS,qBACTlD,MAAS,WACT+F,GAAM,KACN5K,OAAU,SACV+E,WACEK,SAAY,WACZC,eAAkB,aAClBF,WAAc,WACdG,SAAY,UACZL,GAAM,WACNC,IAAO,iBAOP1F,MAAMC,GAAGoL,mBACbrL,MAAMC,GAAGoL,iBAAiBlL,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoL,iBAAiBlL,UAAUC,QAAQC,UACzDiL,aACEC,MAAS,QACTC,OAAU,eACVC,MAAS,QACTC,OAAU,WACVC,QAAW,UACXC,OAAU,SAEZJ,QACEK,YAAe,mBACfC,SAAY,kBAEdL,OACEI,YAAe,mBACfC,SAAY,aAEdJ,QACEI,SAAY,mBACZD,YAAe,mBACfE,SAAY,cAEdJ,SACEE,YAAe,mBACfE,SAAY,aACZD,SAAY,oBACZnD,IAAO,QAETiD,QACEC,YAAe,mBACfE,SAAY,aACZD,SAAY,cACZzB,GAAM,OAERpC,KACE+D,MAAS,UACTC,YAAe,UACfV,MAAS,QACTW,MAAS,MACTC,WAAc,oBACdC,GAAM,OAERC,iBACE9B,MAAS,OACT+B,OAAU,QACVC,MAAS,QACTC,OAAU,SACV9B,KAAQ,YAEV+B,UACE9D,IAAO,MACP+D,QAAW,eACXC,QAAW,aAOX3M,MAAMC,GAAG2M,YACb5M,MAAMC,GAAG2M,UAAUzM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,UAAUzM,UAAUC,QAAQC,UAClDwM,OAAU,WACVnG,KAAQ,QACRoG,MAAS,UACTC,KAAQ,MACRC,YAAe,oBACfC,YAAe,wBACfC,MAAS,OACTzE,KAAQ,SACRjI,OAAU,SACVqH,QAAW,YACXsF,kBAAqB,uBACrBC,cAAiB,4BACjBC,eAAkB,wBAClB9D,UACEE,aAAgB,yCAElBf,OACEC,IAAO,MACPE,KAAQ,SACRyE,SAAY,kBACZC,OAAU,SACV3E,MAAS,UAEX4E,oBACEL,kBAAqB,kCACrBM,uBAA0B,6BAC1BC,mBAAsB,mBACtBC,gBAAmB,6BACnBC,qBAAwB,2BACxBC,iBAAoB,iBACpBC,gBAAmB,oEACnBC,cAAiB,mEAEnBjG,QACES,MAAS,QACTD,MAAS,WACTL,IAAO,SACP+F,YAAe,YACfC,YAAe,QACfC,OAAU,WACVC,SAAY,IACZC,cAAiB,wBACjBC,YAAe,qBACfC,kBAAqB,2CACrBC,oBAAuB,eACvBC,qBAAwB,eACxBC,cAAiB,eACjBC,WAAc,qBACd1G,YAAe,cAOfhI,MAAM2O,aAAe3O,MAAM2O,YAAYtO,SAASuO,gBACpD5O,MAAM2O,YAAYtO,SAASuO,cAC3B9O,EAAEQ,QAAO,EAAMN,MAAM2O,YAAYtO,SAASuO,eACtCC,WAAc,oBACdC,cAAiB,sBACjBC,wBAA2B,gCAC3BC,sBAAyB,6BACzBC,eAAkB,uBAClBC,WAAc,kBACdC,UAAa,mBACbC,YAAe,mBACfC,aAAgB,mBAChBC,UAAa,iBACbC,MAAS,gBACTC,YAAe,mBACfjP,MAAS,SACTC,OAAU,YAIVR,MAAM2O,aAAe3O,MAAM2O,YAAYtO,SAASoP,UACpDzP,MAAM2O,YAAYtO,SAASoP,QAC3B3P,EAAEQ,QAAO,EAAMN,MAAM2O,YAAYtO,SAASoP,SACtClP,MAAS,SACTkI,KAAQ,SACRjI,OAAU,SACVkP,OAAU,YACVC,OAAU,KACVC,mBACIrH,MAAS,eACTsH,YACIxJ,OAAU,QACVyJ,SAAY,OACZpJ,KAAQ,UAGhBqJ,kBACIxH,MAAS,SAEbyH,gBACIzH,MAAS,iBAEb0H,eACI1H,MAAS,cAEb2H,iBACI3H,MAAS,aACT4H,SACIC,aAAgB,iBAChB1O,cAAiB,YACjBE,aAAgB,kBAChBC,YAAe,oBACfwO,SAAY,iBACZC,YAAe,oBACfC,YAAe,mBAGvBC,aACIjI,MAAS,kBACT4H,SACIM,WAAc,gBACdC,kBAAqB,mBACrBC,gBAAmB,gBACnBC,QAAW,aAGnBC,cACItI,MAAS,oBACT4H,SACIW,YAAe,iBACfC,WAAc,iBACdC,cAAiB,iBACjBC,SAAY,qBAGpBC,kBACI3I,MAAS,kBACT4I,YAAe,yCACfC,UAAa,eACbC,UACIC,IAAO,oBACPjL,OAAU,QACVkL,KAAQ,OACR7K,KAAQ,QACR8K,OAAU,iBACVC,KAAQ,UAEZC,WACIC,YAAe,aACfC,SAAY,aACZC,QAAW,QACXC,WAAc,YACdC,QAAW,WACXC,WAAc,aACdC,qBAAwB,wBACxBC,kBAAqB,yBAEzBC,kBACIR,YAAe,iBACfC,SAAY,iBACZC,QAAW,kBACXC,WAAc,sBACdC,QAAW,eACXC,WAAc,iBACdC,qBAAwB,4BACxBC,kBAAqB,4BACrBV,OAAU,uBAEdY,QACIf,SAAY,WACZgB,SAAY,aACZC,IAAO,MACPC,IAAO,MACPtL,MAAS,UACTqB,MAAS,WACTL,IAAO,SACPuK,cAAiB,uBACjBC,YAAe,kBACfC,YAAe,qBACfC,SAAY,mBACZvB,UAAa,kBACbD,YAAe,iBACfyB,YAAe,qBAEnBC,cACIC,UAAa,eACbC,YAAe,gBAGvBC,gBACIzK,MAAS,gBACT6J,QACIa,SAAY,cACZC,WAAc,iBACdC,WAAc,aACdC,UAAa,kBACbC,QAAW,SACXC,YAAe,aACfC,MAAS,SACTC,WAAc,gBACdC,OAAU,YACVC,aAAgB,YAChBC,WAAc,WAGtBC,oBACIC,aAAgB,uCAEpBC,mBACIvL,MAAS,0BACTsL,aAAgB,uFAChBzB,QACI2B,QAAW,iBACXC,OAAU,kBACVC,SAAY,gBAGpBC,4BACIL,aAAgB,6DAKpB7T,MAAM2O,aAAe3O,MAAM2O,YAAYtO,SAAS8T,aACpDnU,MAAM2O,YAAYtO,SAAS8T,WAC3BrU,EAAEQ,QAAO,EAAMN,MAAM2O,YAAYtO,SAAS8T,YACtCxT,cAAiB,6BACjBC,eAAkB,6BAClBwT,cAAiB,0BACjBC,kBAAqB,4BACrB9T,MAAS,SACT0E,OAAU,SACVqP,aAAgB,8BAChBjP,MAAS,WACTkP,OAAU,YACVC,aAAgB,SAChB1N,IAAO,IACPC,GAAM,QACNxB,WACIC,QACII,SAAY,gBACZC,eAAkB,kBAClBF,WAAc,gBACdG,SAAY,gBAEhBY,MACIjB,GAAM,WACNC,IAAO,eACPe,GAAM,gBACNF,GAAM,eAEVF,QACIZ,GAAM,WACNC,IAAO,aACPY,IAAO,2BACPC,GAAM,gBACNC,IAAO,2BACPC,GAAM,qBAMdzG,MAAM2O,aAAe3O,MAAM2O,YAAYtO,SAASoU,UACpDzU,MAAM2O,YAAYtO,SAASoU,QAC3B3U,EAAEQ,QAAO,EAAMN,MAAM2O,YAAYtO,SAASoU,SACtChQ,cAAiB,sBACjBC,eAAkB,uBAClBC,YAAe,oBACfC,YAAe,oBACf8P,UAAa,aACbC,kBACIvE,aAAgB,iBAChB1O,cAAiB,YACjBE,aAAgB,kBAChBC,YAAe,oBACfwO,SAAY,iBACZC,YAAe,oBACfC,YAAe,kBAEnBqE,gBAAmB,UACnBxT,KAAQ,QACRyT,QAAW,aACXC,aACIvF,MAAS,gBACTC,YAAe,oBAEnBuF,KAAQ,YACRC,IAAO,aACPlQ,aAAgB,mBAChBD,UAAa,mBACboQ,YAAe,yBACfpU,OAAU,YACVqU,WAAc,QACdxS,SAAY,gBACZyS,OAAU,oBACVC,aACIC,UAAa,cACbhP,OAAU,QACViP,QAAW,WACXC,UAAa,WACbzF,SAAY,OACZpJ,KAAQ,QACRqG,KAAQ,MACRyI,SAAY,cACZC,SAAY,cACZC,YAAe,oBAEnBC,sBAAyB,yBACzBC,sBAAyB,0BACzBC,OAAU,iBACVC,eACIhF,YAAe,iBACfC,WAAc,iBACdC,cAAiB,iBACjBC,SAAY,oBAEhB5P,OAAU,QACV0U,MAAS,eACTC,cACIvF,WAAc,gBACdC,kBAAqB,mBACrBC,gBAAmB,gBACnBC,QAAW,YAEfqF,KAAQ,aACRC,MAAS,SACTC,aACIC,KAAQ,QACRC,KAAQ,QAEZC,OAAU,gBACVC,QAAW,uBACXC,SAAY,sBACZC,aACIC,aAAgB,4BAChBC,cAAiB,4BACjBC,aAAgB,6BAChBC,cAAiB,gCAErBC,UAAa,cACbC,SAAY,eACZzV,UAAa,eACb0V,WAAc,wBAIdhX,MAAM2O,aAAe3O,MAAM2O,YAAYtO,SAAS4W,OACpDjX,MAAM2O,YAAYtO,SAAS4W,KAC3BnX,EAAEQ,QAAO,EAAMN,MAAM2O,YAAYtO,SAAS4W,MACtCC,QACIC,sBAAyB,sHACzBC,4BAA+B,6DAC/BC,gBAAmB,8EAEvBC,MACIC,KAAQ,QACRC,OAAU,SACVC,KAAQ,YAOZzX,MAAMC,GAAGyX,SACb1X,MAAMC,GAAGyX,OAAOvX,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyX,OAAOvX,UAAUC,SACvCuX,oBAAuB,SACvBC,oBAAuB,YAMrB5X,MAAMC,GAAG4X,WACb7X,MAAMC,GAAG4X,SAAS1X,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4X,SAAS1X,UAAUC,QAAQC,UAC/CyX,OAAU,+BACVC,QAAW,gBACXC,cAAiB,sBACjBC,MAAS,QACTjP,UACIG,KAAQ,UACRG,OAAU,SACVL,WAAc,SACdC,OAAU,qBACVgP,YAAe,mBACfrQ,QAAW,YACXuB,MAAS,uBACT1B,IAAO,wBAKX1H,MAAMC,GAAG4X,WACb7X,MAAMC,GAAG4X,SAAS1X,UAAUC,QAAQ+X,WACpCrY,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4X,SAAS1X,UAAUC,QAAQ+X,YAC/C9X,UACIS,QAAW,gBACXD,OAAU,gBACVF,cAAiB,uBACjBC,eAAkB,0BAQtBZ,MAAMC,GAAGmY,WACbpY,MAAMC,GAAGmY,SAASjY,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmY,SAASjY,UAAUC,QAAQC,UACjD0X,QAAW,gBACXC,cAAiB,sBACjBC,MAAS,WAMPjY,MAAMC,GAAGoY,SACbrY,MAAMC,GAAGoY,OAAOlY,UAAUC,QAAQkY,aAClCxY,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoY,OAAOlY,UAAUC,QAAQkY,cAC/CjP,OAAU,oBACV7I,OAAU,SACVyX,MAAS,QACTvI,OAAU,YACV6I,oBAAuB,iBACvBvT,cAAiB,8CACjBwT,gBAAmB,cACnBC,eAAkB,SAClBC,cAAiB,YACjBC,aAAgB,WAChBC,sBAAyB,iBACzBC,qBAAwB,YAMtB7Y,MAAMC,GAAG6Y,YACb9Y,MAAMC,GAAG6Y,UAAU3Y,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6Y,UAAU3Y,UAAUC,QAAQC,UAClD0Y,SAAY,yBACZC,QAAW,yBACX1G,IAAO,yBACPC,IAAO,2BACP0G,KAAQ,yBACRC,MAAS,0BACTC,IAAO,+BACPzS,KAAQ,0BACR0S,YAAe,6DAMbpZ,MAAMC,GAAGoZ,SACbrZ,MAAMC,GAAGoZ,OAAOlZ,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoZ,OAAOlZ,UAAUC,QAAQkY,cAC/CgB,MAAS,aAMPtZ,MAAMC,GAAGsZ,QACbvZ,MAAMC,GAAGsZ,MAAMpZ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsZ,MAAMpZ,UAAUC,QAAQkY,cAC9C3I,OAAU,QAMR3P,MAAMC,GAAGuZ,UACbxZ,MAAMC,GAAGuZ,QAAQrZ,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuZ,QAAQrZ,UAAUC,QAAQkY,cAChD3I,OAAU,KACVnP,OAAU,YAKRR,MAAMC,GAAGwZ,SACbzZ,MAAMC,GAAGwZ,OAAOtZ,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwZ,OAAOtZ,UAAUC,QAAQkY,cAC/C3I,OAAU,KACVnP,OAAU,aAITkZ,OAAO1Z,MAAM2Z", "file": "kendo.messages.sk-SK.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\r\n/* FlatColorPicker messages */\r\n\r\nif (kendo.ui.FlatColorPicker) {\r\nkendo.ui.FlatColorPicker.prototype.options.messages =\r\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\r\n  \"apply\": \"Použiť\",\r\n  \"cancel\": \"Storno\"\r\n});\r\n}\r\n\r\n/* ColorPicker messages */\r\n\r\nif (kendo.ui.ColorPicker) {\r\nkendo.ui.ColorPicker.prototype.options.messages =\r\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\r\n  \"apply\": \"Použiť\",\r\n  \"cancel\": \"Storno\"\r\n});\r\n}\r\n\r\n/* ColumnMenu messages */\r\n\r\nif (kendo.ui.ColumnMenu) {\r\nkendo.ui.ColumnMenu.prototype.options.messages =\r\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\r\n  \"sortAscending\": \"Usporiadať vzostupne\",\r\n  \"sortDescending\": \"Usporiadať zostupne\",\r\n  \"filter\": \"Filter\",\r\n  \"columns\": \"Stĺpce\",\r\n  \"done\": \"Hotovo\",\r\n  \"settings\": \"Nastavenia stĺpca\",\r\n  \"lock\": \"Zamknúť\",\r\n  \"unlock\": \"Odomknúť\"\r\n});\r\n}\r\n\r\n/* Editor messages */\r\n\r\nif (kendo.ui.Editor) {\r\nkendo.ui.Editor.prototype.options.messages =\r\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\r\n  \"bold\": \"Tučné\",\r\n  \"italic\": \"Kurzíva\",\r\n  \"underline\": \"Podčiarknuté\",\r\n  \"strikethrough\": \"Preškrtnuté\",\r\n  \"superscript\": \"Horný index\",\r\n  \"subscript\": \"Dolný index\",\r\n  \"justifyCenter\": \"Zarovnať na stred\",\r\n  \"justifyLeft\": \"Zarovnať vľavo\",\r\n  \"justifyRight\": \"Zarovnať vpravo\",\r\n  \"justifyFull\": \"Zarovnať do bloku\",\r\n  \"insertUnorderedList\": \"Vložiť odrážkový zoznam\",\r\n  \"insertOrderedList\": \"Vložiť číslovaný zoznam\",\r\n  \"indent\": \"Zväčšiť odsadenie\",\r\n  \"outdent\": \"Zmenšiť odsadenie\",\r\n  \"createLink\": \"Vložiť odkaz\",\r\n  \"unlink\": \"Odstrániť odkaz\",\r\n  \"insertImage\": \"Vložiť obrázok\",\r\n  \"insertFile\": \"Vložiť súbor\",\r\n  \"insertHtml\": \"Vložiť HTML\",\r\n  \"viewHtml\": \"View HTML\",\r\n  \"fontName\": \"Vyberte písmo\",\r\n  \"fontNameInherit\": \"(predvolené písmo)\",\r\n  \"fontSize\": \"Vyberte veľkosť písma\",\r\n  \"fontSizeInherit\": \"(predvolená veľkosť)\",\r\n  \"formatBlock\": \"Formát\",\r\n  \"formatting\": \"Formátovanie\",\r\n  \"foreColor\": \"Farba\",\r\n  \"backColor\": \"Farba pozadia\",\r\n  \"style\": \"Štýly\",\r\n  \"emptyFolder\": \"Prázdny priečinok\",\r\n  \"uploadFile\": \"Nahrať\",\r\n  \"orderBy\": \"Usporiadať podľa:\",\r\n  \"orderBySize\": \"Veľkosti\",\r\n  \"orderByName\": \"Názvu\",\r\n  \"invalidFileType\": \"Vybraný súbor \\\"{0}\\\" nie je podporovaný. Podporované súbory sú {1}.\",\r\n  \"deleteFile\": 'Naozaj chcete odstrániť \"{0}\"?',\r\n  \"overwriteFile\": 'Súbor s názvom \"{0}\" už vo vybratom priečinku existuje. Chcete ho nahradiť?',\r\n  \"directoryNotFound\": \"Priečinok s týmto názvom sa nenašiel.\",\r\n  \"imageWebAddress\": \"Odkaz\",\r\n  \"imageAltText\": \"Alt. text\",\r\n  \"imageWidth\": \"Šírka (px)\",\r\n  \"imageHeight\": \"Výška (px)\",\r\n  \"fileWebAddress\": \"Odkaz\",\r\n  \"fileTitle\": \"Názov\",\r\n  \"linkWebAddress\": \"Odkaz\",\r\n  \"linkText\": \"Text\",\r\n  \"linkToolTip\": \"Tip\",\r\n  \"linkOpenInNewWindow\": \"Otvoriť odkaz v novom okne\",\r\n  \"dialogUpdate\": \"Uložiť\",\r\n  \"dialogInsert\": \"Vložiť\",\r\n  \"dialogButtonSeparator\": \"alebo\",\r\n  \"dialogCancel\": \"Storno\",\r\n  \"createTable\": \"Vložiť tabuľku\",\r\n  \"addColumnLeft\": \"Pridať stĺpec vľavo\",\r\n  \"addColumnRight\": \"Pridať stĺpec vpravo\",\r\n  \"addRowAbove\": \"Pridať riadok nad\",\r\n  \"addRowBelow\": \"Pridať riadok pod\",\r\n  \"deleteRow\": \"Odstrániť riadok\",\r\n  \"deleteColumn\": \"Odstrániť stĺpec\"\r\n});\r\n}\r\n\r\n/* FileBrowser messages */\r\n\r\nif (kendo.ui.FileBrowser) {\r\nkendo.ui.FileBrowser.prototype.options.messages =\r\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\r\n  \"uploadFile\": \"Odoslať\",\r\n  \"orderBy\": \"Usporiadať podľa\",\r\n  \"orderByName\": \"Názvu\",\r\n  \"orderBySize\": \"Veľkosti\",\r\n  \"directoryNotFound\": \"Priečinok s týmto názvom sa nenašiel.\",\r\n  \"emptyFolder\": \"Prázdny priečinok\",\r\n  \"deleteFile\": 'Naozaj chcete odstrániť \"{0}\"?',\r\n  \"invalidFileType\": \"Vybraný súbor \\\"{0}\\\" nie je podporovaný. Podporované súbory sú {1}.\",\r\n  \"overwriteFile\": \"Súbor s názvom \\\"{0}\\\" už vo vybratom priečinku existuje. Chcete ho nahradiť?\",\r\n  \"dropFilesHere\": \"Potiahnite sem súbory, ktoré chcete odoslať\",\r\n  \"search\": \"Hľadať\"\r\n});\r\n}\r\n\r\n/* FilterCell messages */\r\n\r\nif (kendo.ui.FilterCell) {\r\nkendo.ui.FilterCell.prototype.options.messages =\r\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\r\n  \"isTrue\": \"je pravda\",\r\n  \"isFalse\": \"nie je pravda\",\r\n  \"filter\": \"Filtrovať\",\r\n  \"clear\": \"Vyčistiť\",\r\n  \"operator\": \"Operátor\"\r\n});\r\n}\r\n\r\n/* FilterCell operators */\r\n\r\nif (kendo.ui.FilterCell) {\r\nkendo.ui.FilterCell.prototype.options.operators =\r\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\r\n  \"string\": {\r\n    \"eq\": \"Je\",\r\n    \"neq\": \"Nie je\",\r\n    \"startswith\": \"Začína s\",\r\n    \"contains\": \"Obsahuje\",\r\n    \"doesnotcontain\": \"Neobsahuje\",\r\n    \"endswith\": \"Končí s\",\r\n    \"isnull\": \"Je null\",\r\n    \"isnotnull\": \"Nie je null\",\r\n    \"isempty\": \"Je prázdne\",\r\n    \"isnotempty\": \"Nie je prázdne\",\r\n    \"isnullorempty\": \"Nemá hodnotu\",\r\n    \"isnotnullorempty\": \"Má hodnotu\"\r\n  },\r\n  \"number\": {\r\n    \"eq\": \"Rovná sa\",\r\n    \"neq\": \"Nerovná sa\",\r\n    \"gte\": \"Je väčšie alebo sa rovná\",\r\n    \"gt\": \"Je väčšie ako\",\r\n    \"lte\": \"Je menšie alebo sa rovná\",\r\n    \"lt\": \"Je menšie ako\",\r\n    \"isnull\": \"Je null\",\r\n    \"isnotnull\": \"Nie je null\"\r\n  },\r\n  \"date\": {\r\n    \"eq\": \"Je\",\r\n    \"neq\": \"Nie je\",\r\n    \"gte\": \"Nasleduje alebo je\",\r\n    \"gt\": \"Nasleduje\",\r\n    \"lte\": \"Predchádza alebo je\",\r\n    \"lt\": \"Predchádza\",\r\n    \"isnull\": \"Je null\",\r\n    \"isnotnull\": \"Nie je null\"\r\n  },\r\n  \"enums\": {\r\n    \"eq\": \"Je\",\r\n    \"neq\": \"Nie je\",\r\n    \"isnull\": \"Je null\",\r\n    \"isnotnull\": \"Nie je null\"\r\n  }\r\n});\r\n}\r\n\r\n/* FilterMenu messages */\r\n\r\nif (kendo.ui.FilterMenu) {\r\nkendo.ui.FilterMenu.prototype.options.messages =\r\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\r\n  \"info\": \"Zobraziť záznamy s hodnotou, ktorá:\",\r\n  \"isTrue\": \"je pravda\",\r\n  \"isFalse\": \"nie je pravda\",\r\n  \"filter\": \"Filtrovať\",\r\n  \"clear\": \"Vyčistiť\",\r\n  \"and\": \"A zároveň\",\r\n  \"or\": \"Alebo\",\r\n  \"selectValue\": \"-Vyberte hodnotu-\",\r\n  \"operator\": \"Operátor\",\r\n  \"value\": \"Hodnota\",\r\n  \"cancel\": \"Storno\"\r\n});\r\n}\r\n\r\n/* FilterMenu operator messages */\r\n\r\nif (kendo.ui.FilterMenu) {\r\nkendo.ui.FilterMenu.prototype.options.operators =\r\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\r\n  \"string\": {\r\n      \"eq\": \"Je\",\r\n      \"neq\": \"Nie je\",\r\n      \"startswith\": \"Začína s\",\r\n      \"contains\": \"Obsahuje\",\r\n      \"doesnotcontain\": \"Neobsahuje\",\r\n      \"endswith\": \"Končí s\",\r\n      \"isnull\": \"Je null\",\r\n      \"isnotnull\": \"Nie je null\",\r\n      \"isempty\": \"Je prázdne\",\r\n      \"isnotempty\": \"Nie je prázdne\",\r\n      \"isnullorempty\": \"Nemá hodnotu\",\r\n      \"isnotnullorempty\": \"Má hodnotu\"\r\n  },\r\n  \"number\": {\r\n      \"eq\": \"Rovná sa\",\r\n      \"neq\": \"Nerovná sa\",\r\n      \"gte\": \"Je väčšie alebo sa rovná\",\r\n      \"gt\": \"Je väčšie ako\",\r\n      \"lte\": \"Je menšie alebo sa rovná\",\r\n      \"lt\": \"Je menšie ako\",\r\n      \"isnull\": \"Je null\",\r\n      \"isnotnull\": \"Nie je null\"\r\n  },\r\n  \"date\": {\r\n      \"eq\": \"Je\",\r\n      \"neq\": \"Nie je\",\r\n      \"gte\": \"Nasleduje alebo je\",\r\n      \"gt\": \"Nasleduje\",\r\n      \"lte\": \"Predchádza alebo je\",\r\n      \"lt\": \"Predchádza\",\r\n      \"isnull\": \"Je null\",\r\n      \"isnotnull\": \"Nie je null\"\r\n  },\r\n  \"enums\": {\r\n      \"eq\": \"Je\",\r\n      \"neq\": \"Nie je\",\r\n      \"isnull\": \"Je null\",\r\n      \"isnotnull\": \"Nie je null\"\r\n  }\r\n});\r\n}\r\n\r\n/* FilterMultiCheck messages */\r\n\r\nif (kendo.ui.FilterMultiCheck) {\r\n    kendo.ui.FilterMultiCheck.prototype.options.messages =\r\n    $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\r\n        \"checkAll\": \"Všetky\",\r\n        \"clear\": \"Vyčistiť\",\r\n        \"filter\": \"Filtrovať\",\r\n        \"search\": \"Hľadať\"\r\n    });\r\n}\r\n\r\n/* Gantt messages */\r\n\r\nif (kendo.ui.Gantt) {\r\nkendo.ui.Gantt.prototype.options.messages =\r\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\r\n  \"actions\": {\r\n    \"addChild\": \"Pridať podúlohu\",\r\n    \"append\": \"Pridať úlohu\",\r\n    \"insertAfter\": \"Vložiť za\",\r\n    \"insertBefore\": \"Vložiť pred\",\r\n    \"pdf\": \"Exportovať do PDF\"\r\n  },\r\n  \"cancel\": \"Storno\",\r\n  \"deleteDependencyWindowTitle\": \"Odstránenie závislosti\",\r\n  \"deleteTaskWindowTitle\": \"Odstránenie úlohy\",\r\n  \"destroy\": \"Odstrániť\",\r\n  \"editor\": {\r\n    \"assingButton\": \"Priradiť\",\r\n    \"editorTitle\": \"Úloha\",\r\n    \"end\": \"Koniec\",\r\n    \"percentComplete\": \"Hotovo\",\r\n    \"resources\": \"Zdroje\",\r\n    \"resourcesEditorTitle\": \"Zdroje\",\r\n    \"resourcesHeader\": \"Zdroje\",\r\n    \"start\": \"Začiatok\",\r\n    \"title\": \"Názov\",\r\n    \"unitsHeader\": \"Jednotky\"\r\n  },\r\n  \"save\": \"Uložiť\",\r\n  \"views\": {\r\n    \"day\": \"Deň\",\r\n    \"end\": \"Koniec\",\r\n    \"month\": \"Mesiac\",\r\n    \"start\": \"Začiatok\",\r\n    \"week\": \"Týždeň\",\r\n    \"year\": \"Rok\"\r\n  }\r\n});\r\n}\r\n\r\n/* Grid messages */\r\n\r\nif (kendo.ui.Grid) {\r\nkendo.ui.Grid.prototype.options.messages =\r\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\r\n  \"commands\": {\r\n    \"cancel\": \"Zahodiť zmeny\",\r\n    \"canceledit\": \"Storno\",\r\n    \"create\": \"Pridať nový záznam\",\r\n    \"destroy\": \"Odstrániť\",\r\n    \"edit\": \"Upraviť\",\r\n    \"excel\": \"Exportovať do Excelu\",\r\n    \"pdf\": \"Exportovať do PDF\",\r\n    \"save\": \"Uložiť zmeny\",\r\n    \"select\": \"Vybrať\",\r\n    \"update\": \"Uložiť\"\r\n  },\r\n  \"editable\": {\r\n    \"cancelDelete\": \"Storno\",\r\n    \"confirmation\": \"Naozaj chcete odstrániť tento záznam?\",\r\n    \"confirmDelete\": \"Odstrániť\"\r\n  },\r\n  \"noRecords\": \"Žiadne záznamy.\"\r\n});\r\n}\r\n\r\n/* Groupable messages */\r\n\r\nif (kendo.ui.Groupable) {\r\nkendo.ui.Groupable.prototype.options.messages =\r\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\r\n    \"empty\": \"Potiahnite sem záhlavie stĺpca na zoskupenie podľa neho\"\r\n});\r\n}\r\n\r\n/* NumericTextBox messages */\r\n\r\nif (kendo.ui.NumericTextBox) {\r\nkendo.ui.NumericTextBox.prototype.options =\r\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\r\n  \"upArrowText\": \"Zvýšiť hodnotu\",\r\n  \"downArrowText\": \"Znížiť hodnotu\"\r\n});\r\n}\r\n\r\n/* Pager messages */\r\n\r\nif (kendo.ui.Pager) {\r\nkendo.ui.Pager.prototype.options.messages =\r\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\r\n  \"allPages\": \"Všetko\",\r\n  \"display\": \"{0} - {1} z {2} záznamov\",\r\n  \"empty\": \"Žiadny záznam na zobrazenie\",\r\n  \"page\": \"Strana\",\r\n  \"of\": \"z {0}\",\r\n  \"itemsPerPage\": \"záznamov na stranu\",\r\n  \"first\": \"Prejsť na prvú stranu\",\r\n  \"previous\": \"Prejsť na predošlú stranu\",\r\n  \"next\": \"Prejsť na ďalšiu stranu\",\r\n  \"last\": \"Prejsť na poslednú stranu\",\r\n  \"refresh\": \"Obnoviť\",\r\n  \"morePages\": \"Ďalšie strany\"\r\n});\r\n}\r\n\r\n/* PivotGrid messages */\r\n\r\nif (kendo.ui.PivotGrid) {\r\nkendo.ui.PivotGrid.prototype.options.messages =\r\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\r\n  \"measureFields\": \"Potiahnite sem polia údajov\",\r\n  \"columnFields\": \"Potiahnite sem polia stĺpcov\",\r\n  \"rowFields\": \"Potiahnite sem polia riadkov\"\r\n});\r\n}\r\n\r\n/* PivotFieldMenu messages */\r\n\r\nif (kendo.ui.PivotFieldMenu) {\r\nkendo.ui.PivotFieldMenu.prototype.options.messages =\r\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\r\n  \"info\": \"Zobraziť záznamy s hodnotou, ktorá:\",\r\n  \"filterFields\": \"Filter polí\",\r\n  \"filter\": \"Filter\",\r\n  \"include\": \"Zahrnúť polia...\",\r\n  \"title\": \"Polia na zahrnutie\",\r\n  \"clear\": \"Vyčistiť\",\r\n  \"ok\": \"Ok\",\r\n  \"cancel\": \"Storno\",\r\n  \"operators\": {\r\n    \"contains\": \"Obsahuje\",\r\n    \"doesnotcontain\": \"Neobsahuje\",\r\n    \"startswith\": \"Začína s\",\r\n    \"endswith\": \"Končí s\",\r\n    \"eq\": \"Rovná sa\",\r\n    \"neq\": \"Nerovná sa\"\r\n  }\r\n});\r\n}\r\n\r\n/* RecurrenceEditor messages */\r\n\r\nif (kendo.ui.RecurrenceEditor) {\r\nkendo.ui.RecurrenceEditor.prototype.options.messages =\r\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\r\n  \"frequencies\": {\r\n    \"never\": \"Nikdy\",\r\n    \"hourly\": \"Každú hodinu\",\r\n    \"daily\": \"Denne\",\r\n    \"weekly\": \"Týždenne\",\r\n    \"monthly\": \"Mesačne\",\r\n    \"yearly\": \"Ročne\"\r\n  },\r\n  \"hourly\": {\r\n    \"repeatEvery\": \"Opakovať každú: \",\r\n    \"interval\": \" hodinu(hodín)\"\r\n  },\r\n  \"daily\": {\r\n    \"repeatEvery\": \"Opakovať každý: \",\r\n    \"interval\": \" deň(dní)\"\r\n  },\r\n  \"weekly\": {\r\n    \"interval\": \" týždeň(týždňov)\",\r\n    \"repeatEvery\": \"Opakovať každý: \",\r\n    \"repeatOn\": \"Opakovať: \"\r\n  },\r\n  \"monthly\": {\r\n    \"repeatEvery\": \"Opakovať každý: \",\r\n    \"repeatOn\": \"Opakovať: \",\r\n    \"interval\": \" mesiac(mesiacov)\",\r\n    \"day\": \"Deň \"\r\n  },\r\n  \"yearly\": {\r\n    \"repeatEvery\": \"Opakovať každý: \",\r\n    \"repeatOn\": \"Opakovať: \",\r\n    \"interval\": \" rok(rokov)\",\r\n    \"of\": \" z \"\r\n  },\r\n  \"end\": {\r\n    \"label\": \"Koniec:\",\r\n    \"mobileLabel\": \"Ukončiť\",\r\n    \"never\": \"Nikdy\",\r\n    \"after\": \"Po \",\r\n    \"occurrence\": \" opakovaní(-iach)\",\r\n    \"on\": \"On \"\r\n  },\r\n  \"offsetPositions\": {\r\n    \"first\": \"prvý\",\r\n    \"second\": \"druhý\",\r\n    \"third\": \"tretí\",\r\n    \"fourth\": \"štvrtý\",\r\n    \"last\": \"posledný\"\r\n  },\r\n  \"weekdays\": {\r\n    \"day\": \"deň\",\r\n    \"weekday\": \"pracovný deň\",\r\n    \"weekend\": \"víkend\"\r\n  }\r\n});\r\n}\r\n\r\n/* Scheduler messages */\r\n\r\nif (kendo.ui.Scheduler) {\r\nkendo.ui.Scheduler.prototype.options.messages =\r\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\r\n  \"allDay\": \"celý deň\",\r\n  \"date\": \"Dátum\",\r\n  \"event\": \"Udalosť\",\r\n  \"time\": \"Čas\",\r\n  \"showFullDay\": \"Zobraziť celý deň\",\r\n  \"showWorkDay\": \"Zobraziť pracovný čas\",\r\n  \"today\": \"Dnes\",\r\n  \"save\": \"Uložiť\",\r\n  \"cancel\": \"Storno\",\r\n  \"destroy\": \"Odstrániť\",\r\n  \"deleteWindowTitle\": \"Odstránenie udalosti\",\r\n  \"ariaSlotLabel\": \"Vybraté od {0:t} do {1:t}\",\r\n  \"ariaEventLabel\": \"{0} dňa {1:D} o {2:t}\",\r\n  \"editable\": {\r\n    \"confirmation\": \"Naozaj chcete odstrániť túto udalosť?\"\r\n  },\r\n  \"views\": {\r\n    \"day\": \"Deň\",\r\n    \"week\": \"Týždeň\",\r\n    \"workWeek\": \"Pracovný týždeň\",\r\n    \"agenda\": \"Agenda\",\r\n    \"month\": \"Mesiac\"\r\n  },\r\n  \"recurrenceMessages\": {\r\n    \"deleteWindowTitle\": \"Odstránenie opakovanej udalosti\",\r\n    \"deleteWindowOccurrence\": \"Odstrániť aktuálnu udalosť\",\r\n    \"deleteWindowSeries\": \"Odstrániť všetko\",\r\n    \"editWindowTitle\": \"Úprava opakovanej udalosti\",\r\n    \"editWindowOccurrence\": \"Upraviť aktuálnu udalosť\",\r\n    \"editWindowSeries\": \"Upraviť všetko\",\r\n    \"deleteRecurring\": \"Chcete odstrániť len túto udalosť alebo aj všetky jej opakovania?\",\r\n    \"editRecurring\": \"Chcete upraviť len túto udalosť alebo aj všetky jej opakovania?\"\r\n  },\r\n  \"editor\": {\r\n    \"title\": \"Názov\",\r\n    \"start\": \"Začiatok\",\r\n    \"end\": \"Koniec\",\r\n    \"allDayEvent\": \"Celodenný\",\r\n    \"description\": \"Popis\",\r\n    \"repeat\": \"Opakovať\",\r\n    \"timezone\": \" \",\r\n    \"startTimezone\": \"Časové pásmo začiatku\",\r\n    \"endTimezone\": \"Časové pásmo konca\",\r\n    \"separateTimezones\": \"Rôzne časové pásma pre začiatok a koniec\",\r\n    \"timezoneEditorTitle\": \"Časové pásma\",\r\n    \"timezoneEditorButton\": \"Časové pásmo\",\r\n    \"timezoneTitle\": \"Časové pásma\",\r\n    \"noTimezone\": \"Bez časového pásma\",\r\n    \"editorTitle\": \"Udalosť\"\r\n  }\r\n});\r\n}\r\n\r\n/* Spreadsheet messages */\r\n\r\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\r\nkendo.spreadsheet.messages.borderPalette =\r\n$.extend(true, kendo.spreadsheet.messages.borderPalette, {\r\n    \"allBorders\": \"Všetky orámovania\",\r\n    \"insideBorders\": \"Vnútorné orámovania\",\r\n    \"insideHorizontalBorders\": \"Vnútorné vodorovné orámovania\",\r\n    \"insideVerticalBorders\": \"Vnútorné zvislé orámovania\",\r\n    \"outsideBorders\": \"Vonkajšie orámovania\",\r\n    \"leftBorder\": \"Ľavé orámovanie\",\r\n    \"topBorder\": \"Horné orámovanie\",\r\n    \"rightBorder\": \"Pravé orámovanie\",\r\n    \"bottomBorder\": \"Dolné orámovanie\",\r\n    \"noBorders\": \"Bez orámovania\",\r\n    \"reset\": \"Nulovať farbu\",\r\n    \"customColor\": \"Vlastná farba...\",\r\n    \"apply\": \"Použiť\",\r\n    \"cancel\": \"Storno\"\r\n});\r\n}\r\n\r\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\r\nkendo.spreadsheet.messages.dialogs =\r\n$.extend(true, kendo.spreadsheet.messages.dialogs, {\r\n    \"apply\": \"Použiť\",\r\n    \"save\": \"Uložiť\",\r\n    \"cancel\": \"Storno\",\r\n    \"remove\": \"Odstrániť\",\r\n    \"okText\": \"OK\",\r\n    \"formatCellsDialog\": {\r\n        \"title\": \"Formátovanie\",\r\n        \"categories\": {\r\n            \"number\": \"Číslo\",\r\n            \"currency\": \"Mena\",\r\n            \"date\": \"Dátum\"\r\n        }\r\n    },\r\n    \"fontFamilyDialog\": {\r\n        \"title\": \"Písmo\"\r\n    },\r\n    \"fontSizeDialog\": {\r\n        \"title\": \"Veľkosť písma\"\r\n    },\r\n    \"bordersDialog\": {\r\n        \"title\": \"Orámovania\"\r\n    },\r\n    \"alignmentDialog\": {\r\n        \"title\": \"Zarovnanie\",\r\n        \"buttons\": {\r\n            \"justtifyLeft\": \"Zarovnať vľavo\",\r\n            \"justifyCenter\": \"Centrovať\",\r\n            \"justifyRight\": \"Zarovnať vpravo\",\r\n            \"justifyFull\": \"Zarovnať do bloku\",\r\n            \"alignTop\": \"Zarovnať nahor\",\r\n            \"alignMiddle\": \"Zarovnať na stred\",\r\n            \"alignBottom\": \"Zarovnať nadol\"\r\n        }\r\n    },\r\n    \"mergeDialog\": {\r\n        \"title\": \"Spájanie buniek\",\r\n        \"buttons\": {\r\n            \"mergeCells\": \"Spojiť všetko\",\r\n            \"mergeHorizontally\": \"Spojiť vodorovne\",\r\n            \"mergeVertically\": \"Spojiť zvisle\",\r\n            \"unmerge\": \"Rozpojiť\"\r\n        }\r\n    },\r\n    \"freezeDialog\": {\r\n        \"title\": \"Zmrazenie panelov\",\r\n        \"buttons\": {\r\n            \"freezePanes\": \"Zmraziť panely\",\r\n            \"freezeRows\": \"Zmraziť riadky\",\r\n            \"freezeColumns\": \"Zmraziť stĺpce\",\r\n            \"unfreeze\": \"Rozmraziť panely\"\r\n        }\r\n    },\r\n    \"validationDialog\": {\r\n        \"title\": \"Overenie údajom\",\r\n        \"hintMessage\": \"Prosím zadajte platnú {0} hodnotu {1}.\",\r\n        \"hintTitle\": \"Overenie {0}\",\r\n        \"criteria\": {\r\n            \"any\": \"Akákoľvek hodnota\",\r\n            \"number\": \"Číslo\",\r\n            \"text\": \"Text\",\r\n            \"date\": \"Dátum\",\r\n            \"custom\": \"Vlastný vzorec\",\r\n            \"list\": \"Zoznam\"\r\n        },\r\n        \"comparers\": {\r\n            \"greaterThan\": \"väčšie ako\",\r\n            \"lessThan\": \"menšie ako\",\r\n            \"between\": \"medzi\",\r\n            \"notBetween\": \"nie medzi\",\r\n            \"equalTo\": \"rovná sa\",\r\n            \"notEqualTo\": \"nerovná sa\",\r\n            \"greaterThanOrEqualTo\": \"väčšie alebo sa rovná\",\r\n            \"lessThanOrEqualTo\": \"menšie alebo sa rovná\"\r\n        },\r\n        \"comparerMessages\": {\r\n            \"greaterThan\": \"väčšie ako {0}\",\r\n            \"lessThan\": \"menšie ako {0}\",\r\n            \"between\": \"medzi {0} a {1}\",\r\n            \"notBetween\": \"nie medzi {0} a {1}\",\r\n            \"equalTo\": \"rovná sa {0}\",\r\n            \"notEqualTo\": \"nerovná sa {0}\",\r\n            \"greaterThanOrEqualTo\": \"väčšie alebo rovná sa {0}\",\r\n            \"lessThanOrEqualTo\": \"menšie alebo rovná sa {0}\",\r\n            \"custom\": \"ktoré vyhovuje: {0}\"\r\n        },\r\n        \"labels\": {\r\n            \"criteria\": \"Kritériá\",\r\n            \"comparer\": \"Porovnávač\",\r\n            \"min\": \"Min\",\r\n            \"max\": \"Max\",\r\n            \"value\": \"Hodnota\",\r\n            \"start\": \"Začiatok\",\r\n            \"end\": \"Koniec\",\r\n            \"onInvalidData\": \"Pri platných údajoch\",\r\n            \"rejectInput\": \"Odmietnuť vstup\",\r\n            \"showWarning\": \"Zobraziť varovanie\",\r\n            \"showHint\": \"Zobraziť pomôcku\",\r\n            \"hintTitle\": \"Titulok pomôcky\",\r\n            \"hintMessage\": \"Správa pomôcky\",\r\n            \"ignoreBlank\": \"Ignorovať prázdne\"\r\n        },\r\n        \"placeholders\": {\r\n            \"typeTitle\": \"Titulok typu\",\r\n            \"typeMessage\": \"Správa typu\"\r\n        }\r\n    },\r\n    \"exportAsDialog\": {\r\n        \"title\": \"Exportovať...\",\r\n        \"labels\": {\r\n            \"fileName\": \"Meno súboru\",\r\n            \"saveAsType\": \"Uložiť ako typ\",\r\n            \"exportArea\": \"Exportovať\",\r\n            \"paperSize\": \"Veľkosť papiera\",\r\n            \"margins\": \"Okraje\",\r\n            \"orientation\": \"Orientácia\",\r\n            \"print\": \"Tlačiť\",\r\n            \"guidelines\": \"Pomocné čiary\",\r\n            \"center\": \"Centrovať\",\r\n            \"horizontally\": \"Vodorovne\",\r\n            \"vertically\": \"Zvisle\"\r\n        }\r\n    },\r\n    \"modifyMergedDialog\": {\r\n        \"errorMessage\": \"Nemožno zmeniť časť spojenej bunky.\"\r\n    },\r\n    \"useKeyboardDialog\": {\r\n        \"title\": \"Kopírovanie a vkladanie\",\r\n        \"errorMessage\": \"Tieto akcie sa nemožno ovládať cez menu. Prosím, použite klávesové skratky namiesto:\",\r\n        \"labels\": {\r\n            \"forCopy\": \"na kopírovanie\",\r\n            \"forCut\": \"na vystrihnutie\",\r\n            \"forPaste\": \"na vloženie\"\r\n        }\r\n    },\r\n    \"unsupportedSelectionDialog\": {\r\n        \"errorMessage\": \"Táto akcia nemôže byť vykonaná na viacnásobnom výbere.\"\r\n    }\r\n});\r\n}\r\n\r\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\r\nkendo.spreadsheet.messages.filterMenu =\r\n$.extend(true, kendo.spreadsheet.messages.filterMenu, {\r\n    \"sortAscending\": \"Usporiadať výber od A do Z\",\r\n    \"sortDescending\": \"Usporiadať výber od Z do A\",\r\n    \"filterByValue\": \"Filtrovať podľa hodnoty\",\r\n    \"filterByCondition\": \"Filtrovať podľa podmienky\",\r\n    \"apply\": \"Použiť\",\r\n    \"search\": \"Hľadať\",\r\n    \"addToCurrent\": \"Pridať do aktuálneho výberu\",\r\n    \"clear\": \"Vyčistiť\",\r\n    \"blanks\": \"(Prázdne)\",\r\n    \"operatorNone\": \"Žiadne\",\r\n    \"and\": \"A\",\r\n    \"or\": \"ALEBO\",\r\n    \"operators\": {\r\n        \"string\": {\r\n            \"contains\": \"Text obsahuje\",\r\n            \"doesnotcontain\": \"Text neobsahuje\",\r\n            \"startswith\": \"Text začína s\",\r\n            \"endswith\": \"Text končí s\"\r\n        },\r\n        \"date\": {\r\n            \"eq\": \"Dátum je\",\r\n            \"neq\": \"Dátum nie je\",\r\n            \"lt\": \"Dátum je pred\",\r\n            \"gt\": \"Dátum je po\"\r\n        },\r\n        \"number\": {\r\n            \"eq\": \"Rovná sa\",\r\n            \"neq\": \"Nerovná sa\",\r\n            \"gte\": \"Je väčšie alebo rovná sa\",\r\n            \"gt\": \"Je väčšie ako\",\r\n            \"lte\": \"Je menšie alebo rovná sa\",\r\n            \"lt\": \"Je menšie ako\"\r\n        }\r\n    }\r\n});\r\n}\r\n\r\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\r\nkendo.spreadsheet.messages.toolbar =\r\n$.extend(true, kendo.spreadsheet.messages.toolbar, {\r\n    \"addColumnLeft\": \"Pridať stĺpec vľavo\",\r\n    \"addColumnRight\": \"Pridať stĺpec vpravo\",\r\n    \"addRowAbove\": \"Pridať riadok nad\",\r\n    \"addRowBelow\": \"Pridať riadok pod\",\r\n    \"alignment\": \"Zarovnanie\",\r\n    \"alignmentButtons\": {\r\n        \"justtifyLeft\": \"Zarovnať vľavo\",\r\n        \"justifyCenter\": \"Centrovať\",\r\n        \"justifyRight\": \"Zarovnať vpravo\",\r\n        \"justifyFull\": \"Zarovnať do bloku\",\r\n        \"alignTop\": \"Zarovnať nahor\",\r\n        \"alignMiddle\": \"Zarovnať na stred\",\r\n        \"alignBottom\": \"Zarovnať nadol\"\r\n    },\r\n    \"backgroundColor\": \"Pozadie\",\r\n    \"bold\": \"Tučné\",\r\n    \"borders\": \"Orámovania\",\r\n    \"colorPicker\": {\r\n        \"reset\": \"Nulovať farbu\",\r\n        \"customColor\": \"Vlastná farba...\"\r\n    },\r\n    \"copy\": \"Kopírovať\",\r\n    \"cut\": \"Vystrihnúť\",\r\n    \"deleteColumn\": \"Odstrániť stĺpec\",\r\n    \"deleteRow\": \"Odstrániť riadok\",\r\n    \"excelImport\": \"Importovať z Excelu...\",\r\n    \"filter\": \"Filtrovať\",\r\n    \"fontFamily\": \"Písmo\",\r\n    \"fontSize\": \"Veľkosť písma\",\r\n    \"format\": \"Vlastný formát...\",\r\n    \"formatTypes\": {\r\n        \"automatic\": \"Automatický\",\r\n        \"number\": \"Číslo\",\r\n        \"percent\": \"Percentá\",\r\n        \"financial\": \"Finančný\",\r\n        \"currency\": \"Mena\",\r\n        \"date\": \"Dátum\",\r\n        \"time\": \"Čas\",\r\n        \"dateTime\": \"Dátum a čas\",\r\n        \"duration\": \"Časový úsek\",\r\n        \"moreFormats\": \"Viac formátov...\"\r\n    },\r\n    \"formatDecreaseDecimal\": \"Znížiť destinné miesta\",\r\n    \"formatIncreaseDecimal\": \"Zvýšiť desatinné miesta\",\r\n    \"freeze\": \"Zmraziť panely\",\r\n    \"freezeButtons\": {\r\n        \"freezePanes\": \"Zmraziť panely\",\r\n        \"freezeRows\": \"Zmraziť riadky\",\r\n        \"freezeColumns\": \"Zmraziť stĺpce\",\r\n        \"unfreeze\": \"Rozmraziť panely\"\r\n    },\r\n    \"italic\": \"Šikmé\",\r\n    \"merge\": \"Spojiť bunky\",\r\n    \"mergeButtons\": {\r\n        \"mergeCells\": \"Spojiť všetko\",\r\n        \"mergeHorizontally\": \"Spojiť vodorovne\",\r\n        \"mergeVertically\": \"Spojiť zvisle\",\r\n        \"unmerge\": \"Rozpojiž\"\r\n    },\r\n    \"open\": \"Otvoriť...\",\r\n    \"paste\": \"Vložiť\",\r\n    \"quickAccess\": {\r\n        \"redo\": \"Znova\",\r\n        \"undo\": \"Späť\"\r\n    },\r\n    \"saveAs\": \"Uložiť Ako...\",\r\n    \"sortAsc\": \"Usporiadať vzostupne\",\r\n    \"sortDesc\": \"Usporiadať zostupne\",\r\n    \"sortButtons\": {\r\n        \"sortSheetAsc\": \"Usporiadať list od A do Z\",\r\n        \"sortSheetDesc\": \"Usporiadať list od Z do A\",\r\n        \"sortRangeAsc\": \"Usporiadať výber od A do Z\",\r\n        \"sortRangeDesc\": \"Usporiadať výber od o Z do A\"\r\n    },\r\n    \"textColor\": \"Farba Textu\",\r\n    \"textWrap\": \"Zalomiť text\",\r\n    \"underline\": \"Podčiarknuté\",\r\n    \"validation\": \"Overenie údajov...\"\r\n});\r\n}\r\n\r\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\r\nkendo.spreadsheet.messages.view =\r\n$.extend(true, kendo.spreadsheet.messages.view, {\r\n    \"errors\": {\r\n        \"shiftingNonblankCells\": \"Nemožno vložiť bunky kvôli možnosti straty dát. Vyberte iné miesto na vloženie alebo odstráňte údaje z konca listu.\",\r\n        \"filterRangeContainingMerges\": \"Nemožno vytvoriť filter na výbere, ktoré obsahuje spájania\",\r\n        \"validationError\": \"Hodnota, ktorú ste zadali porušuje pravidlá platnosti stanovené pre bunku.\"\r\n    },\r\n    \"tabs\": {\r\n        \"home\": \"Domov\",\r\n        \"insert\": \"Vložiť\",\r\n        \"data\": \"Údaje\"\r\n    }\r\n});\r\n}\r\n\r\n/* Slider messages */\r\n\r\nif (kendo.ui.Slider) {\r\nkendo.ui.Slider.prototype.options =\r\n$.extend(true, kendo.ui.Slider.prototype.options,{\r\n  \"increaseButtonTitle\": \"Zvýšiť\",\r\n  \"decreaseButtonTitle\": \"Znížiť\"\r\n});\r\n}\r\n\r\n/* TreeList messages */\r\n\r\nif (kendo.ui.TreeList) {\r\nkendo.ui.TreeList.prototype.options.messages =\r\n$.extend(true, kendo.ui.TreeList.prototype.options.messages, {\r\n    \"noRows\": \"Žiadne záznamy na zobrazenie\",\r\n    \"loading\": \"Nahrávanie...\",\r\n    \"requestFailed\": \"Požiadavka zlyhala.\",\r\n    \"retry\": \"Znova\",\r\n    \"commands\": {\r\n        \"edit\": \"Upraviť\",\r\n        \"update\": \"Uložiť\",\r\n        \"canceledit\": \"Storno\",\r\n        \"create\": \"Pridať nový záznam\",\r\n        \"createchild\": \"Pridať podzáznam\",\r\n        \"destroy\": \"Odstrániť\",\r\n        \"excel\": \"Exportovať do Excelu\",\r\n        \"pdf\": \"Exportovať do PDF\"\r\n    }\r\n});\r\n}\r\n\r\nif (kendo.ui.TreeList) {\r\nkendo.ui.TreeList.prototype.options.columnMenu =\r\n$.extend(true, kendo.ui.TreeList.prototype.options.columnMenu, {\r\n    \"messages\": {\r\n        \"columns\": \"Zvoľte stĺpce\",\r\n        \"filter\": \"Použiť filter\",\r\n        \"sortAscending\": \"Usporiadať vzostupne\",\r\n        \"sortDescending\": \"Usporiadať zostupne\"\r\n    }\r\n});\r\n}\r\n\r\n\r\n/* TreeView messages */\r\n\r\nif (kendo.ui.TreeView) {\r\nkendo.ui.TreeView.prototype.options.messages =\r\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\r\n  \"loading\": \"Nahrávanie...\",\r\n  \"requestFailed\": \"Požiadavka zlyhala.\",\r\n  \"retry\": \"Znova\"\r\n});\r\n}\r\n\r\n/* Upload messages */\r\n\r\nif (kendo.ui.Upload) {\r\nkendo.ui.Upload.prototype.options.localization=\r\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\r\n  \"select\": \"Vyberte súbory...\",\r\n  \"cancel\": \"Storno\",\r\n  \"retry\": \"Znova\",\r\n  \"remove\": \"Odstrániť\",\r\n  \"uploadSelectedFiles\": \"Odoslať súbory\",\r\n  \"dropFilesHere\": \"potiahnite sem súbory, ktoré chcete odoslať\",\r\n  \"statusUploading\": \"odosielanie\",\r\n  \"statusUploaded\": \"hotovo\",\r\n  \"statusWarning\": \"varovanie\",\r\n  \"statusFailed\": \"zlyhanie\",\r\n  \"headerStatusUploading\": \"Odosielanie...\",\r\n  \"headerStatusUploaded\": \"Hotovo\"\r\n});\r\n}\r\n\r\n/* Validator messages */\r\n\r\nif (kendo.ui.Validator) {\r\nkendo.ui.Validator.prototype.options.messages =\r\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\r\n  \"required\": \"{0} je požadovaný údaj\",\r\n  \"pattern\": \"{0} nie je platný údaj\",\r\n  \"min\": \"{0} musí byť aspoň {1}\",\r\n  \"max\": \"{0} môže byť najviac {1}\",\r\n  \"step\": \"{0} nie je platný údaj\",\r\n  \"email\": \"{0} nie je platný email\",\r\n  \"url\": \"{0} nie je platná adresa URL\",\r\n  \"date\": \"{0} nie je platný dátum\",\r\n  \"dateCompare\": \"Koncový dátum musí byť väčší alebo rovný ako počiatočný\"\r\n});\r\n}\r\n\r\n/* Dialog */\r\n\r\nif (kendo.ui.Dialog) {\r\nkendo.ui.Dialog.prototype.options.messages =\r\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\r\n  \"close\": \"Zavrieť\"\r\n});\r\n}\r\n\r\n/* Alert */\r\n\r\nif (kendo.ui.Alert) {\r\nkendo.ui.Alert.prototype.options.messages =\r\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\r\n  \"okText\": \"OK\"\r\n});\r\n}\r\n\r\n/* Confirm */\r\n\r\nif (kendo.ui.Confirm) {\r\nkendo.ui.Confirm.prototype.options.messages =\r\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\r\n  \"okText\": \"OK\",\r\n  \"cancel\": \"Storno\"\r\n});\r\n}\r\n\r\n/* Prompt */\r\nif (kendo.ui.Prompt) {\r\nkendo.ui.Prompt.prototype.options.messages =\r\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\r\n  \"okText\": \"OK\",\r\n  \"cancel\": \"Storno\"\r\n});\r\n}\r\n\r\n})(window.kendo.jQuery);\r\n}));"]}