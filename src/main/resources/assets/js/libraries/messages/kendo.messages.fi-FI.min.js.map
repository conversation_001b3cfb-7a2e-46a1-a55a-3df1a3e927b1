{"version": 3, "sources": ["messages/kendo.messages.fi-FI.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "dialogs", "remove", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "blanks", "operatorNone", "toolbar", "alignment", "alignmentButtons", "backgroundColor", "borders", "copy", "cut", "excelExport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "TreeView", "Upload", "localization", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,QACTC,OAAU,aAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,QACTC,OAAU,aAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,oBACjBC,eAAkB,oBAClBC,OAAU,UACVC,QAAW,YACXC,KAAQ,SACRC,SAAY,kBACZC,KAAQ,UACRC,OAAU,oBAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,YACRC,OAAU,aACVC,UAAa,eACbC,cAAiB,aACjBC,YAAe,aACfC,UAAa,aACbC,cAAiB,UACjBC,YAAe,mBACfC,aAAgB,iBAChBC,YAAe,wBACfC,oBAAuB,cACvBC,kBAAqB,0BACrBC,OAAU,sBACVC,QAAW,sBACXC,WAAc,oBACdC,OAAU,qBACVC,YAAe,aACfC,WAAc,iBACdC,WAAc,aACdC,SAAY,aACZC,SAAY,iBACZC,gBAAmB,mBACnBC,SAAY,sBACZC,gBAAmB,iBACnBC,YAAe,WACfC,WAAc,WACdC,UAAa,OACbC,UAAa,aACbC,MAAS,SACTC,YAAe,eACfC,WAAc,oBACdC,QAAW,qBACXC,YAAe,OACfC,YAAe,OACfC,gBAAmB,+DACnBC,WAAc,6CACdC,cAAiB,2FACjBC,kBAAqB,0CACrBC,gBAAmB,aACnBC,aAAgB,mBAChBC,WAAc,cACdC,YAAe,eACfC,eAAkB,aAClBC,UAAa,UACbC,eAAkB,aAClBC,SAAY,SACZC,YAAe,cACfC,oBAAuB,gCACvBC,aAAgB,UAChBC,aAAgB,QAChBC,sBAAyB,MACzBC,aAAgB,UAChBC,YAAe,iBACfC,cAAiB,0BACjBC,eAAkB,wBAClBC,YAAe,yBACfC,YAAe,yBACfC,UAAa,cACbC,aAAgB,mBAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,oBACdC,QAAW,oBACXE,YAAe,OACfD,YAAe,OACfK,kBAAqB,0CACrBR,YAAe,eACfM,WAAc,6CACdD,gBAAmB,+DACnBE,cAAiB,2FACjBwB,cAAiB,gDACjBC,OAAU,SAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,KACVC,QAAW,SACXvE,OAAU,UACVwE,MAAS,WACTC,SAAY,iBAMVtF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,KACNC,IAAO,SACPC,WAAc,QACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,UAEdC,QACEN,GAAM,KACNC,IAAO,SACPM,IAAO,6BACPC,GAAM,mBACNC,IAAO,6BACPC,GAAM,oBAERC,MACEX,GAAM,KACNC,IAAO,SACPM,IAAO,6BACPC,GAAM,mBACNC,IAAO,+BACPC,GAAM,sBAERE,OACEZ,GAAM,KACNC,IAAO,aAOP1F,MAAMC,GAAGqG,aACbtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,UACnDkG,KAAQ,+BACRpB,OAAU,KACVC,QAAW,SACXvE,OAAU,UACVwE,MAAS,WACTmB,IAAO,KACPC,GAAM,MACNC,YAAe,iBACfpB,SAAY,cACZqB,MAAS,OACTnG,OAAU,aAMRR,MAAMC,GAAGqG,aACbtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,KACNC,IAAO,SACPC,WAAc,QACdC,SAAY,WACZC,eAAkB,aAClBC,SAAY,UAEdC,QACEN,GAAM,KACNC,IAAO,SACPM,IAAO,6BACPC,GAAM,mBACNC,IAAO,6BACPC,GAAM,oBAERC,MACEX,GAAM,KACNC,IAAO,SACPM,IAAO,6BACPC,GAAM,mBACNC,IAAO,+BACPC,GAAM,sBAERE,OACEZ,GAAM,KACNC,IAAO,aAOP1F,MAAMC,GAAG2G,mBACb5G,MAAMC,GAAG2G,iBAAiBzG,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,iBAAiBzG,UAAUC,QAAQC,UACzDwG,SAAY,iBACZxB,MAAS,WACTxE,OAAU,WACVoE,OAAU,SAMRjF,MAAMC,GAAG6G,QACb9G,MAAMC,GAAG6G,MAAM3G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6G,MAAM3G,UAAUC,QAAQC,UAC9C0G,SACEC,SAAY,cACZC,OAAU,gBACVC,YAAe,oBACfC,aAAgB,oBAChBC,IAAO,WAET5G,OAAU,UACV6G,4BAA+B,oBAC/BC,sBAAyB,iBACzBC,QAAW,SACXC,QACEC,aAAgB,WAChBC,YAAe,UACfC,IAAO,QACPC,gBAAmB,SACnBC,UAAa,YACbC,qBAAwB,YACxBC,gBAAmB,YACnBC,MAAS,OACTC,MAAS,UACTC,YAAe,WAEjBC,KAAQ,WACRC,OACEC,IAAO,QACPV,IAAO,QACPW,MAAS,WACTN,MAAS,OACTO,KAAQ,SACRC,KAAQ,YAORxI,MAAMC,GAAGwI,OACbzI,MAAMC,GAAGwI,KAAKtI,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwI,KAAKtI,UAAUC,QAAQC,UAC7CqI,UACElI,OAAU,oBACVmI,WAAc,UACdC,OAAU,oBACVrB,QAAW,SACXsB,KAAQ,UACRC,MAAS,eACT1B,IAAO,UACPe,KAAQ,qBACRY,OAAU,UACVC,OAAU,WAEZC,UACEC,aAAgB,UAChBC,aAAgB,sCAChBC,cAAiB,UAEnBC,UAAa,mBAMXrJ,MAAMC,GAAGqJ,YACbtJ,MAAMC,GAAGqJ,UAAUnJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqJ,UAAUnJ,UAAUC,QAAQC,UAClDkJ,MAAS,oEAMPvJ,MAAMC,GAAGuJ,iBACbxJ,MAAMC,GAAGuJ,eAAerJ,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuJ,eAAerJ,UAAUC,SAC/CqJ,YAAe,gBACfC,cAAiB,mBAMf1J,MAAMC,GAAG0J,QACb3J,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,MAAMxJ,UAAUC,QAAQC,UAC9CuJ,SAAY,SACZC,QAAW,gCACXN,MAAS,cACTO,KAAQ,OACRC,GAAM,QACNC,aAAgB,kBAChBC,MAAS,mBACTC,SAAY,iBACZC,KAAQ,gBACRC,KAAQ,iBACRC,QAAW,UACXC,UAAa,kBAMXtK,MAAMC,GAAGsK,YACbvK,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQC,UAClDmK,cAAiB,0BACjBC,aAAgB,yBAChBC,UAAa,wBAMX1K,MAAMC,GAAG0K,iBACb3K,MAAMC,GAAG0K,eAAexK,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0K,eAAexK,UAAUC,QAAQC,UACvDkG,KAAQ,+BACRqE,aAAgB,oBAChB/J,OAAU,UACVgK,QAAW,sBACX5C,MAAS,yBACT5C,MAAS,WACTyF,GAAM,KACNtK,OAAU,UACV+E,WACEK,SAAY,WACZC,eAAkB,aAClBF,WAAc,QACdG,SAAY,SACZL,GAAM,KACNC,IAAO,aAOP1F,MAAMC,GAAG8K,mBACb/K,MAAMC,GAAG8K,iBAAiB5K,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8K,iBAAiB5K,UAAUC,QAAQC,UACzD2K,aACEC,MAAS,aACTC,OAAU,aACVC,MAAS,aACTC,OAAU,cACVC,QAAW,gBACXC,OAAU,cAEZJ,QACEK,YAAe,gBACfC,SAAY,UAEdL,OACEI,YAAe,gBACfC,SAAY,UAEdJ,QACEI,SAAY,UACZD,YAAe,gBACfE,SAAY,oBAEdJ,SACEE,YAAe,gBACfE,SAAY,mBACZD,SAAY,YACZnD,IAAO,UAETiD,QACEC,YAAe,gBACfE,SAAY,mBACZD,SAAY,SACZzB,GAAM,SAERpC,KACE+D,MAAS,SACTC,YAAe,SACfV,MAAS,aACTW,MAAS,iBACTC,WAAc,UACdC,GAAM,eAERC,iBACE9B,MAAS,cACT+B,OAAU,SACVC,MAAS,SACTC,OAAU,SACV9B,KAAQ,aAEV+B,UACE9D,IAAO,QACP+D,QAAW,YACXC,QAAW,kBAOXrM,MAAMC,GAAGqM,YACbtM,MAAMC,GAAGqM,UAAUnM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,UAAUnM,UAAUC,QAAQC,UAClDkM,OAAU,aACVnG,KAAQ,aACRoG,MAAS,YACTC,KAAQ,aACRC,YAAe,mBACfC,YAAe,mBACfC,MAAS,SACTzE,KAAQ,WACR3H,OAAU,UACV+G,QAAW,SACXsF,kBAAqB,mBACrBC,cAAiB,0BACjBC,eAAkB,sBAClB9D,UACEE,aAAgB,yCAElBf,OACEC,IAAO,QACPE,KAAQ,SACRyE,SAAY,YACZC,OAAU,SACV3E,MAAS,YAEX4E,oBACEL,kBAAqB,4BACrBM,uBAA0B,6BAC1BC,mBAAsB,2BACtBC,gBAAmB,+BACnBC,qBAAwB,+BACxBC,iBAAoB,6BACpBC,gBAAmB,2DACnBC,cAAiB,2DAEnBjG,QACES,MAAS,UACTD,MAAS,OACTL,IAAO,QACP+F,YAAe,wBACfC,YAAe,SACfC,OAAU,cACVC,SAAY,IACZC,cAAiB,wBACjBC,YAAe,yBACfC,kBAAqB,iDACrBC,oBAAuB,iBACvBC,qBAAwB,cACxBC,cAAiB,iBACjBC,WAAc,oBACd1G,YAAe,gBAOf1H,MAAMqO,aAAerO,MAAMqO,YAAYhO,SAASiO,gBACpDtO,MAAMqO,YAAYhO,SAASiO,cAC3BxO,EAAEQ,QAAO,EAAMN,MAAMqO,YAAYhO,SAASiO,eACxCC,WAAc,gBACdC,cAAiB,aACjBC,wBAA2B,uBAC3BC,sBAAyB,uBACzBC,eAAkB,aAClBC,WAAc,cACdC,UAAa,WACbC,YAAe,cACfC,aAAgB,WAChBC,UAAa,oBAIXhP,MAAMqO,aAAerO,MAAMqO,YAAYhO,SAAS4O,UACpDjP,MAAMqO,YAAYhO,SAAS4O,QAC3BnP,EAAEQ,QAAO,EAAMN,MAAMqO,YAAYhO,SAAS4O,SACxC1O,MAAS,QACT4H,KAAQ,WACR3H,OAAU,UACV0O,OAAU,SACVC,OAAU,KACVC,mBACEnH,MAAS,WACToH,YACEtJ,OAAU,SACVuJ,SAAY,WACZlJ,KAAQ,eAGZmJ,kBACEtH,MAAS,UAEXuH,gBACEvH,MAAS,cAEXwH,eACExH,MAAS,eAEXyH,iBACEzH,MAAS,YACT0H,SACCC,aAAgB,mBAChBlO,cAAiB,UACjBE,aAAgB,iBAChBC,YAAe,wBACfgO,SAAY,aACZC,YAAe,iBACfC,YAAe,eAGlBC,aACE/H,MAAS,gBACT0H,SACEM,WAAc,iBACdC,kBAAqB,qBACrBC,gBAAmB,qBACnBC,QAAW,gCAGfC,cACEpI,MAAS,kBACT0H,SACEW,YAAe,kBACfC,WAAc,iBACdC,cAAiB,qBACjBC,SAAY,mBAGhBC,kBACEzI,MAAS,mBACT0I,YAAe,mDACfC,UAAa,iBACbC,UACEC,IAAO,eACP/K,OAAU,SACVgL,KAAQ,SACR3K,KAAQ,aACR4K,OAAU,mBAEZC,WACEC,YAAe,gBACfC,SAAY,gBACZC,QAAW,UACXC,WAAc,aACdC,QAAW,QACXC,WAAc,WACdC,qBAAwB,0BACxBC,kBAAqB,2BAEvBC,kBACER,YAAe,oBACfC,SAAY,oBACZC,QAAW,qBACXC,WAAc,wBACdC,QAAW,YACXC,WAAc,eACdC,qBAAwB,8BACxBC,kBAAqB,8BACrBT,OAAU,2BAEZW,QACEd,SAAY,WACZe,SAAY,WACZC,IAAO,MACPC,IAAO,MACPnL,MAAS,OACTqB,MAAS,OACTL,IAAO,QACPoK,cAAiB,kBACjBC,YAAe,cACfC,YAAe,iBACfC,SAAY,cACZtB,UAAa,kBACbD,YAAe,eAEjBwB,cACEC,UAAa,iBACbC,YAAe,kBAGnBC,gBACErK,MAAS,sBACT0J,QACEY,SAAY,eACZC,WAAc,qBAGlBC,oBACEC,aAAgB,yCAElBC,mBACE1K,MAAS,2BACTyK,aAAgB,qEAChBf,QACEiB,QAAW,gBACXC,OAAU,eACVC,SAAY,iBAGhBC,4BACEL,aAAgB,oDAKhB1S,MAAMqO,aAAerO,MAAMqO,YAAYhO,SAAS2S,aACpDhT,MAAMqO,YAAYhO,SAAS2S,WAC3BlT,EAAEQ,QAAO,EAAMN,MAAMqO,YAAYhO,SAAS2S,YACxCrS,cAAiB,kBACjBC,eAAkB,kBAClBqS,cAAiB,4BACjBC,kBAAqB,4BACrB3S,MAAS,QACT0E,OAAU,MACVI,MAAS,WACT8N,OAAU,WACVC,aAAgB,YAChB5M,IAAO,KACPC,GAAM,MACNlB,WACEC,QACEI,SAAY,kBACZC,eAAkB,oBAClBF,WAAc,eACdG,SAAY,iBAEdM,MACEX,GAAO,gBACPC,IAAO,oBACPS,GAAO,6BACPF,GAAO,gCAETF,QACEN,GAAM,KACNC,IAAO,SACPM,IAAO,6BACPC,GAAM,mBACNC,IAAO,6BACPC,GAAM,wBAMRnG,MAAMqO,aAAerO,MAAMqO,YAAYhO,SAASgT,UACpDrT,MAAMqO,YAAYhO,SAASgT,QAC3BvT,EAAEQ,QAAO,EAAMN,MAAMqO,YAAYhO,SAASgT,SACxC5O,cAAiB,0BACjBC,eAAkB,0BAClBC,YAAe,yBACfC,YAAe,yBACf0O,UAAa,YACbC,kBACE3D,aAAgB,mBAChBlO,cAAiB,UACjBE,aAAgB,iBAChBC,YAAe,wBACfgO,SAAY,aACZC,YAAe,iBACfC,YAAe,cAEjByD,gBAAmB,aACnBpS,KAAQ,YACRqS,QAAW,cACXC,KAAQ,SACRC,IAAO,UACP7O,aAAgB,gBAChBD,UAAa,cACb+O,YAAe,kBACf/S,OAAU,UACVgT,WAAc,SACdnR,SAAY,aACZoR,OAAU,yBACVC,aACEC,UAAa,gBACbjO,OAAU,SACVkO,QAAW,iBACXC,UAAa,wBACb5E,SAAY,WACZlJ,KAAQ,aACRqG,KAAQ,aACR0H,SAAY,aACZC,SAAY,QACZC,YAAe,uBAEjBC,sBAAyB,sBACzBC,sBAAyB,oBACzBC,OAAU,kBACVC,eACEnE,YAAe,kBACfC,WAAc,iBACdC,cAAiB,qBACjBC,SAAY,kBAEdpP,OAAU,aACVqT,MAAS,gBACTC,cACE1E,WAAc,iBACdC,kBAAqB,oBACrBC,gBAAmB,qBACnBC,QAAW,+BAEbwE,KAAQ,UACRC,MAAS,QACTC,aACEC,KAAQ,gBACRC,KAAQ,SAEVC,OAAU,sBACVC,QAAW,oBACXC,SAAY,oBACZC,aACEC,aAAgB,0BAChBC,cAAiB,0BACjBC,aAAgB,yBAChBC,cAAiB,0BAEnBC,UAAa,eACbC,SAAY,kBACZpU,UAAa,eACbqU,WAAc,yBAIZ3V,MAAMqO,aAAerO,MAAMqO,YAAYhO,SAASuV,OACpD5V,MAAMqO,YAAYhO,SAASuV,KAC3B9V,EAAEQ,QAAO,EAAMN,MAAMqO,YAAYhO,SAASuV,MACxCC,QACEC,sBAAyB,4GACzBC,4BAA+B,qDAEjCC,MACEC,KAAQ,UACRC,OAAU,QACVC,KAAQ,WAORnW,MAAMC,GAAGmW,SACbpW,MAAMC,GAAGmW,OAAOjW,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmW,OAAOjW,UAAUC,SACvCiW,oBAAuB,QACvBC,oBAAuB,aAMrBtW,MAAMC,GAAGsW,WACbvW,MAAMC,GAAGsW,SAASpW,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsW,SAASpW,UAAUC,QAAQC,UACjDmW,OAAU,cACVC,QAAW,cACXC,cAAiB,0BACjBC,MAAS,kBACTjO,UACIG,KAAQ,UACRG,OAAU,UACVL,WAAc,UACdC,OAAU,oBACVgO,YAAe,oBACfrP,QAAW,SACXuB,MAAS,eACT1B,IAAO,cAOTpH,MAAMC,GAAG4W,WACb7W,MAAMC,GAAG4W,SAAS1W,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4W,SAAS1W,UAAUC,QAAQC,UACjDoW,QAAW,cACXC,cAAiB,0BACjBC,MAAS,qBAMP3W,MAAMC,GAAG6W,SACb9W,MAAMC,GAAG6W,OAAO3W,UAAUC,QAAQ2W,aAClCjX,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6W,OAAO3W,UAAUC,QAAQ2W,cAC/ChO,OAAU,wBACVvI,OAAU,UACVmW,MAAS,kBACTzH,OAAU,SACV8H,oBAAuB,8BACvBhS,cAAiB,iDACjBiS,gBAAmB,WACnBC,eAAkB,UAClBC,cAAiB,WACjBC,aAAgB,cAChBC,sBAAyB,cACzBC,qBAAwB,YAMtBtX,MAAMC,GAAGsX,YACbvX,MAAMC,GAAGsX,UAAUpX,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsX,UAAUpX,UAAUC,QAAQC,UAClDmX,SAAY,gBACZC,QAAW,gBACX5F,IAAO,4CACPC,IAAO,4CACP4F,KAAQ,gBACRC,MAAS,0CACTC,IAAO,6BACPxR,KAAQ,oCACRyR,YAAe,yEAMb7X,MAAMC,GAAG6X,SACb9X,MAAMC,GAAG6X,OAAO3X,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6X,OAAO3X,UAAUC,QAAQ2W,cAC/CgB,MAAS,YAMP/X,MAAMC,GAAG+X,QACbhY,MAAMC,GAAG+X,MAAM7X,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+X,MAAM7X,UAAUC,QAAQ2W,cAC9C5H,OAAU,QAMRnP,MAAMC,GAAGgY,UACbjY,MAAMC,GAAGgY,QAAQ9X,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgY,QAAQ9X,UAAUC,QAAQ2W,cAChD5H,OAAU,KACV3O,OAAU,aAKRR,MAAMC,GAAGiY,SACblY,MAAMC,GAAGiY,OAAO/X,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiY,OAAO/X,UAAUC,QAAQ2W,cAC/C5H,OAAU,KACV3O,OAAU,cAIT2X,OAAOnY,MAAMoY", "file": "kendo.messages.fi-FI.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"Käytä\",\n  \"cancel\": \"Peruuta\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"<PERSON><PERSON>yt<PERSON>\",\n  \"cancel\": \"Peruuta\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"<PERSON>useva järjestys\",\n  \"sortDescending\": \"<PERSON>keva järjestys\",\n  \"filter\": \"Suoda<PERSON>\",\n  \"columns\": \"Sarakkeet\",\n  \"done\": \"Valmis\",\n  \"settings\": \"Sarakeasetukset\",\n  \"lock\": \"Lukitse\",\n  \"unlock\": \"Poista lukinta\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Lihavoitu\",\n  \"italic\": \"Kursivoitu\",\n  \"underline\": \"Alleviivattu\",\n  \"strikethrough\": \"Yliviivaus\",\n  \"superscript\": \"Yläindeksi\",\n  \"subscript\": \"Alaindeksi\",\n  \"justifyCenter\": \"Keskitä\",\n  \"justifyLeft\": \"Tasaa vasemmalle\",\n  \"justifyRight\": \"Tasaa oikealle\",\n  \"justifyFull\": \"Tasaa molemmat reunat\",\n  \"insertUnorderedList\": \"Lisää lista\",\n  \"insertOrderedList\": \"Lisää järjestetty lista\",\n  \"indent\": \"Suurenna sisennystä\",\n  \"outdent\": \"Pienennä sisennystä\",\n  \"createLink\": \"Lisää hyperlinkki\",\n  \"unlink\": \"Poista hyperlinkki\",\n  \"insertImage\": \"Lisää kuva\",\n  \"insertFile\": \"Lisää tiedosto\",\n  \"insertHtml\": \"Lisää HTML\",\n  \"viewHtml\": \"Näytä HTML\",\n  \"fontName\": \"Valitse fontti\",\n  \"fontNameInherit\": \"(peritty fontti)\",\n  \"fontSize\": \"Valitse fontin koko\",\n  \"fontSizeInherit\": \"(peritty koko)\",\n  \"formatBlock\": \"Muotoilu\",\n  \"formatting\": \"Muotoilu\",\n  \"foreColor\": \"Väri\",\n  \"backColor\": \"Taustaväri\",\n  \"style\": \"Tyylit\",\n  \"emptyFolder\": \"Tyhjä kansio\",\n  \"uploadFile\": \"Lataa palvelimeen\",\n  \"orderBy\": \"Järjestelyperuste:\",\n  \"orderBySize\": \"Koko\",\n  \"orderByName\": \"Nimi\",\n  \"invalidFileType\": \"Tiedosto \\\"{0}\\\" ei kelpaa. Tuettuja tiedostomuotoja ovat {1}.\",\n  \"deleteFile\": \"Haluatko varmasti poistaa tiedoston \\\"{0}\\\"?\",\n  \"overwriteFile\": \"Tiedosto \\\"{0}\\\" on jo olemassa nykyisessä hakemistossa. Haluatko ylikirjoittaa tiedoston?\",\n  \"directoryNotFound\": \"Halutun nimistä hakemistoa ei löytynyt.\",\n  \"imageWebAddress\": \"WWW-osoite\",\n  \"imageAltText\": \"Vaihtoehtokuvaus\",\n  \"imageWidth\": \"Leveys (px)\",\n  \"imageHeight\": \"Korkeus (px)\",\n  \"fileWebAddress\": \"WWW-osoite\",\n  \"fileTitle\": \"Otsikko\",\n  \"linkWebAddress\": \"WWW-osoite\",\n  \"linkText\": \"Teksti\",\n  \"linkToolTip\": \"Vihjeteksti\",\n  \"linkOpenInNewWindow\": \"Avaa linkki uudessa ikkunassa\",\n  \"dialogUpdate\": \"Päivitä\",\n  \"dialogInsert\": \"Lisää\",\n  \"dialogButtonSeparator\": \"tai\",\n  \"dialogCancel\": \"Peruuta\",\n  \"createTable\": \"Lisää taulukko\",\n  \"addColumnLeft\": \"Lisää sarake vasemmalle\",\n  \"addColumnRight\": \"Lisää sarake oikealle\",\n  \"addRowAbove\": \"Lisää rivi yläpuolelle\",\n  \"addRowBelow\": \"Lisää rivi alapuolelle\",\n  \"deleteRow\": \"Poista rivi\",\n  \"deleteColumn\": \"Poista sarake\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"Lataa palvelimeen\",\n  \"orderBy\": \"Järjestelyperuste\",\n  \"orderByName\": \"Nimi\",\n  \"orderBySize\": \"Koko\",\n  \"directoryNotFound\": \"Halutun nimistä hakemistoa ei löytynyt.\",\n  \"emptyFolder\": \"Tyhjä kansio\",\n  \"deleteFile\": \"Haluatko varmasti poistaa tiedoston \\\"{0}\\\"?\",\n  \"invalidFileType\": \"Tiedosto \\\"{0}\\\" ei kelpaa. Tuettuja tiedostomuotoja ovat {1}.\",\n  \"overwriteFile\": \"Tiedosto \\\"{0}\\\" on jo olemassa nykyisessä hakemistossa. Haluatko ylikirjoittaa tiedoston?\",\n  \"dropFilesHere\": \"pudota tiedosto tähän ladataksesi palvelimeen\",\n  \"search\": \"Hae\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"on\",\n  \"isFalse\": \"ei ole\",\n  \"filter\": \"Suodata\",\n  \"clear\": \"Tyhjennä\",\n  \"operator\": \"Operaattori\"\n});\n}\n\n/* FilterCell operators */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\",\n    \"startswith\": \"Alkaa\",\n    \"contains\": \"Sisältää\",\n    \"doesnotcontain\": \"Ei sisällä\",\n    \"endswith\": \"Loppuu\"\n  },\n  \"number\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\",\n    \"gte\": \"On tasan tai suurempi kuin\",\n    \"gt\": \"On suurempi kuin\",\n    \"lte\": \"On tasan tai pienempi kuin\",\n    \"lt\": \"On pienempi kuin\"\n  },\n  \"date\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\",\n    \"gte\": \"On tasan tai myöhempi kuin\",\n    \"gt\": \"On myöhempi kuin\",\n    \"lte\": \"On tasan tai aikaisempi kuin\",\n    \"lt\": \"On aikaisempi kuin\"\n  },\n  \"enums\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\"\n  }\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"Näytä tulokset, joiden arvo:\",\n  \"isTrue\": \"on\",\n  \"isFalse\": \"ei ole\",\n  \"filter\": \"Suodata\",\n  \"clear\": \"Tyhjennä\",\n  \"and\": \"Ja\",\n  \"or\": \"Tai\",\n  \"selectValue\": \"-Valitse arvo-\",\n  \"operator\": \"Operaattori\",\n  \"value\": \"Arvo\",\n  \"cancel\": \"Peruuta\"\n});\n}\n\n/* FilterMenu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\",\n    \"startswith\": \"Alkaa\",\n    \"contains\": \"Sisältää\",\n    \"doesnotcontain\": \"Ei sisällä\",\n    \"endswith\": \"Loppuu\"\n  },\n  \"number\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\",\n    \"gte\": \"On tasan tai suurempi kuin\",\n    \"gt\": \"On suurempi kuin\",\n    \"lte\": \"On tasan tai pienempi kuin\",\n    \"lt\": \"On pienempi kuin\"\n  },\n  \"date\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\",\n    \"gte\": \"On tasan tai myöhempi kuin\",\n    \"gt\": \"On myöhempi kuin\",\n    \"lte\": \"On tasan tai aikaisempi kuin\",\n    \"lt\": \"On aikaisempi kuin\"\n  },\n  \"enums\": {\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\"\n  }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"checkAll\": \"Valitse kaikki\",\n  \"clear\": \"Tyhjennä\",\n  \"filter\": \"Suodatus\",\n  \"search\": \"Hae\"\n});\n}\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"actions\": {\n    \"addChild\": \"Lisää lapsi\",\n    \"append\": \"Lisää tehtävä\",\n    \"insertAfter\": \"Lisää alapuolelle\",\n    \"insertBefore\": \"Lisää yläpuolelle\",\n    \"pdf\": \"Vie PDF\"\n  },\n  \"cancel\": \"Peruuta\",\n  \"deleteDependencyWindowTitle\": \"Poista riippuvuus\",\n  \"deleteTaskWindowTitle\": \"Poista tehtävä\",\n  \"destroy\": \"Poista\",\n  \"editor\": {\n    \"assingButton\": \"Vastuuta\",\n    \"editorTitle\": \"Tehtävä\",\n    \"end\": \"Loppu\",\n    \"percentComplete\": \"Valmis\",\n    \"resources\": \"Resurssit\",\n    \"resourcesEditorTitle\": \"Resurssit\",\n    \"resourcesHeader\": \"Resurssit\",\n    \"start\": \"Alku\",\n    \"title\": \"Otsikko\",\n    \"unitsHeader\": \"Yksiköt\"\n  },\n  \"save\": \"Tallenna\",\n  \"views\": {\n    \"day\": \"Päivä\",\n    \"end\": \"Loppu\",\n    \"month\": \"Kuukausi\",\n    \"start\": \"Alku\",\n    \"week\": \"Viikko\",\n    \"year\": \"Vuosi\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"Peruuta muutokset\",\n    \"canceledit\": \"Peruuta\",\n    \"create\": \"Lisää uusi tietue\",\n    \"destroy\": \"Poista\",\n    \"edit\": \"Muokkaa\",\n    \"excel\": \"Vie Exceliin\",\n    \"pdf\": \"Vie PDF\",\n    \"save\": \"Tallenna muutokset\",\n    \"select\": \"Valitse\",\n    \"update\": \"Päivitä\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"Peruuta\",\n    \"confirmation\": \"Haluatko varmasti poistaa tietueen?\",\n    \"confirmDelete\": \"Poista\"\n  },\n  \"noRecords\": \"Ei tietueita.\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Vedä sarakeotsikko tähän ryhmitelläksesi sarakkeen perusteella\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Kasvata arvoa\",\n  \"downArrowText\": \"Vähennä arvoa\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Kaikki\",\n  \"display\": \"{0} - {1} yht. {2} tuloksesta\",\n  \"empty\": \"Ei tuloksia\",\n  \"page\": \"Sivu\",\n  \"of\": \"/ {0}\",\n  \"itemsPerPage\": \"tulosta sivulla\",\n  \"first\": \"Ensimmäinen sivu\",\n  \"previous\": \"Edellinen sivu\",\n  \"next\": \"Seuraava sivu\",\n  \"last\": \"Viimeinen sivu\",\n  \"refresh\": \"Päivitä\",\n  \"morePages\": \"Lisää sivuja\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"Pudota datakentät tähän\",\n  \"columnFields\": \"Pudota sarakkeet tähän\",\n  \"rowFields\": \"Pudota rivit tähän\"\n});\n}\n\n/* PivotFieldMenu messages */\n\nif (kendo.ui.PivotFieldMenu) {\nkendo.ui.PivotFieldMenu.prototype.options.messages =\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\n  \"info\": \"Näytä tulokset, joiden arvo:\",\n  \"filterFields\": \"Kenttien suodatus\",\n  \"filter\": \"Suodata\",\n  \"include\": \"Sisällytä kentät...\",\n  \"title\": \"Sisällytettävät kentät\",\n  \"clear\": \"Tyhjennä\",\n  \"ok\": \"Ok\",\n  \"cancel\": \"Peruuta\",\n  \"operators\": {\n    \"contains\": \"Sisältää\",\n    \"doesnotcontain\": \"Ei sisällä\",\n    \"startswith\": \"Alkaa\",\n    \"endswith\": \"Loppuu\",\n    \"eq\": \"On\",\n    \"neq\": \"Ei ole\"\n  }\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"Ei koskaan\",\n    \"hourly\": \"Joka tunti\",\n    \"daily\": \"Päivittäin\",\n    \"weekly\": \"Viikoittain\",\n    \"monthly\": \"Joka kuukausi\",\n    \"yearly\": \"Joka vuosi\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"Toista joka: \",\n    \"interval\": \" tunti\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"Toista joka: \",\n    \"interval\": \" päivä\"\n  },\n  \"weekly\": {\n    \"interval\": \" viikko\",\n    \"repeatEvery\": \"Toista joka: \",\n    \"repeatOn\": \"Toista tällöin: \"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"Toista joka: \",\n    \"repeatOn\": \"Toista tällöin: \",\n    \"interval\": \" kuukausi\",\n    \"day\": \"Päivä \"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"Toista joka: \",\n    \"repeatOn\": \"Toista tällöin: \",\n    \"interval\": \" vuosi\",\n    \"of\": \" kk: \"\n  },\n  \"end\": {\n    \"label\": \"Loppu:\",\n    \"mobileLabel\": \"Loppuu\",\n    \"never\": \"Ei koskaan\",\n    \"after\": \"Kun toistettu \",\n    \"occurrence\": \" kertaa\",\n    \"on\": \"Tarkka pvm \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"ensimmäinen\",\n    \"second\": \"toinen\",\n    \"third\": \"kolmas\",\n    \"fourth\": \"neljäs\",\n    \"last\": \"viimeinen\"\n  },\n  \"weekdays\": {\n    \"day\": \"päivä\",\n    \"weekday\": \"arkipäivä\",\n    \"weekend\": \"viikonloppu\"\n  }\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"koko päivä\",\n  \"date\": \"Päivämäärä\",\n  \"event\": \"Tapahtuma\",\n  \"time\": \"Kellonaika\",\n  \"showFullDay\": \"Näytä koko päivä\",\n  \"showWorkDay\": \"Näytä virka-aika\",\n  \"today\": \"Tänään\",\n  \"save\": \"Tallenna\",\n  \"cancel\": \"Peruuta\",\n  \"destroy\": \"Poista\",\n  \"deleteWindowTitle\": \"Poista tapahtuma\",\n  \"ariaSlotLabel\": \"Valittuna {0:t} - {1:t}\",\n  \"ariaEventLabel\": \"{0} {1:D} klo {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"Haluatko varmasti poistaa tapahtuman?\"\n  },\n  \"views\": {\n    \"day\": \"Päivä\",\n    \"week\": \"Viikko\",\n    \"workWeek\": \"Työviikko\",\n    \"agenda\": \"Agenda\",\n    \"month\": \"Kuukausi\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"Poista toistuva tapahtuma\",\n    \"deleteWindowOccurrence\": \"Poista vain tämä tapahtuma\",\n    \"deleteWindowSeries\": \"Poista kaikki tapahtumat\",\n    \"editWindowTitle\": \"Muokkaa toistuvaa tapahtumaa\",\n    \"editWindowOccurrence\": \"Muokkaa vain tätä tapahtumaa\",\n    \"editWindowSeries\": \"Muokkaa kaikkia tapahtumia\",\n    \"deleteRecurring\": \"Haluatko poistaa vain tämän tapahtuman, vai koko sarjan?\",\n    \"editRecurring\": \"Haluatko muokata vain tätä tapahtumaa, vai koko sarjaa?\"\n  },\n  \"editor\": {\n    \"title\": \"Otsikko\",\n    \"start\": \"Alku\",\n    \"end\": \"Loppu\",\n    \"allDayEvent\": \"Koko päivän tapahtuma\",\n    \"description\": \"Kuvaus\",\n    \"repeat\": \"Toistuminen\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"Alkamisen aikavyöhyke\",\n    \"endTimezone\": \"Loppumisen aikavyöhyke\",\n    \"separateTimezones\": \"Eri aikavyöhykkeet alkamiselle ja loppumiselle\",\n    \"timezoneEditorTitle\": \"Aikavyöhykkeet\",\n    \"timezoneEditorButton\": \"Aikavyöhyke\",\n    \"timezoneTitle\": \"Aikavyöhykkeet\",\n    \"noTimezone\": \"Ei aikavyöhykettä\",\n    \"editorTitle\": \"Tapahtuma\"\n  }\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\nkendo.spreadsheet.messages.borderPalette =\n$.extend(true, kendo.spreadsheet.messages.borderPalette,{\n  \"allBorders\": \"Kaikki reunat\",\n  \"insideBorders\": \"Sisäreunat\",\n  \"insideHorizontalBorders\": \"Vaakasuora sisäreuna\",\n  \"insideVerticalBorders\": \"Pystysuora sisäreuna\",\n  \"outsideBorders\": \"Ulkoreunat\",\n  \"leftBorder\": \"Vasen reuna\",\n  \"topBorder\": \"Yläreuna\",\n  \"rightBorder\": \"Oikea reuna\",\n  \"bottomBorder\": \"Alareuna\",\n  \"noBorders\": \"Ei reunaviivaa\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\nkendo.spreadsheet.messages.dialogs =\n$.extend(true, kendo.spreadsheet.messages.dialogs,{\n  \"apply\": \"Käytä\",\n  \"save\": \"Tallenna\",\n  \"cancel\": \"Peruuta\",\n  \"remove\": \"Poista\",\n  \"okText\": \"OK\",\n  \"formatCellsDialog\": {\n    \"title\": \"Muotoilu\",\n    \"categories\": {\n      \"number\": \"Numero\",\n      \"currency\": \"Valuutta\",\n      \"date\": \"Päivämäärä\"\n      }\n  },\n  \"fontFamilyDialog\": {\n    \"title\": \"Fontti\"\n  },\n  \"fontSizeDialog\": {\n    \"title\": \"Fonttikoko\"\n  },\n  \"bordersDialog\": {\n    \"title\": \"Reunaviivat\"\n  },\n  \"alignmentDialog\": {\n    \"title\": \"Asemointi\",\n    \"buttons\": {\n     \"justtifyLeft\": \"Tasaa vasemmalle\",\n     \"justifyCenter\": \"Keskitä\",\n     \"justifyRight\": \"Tasaa oikealle\",\n     \"justifyFull\": \"Tasaa molemmat reunat\",\n     \"alignTop\": \"Tasaa ylös\",\n     \"alignMiddle\": \"Tasaa keskelle\",\n     \"alignBottom\": \"Tasaa alas\"\n    }\n  },\n  \"mergeDialog\": {\n    \"title\": \"Yhdistä solut\",\n    \"buttons\": {\n      \"mergeCells\": \"Yhdistä kaikki\",\n      \"mergeHorizontally\": \"Yhdistä vaakasolut\",\n      \"mergeVertically\": \"Yhdistä pystysolut\",\n      \"unmerge\": \"Poista solujen yhdistäminen\"\n    }\n  },\n  \"freezeDialog\": {\n    \"title\": \"Kiinnitä ruudut\",\n    \"buttons\": {\n      \"freezePanes\": \"Kiinnitä ruudut\",\n      \"freezeRows\": \"Kiinnitä rivit\",\n      \"freezeColumns\": \"Kiinnitä sarakkeet\",\n      \"unfreeze\": \"Vapauta ruudut\"\n    }\n  },\n  \"validationDialog\": {\n    \"title\": \"Datan validointi\",\n    \"hintMessage\": \"Anna kelvollinen arvo tyyppiä \\\"{0}\\\" kohtaan {1}.\",\n    \"hintTitle\": \"Validointi {0}\",\n    \"criteria\": {\n      \"any\": \"Mikä tahansa\",\n      \"number\": \"Numero\",\n      \"text\": \"Teksti\",\n      \"date\": \"Päivämäärä\",\n      \"custom\": \"Mukautettu ehto\"\n    },\n    \"comparers\": {\n      \"greaterThan\": \"suurempi kuin\",\n      \"lessThan\": \"pienempi kuin\",\n      \"between\": \"väliltä\",\n      \"notBetween\": \"ei väliltä\",\n      \"equalTo\": \"tasan\",\n      \"notEqualTo\": \"eri kuin\",\n      \"greaterThanOrEqualTo\": \"tasan tai suurempi kuin\",\n      \"lessThanOrEqualTo\": \"tasan tai pienempi kuin\"\n    },\n    \"comparerMessages\": {\n      \"greaterThan\": \"suurempi kuin {0}\",\n      \"lessThan\": \"pienempi kuin {0}\",\n      \"between\": \"väliltä {0} ja {1}\",\n      \"notBetween\": \"ei väliltä {0} ja {1}\",\n      \"equalTo\": \"tasan {0}\",\n      \"notEqualTo\": \"eri kuin {0}\",\n      \"greaterThanOrEqualTo\": \"tasan tai suurempi kuin {0}\",\n      \"lessThanOrEqualTo\": \"tasan tai pienempi kuin {0}\",\n      \"custom\": \"joka täyttää ehdon: {0}\"\n    },\n    \"labels\": {\n      \"criteria\": \"Kriteeri\",\n      \"comparer\": \"Vertailu\",\n      \"min\": \"Min\",\n      \"max\": \"Max\",\n      \"value\": \"Arvo\",\n      \"start\": \"Alku\",\n      \"end\": \"Loppu\",\n      \"onInvalidData\": \"Epäkelvot arvot\",\n      \"rejectInput\": \"Hylkää arvo\",\n      \"showWarning\": \"Näytä varoitus\",\n      \"showHint\": \"Näytä vihje\",\n      \"hintTitle\": \"Vihjeen otsikko\",\n      \"hintMessage\": \"Vihjeteksti\"\n    },\n    \"placeholders\": {\n      \"typeTitle\": \"Tyypin otsikko\",\n      \"typeMessage\": \"Tyypin viesti\"\n    }\n  },\n  \"exportAsDialog\": {\n    \"title\": \"Tallenna nimellä...\",\n    \"labels\": {\n      \"fileName\": \"Tiedostonimi\",\n      \"saveAsType\": \"Tiedoston tyyppi\"\n    }\n  },\n  \"modifyMergedDialog\": {\n    \"errorMessage\": \"Yhdistetyn solun osaa ei voi muuttaa.\"\n  },\n  \"useKeyboardDialog\": {\n    \"title\": \"Kopiointi ja liittäminen\",\n    \"errorMessage\": \"Näitä toimintoja ei voi käyttää valikosta. Käytä näppäinoikoteitä:\",\n    \"labels\": {\n      \"forCopy\": \"kopioidaksesi\",\n      \"forCut\": \"leikataksesi\",\n      \"forPaste\": \"liittääksesi\"\n    }\n  },\n  \"unsupportedSelectionDialog\": {\n    \"errorMessage\": \"Toimintoa ei voi suorittaa useille kohteille.\"\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\nkendo.spreadsheet.messages.filterMenu =\n$.extend(true, kendo.spreadsheet.messages.filterMenu,{\n  \"sortAscending\": \"Lajittele A - Ö\",\n  \"sortDescending\": \"Lajittele Ö - A\",\n  \"filterByValue\": \"Suodata arvon perusteella\",\n  \"filterByCondition\": \"Suodata ehdon perusteella\",\n  \"apply\": \"Käytä\",\n  \"search\": \"Hae\",\n  \"clear\": \"Tyhjennä\",\n  \"blanks\": \"(Tyhjät)\",\n  \"operatorNone\": \"Ei mitään\",\n  \"and\": \"JA\",\n  \"or\": \"TAI\",\n  \"operators\": {\n    \"string\": {\n      \"contains\": \"Teksti sisältää\",\n      \"doesnotcontain\": \"Teksti ei sisällä\",\n      \"startswith\": \"Teksti alkaa\",\n      \"endswith\": \"Teksti loppuu\"\n    },\n    \"date\": {\n      \"eq\":  \"Päivämäärä on\",\n      \"neq\": \"Päivämäärä ei ole\",\n      \"lt\":  \"Päivämäärä on aiemmin kuin\",\n      \"gt\":  \"Päivämäärä on myöhemmin kuin\"\n    },\n    \"number\": {\n      \"eq\": \"On\",\n      \"neq\": \"Ei ole\",\n      \"gte\": \"On tasan tai suurempi kuin\",\n      \"gt\": \"On suurempi kuin\",\n      \"lte\": \"On tasan tai pienempi kuin\",\n      \"lt\": \"On pienempi kuin\"\n    }\n  }\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\nkendo.spreadsheet.messages.toolbar =\n$.extend(true, kendo.spreadsheet.messages.toolbar,{\n  \"addColumnLeft\": \"Lisää sarake vasemmalle\",\n  \"addColumnRight\": \"Lisää sarake vasemmalle\",\n  \"addRowAbove\": \"Lisää rivi yläpuolelle\",\n  \"addRowBelow\": \"Lisää rivi alapuolelle\",\n  \"alignment\": \"Alignment\",\n  \"alignmentButtons\": {\n    \"justtifyLeft\": \"Tasaa vasemmalle\",\n    \"justifyCenter\": \"Keskitä\",\n    \"justifyRight\": \"Tasaa oikealle\",\n    \"justifyFull\": \"Tasaa molemmat reunat\",\n    \"alignTop\": \"Tasaa ylös\",\n    \"alignMiddle\": \"Tasaa keskelle\",\n    \"alignBottom\": \"Tasaa alas\"\n  },\n  \"backgroundColor\": \"Taustaväri\",\n  \"bold\": \"Lihavoitu\",\n  \"borders\": \"Reunaviivat\",\n  \"copy\": \"Kopioi\",\n  \"cut\": \"Leikkaa\",\n  \"deleteColumn\": \"Poista sarake\",\n  \"deleteRow\": \"Poista rivi\",\n  \"excelExport\": \"Vie Exceliin...\",\n  \"filter\": \"Suodata\",\n  \"fontFamily\": \"Fontti\",\n  \"fontSize\": \"Fonttikoko\",\n  \"format\": \"Mukautettu muotoilu...\",\n  \"formatTypes\": {\n    \"automatic\": \"Automaattinen\",\n    \"number\": \"Numero\",\n    \"percent\": \"Prosenttimuoto\",\n    \"financial\": \"Kirjanpidon lukumuoto\",\n    \"currency\": \"Valuutta\",\n    \"date\": \"Päivämäärä\",\n    \"time\": \"Kellonaika\",\n    \"dateTime\": \"Pvm ja klo\",\n    \"duration\": \"Kesto\",\n    \"moreFormats\": \"Lisää muotoiluja...\"\n  },\n  \"formatDecreaseDecimal\": \"Vähennä desimaaleja\",\n  \"formatIncreaseDecimal\": \"Lisää desimaaleja\",\n  \"freeze\": \"Kiinnitä ruudut\",\n  \"freezeButtons\": {\n    \"freezePanes\": \"Kiinnitä ruudut\",\n    \"freezeRows\": \"Kiinnitä rivit\",\n    \"freezeColumns\": \"Kiinnitä sarakkeet\",\n    \"unfreeze\": \"Vapauta ruudut\"\n  },\n  \"italic\": \"Kursivoitu\",\n  \"merge\": \"Yhdistä solut\",\n  \"mergeButtons\": {\n    \"mergeCells\": \"Yhdistä kaikki\",\n    \"mergeHorizontally\": \"Yhdistä vaakasolu\",\n    \"mergeVertically\": \"Yhdistä pystysolut\",\n    \"unmerge\": \"Poista solujen yhdistäminen\"\n  },\n  \"open\": \"Avaa...\",\n  \"paste\": \"Liitä\",\n  \"quickAccess\": {\n    \"redo\": \"Tee uudelleen\",\n    \"undo\": \"Kumoa\"\n  },\n  \"saveAs\": \"Tallenna nimellä...\",\n  \"sortAsc\": \"Nouseva järjestys\",\n  \"sortDesc\": \"Laskeva järjestys\",\n  \"sortButtons\": {\n    \"sortSheetAsc\": \"Järjestä työkirja A - Ö\",\n    \"sortSheetDesc\": \"Järjestä työkirja Ö - A\",\n    \"sortRangeAsc\": \"Järjestä valinta A - Ö\",\n    \"sortRangeDesc\": \"Järjestä valinta Ö - A\"\n  },\n  \"textColor\": \"Tekstin väri\",\n  \"textWrap\": \"Tekstin rivitys\",\n  \"underline\": \"Alleviivattu\",\n  \"validation\": \"Datan validointi...\"\n});\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\nkendo.spreadsheet.messages.view =\n$.extend(true, kendo.spreadsheet.messages.view,{\n  \"errors\": {\n    \"shiftingNonblankCells\": \"Soluja ei voi lisätä datahävikin vuoksi. Valitse toinen lisäyskohta tai poista tietoja työkirjan lopusta.\",\n    \"filterRangeContainingMerges\": \"Suodatinta ei voi asettaa yhdistettyyn sisältöön.\"\n  },\n  \"tabs\": {\n    \"home\": \"Aloitus\",\n    \"insert\": \"Lisää\",\n    \"data\": \"Data\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"Lisää\",\n  \"decreaseButtonTitle\": \"Vähennä\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages,{\n  \"noRows\": \"Ei tuloksia\",\n  \"loading\": \"Ladataan...\",\n  \"requestFailed\": \"Sivupyyntö epäonnistui.\",\n  \"retry\": \"Yritä uudelleen\",\n  \"commands\": {\n      \"edit\": \"Muokkaa\",\n      \"update\": \"Päivitä\",\n      \"canceledit\": \"Peruuta\",\n      \"create\": \"Lisää uusi tietue\",\n      \"createchild\": \"Lisää lapsitietue\",\n      \"destroy\": \"Poista\",\n      \"excel\": \"Vie Exceliin\",\n      \"pdf\": \"Vie PDF\"\n  }\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"Ladataan...\",\n  \"requestFailed\": \"Sivupyyntö epäonnistui.\",\n  \"retry\": \"Yritä uudelleen\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization=\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"Valitse tiedostoja...\",\n  \"cancel\": \"Peruuta\",\n  \"retry\": \"Yritä uudelleen\",\n  \"remove\": \"Poista\",\n  \"uploadSelectedFiles\": \"Lataa tiedostot palvelimeen\",\n  \"dropFilesHere\": \"pudota tiedostot tähän ladataksesi palvelimeen\",\n  \"statusUploading\": \"ladataan\",\n  \"statusUploaded\": \"ladattu\",\n  \"statusWarning\": \"varoitus\",\n  \"statusFailed\": \"epäonnistui\",\n  \"headerStatusUploading\": \"Ladataan...\",\n  \"headerStatusUploaded\": \"Valmis\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} vaaditaan\",\n  \"pattern\": \"{0} ei kelpaa\",\n  \"min\": \"{0} on oltava tasan tai suurempi kuin {1}\",\n  \"max\": \"{0} on oltava tasan tai pienempi kuin {1}\",\n  \"step\": \"{0} ei kelpaa\",\n  \"email\": \"{0} ei ole kelvollinen sähköpostiosoite\",\n  \"url\": \"{0} ei ole kelvollinen URL\",\n  \"date\": \"{0} ei ole kelvollinen päivämäärä\",\n  \"dateCompare\": \"Loppupäivämäärän on oltava sama tai myöhäisempi kuin alkupäivämäärä\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Sulkea\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Peruuta\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Peruuta\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}