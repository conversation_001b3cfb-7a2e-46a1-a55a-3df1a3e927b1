{"version": 3, "sources": ["messages/kendo.messages.el-GR.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "noColor", "clearColor", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "cleanFormatting", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "isnull", "isnotnull", "isempty", "isnotempty", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "checkAll", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "MediaPlayer", "pause", "play", "mute", "unmute", "quality", "fullscreen", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "remove", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "confirmationDialog", "text", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "min", "max", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "columnMenu", "TreeView", "Upload", "localization", "clearSelectedFiles", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "invalidMaxFileSize", "invalidMinFileSize", "invalidFileExtension", "Validator", "required", "pattern", "step", "email", "url", "dateCompare", "progress", "Dialog", "close", "<PERSON><PERSON>", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGRC,MAAMC,GAAGC,kBACXF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SACzCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,WACTC,OAAU,QACVC,QAAW,cACXC,WAAc,kBAMhBV,MAAMC,GAAGU,cACXX,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGU,YAAYR,UAAUC,QAAQC,UACpDE,MAAS,WACTC,OAAU,QACVC,QAAW,cACXC,WAAc,kBAMhBV,MAAMC,GAAGW,aACXZ,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGW,WAAWT,UAAUC,QAAQC,UACnDQ,cAAiB,qBACjBC,eAAkB,sBAClBC,OAAU,SACVC,QAAW,SACXC,KAAQ,WACRC,SAAY,kBACZC,KAAQ,WACRC,OAAU,gBAMZpB,MAAMC,GAAGoB,SACXrB,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,OAAOlB,UAAUC,QAAQC,UAC/CiB,KAAQ,SACRC,OAAU,SACVC,UAAa,cACbC,cAAiB,oBACjBC,YAAe,UACfC,UAAa,UACbC,cAAiB,kBACjBC,YAAe,oBACfC,aAAgB,iBAChBC,YAAe,UACfC,oBAAuB,oCACvBC,kBAAqB,iCACrBC,OAAU,QACVC,QAAW,WACXC,WAAc,qBACdC,OAAU,qBACVC,YAAe,mBACfC,WAAc,mBACdC,WAAc,gBACdC,SAAY,kBACZC,SAAY,qCACZC,gBAAmB,gCACnBC,SAAY,kCACZC,gBAAmB,0BACnBC,YAAe,cACfC,WAAc,cACdC,UAAa,QACbC,UAAa,eACbC,MAAS,OACTC,YAAe,gBACfC,WAAc,cACdC,QAAW,iBACXC,YAAe,UACfC,YAAe,QACfC,gBAAmB,mFACnBC,WAAc,oDACdC,cAAiB,+FACjBC,kBAAqB,oCACrBC,gBAAmB,wBACnBC,aAAgB,sBAChBC,WAAc,cACdC,YAAe,YACfC,eAAkB,wBAClBC,UAAa,SACbC,eAAkB,wBAClBC,SAAY,UACZC,YAAe,UACfC,oBAAuB,oCACvBC,aAAgB,WAChBC,aAAgB,WAChBC,sBAAyB,IACzBC,aAAgB,QAChBC,gBAAmB,2BACnBC,YAAe,oBACfC,cAAiB,2BACjBC,eAAkB,wBAClBC,YAAe,wBACfC,YAAe,wBACfC,UAAa,mBACbC,aAAgB,kBAChBC,SAAY,UACZC,YAAe,gBACfC,SAAY,UACZC,QAAW,OACXC,iBAAoB,iBACpBC,QAAW,UACXC,QAAW,WACXC,MAAS,SACTC,OAAU,OACVC,YAAe,cACfC,YAAe,kBACfC,WAAc,mBACdC,UAAa,eACbC,WAAc,QACdC,SAAY,YACZC,GAAM,KACNC,OAAU,YACVC,YAAe,kBACfC,gBAAmB,mBACnBC,SAAY,sBACZC,0BAA6B,iCAC7BC,UAAa,wBACbC,YAAe,sBACfC,WAAc,qBACdC,aAAgB,6BAChBC,eAAkB,2BAClBC,cAAiB,0BACjBC,gBAAmB,6BACnBC,kBAAqB,2BACrBC,iBAAoB,0BACpBC,gBAAmB,6BACnBC,kBAAqB,2BACrBC,iBAAoB,0BACpBC,YAAe,yBACfnG,QAAW,SACXoG,KAAQ,UACRC,eAAkB,6BAMpBrH,MAAMC,GAAGqH,cACXtH,MAAMC,GAAGqH,YAAYnH,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqH,YAAYnH,UAAUC,QAAQC,UACpD+C,WAAc,cACdC,QAAW,gBACXE,YAAe,QACfD,YAAe,UACfK,kBAAqB,wCACrBR,YAAe,gBACfM,WAAc,oDACdD,gBAAmB,wFACnBE,cAAiB,wFACjB6D,cAAiB,kCACjBC,OAAU,eAMZxH,MAAMC,GAAGwH,aACXzH,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQC,UACnDqH,OAAU,eACVC,QAAW,eACX5G,OAAU,SACV6G,MAAS,aACTC,SAAY,eAMd7H,MAAMC,GAAGwH,aACXzH,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQ0H,UACpChI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwH,WAAWtH,UAAUC,QAAQ0H,WACnDC,QACEC,GAAM,eACNC,IAAO,mBACPC,WAAc,YACdC,SAAY,WACZC,eAAkB,eAClBC,SAAY,eACZC,OAAU,aACVC,UAAa,iBACbC,QAAW,aACXC,WAAc,kBAEhBC,QACEV,GAAM,eACNC,IAAO,mBACPU,IAAO,4BACPC,GAAM,uBACNC,IAAO,2BACPC,GAAM,sBACNR,OAAU,aACVC,UAAa,kBAEfQ,MACEf,GAAM,eACNC,IAAO,mBACPU,IAAO,sBACPC,GAAM,aACNC,IAAO,sBACPC,GAAM,aACNR,OAAU,aACVC,UAAa,kBAEfS,OACEhB,GAAM,eACNC,IAAO,mBACPK,OAAU,aACVC,UAAa,qBAOjBvI,MAAMC,GAAGgJ,aACXjJ,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQC,UACnD6I,KAAQ,iCACRxB,OAAU,eACVC,QAAW,eACX5G,OAAU,SACV6G,MAAS,aACTuB,IAAO,MACPC,GAAM,IACNC,YAAe,kBACfxB,SAAY,UACZyB,MAAS,OACT9I,OAAU,WAMZR,MAAMC,GAAGgJ,aACXjJ,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQ0H,UACpChI,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgJ,WAAW9I,UAAUC,QAAQ0H,WACnDC,QACEC,GAAM,eACNC,IAAO,mBACPC,WAAc,YACdC,SAAY,WACZC,eAAkB,eAClBC,SAAY,eACZC,OAAU,aACVC,UAAa,iBACbC,QAAW,aACXC,WAAc,kBAEhBC,QACEV,GAAM,eACNC,IAAO,mBACPU,IAAO,4BACPC,GAAM,uBACNC,IAAO,2BACPC,GAAM,sBACNR,OAAU,aACVC,UAAa,kBAEfQ,MACEf,GAAM,eACNC,IAAO,mBACPU,IAAO,sBACPC,GAAM,aACNC,IAAO,sBACPC,GAAM,aACNR,OAAU,aACVC,UAAa,kBAEfS,OACEhB,GAAM,eACNC,IAAO,mBACPK,OAAU,aACVC,UAAa,qBAOjBvI,MAAMC,GAAGsJ,mBACXvJ,MAAMC,GAAGsJ,iBAAiBpJ,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsJ,iBAAiBpJ,UAAUC,QAAQC,UACzDmJ,SAAY,eACZ5B,MAAS,aACT7G,OAAU,SACVyG,OAAU,eAMZxH,MAAMC,GAAGwJ,QACXzJ,MAAMC,GAAGwJ,MAAMtJ,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwJ,MAAMtJ,UAAUC,QAAQC,UAC9CqJ,SACEC,SAAY,mBACZC,OAAU,oBACVC,YAAe,gBACfC,aAAgB,gBAChBC,IAAO,kBAETvJ,OAAU,QACVwJ,4BAA+B,qBAC/BC,sBAAyB,oBACzBC,QAAW,WACXC,QACEC,aAAgB,UAChBC,YAAe,UACfC,IAAO,QACPC,gBAAmB,aACnBC,UAAa,QACbC,qBAAwB,QACxBC,gBAAmB,QACnBC,MAAS,OACTC,MAAS,SACTC,YAAe,WAEjBC,KAAQ,aACRC,OACEC,IAAO,QACPV,IAAO,QACPW,MAAS,QACTN,MAAS,OACTO,KAAQ,WACRC,KAAQ,aAOZnL,MAAMC,GAAGmL,OACXpL,MAAMC,GAAGmL,KAAKjL,UAAUC,QAAQC,SAC9BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmL,KAAKjL,UAAUC,QAAQC,UAC7CgL,UACE7K,OAAU,kBACV8K,WAAc,UACdC,OAAU,oBACVrB,QAAW,WACXsB,KAAQ,cACRC,MAAS,mBACT1B,IAAO,iBACPe,KAAQ,qBACRY,OAAU,UACVC,OAAU,YAEZC,UACEC,aAAgB,QAChBC,aAAgB,4DAChBC,cAAiB,YAEnBC,UAAa,4BAMfhM,MAAMC,GAAGgM,WACXjM,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,UACjD6L,OAAU,wBACVC,QAAW,aACXC,cAAiB,oBACjBC,MAAS,gBACThB,UACEG,KAAQ,cACRG,OAAU,WACVL,WAAc,QACdC,OAAU,oBACVe,YAAe,4BACfpC,QAAW,WACXuB,MAAS,mBACT1B,IAAO,qBAOX/J,MAAMC,GAAGsM,YACXvM,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQC,UAClDmM,MAAS,iEAMXxM,MAAMC,GAAGwM,iBACXzM,MAAMC,GAAGwM,eAAetM,UAAUC,QAChCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,eAAetM,UAAUC,SAC/CsM,YAAe,eACfC,cAAiB,kBAMnB3M,MAAMC,GAAG2M,cACX5M,MAAMC,GAAG2M,YAAYzM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,YAAYzM,UAAUC,QAAQC,UACpDwM,MAAS,QACTC,KAAQ,WACRC,KAAQ,SACRC,OAAU,oBACVC,QAAW,WACXC,WAAc,kBAMhBlN,MAAMC,GAAGkN,QACXnN,MAAMC,GAAGkN,MAAMhN,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkN,MAAMhN,UAAUC,QAAQC,UAC9C+M,SAAY,MACZC,QAAW,gCACXb,MAAS,2BACTc,KAAQ,SACRC,GAAM,UACNC,aAAgB,yBAChBC,MAAS,6BACTC,SAAY,mCACZC,KAAQ,+BACRC,KAAQ,iCACRC,QAAW,WACXC,UAAa,0BAMf9N,MAAMC,GAAG8N,YACX/N,MAAMC,GAAG8N,UAAU5N,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8N,UAAU5N,UAAUC,QAAQC,UAClD2N,cAAiB,mBACjBC,aAAgB,mBAChBC,UAAa,wBAMflO,MAAMC,GAAGkO,iBACXnO,MAAMC,GAAGkO,eAAehO,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkO,eAAehO,UAAUC,QAAQC,UACvD6I,KAAQ,iCACRkF,aAAgB,gBAChBrN,OAAU,SACVsN,QAAW,wBACXzD,MAAS,8BACThD,MAAS,aACT0G,GAAM,UACN9N,OAAU,QACVsH,WACEK,SAAY,WACZC,eAAkB,eAClBF,WAAc,YACdG,SAAY,eACZL,GAAM,eACNC,IAAO,uBAOXjI,MAAMC,GAAGsO,mBACXvO,MAAMC,GAAGsO,iBAAiBpO,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsO,iBAAiBpO,UAAUC,QAAQC,UACzDmO,aACEC,MAAS,OACTC,OAAU,SACVC,MAAS,WACTC,OAAU,cACVC,QAAW,UACXC,OAAU,UAEZJ,QACEK,YAAe,mBACfC,SAAY,YAEdL,OACEI,YAAe,mBACfC,SAAY,aAEdJ,QACEI,SAAY,gBACZD,YAAe,mBACfE,SAAY,mBAEdJ,SACEE,YAAe,mBACfE,SAAY,kBACZD,SAAY,YACZhE,IAAO,UAET8D,QACEC,YAAe,mBACfE,SAAY,iBACZD,SAAY,aACZzB,GAAM,SAERjD,KACE4E,MAAS,SACTC,YAAe,YACfV,MAAS,OACTW,MAAS,QACTC,WAAc,iBACdC,GAAM,QAERC,iBACE9B,MAAS,QACT+B,OAAU,UACVC,MAAS,QACTC,OAAU,SACV9B,KAAQ,aAEV+B,UACE3E,IAAO,QACP4E,QAAW,aACXC,QAAW,qBAOf7P,MAAMC,GAAG6P,YACX9P,MAAMC,GAAG6P,UAAU3P,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6P,UAAU3P,UAAUC,QAAQC,UAClD0P,OAAU,WACVhH,KAAQ,aACRiH,MAAS,UACTC,KAAQ,MACRC,YAAe,uBACfC,YAAe,uBACfC,MAAS,SACTtF,KAAQ,aACRtK,OAAU,QACV0J,QAAW,WACXmG,kBAAqB,qBACrBC,cAAiB,8BACjBC,eAAkB,0BAClB3E,UACEE,aAAgB,uDAElBf,OACEC,IAAO,QACPE,KAAQ,WACRsF,SAAY,oBACZC,OAAU,UACVxF,MAAS,SAEXyF,oBACEL,kBAAqB,0CACrBM,uBAA0B,+BAC1BC,mBAAsB,sBACtBC,gBAAmB,6CACnBC,qBAAwB,kCACxBC,iBAAoB,yBACpBC,gBAAmB,iEACnBC,cAAiB,uEAEnB9G,QACES,MAAS,SACTD,MAAS,OACTL,IAAO,QACP4G,YAAe,mBACfC,YAAe,YACfC,OAAU,YACVC,SAAY,IACZC,cAAiB,qBACjBC,YAAe,kBACfC,kBAAqB,0CACrBC,oBAAuB,aACvBC,qBAAwB,YACxBC,cAAiB,aACjBC,WAAc,kBACdvH,YAAe,cAOnBrK,MAAM6R,aAAe7R,MAAM6R,YAAYxR,SAASyR,gBAClD9R,MAAM6R,YAAYxR,SAASyR,cACzBhS,EAAEQ,QAAO,EAAMN,MAAM6R,YAAYxR,SAASyR,eACxCC,WAAc,gBACdC,cAAiB,mBACjBC,wBAA2B,6BAC3BC,sBAAyB,0BACzBC,eAAkB,mBAClBC,WAAc,kBACdC,UAAa,cACbC,YAAe,eACfC,aAAgB,cAChBC,UAAa,eACbC,MAAS,qBACTC,YAAe,yBACfnS,MAAS,WACTC,OAAU,WAIZR,MAAM6R,aAAe7R,MAAM6R,YAAYxR,SAASsS,UAClD3S,MAAM6R,YAAYxR,SAASsS,QACzB7S,EAAEQ,QAAO,EAAMN,MAAM6R,YAAYxR,SAASsS,SACxCpS,MAAS,WACTuK,KAAQ,aACRtK,OAAU,QACVoS,OAAU,WACVvG,MAAS,kBACTwG,OAAU,cACVC,OAAU,UACVC,mBACEnI,MAAS,QACToI,YACEtK,OAAU,QACVuK,SAAY,UACZlK,KAAQ,eAGZmK,kBACEtI,MAAS,iBAEXuI,gBACEvI,MAAS,0BAEXwI,eACExI,MAAS,UAEXyI,iBACEzI,MAAS,WACT0I,SACEC,aAAgB,oBAChB3R,cAAiB,SACjBE,aAAgB,iBAChBC,YAAe,UACfyR,SAAY,gBACZC,YAAe,gBACfC,YAAe,kBAGnBC,aACE/I,MAAS,kBACT0I,SACEM,WAAc,gBACdC,kBAAqB,qBACrBC,gBAAmB,kBACnBC,QAAW,0BAGfC,cACEpJ,MAAS,mBACT0I,SACEW,YAAe,mBACfC,WAAc,gBACdC,cAAiB,gBACjBC,SAAY,uBAGhBC,oBACEC,KAAQ,yDACR1J,MAAS,mBAEX2J,kBACE3J,MAAS,sBACT4J,YAAe,8CACfC,UAAa,gBACbC,UACEC,IAAO,mBACPjM,OAAU,UACV4L,KAAQ,UACRvL,KAAQ,aACR6L,OAAU,sBACVC,KAAQ,SAEVC,WACEC,YAAe,iBACfC,SAAY,gBACZC,QAAW,SACXC,WAAc,aACdC,QAAW,SACXC,WAAc,aACdC,qBAAwB,sBACxBC,kBAAqB,sBAEvBC,kBACER,YAAe,qBACfC,SAAY,oBACZC,QAAW,qBACXC,WAAc,yBACdC,QAAW,aACXC,WAAc,iBACdC,qBAAwB,0BACxBC,kBAAqB,yBACrBV,OAAU,gCAEZY,QACEd,SAAY,WACZe,SAAY,YACZC,IAAO,WACPC,IAAO,UACPrM,MAAS,OACTqB,MAAS,OACTL,IAAO,QACPsL,cAAiB,wBACjBC,YAAe,qBACfC,YAAe,sBACfC,SAAY,gBACZtB,UAAa,oBACbD,YAAe,mBACfwB,YAAe,gBAEjBC,cACEC,UAAa,eACbC,YAAe,oBAGnBC,gBACExL,MAAS,aACT4K,QACEa,SAAY,gBACZC,WAAc,sBACdC,WAAc,UACdC,UAAa,kBACbC,QAAW,YACXC,YAAe,cACfC,MAAS,WACTC,WAAc,qBACdC,OAAU,SACVC,aAAgB,YAChBC,WAAc,WAGlBC,oBACEC,aAAgB,2DAElBC,mBACEtM,MAAS,2BACTqM,aAAgB,gHAChBzB,QACE2B,QAAW,gBACXC,OAAU,cACVC,SAAY,mBAGhBC,4BACEL,aAAgB,4DAKpBjX,MAAM6R,aAAe7R,MAAM6R,YAAYxR,SAASkX,aAClDvX,MAAM6R,YAAYxR,SAASkX,WACzBzX,EAAEQ,QAAO,EAAMN,MAAM6R,YAAYxR,SAASkX,YACxC1W,cAAiB,0BACjBC,eAAkB,0BAClB0W,cAAiB,iBACjBC,kBAAqB,oBACrBlX,MAAS,WACTiH,OAAU,YACVkQ,aAAgB,+BAChB9P,MAAS,aACT+P,OAAU,SACVC,aAAgB,SAChBzO,IAAO,MACPC,GAAM,IACNtB,WACEC,QACEI,SAAY,mBACZC,eAAkB,uBAClBF,WAAc,qBACdG,SAAY,wBAEdU,MACEf,GAAM,mBACNC,IAAO,uBACPa,GAAM,4BACNF,GAAM,6BAERF,QACEV,GAAM,eACNC,IAAO,mBACPU,IAAO,6BACPC,GAAM,uBACNC,IAAO,4BACPC,GAAM,2BAMZ9I,MAAM6R,aAAe7R,MAAM6R,YAAYxR,SAASwX,UAClD7X,MAAM6R,YAAYxR,SAASwX,QACzB/X,EAAEQ,QAAO,EAAMN,MAAM6R,YAAYxR,SAASwX,SACxCjT,cAAiB,4BACjBC,eAAkB,yBAClBC,YAAe,wBACfC,YAAe,wBACfe,UAAa,eACbgS,kBACEvE,aAAgB,wBAChB3R,cAAiB,SACjBE,aAAgB,qBAChBC,YAAe,UACfyR,SAAY,oBACZC,YAAe,oBACfC,YAAe,qBAEjBqE,gBAAmB,QACnBzW,KAAQ,SACR0W,QAAW,SACXC,aACExF,MAAS,qBACTC,YAAe,2BAEjBwF,KAAQ,YACRC,IAAO,UACPlT,aAAgB,kBAChBD,UAAa,mBACboT,YAAe,wBACfrX,OAAU,SACVsX,WAAc,gBACdzV,SAAY,yBACZ0V,OAAU,2BACVC,aACEC,UAAa,WACb9P,OAAU,UACV+P,QAAW,UACXC,UAAa,aACbzF,SAAY,SACZlK,KAAQ,aACRkH,KAAQ,MACR0I,SAAY,iBACZC,SAAY,WACZC,YAAe,0BAEjBC,sBAAyB,mBACzBC,sBAAyB,mBACzBC,OAAU,mBACVC,eACEhF,YAAe,mBACfC,WAAc,iBACdC,cAAiB,gBACjBC,SAAY,4BAEd7S,OAAU,SACV2X,MAAS,kBACTC,cACEvF,WAAc,gBACdC,kBAAqB,qBACrBC,gBAAmB,kBACnBC,QAAW,qBAEbqF,KAAQ,YACRC,MAAS,aACTC,aACEC,KAAQ,oBACRC,KAAQ,YAEVC,OAAU,mBACVC,QAAW,qBACXC,SAAY,sBACZC,aACEC,aAAgB,2BAChBC,cAAiB,2BACjBC,aAAgB,2BAChBC,cAAiB,4BAEnBC,UAAa,iBACbC,SAAY,sBACZ1Y,UAAa,cACb2Y,WAAc,4BAIhBna,MAAM6R,aAAe7R,MAAM6R,YAAYxR,SAAS+Z,OAClDpa,MAAM6R,YAAYxR,SAAS+Z,KACzBta,EAAEQ,QAAO,EAAMN,MAAM6R,YAAYxR,SAAS+Z,MACxCC,QACEC,sBAAyB,kJACzBC,4BAA+B,0DAC/BC,gBAAmB,sEAErBC,MACEC,KAAQ,SACRC,OAAU,WACVC,KAAQ,eAOZ5a,MAAMC,GAAG4a,SACX7a,MAAMC,GAAG4a,OAAO1a,UAAUC,QACxBN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4a,OAAO1a,UAAUC,SACvC0a,oBAAuB,SACvBC,oBAAuB,YAMzB/a,MAAMC,GAAGgM,WACXjM,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQC,UACjD6L,OAAU,wBACVC,QAAW,cACXC,cAAiB,kBACjBC,MAAS,YACThB,UACEG,KAAQ,cACRG,OAAU,WACVL,WAAc,QACdC,OAAU,oBACVe,YAAe,4BACfpC,QAAW,WACXuB,MAAS,mBACT1B,IAAO,qBAKX/J,MAAMC,GAAGgM,WACXjM,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQ4a,WAClClb,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgM,SAAS9L,UAAUC,QAAQ4a,YACjD3a,UACEW,QAAW,kBACXD,OAAU,mBACVF,cAAiB,oBACjBC,eAAkB,yBAOtBd,MAAMC,GAAGgb,WACXjb,MAAMC,GAAGgb,SAAS9a,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgb,SAAS9a,UAAUC,QAAQC,UACjD8L,QAAW,cACXC,cAAiB,kBACjBC,MAAS,eAMXrM,MAAMC,GAAGib,SACXlb,MAAMC,GAAGib,OAAO/a,UAAUC,QAAQ+a,aAChCrb,EAAEQ,QAAO,EAAMN,MAAMC,GAAGib,OAAO/a,UAAUC,QAAQ+a,cAC/CzP,OAAU,qBACVlL,OAAU,QACV6L,MAAS,YACTuG,OAAU,WACVwI,mBAAsB,aACtBC,oBAAuB,sBACvB9T,cAAiB,mCACjB+T,gBAAmB,cACnBC,eAAkB,UAClBC,cAAiB,gBACjBC,aAAgB,UAChBC,sBAAyB,iBACzBC,qBAAwB,aACxBC,mBAAsB,4CACtBC,mBAAsB,2CACtBC,qBAAwB,+BAM1B9b,MAAMC,GAAG8b,YACX/b,MAAMC,GAAG8b,UAAU5b,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8b,UAAU5b,UAAUC,QAAQC,UAClD2b,SAAY,wBACZC,QAAW,uBACXvG,IAAO,kDACPC,IAAO,kDACPuG,KAAQ,uBACRC,MAAS,6BACTC,IAAO,2BACPrT,KAAQ,kCACRsT,YAAe,6EAKjBrc,MAAMC,GAAGqc,WACXtc,MAAMC,GAAGqc,SAASjc,SAChBP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqc,SAASjc,UAC/B8L,QAAS,gBAMXnM,MAAMC,GAAGsc,SACXvc,MAAMC,GAAGsc,OAAOpc,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsc,OAAOpc,UAAUC,QAAQ+a,cAC/CqB,MAAS,cAMXxc,MAAMC,GAAGwc,QACXzc,MAAMC,GAAGwc,MAAMtc,UAAUC,QAAQC,SAC/BP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwc,MAAMtc,UAAUC,QAAQ+a,cAC9CrI,OAAU,aAMZ9S,MAAMC,GAAGyc,UACX1c,MAAMC,GAAGyc,QAAQvc,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyc,QAAQvc,UAAUC,QAAQ+a,cAChDrI,OAAU,UACVtS,OAAU,WAKZR,MAAMC,GAAG0c,SACX3c,MAAMC,GAAG0c,OAAOxc,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0c,OAAOxc,UAAUC,QAAQ+a,cAC/CrI,OAAU,UACVtS,OAAU,YAIfoc,OAAO5c,MAAM6c", "file": "kendo.messages.el-GR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\r\n  /* FlatColorPicker messages */\r\n\r\n  if (kendo.ui.FlatColorPicker) {\r\n    kendo.ui.FlatColorPicker.prototype.options.messages =\r\n      $.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages, {\r\n        \"apply\": \"Εφαρμογή\",\r\n        \"cancel\": \"Άκυρο\",\r\n        \"noColor\": \"Χωρίς Χρώμα\",\r\n        \"clearColor\": \"Καθαρό Χρώμα\"\r\n      });\r\n  }\r\n\r\n  /* ColorPicker messages */\r\n\r\n  if (kendo.ui.ColorPicker) {\r\n    kendo.ui.ColorPicker.prototype.options.messages =\r\n      $.extend(true, kendo.ui.ColorPicker.prototype.options.messages, {\r\n        \"apply\": \"Εφαρμογή\",\r\n        \"cancel\": \"Άκυρο\",\r\n        \"noColor\": \"Χω<PERSON><PERSON>ς Χρώμα\",\r\n        \"clearColor\": \"Καθαρό Χρώμα\"\r\n      });\r\n  }\r\n\r\n  /* ColumnMenu messages */\r\n\r\n  if (kendo.ui.ColumnMenu) {\r\n    kendo.ui.ColumnMenu.prototype.options.messages =\r\n      $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\r\n        \"sortAscending\": \"Αύξουσα Ταξινόμηση\",\r\n        \"sortDescending\": \"Φθίνουσα Ταξινόμηση\",\r\n        \"filter\": \"Φίλτρο\",\r\n        \"columns\": \"Στήλες\",\r\n        \"done\": \"Εφαρμογή\",\r\n        \"settings\": \"Στήλη Ρυθμίσεων\",\r\n        \"lock\": \"Κλείδωμα\",\r\n        \"unlock\": \"Ξεκλείδωμα\"\r\n      });\r\n  }\r\n\r\n  /* Editor messages */\r\n\r\n  if (kendo.ui.Editor) {\r\n    kendo.ui.Editor.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Editor.prototype.options.messages, {\r\n        \"bold\": \"Έντονα\",\r\n        \"italic\": \"Πλάγια\",\r\n        \"underline\": \"Υπογράμμιση\",\r\n        \"strikethrough\": \"Διακριτή Διαγραφή\",\r\n        \"superscript\": \"Εκθέτης\",\r\n        \"subscript\": \"Δείκτης\",\r\n        \"justifyCenter\": \"Στοίχιση Κέντρο\",\r\n        \"justifyLeft\": \"Στοίχιση Αριστερά\",\r\n        \"justifyRight\": \"Στοίχιση Δεξιά\",\r\n        \"justifyFull\": \"Justify\",\r\n        \"insertUnorderedList\": \"Τοποθετήστε μη διατεταγμένη λίστα\",\r\n        \"insertOrderedList\": \"Τοποθετήστε διατεταγμένη λίστα\",\r\n        \"indent\": \"Εσοχή\",\r\n        \"outdent\": \"Προεξοχή\",\r\n        \"createLink\": \"Εισαγωγή Συνδέσμου\",\r\n        \"unlink\": \"Αφαίρεση Συνδέσμου\",\r\n        \"insertImage\": \"Εισαγωγή Εικόνας\",\r\n        \"insertFile\": \"Εισαγωγή Αρχείου\",\r\n        \"insertHtml\": \"Εισαγωγή HTML\",\r\n        \"viewHtml\": \"Επισκόπηση HTML\",\r\n        \"fontName\": \"Επιλογή Οικογένειας Γραμματοσειράς\",\r\n        \"fontNameInherit\": \"(Κληρονομημένη Γραμματοσειρά)\",\r\n        \"fontSize\": \"Επιλογή Μεγέθους Γραμματοσειράς\",\r\n        \"fontSizeInherit\": \"(Κληρονομημένη Μέγεθος)\",\r\n        \"formatBlock\": \"Μορφοποίηση\",\r\n        \"formatting\": \"Μορφοποίηση\",\r\n        \"foreColor\": \"Χρώμα\",\r\n        \"backColor\": \"Χρώμα Φόντου\",\r\n        \"style\": \"Στυλ\",\r\n        \"emptyFolder\": \"Κενός Φάκελος\",\r\n        \"uploadFile\": \"Μεταφόρτωση\",\r\n        \"orderBy\": \"Διευθέτηση με:\",\r\n        \"orderBySize\": \"Μέγεθος\",\r\n        \"orderByName\": \"Όνομα\",\r\n        \"invalidFileType\": \"Το επιλεγμένο αρχείο \\\"{0}\\\" δεν είναι έγκυρο. Υποστηριζόμενοι τύποι είναι οι {1}.\",\r\n        \"deleteFile\": 'Είστε σίγουροι ότι θέλετε να διαγράψετε το \"{0}\"?',\r\n        \"overwriteFile\": 'Το αρχείο με όνομα \"{0}\" υπάρχει ήδη στον συγκεκριμένο φάκελο. Θέλετε να το αντικαταστήσετε?',\r\n        \"directoryNotFound\": \"Το όνομα του φακέλου δεν βρέθηκε.\",\r\n        \"imageWebAddress\": \"Ηλεκτρονική Διεύθυνση\",\r\n        \"imageAltText\": \"Εναλλακτικό Κείμενο\",\r\n        \"imageWidth\": \"Πλάτος (px)\",\r\n        \"imageHeight\": \"Ύψος (px)\",\r\n        \"fileWebAddress\": \"Ηλεκτρονική Διεύθυνση\",\r\n        \"fileTitle\": \"Τίτλος\",\r\n        \"linkWebAddress\": \"Ηλεκτρονική Διεύθυνση\",\r\n        \"linkText\": \"Κείμενο\",\r\n        \"linkToolTip\": \"ToolTip\",\r\n        \"linkOpenInNewWindow\": \"Άνοιγμα συνδέσμου σε νέα καρτέλα.\",\r\n        \"dialogUpdate\": \"Ανανέωση\",\r\n        \"dialogInsert\": \"Εισαγωγή\",\r\n        \"dialogButtonSeparator\": \"ή\",\r\n        \"dialogCancel\": \"Άκυρο\",\r\n        \"cleanFormatting\": \"Καθαρίστε τη Μορφοποίηση\",\r\n        \"createTable\": \"Δημιουργία πίνακα\",\r\n        \"addColumnLeft\": \"Εισαγωγή στήλης αριστερά\",\r\n        \"addColumnRight\": \"Εισαγωγή στήλης δεξιά\",\r\n        \"addRowAbove\": \"Εισαγωγή γραμμής πάνω\",\r\n        \"addRowBelow\": \"Εισαγωγή γραμμής κάτω\",\r\n        \"deleteRow\": \"Διαγραφή γραμμής\",\r\n        \"deleteColumn\": \"Διαγραφή στήλης\",\r\n        \"dialogOk\": \"Εντάξει\",\r\n        \"tableWizard\": \"Οδηγός πίνακα\",\r\n        \"tableTab\": \"Πίνακας\",\r\n        \"cellTab\": \"Κελί\",\r\n        \"accessibilityTab\": \"Προσβασιμότητα\",\r\n        \"caption\": \"Λεζάντα\",\r\n        \"summary\": \"Περίληψη\",\r\n        \"width\": \"Πλάτος\",\r\n        \"height\": \"Ύψος\",\r\n        \"cellSpacing\": \"Κενό Κελιών\",\r\n        \"cellPadding\": \"Επένδυση Κελιών\",\r\n        \"cellMargin\": \"Περιθώριο Κελιών\",\r\n        \"alignment\": \"Ευθυγράμμιση\",\r\n        \"background\": \"Φόντο\",\r\n        \"cssClass\": \"Κλάση CSS\",\r\n        \"id\": \"ID\",\r\n        \"border\": \"Περιθώριο\",\r\n        \"borderStyle\": \"Στυλ Περιθώριου\",\r\n        \"collapseBorders\": \"Ένωση Περιθώριου\",\r\n        \"wrapText\": \"Αναδίπλωση Κειμένου\",\r\n        \"associateCellsWithHeaders\": \"Σύνδεση Κελιών με Επικεφαλίδες\",\r\n        \"alignLeft\": \"Ευθυγράμμιση Αριστερά\",\r\n        \"alignCenter\": \"Ευθυγράμμιση Κέντρο\",\r\n        \"alignRight\": \"Ευθυγράμμιση Δεξιά\",\r\n        \"alignLeftTop\": \"Ευθυγράμμιση Αριστερά Πάνω\",\r\n        \"alignCenterTop\": \"Ευθυγράμμιση Κέντρο Πάνω\",\r\n        \"alignRightTop\": \"Ευθυγράμμιση Δεξιά Πάνω\",\r\n        \"alignLeftMiddle\": \"Ευθυγράμμιση Αριστερά Μέση\",\r\n        \"alignCenterMiddle\": \"Ευθυγράμμιση Κέντρο Μέση\",\r\n        \"alignRightMiddle\": \"Ευθυγράμμιση Δεξιά Μέση\",\r\n        \"alignLeftBottom\": \"Ευθυγράμμιση Αριστερά Κάτω\",\r\n        \"alignCenterBottom\": \"Ευθυγράμμιση Κέντρο Κάτω\",\r\n        \"alignRightBottom\": \"Ευθυγράμμιση Δεξιά Κάτω\",\r\n        \"alignRemove\": \"Αφαίρεση Ευθυγράμμισης\",\r\n        \"columns\": \"Στήλες\",\r\n        \"rows\": \"Γραμμές\",\r\n        \"selectAllCells\": \"Επιλογή όλων των κελιών\"\r\n      });\r\n  }\r\n\r\n  /* FileBrowser messages */\r\n\r\n  if (kendo.ui.FileBrowser) {\r\n    kendo.ui.FileBrowser.prototype.options.messages =\r\n      $.extend(true, kendo.ui.FileBrowser.prototype.options.messages, {\r\n        \"uploadFile\": \"Μεταφόρτωση\",\r\n        \"orderBy\": \"Διευθέτηση με\",\r\n        \"orderByName\": \"Όνομα\",\r\n        \"orderBySize\": \"Μέγεθος\",\r\n        \"directoryNotFound\": \"Δεν βρέθηκε Φάκελος με αυτό το όνομα.\",\r\n        \"emptyFolder\": \"Κενός Φάκελος\",\r\n        \"deleteFile\": 'Είστε σίγουροι ότι θέλετε να διαγράψετε το \"{0}\"?',\r\n        \"invalidFileType\": \"Το επιλεγμένο αρχείο \\\"{0}\\\" δνε είναι έγκυρο. Υποστηριζόμενοι τύποι αρχείων είναι {1}.\",\r\n        \"overwriteFile\": \"Το αρχείο με όνομα \\\"{0}\\\" υπάρχει ήδη σε αυτό το φάκελο. Θέλετε να το αντικαταστήσετε?\",\r\n        \"dropFilesHere\": \"Σύρτε το αρχείο για μεταφόρτωση\",\r\n        \"search\": \"Αναζήτηση\"\r\n      });\r\n  }\r\n\r\n  /* FilterCell messages */\r\n\r\n  if (kendo.ui.FilterCell) {\r\n    kendo.ui.FilterCell.prototype.options.messages =\r\n      $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\r\n        \"isTrue\": \"είναι αληθές\",\r\n        \"isFalse\": \"είναι ψευδές\",\r\n        \"filter\": \"Φίλτρο\",\r\n        \"clear\": \"Εκκαθάριση\",\r\n        \"operator\": \"Χειριστής\"\r\n      });\r\n  }\r\n\r\n  /* FilterCell operators */\r\n\r\n  if (kendo.ui.FilterCell) {\r\n    kendo.ui.FilterCell.prototype.options.operators =\r\n      $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\r\n        \"string\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"startswith\": \"Ξεκινά με\",\r\n          \"contains\": \"Περιέχει\",\r\n          \"doesnotcontain\": \"Δεν περιέχει\",\r\n          \"endswith\": \"Τελειώνει με\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν είναι null\",\r\n          \"isempty\": \"Είναι κενό\",\r\n          \"isnotempty\": \"Δεν είναι κενό\"\r\n        },\r\n        \"number\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"gte\": \"Είναι μεγαλύτερο ή ίσο με\",\r\n          \"gt\": \"Είναι μεγαλύτερο από\",\r\n          \"lte\": \"Είναι μικρότερο ή ίσο με\",\r\n          \"lt\": \"Είναι μικρότερο από\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν είναι null\"\r\n        },\r\n        \"date\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"gte\": \"Είναι μετά ή ίσο με\",\r\n          \"gt\": \"Είναι μετά\",\r\n          \"lte\": \"Είναι πριν ή ίσο με\",\r\n          \"lt\": \"Είναι πριν\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν είναι null\"\r\n        },\r\n        \"enums\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν είναι null\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* FilterMenu messages */\r\n\r\n  if (kendo.ui.FilterMenu) {\r\n    kendo.ui.FilterMenu.prototype.options.messages =\r\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\r\n        \"info\": \"Δείξε αντικείμενα με τιμή που:\",\r\n        \"isTrue\": \"είναι αληθές\",\r\n        \"isFalse\": \"είναι ψευδές\",\r\n        \"filter\": \"Φίλτρο\",\r\n        \"clear\": \"Εκκαθάριση\",\r\n        \"and\": \"Και\",\r\n        \"or\": \"Ή\",\r\n        \"selectValue\": \"-Επιλογή Τιμής-\",\r\n        \"operator\": \"Σύμβολο\",\r\n        \"value\": \"Τιμή\",\r\n        \"cancel\": \"Άκυρο\"\r\n      });\r\n  }\r\n\r\n  /* FilterMenu operator messages */\r\n\r\n  if (kendo.ui.FilterMenu) {\r\n    kendo.ui.FilterMenu.prototype.options.operators =\r\n      $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\r\n        \"string\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"startswith\": \"Ξεκινά με\",\r\n          \"contains\": \"Περιέχει\",\r\n          \"doesnotcontain\": \"Δεν περιέχει\",\r\n          \"endswith\": \"Τελειώνει με\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν είναι null\",\r\n          \"isempty\": \"Είναι κενό\",\r\n          \"isnotempty\": \"Δεν είναι κενό\"\r\n        },\r\n        \"number\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"gte\": \"Είναι μεγαλύτερο ή ίσο με\",\r\n          \"gt\": \"Είναι μεγαλύτερο από\",\r\n          \"lte\": \"Είναι μικρότερο ή ίσο με\",\r\n          \"lt\": \"Είναι μικρότερο από\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν είναι null\"\r\n        },\r\n        \"date\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"gte\": \"Είναι μετά ή ίσο με\",\r\n          \"gt\": \"Είναι μετά\",\r\n          \"lte\": \"Είναι πριν ή ίσο με\",\r\n          \"lt\": \"Είναι πριν\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν έιναι null\"\r\n        },\r\n        \"enums\": {\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\",\r\n          \"isnull\": \"Είναι null\",\r\n          \"isnotnull\": \"Δεν είναι null\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* FilterMultiCheck messages */\r\n\r\n  if (kendo.ui.FilterMultiCheck) {\r\n    kendo.ui.FilterMultiCheck.prototype.options.messages =\r\n      $.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages, {\r\n        \"checkAll\": \"Επιλογή όλων\",\r\n        \"clear\": \"Εκκαθάριση\",\r\n        \"filter\": \"Φίλτρο\",\r\n        \"search\": \"Αναζήτηση\"\r\n      });\r\n  }\r\n\r\n  /* Gantt messages */\r\n\r\n  if (kendo.ui.Gantt) {\r\n    kendo.ui.Gantt.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Gantt.prototype.options.messages, {\r\n        \"actions\": {\r\n          \"addChild\": \"Προσθήκη Παιδιού\",\r\n          \"append\": \"Προσθήκη Εργασίας\",\r\n          \"insertAfter\": \"Προσθήκη κάτω\",\r\n          \"insertBefore\": \"Προσθήκη πάνω\",\r\n          \"pdf\": \"Εξαγωγή σε PDF\"\r\n        },\r\n        \"cancel\": \"Άκυρο\",\r\n        \"deleteDependencyWindowTitle\": \"Διαγραφή Εξάρτησης\",\r\n        \"deleteTaskWindowTitle\": \"Διαγραφή Εργασίας\",\r\n        \"destroy\": \"Διαγραφή\",\r\n        \"editor\": {\r\n          \"assingButton\": \"Ανάθεση\",\r\n          \"editorTitle\": \"Εργασία\",\r\n          \"end\": \"Τέλος\",\r\n          \"percentComplete\": \"Ολοκλήρωση\",\r\n          \"resources\": \"Πόροι\",\r\n          \"resourcesEditorTitle\": \"Πόροι\",\r\n          \"resourcesHeader\": \"Πόροι\",\r\n          \"start\": \"Αρχή\",\r\n          \"title\": \"Τίτλος\",\r\n          \"unitsHeader\": \"Μονάδες\"\r\n        },\r\n        \"save\": \"Αποθήκευση\",\r\n        \"views\": {\r\n          \"day\": \"Ημέρα\",\r\n          \"end\": \"Τέλος\",\r\n          \"month\": \"Μήνας\",\r\n          \"start\": \"Αρχή\",\r\n          \"week\": \"Εβδομάδα\",\r\n          \"year\": \"Χρόνος\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* Grid messages */\r\n\r\n  if (kendo.ui.Grid) {\r\n    kendo.ui.Grid.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Grid.prototype.options.messages, {\r\n        \"commands\": {\r\n          \"cancel\": \"Ακύρωση αλλαγών\",\r\n          \"canceledit\": \"Ακύρωση\",\r\n          \"create\": \"Προσθήκη Εγγραφής\",\r\n          \"destroy\": \"Διαγραφή\",\r\n          \"edit\": \"Επεξεργασία\",\r\n          \"excel\": \"Εξαγωγή σε Excel\",\r\n          \"pdf\": \"Εξαγωγή σε PDF\",\r\n          \"save\": \"Αποθήκευση Αλλαγών\",\r\n          \"select\": \"Επιλογή\",\r\n          \"update\": \"Ανανέωση\"\r\n        },\r\n        \"editable\": {\r\n          \"cancelDelete\": \"Άκυρο\",\r\n          \"confirmation\": \"Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εγγραφή;\",\r\n          \"confirmDelete\": \"Διαγραφή\"\r\n        },\r\n        \"noRecords\": \"Δεν υπάρχουν εγγραφές.\"\r\n      });\r\n  }\r\n\r\n  /* TreeList messages */\r\n\r\n  if (kendo.ui.TreeList) {\r\n    kendo.ui.TreeList.prototype.options.messages =\r\n      $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\r\n        \"noRows\": \"Δεν υπάρχουν εγγραφές\",\r\n        \"loading\": \"Φόρτωση...\",\r\n        \"requestFailed\": \"Αποτυχία Αίτησης.\",\r\n        \"retry\": \"Ξαναδοκιμάστε\",\r\n        \"commands\": {\r\n          \"edit\": \"Επεξεργασία\",\r\n          \"update\": \"Ανανέωση\",\r\n          \"canceledit\": \"Άκυρο\",\r\n          \"create\": \"Προσθήκη Εγγραφής\",\r\n          \"createchild\": \"Προσθήκη Εγγραφής Παιδιού\",\r\n          \"destroy\": \"Διαγραφή\",\r\n          \"excel\": \"Εξαγωγή σε Excel\",\r\n          \"pdf\": \"Εξαγωγή σε PDF\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* Groupable messages */\r\n\r\n  if (kendo.ui.Groupable) {\r\n    kendo.ui.Groupable.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\r\n        \"empty\": \"Τραβήξτε μια στήλη εδώ για να φιλτραριστεί με αυτή τη στήλη\"\r\n      });\r\n  }\r\n\r\n  /* NumericTextBox messages */\r\n\r\n  if (kendo.ui.NumericTextBox) {\r\n    kendo.ui.NumericTextBox.prototype.options =\r\n      $.extend(true, kendo.ui.NumericTextBox.prototype.options, {\r\n        \"upArrowText\": \"Αύξηση Τιμής\",\r\n        \"downArrowText\": \"Μείωση Τιμής\"\r\n      });\r\n  }\r\n\r\n  /* MediaPlayer messages */\r\n\r\n  if (kendo.ui.MediaPlayer) {\r\n    kendo.ui.MediaPlayer.prototype.options.messages =\r\n      $.extend(true, kendo.ui.MediaPlayer.prototype.options.messages, {\r\n        \"pause\": \"Παύση\",\r\n        \"play\": \"Εκκίνηση\",\r\n        \"mute\": \"Σίγαση\",\r\n        \"unmute\": \"Κατάργηση Σίγασης\",\r\n        \"quality\": \"Ποιότητα\",\r\n        \"fullscreen\": \"Πλήρης Οθόνη\"\r\n      });\r\n  }\r\n\r\n  /* Pager messages */\r\n\r\n  if (kendo.ui.Pager) {\r\n    kendo.ui.Pager.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Pager.prototype.options.messages, {\r\n        \"allPages\": \"Όλα\",\r\n        \"display\": \"{0} - {1} από {2} αντικείμενα\",\r\n        \"empty\": \"Δεν υπάρχουν αντικείμενα\",\r\n        \"page\": \"Σελίδα\",\r\n        \"of\": \"από {0}\",\r\n        \"itemsPerPage\": \"αντικείμενα ανα σελίδα\",\r\n        \"first\": \"Πηγαίντε στην πρώτη σελίδα\",\r\n        \"previous\": \"Πηγαίντε στην προηγούμενη σελίδα\",\r\n        \"next\": \"Πηγαίντε στην επόμενη σελίδα\",\r\n        \"last\": \"Πηγαίντε στην τελευταία σελίδα\",\r\n        \"refresh\": \"Ανανέωση\",\r\n        \"morePages\": \"Περισσότερες Σελίδες\"\r\n      });\r\n  }\r\n\r\n  /* PivotGrid messages */\r\n\r\n  if (kendo.ui.PivotGrid) {\r\n    kendo.ui.PivotGrid.prototype.options.messages =\r\n      $.extend(true, kendo.ui.PivotGrid.prototype.options.messages, {\r\n        \"measureFields\": \"Σύρετε πεδία εδώ\",\r\n        \"columnFields\": \"Σύρετε στήλη εδώ\",\r\n        \"rowFields\": \"Σύρετε γραμμές εδώ\"\r\n      });\r\n  }\r\n\r\n  /* PivotFieldMenu messages */\r\n\r\n  if (kendo.ui.PivotFieldMenu) {\r\n    kendo.ui.PivotFieldMenu.prototype.options.messages =\r\n      $.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages, {\r\n        \"info\": \"Δείξε αντικείμενα με τιμή που:\",\r\n        \"filterFields\": \"Φίλτρο Πεδίων\",\r\n        \"filter\": \"Φίλτρο\",\r\n        \"include\": \"Συμπεριέλαβε πεδία...\",\r\n        \"title\": \"Πεδία που θα συμπεριληφθούν\",\r\n        \"clear\": \"Εκκαθάριση\",\r\n        \"ok\": \"Εντάξει\",\r\n        \"cancel\": \"Άκυρο\",\r\n        \"operators\": {\r\n          \"contains\": \"Περιέχει\",\r\n          \"doesnotcontain\": \"Δεν περιέχει\",\r\n          \"startswith\": \"Ξεκινά με\",\r\n          \"endswith\": \"Τελειώνει με\",\r\n          \"eq\": \"Είναι ίσο με\",\r\n          \"neq\": \"Δεν είναι ίσο με\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* RecurrenceEditor messages */\r\n\r\n  if (kendo.ui.RecurrenceEditor) {\r\n    kendo.ui.RecurrenceEditor.prototype.options.messages =\r\n      $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\r\n        \"frequencies\": {\r\n          \"never\": \"Ποτέ\",\r\n          \"hourly\": \"Ωριαία\",\r\n          \"daily\": \"Ημερήσια\",\r\n          \"weekly\": \"Εβδομαδιαία\",\r\n          \"monthly\": \"Μηνιαία\",\r\n          \"yearly\": \"Ετήσια\"\r\n        },\r\n        \"hourly\": {\r\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\r\n          \"interval\": \" ώρα(ες)\"\r\n        },\r\n        \"daily\": {\r\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\r\n          \"interval\": \" μέρα(ες)\"\r\n        },\r\n        \"weekly\": {\r\n          \"interval\": \" εβδομάδα(ες)\",\r\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\r\n          \"repeatOn\": \"Επανάληψη την: \"\r\n        },\r\n        \"monthly\": {\r\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\r\n          \"repeatOn\": \"Επανάληψη τον: \",\r\n          \"interval\": \" μήνα(ες)\",\r\n          \"day\": \"Ημέρα \"\r\n        },\r\n        \"yearly\": {\r\n          \"repeatEvery\": \"Επανάληψη κάθε: \",\r\n          \"repeatOn\": \"Επανάληψη το: \",\r\n          \"interval\": \" χρόνο(ια)\",\r\n          \"of\": \" από \"\r\n        },\r\n        \"end\": {\r\n          \"label\": \"Τέλος:\",\r\n          \"mobileLabel\": \"Τελειώνει\",\r\n          \"never\": \"Ποτέ\",\r\n          \"after\": \"Μετά \",\r\n          \"occurrence\": \" εμφάνιση(εις)\",\r\n          \"on\": \"Τον \"\r\n        },\r\n        \"offsetPositions\": {\r\n          \"first\": \"πρώτο\",\r\n          \"second\": \"δεύτερο\",\r\n          \"third\": \"τρίτο\",\r\n          \"fourth\": \"τέταρο\",\r\n          \"last\": \"τελευταίο\"\r\n        },\r\n        \"weekdays\": {\r\n          \"day\": \"ημέρα\",\r\n          \"weekday\": \"καθημερινή\",\r\n          \"weekend\": \"σαββατοκύριακο\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* Scheduler messages */\r\n\r\n  if (kendo.ui.Scheduler) {\r\n    kendo.ui.Scheduler.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\r\n        \"allDay\": \"όλη μέρα\",\r\n        \"date\": \"Ημερομηνία\",\r\n        \"event\": \"Γεγονός\",\r\n        \"time\": \"Ώρα\",\r\n        \"showFullDay\": \"Δείξε ολόκληρη ημέρα\",\r\n        \"showWorkDay\": \"Δείξε εργάσιμες ώρες\",\r\n        \"today\": \"Σήμερα\",\r\n        \"save\": \"Αποθήκευση\",\r\n        \"cancel\": \"Άκυρο\",\r\n        \"destroy\": \"Διαγραφή\",\r\n        \"deleteWindowTitle\": \"Διαγραφή γεγονότος\",\r\n        \"ariaSlotLabel\": \"Επιλογή από {0:t} έως {1:t}\",\r\n        \"ariaEventLabel\": \"{0} από {1:D} την {2:t}\",\r\n        \"editable\": {\r\n          \"confirmation\": \"Είστε σίγουροι ότι θέλετε να διαγράψετε το γεγονός;\"\r\n        },\r\n        \"views\": {\r\n          \"day\": \"Ημέρα\",\r\n          \"week\": \"Εβδομάδα\",\r\n          \"workWeek\": \"Εργάσιμη Εβδομάδα\",\r\n          \"agenda\": \"Ατζέντα\",\r\n          \"month\": \"Μήνας\"\r\n        },\r\n        \"recurrenceMessages\": {\r\n          \"deleteWindowTitle\": \"Διαγραφή επαναλαμβανόμενων αντικειμένων\",\r\n          \"deleteWindowOccurrence\": \"Διαγραφή τρέχουσας εμφάνισης\",\r\n          \"deleteWindowSeries\": \"Διαγραφή της σειράς\",\r\n          \"editWindowTitle\": \"Επεξεργασία επαναλαμβανόμενου αντικειμένου\",\r\n          \"editWindowOccurrence\": \"Επεξεργασία τρέχουσας εμφάνισης\",\r\n          \"editWindowSeries\": \"Επεξεργασία της σειράς\",\r\n          \"deleteRecurring\": \"Θέλετε να διαγράψετε μόνο αυτό το γεγονός ή ολόκληρη τη σειρά;\",\r\n          \"editRecurring\": \"Θέλετε να επεξεργαστείτε μονο αυτό το γεγονός ή ολόκληρη της σειρά;\"\r\n        },\r\n        \"editor\": {\r\n          \"title\": \"Τίτλος\",\r\n          \"start\": \"Αρχή\",\r\n          \"end\": \"Τέλος\",\r\n          \"allDayEvent\": \"Ολοήμερο γεγονός\",\r\n          \"description\": \"Περιγραφή\",\r\n          \"repeat\": \"Επανάληψη\",\r\n          \"timezone\": \" \",\r\n          \"startTimezone\": \"Εκκίνηση ζώνη ώρας\",\r\n          \"endTimezone\": \"Τέλος ζώνη ώρας\",\r\n          \"separateTimezones\": \"Χρήση διαφορετικών αρχή-τέλος ζώνη ώρας\",\r\n          \"timezoneEditorTitle\": \"Ζώνες Ώρας\",\r\n          \"timezoneEditorButton\": \"Ζώνη Ώρας\",\r\n          \"timezoneTitle\": \"Ζώνες Ώρας\",\r\n          \"noTimezone\": \"Καμία ζώνη ώρας\",\r\n          \"editorTitle\": \"Γεγονός\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* Spreadsheet messages */\r\n\r\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\r\n    kendo.spreadsheet.messages.borderPalette =\r\n      $.extend(true, kendo.spreadsheet.messages.borderPalette, {\r\n        \"allBorders\": \"Όλα τα σύνορα\",\r\n        \"insideBorders\": \"Εσωτερικά σύνορα\",\r\n        \"insideHorizontalBorders\": \"Εσωτερικά οριζότνια σύνορα\",\r\n        \"insideVerticalBorders\": \"Εσωτερικά κάθετα σύνορα\",\r\n        \"outsideBorders\": \"Εξωτερικά σύνορα\",\r\n        \"leftBorder\": \"Αριστερό Σύνορο\",\r\n        \"topBorder\": \"Πάνω Σύνορο\",\r\n        \"rightBorder\": \"Δεξιά Σύνορο\",\r\n        \"bottomBorder\": \"Κάτω Σύνορο\",\r\n        \"noBorders\": \"Χωρίς Σύνορα\",\r\n        \"reset\": \"Επαναφορά Χρώματος\",\r\n        \"customColor\": \"Προσαρμοσμένο Χρώμα...\",\r\n        \"apply\": \"Εφαρμογή\",\r\n        \"cancel\": \"Άκυρο\"\r\n      });\r\n  }\r\n\r\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\r\n    kendo.spreadsheet.messages.dialogs =\r\n      $.extend(true, kendo.spreadsheet.messages.dialogs, {\r\n        \"apply\": \"Εφαρμογή\",\r\n        \"save\": \"Αποθήκευση\",\r\n        \"cancel\": \"Άκυρο\",\r\n        \"remove\": \"Αφαίρεση\",\r\n        \"retry\": \"Ξαναπροσπαθήστε\",\r\n        \"revert\": \"Επαναστροφή\",\r\n        \"okText\": \"Εντάξει\",\r\n        \"formatCellsDialog\": {\r\n          \"title\": \"Μορφή\",\r\n          \"categories\": {\r\n            \"number\": \"Χρώμα\",\r\n            \"currency\": \"Νόμισμα\",\r\n            \"date\": \"Ημερομηνία\"\r\n          }\r\n        },\r\n        \"fontFamilyDialog\": {\r\n          \"title\": \"Γραμματοσειρά\"\r\n        },\r\n        \"fontSizeDialog\": {\r\n          \"title\": \"Μέγεθος Γραμματοσειράς\"\r\n        },\r\n        \"bordersDialog\": {\r\n          \"title\": \"Σύνορα\"\r\n        },\r\n        \"alignmentDialog\": {\r\n          \"title\": \"Στοίχιση\",\r\n          \"buttons\": {\r\n            \"justtifyLeft\": \"Στοίχιση Αριστερά\",\r\n            \"justifyCenter\": \"Κέντρο\",\r\n            \"justifyRight\": \"Στοίχιση Δεξιά\",\r\n            \"justifyFull\": \"Justify\",\r\n            \"alignTop\": \"Στοίχιση Πάνω\",\r\n            \"alignMiddle\": \"Στοίχιση Μέση\",\r\n            \"alignBottom\": \"Στοίχιση Κάτω\"\r\n          }\r\n        },\r\n        \"mergeDialog\": {\r\n          \"title\": \"Συνένωση Κελιών\",\r\n          \"buttons\": {\r\n            \"mergeCells\": \"Συνένωση όλων\",\r\n            \"mergeHorizontally\": \"Συνένωση οριζόντια\",\r\n            \"mergeVertically\": \"Συνένωση κάθετα\",\r\n            \"unmerge\": \"Κατάργηση Συγχώνευσης\"\r\n          }\r\n        },\r\n        \"freezeDialog\": {\r\n          \"title\": \"Πάγωμα Παραθύρων\",\r\n          \"buttons\": {\r\n            \"freezePanes\": \"Πάγωμα Παραθύρων\",\r\n            \"freezeRows\": \"Πάγωμα Σειρών\",\r\n            \"freezeColumns\": \"Πάγωμα Στηλών\",\r\n            \"unfreeze\": \"Ξεπάγωμα Παραθύρων\"\r\n          }\r\n        },\r\n        \"confirmationDialog\": {\r\n          \"text\": \"Είστε σίγουροι ότι θέλετε να διαγράψετε αυτό το φύλλο;\",\r\n          \"title\": \"Αφαίρεση Φύλλου\"\r\n        },\r\n        \"validationDialog\": {\r\n          \"title\": \"Επικύρωση Δεδομένων\",\r\n          \"hintMessage\": \"Παρακαλώ εισάγεται μια έγκυρη {0} τιμή {1}.\",\r\n          \"hintTitle\": \"Επικύρωση {0}\",\r\n          \"criteria\": {\r\n            \"any\": \"Οποιαδήποτε Τιμή\",\r\n            \"number\": \"Αριθμός\",\r\n            \"text\": \"Κείμενο\",\r\n            \"date\": \"Ημερομηνία\",\r\n            \"custom\": \"Διαμορφωμένος τύπος\",\r\n            \"list\": \"Λίστα\"\r\n          },\r\n          \"comparers\": {\r\n            \"greaterThan\": \"μεγαλύτερο από\",\r\n            \"lessThan\": \"μικρότερο από\",\r\n            \"between\": \"μεταξύ\",\r\n            \"notBetween\": \"όχι μεταξύ\",\r\n            \"equalTo\": \"ίσο με\",\r\n            \"notEqualTo\": \"όχι ίσο με\",\r\n            \"greaterThanOrEqualTo\": \"μεγαλύτερο ή ίσο με\",\r\n            \"lessThanOrEqualTo\": \"μικρότερο ή ίσο με\"\r\n          },\r\n          \"comparerMessages\": {\r\n            \"greaterThan\": \"μεγαλύτερο από {0}\",\r\n            \"lessThan\": \"μικρότερο από {0}\",\r\n            \"between\": \"μεταξύ {0} και {1}\",\r\n            \"notBetween\": \"όχι μεταξύ {0} και {1}\",\r\n            \"equalTo\": \"ίσο με {0}\",\r\n            \"notEqualTo\": \"όχι ίσο με {0}\",\r\n            \"greaterThanOrEqualTo\": \"μεγαλύτερο ή ίσο με {0}\",\r\n            \"lessThanOrEqualTo\": \"μικρότερο ή ίσο με {0}\",\r\n            \"custom\": \"που ικανοποιεί τον τύπο: {0}\"\r\n          },\r\n          \"labels\": {\r\n            \"criteria\": \"Κριτήρια\",\r\n            \"comparer\": \"Συγκριτής\",\r\n            \"min\": \"Ελάχιστο\",\r\n            \"max\": \"Μέγιστο\",\r\n            \"value\": \"Τιμή\",\r\n            \"start\": \"Αρχή\",\r\n            \"end\": \"Τέλος\",\r\n            \"onInvalidData\": \"Σε μη-έγκυρα δεδομένα\",\r\n            \"rejectInput\": \"Απόρριψη Εισαγωγής\",\r\n            \"showWarning\": \"Δείξε προειδοποίηση\",\r\n            \"showHint\": \"Δείξε βοήθεια\",\r\n            \"hintTitle\": \"Βοηθητικός΄τίτλος\",\r\n            \"hintMessage\": \"Βοηθητικό μήνυμα\",\r\n            \"ignoreBlank\": \"Αγνόησε κενά\"\r\n          },\r\n          \"placeholders\": {\r\n            \"typeTitle\": \"Τύπος τίτλου\",\r\n            \"typeMessage\": \"Τυπος μηνύματος\"\r\n          }\r\n        },\r\n        \"exportAsDialog\": {\r\n          \"title\": \"Εξαγωγή...\",\r\n          \"labels\": {\r\n            \"fileName\": \"Όνομα Αρχείου\",\r\n            \"saveAsType\": \"Αποθήκευση ως τύπος\",\r\n            \"exportArea\": \"Εξαγωγή\",\r\n            \"paperSize\": \"Μέγεθος Χαρτιού\",\r\n            \"margins\": \"Περιθώρια\",\r\n            \"orientation\": \"Orientation\",\r\n            \"print\": \"Εκτύπωση\",\r\n            \"guidelines\": \"Βοηθητικές Γραμμες\",\r\n            \"center\": \"Κέντρο\",\r\n            \"horizontally\": \"οριζόντια\",\r\n            \"vertically\": \"Κάθετα\"\r\n          }\r\n        },\r\n        \"modifyMergedDialog\": {\r\n          \"errorMessage\": \"Δεν μπορεί να γίνει αλλαγή μέρους συγχωνευμένου κελιού.\"\r\n        },\r\n        \"useKeyboardDialog\": {\r\n          \"title\": \"Αντιγραφή και Επικόλληση\",\r\n          \"errorMessage\": \"Αυτές οι πράξεις δεν μπορούν να γίνουν από αυτό το μενού. Παρακαλώ χρησιμοποιήστε συντομεύσεις πληκτρολογίου:\",\r\n          \"labels\": {\r\n            \"forCopy\": \"για Αντιγραφή\",\r\n            \"forCut\": \"για Αποκοπή\",\r\n            \"forPaste\": \"για Επικόλληση\"\r\n          }\r\n        },\r\n        \"unsupportedSelectionDialog\": {\r\n          \"errorMessage\": \"Αυτή η πράξη δεν μπορεί να γίνει σε πολλαπλή επιλογή.\"\r\n        }\r\n      });\r\n  }\r\n\r\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\r\n    kendo.spreadsheet.messages.filterMenu =\r\n      $.extend(true, kendo.spreadsheet.messages.filterMenu, {\r\n        \"sortAscending\": \"Ταξινόμιση εύρος A σε Ω\",\r\n        \"sortDescending\": \"Ταξινόμιση εύρος Ω σε A\",\r\n        \"filterByValue\": \"Φίλτρο με τιμή\",\r\n        \"filterByCondition\": \"Φίλτρο με συνθήκη\",\r\n        \"apply\": \"Εφαρμογή\",\r\n        \"search\": \"Αναζήτηση\",\r\n        \"addToCurrent\": \"Προσθήκη στην τωρινή επιλογή\",\r\n        \"clear\": \"Εκκαθάριση\",\r\n        \"blanks\": \"(Κενά)\",\r\n        \"operatorNone\": \"Κανένα\",\r\n        \"and\": \"ΚΑΙ\",\r\n        \"or\": \"Ή\",\r\n        \"operators\": {\r\n          \"string\": {\r\n            \"contains\": \"Κείμενο περιέχει\",\r\n            \"doesnotcontain\": \"Κείμενο δεν περιέχει\",\r\n            \"startswith\": \"Κείμενο αρχίζει με\",\r\n            \"endswith\": \"Κείμενο τελειώνει με\"\r\n          },\r\n          \"date\": {\r\n            \"eq\": \"Ημερομηνία είναι\",\r\n            \"neq\": \"Ημερομηνία δεν είναι\",\r\n            \"lt\": \"Ημερομηνία είναι πριν από\",\r\n            \"gt\": \"Ημερομηνία είναι μετά από\"\r\n          },\r\n          \"number\": {\r\n            \"eq\": \"Είναι ίσο με\",\r\n            \"neq\": \"Δεν είναι ίσο με\",\r\n            \"gte\": \"Είναι μεγαλύτερο ή ίσό από\",\r\n            \"gt\": \"Είναι μεγαλύτερο από\",\r\n            \"lte\": \"Είναι μικρότερο ή ίσο από\",\r\n            \"lt\": \"Είναι μικρότερο από\"\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\r\n    kendo.spreadsheet.messages.toolbar =\r\n      $.extend(true, kendo.spreadsheet.messages.toolbar, {\r\n        \"addColumnLeft\": \"Προσθήκη γραμμής αριστερά\",\r\n        \"addColumnRight\": \"Προσθήκη γραμμής δεξιά\",\r\n        \"addRowAbove\": \"Προσθήκη γραμμής πάνω\",\r\n        \"addRowBelow\": \"Προσθήκη γραμμής κάτω\",\r\n        \"alignment\": \"Ευθυγράμμιση\",\r\n        \"alignmentButtons\": {\r\n          \"justtifyLeft\": \"Ευθυγράμμιση Αριστερά\",\r\n          \"justifyCenter\": \"Κέντρο\",\r\n          \"justifyRight\": \"Ευθυγράμμιση Δεξιά\",\r\n          \"justifyFull\": \"Justify\",\r\n          \"alignTop\": \"Ευθυγράμμιση Πάνω\",\r\n          \"alignMiddle\": \"Ευθυγράμμιση Μέση\",\r\n          \"alignBottom\": \"Ευθυγράμμιση Κάτω\"\r\n        },\r\n        \"backgroundColor\": \"Φόντο\",\r\n        \"bold\": \"Έντονα\",\r\n        \"borders\": \"Σύνορα\",\r\n        \"colorPicker\": {\r\n          \"reset\": \"Επαναφορά Χρώματος\",\r\n          \"customColor\": \"Προσχεδιασμένο Χρώμα...\"\r\n        },\r\n        \"copy\": \"Αντιγραφή\",\r\n        \"cut\": \"Αποκοπή\",\r\n        \"deleteColumn\": \"Διαγραφή στήλης\",\r\n        \"deleteRow\": \"Διαγραφή γραμμής\",\r\n        \"excelImport\": \"Εισαγωγή από Excel...\",\r\n        \"filter\": \"φίλτρο\",\r\n        \"fontFamily\": \"Γραμματοσειρά\",\r\n        \"fontSize\": \"Μέγεθος Γραμματοσειράς\",\r\n        \"format\": \"Προσχεδιασμένος Τύπος...\",\r\n        \"formatTypes\": {\r\n          \"automatic\": \"Αυτόματο\",\r\n          \"number\": \"Αριθμός\",\r\n          \"percent\": \"Ποσοστό\",\r\n          \"financial\": \"Οικονομικά\",\r\n          \"currency\": \"Νόμισα\",\r\n          \"date\": \"Ημερομηνία\",\r\n          \"time\": \"Ώρα\",\r\n          \"dateTime\": \"Ημερομηνία-Ώρα\",\r\n          \"duration\": \"Διάρκεια\",\r\n          \"moreFormats\": \"Περισσότερα formats...\"\r\n        },\r\n        \"formatDecreaseDecimal\": \"Μείωση δεκαδικών\",\r\n        \"formatIncreaseDecimal\": \"Αύξηση δεκαδικών\",\r\n        \"freeze\": \"Πάγωμα παραθύρων\",\r\n        \"freezeButtons\": {\r\n          \"freezePanes\": \"Πάγωμα παραθύρων\",\r\n          \"freezeRows\": \"Πάγωμα γραμμών\",\r\n          \"freezeColumns\": \"Πάγωμα στηλών\",\r\n          \"unfreeze\": \"Ξεπάγωμα παραθύρων panes\"\r\n        },\r\n        \"italic\": \"Πλάγια\",\r\n        \"merge\": \"Συνένωση κελιών\",\r\n        \"mergeButtons\": {\r\n          \"mergeCells\": \"Συνένωση όλων\",\r\n          \"mergeHorizontally\": \"Σενένωση οριζόντια\",\r\n          \"mergeVertically\": \"Συνένωση κάθετα\",\r\n          \"unmerge\": \"Ακύρωση Συνένωσης\"\r\n        },\r\n        \"open\": \"Άνογμα...\",\r\n        \"paste\": \"Επικόλληση\",\r\n        \"quickAccess\": {\r\n          \"redo\": \"Ακύρωση Αναίρεσης\",\r\n          \"undo\": \"Αναίρεση\"\r\n        },\r\n        \"saveAs\": \"Αποθήκευση ως...\",\r\n        \"sortAsc\": \"Αύξουσα Ταξινόμιση\",\r\n        \"sortDesc\": \"Φθήνουσα Ταξινόμιση\",\r\n        \"sortButtons\": {\r\n          \"sortSheetAsc\": \"Ταξινόμιση φύλλου A σε Ω\",\r\n          \"sortSheetDesc\": \"Ταξινόμιση φύλλου Ω σε A\",\r\n          \"sortRangeAsc\": \"Ταξινόμιση εύρους A σε Ω\",\r\n          \"sortRangeDesc\": \"Ταξινόμιση εύρους Ω σε A\"\r\n        },\r\n        \"textColor\": \"Χρώμα Κειμένου\",\r\n        \"textWrap\": \"Αναδίπλωση κειμένου\",\r\n        \"underline\": \"Υπογράμμιση\",\r\n        \"validation\": \"Επικύρωση Δεδομένων...\"\r\n      });\r\n  }\r\n\r\n  if (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\r\n    kendo.spreadsheet.messages.view =\r\n      $.extend(true, kendo.spreadsheet.messages.view, {\r\n        \"errors\": {\r\n          \"shiftingNonblankCells\": \"Δεν μπορεί να γίνει εισαγωγή κελιών λόγω πιθανότητας απώλειας δεδομένων. Επιλέξτε άλλη τοποθεσία ή διαγράψτε τα δεδομένα από το φύλλο εργασίας.\",\r\n          \"filterRangeContainingMerges\": \"Δεν μπορεί να δημιουργηθεί φίλτρο μέσα στο εύρος ένωσης\",\r\n          \"validationError\": \"Η τιμή που εισάγετε παραβιάζει την επικύρωση δεδομένων του κελιού.\"\r\n        },\r\n        \"tabs\": {\r\n          \"home\": \"Αρχική\",\r\n          \"insert\": \"Εισαγωγή\",\r\n          \"data\": \"Δεδομένα\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* Slider messages */\r\n\r\n  if (kendo.ui.Slider) {\r\n    kendo.ui.Slider.prototype.options =\r\n      $.extend(true, kendo.ui.Slider.prototype.options, {\r\n        \"increaseButtonTitle\": \"Αύξηση\",\r\n        \"decreaseButtonTitle\": \"Μείωση\"\r\n      });\r\n  }\r\n\r\n  /* TreeList messages */\r\n\r\n  if (kendo.ui.TreeList) {\r\n    kendo.ui.TreeList.prototype.options.messages =\r\n      $.extend(true, kendo.ui.TreeList.prototype.options.messages, {\r\n        \"noRows\": \"Δεν υπάρχουν εγγραφές\",\r\n        \"loading\": \"Φορτώνει...\",\r\n        \"requestFailed\": \"Αίτημα απέτυχε.\",\r\n        \"retry\": \"Επανάληψη\",\r\n        \"commands\": {\r\n          \"edit\": \"Επεξεργασία\",\r\n          \"update\": \"Ανανέωση\",\r\n          \"canceledit\": \"Άκυρο\",\r\n          \"create\": \"Προσθήκη Εγγραφής\",\r\n          \"createchild\": \"Προσθήκη Εγγραφής παιδιού\",\r\n          \"destroy\": \"Διαγραφή\",\r\n          \"excel\": \"Εξαγωγή σε Excel\",\r\n          \"pdf\": \"Εξαγωγή σε PDF\"\r\n        }\r\n      });\r\n  }\r\n\r\n  if (kendo.ui.TreeList) {\r\n    kendo.ui.TreeList.prototype.options.columnMenu =\r\n      $.extend(true, kendo.ui.TreeList.prototype.options.columnMenu, {\r\n        \"messages\": {\r\n          \"columns\": \"Επιλέξτε στήλες\",\r\n          \"filter\": \"Εφαρμογή φίλτρων\",\r\n          \"sortAscending\": \"Ταξινόμιση (αύξ.)\",\r\n          \"sortDescending\": \"Ταξινόμιση (φθήν.)\"\r\n        }\r\n      });\r\n  }\r\n\r\n  /* TreeView messages */\r\n\r\n  if (kendo.ui.TreeView) {\r\n    kendo.ui.TreeView.prototype.options.messages =\r\n      $.extend(true, kendo.ui.TreeView.prototype.options.messages, {\r\n        \"loading\": \"Φορτώνει...\",\r\n        \"requestFailed\": \"Αίτημα απέτυχε.\",\r\n        \"retry\": \"Επανάληψη\"\r\n      });\r\n  }\r\n\r\n  /* Upload messages */\r\n\r\n  if (kendo.ui.Upload) {\r\n    kendo.ui.Upload.prototype.options.localization =\r\n      $.extend(true, kendo.ui.Upload.prototype.options.localization, {\r\n        \"select\": \"Επιλογή αρχέιων...\",\r\n        \"cancel\": \"Άκυρο\",\r\n        \"retry\": \"Επανάληψη\",\r\n        \"remove\": \"Αφαίρεση\",\r\n        \"clearSelectedFiles\": \"Εκκαθάριση\",\r\n        \"uploadSelectedFiles\": \"Μεταφόρτωση αρχείων\",\r\n        \"dropFilesHere\": \"Σύρτε αρχεία εδώ για μεταφόρτωση\",\r\n        \"statusUploading\": \"μεταφόρτωση\",\r\n        \"statusUploaded\": \"ανέβηκε\",\r\n        \"statusWarning\": \"προειδοποίηση\",\r\n        \"statusFailed\": \"απέτυχε\",\r\n        \"headerStatusUploading\": \"Μεταφόρτωση...\",\r\n        \"headerStatusUploaded\": \"Ολοκλήρωση\",\r\n        \"invalidMaxFileSize\": \"Το μέγεθος του αρχείου είναι πολύ μεγάλο.\",\r\n        \"invalidMinFileSize\": \"Το μέγεθος του αρχείου είναι πολύ μικρό.\",\r\n        \"invalidFileExtension\": \"Μη έγκυρος τύπος αρχείου.\"\r\n      });\r\n  }\r\n\r\n  /* Validator messages */\r\n\r\n  if (kendo.ui.Validator) {\r\n    kendo.ui.Validator.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Validator.prototype.options.messages, {\r\n        \"required\": \"{0} είναι υποχρεωτικό\",\r\n        \"pattern\": \"{0} δεν είναι έγκυρο\",\r\n        \"min\": \"{0} πρέπει να είναι μεγαλύτερο από ή ίσο με {1}\",\r\n        \"max\": \"{0} πρέπει να είναι μικτρότερο από ή ίσο με {1}\",\r\n        \"step\": \"{0} δεν είναι έγκυρο\",\r\n        \"email\": \"{0} δεν είναι έγκυρο email\",\r\n        \"url\": \"{0} δεν είναι έγκυρο URL\",\r\n        \"date\": \"{0} δεν είναι έγκυρη ημερομηνία\",\r\n        \"dateCompare\": \"Η ημερομηνία λήξης πρέπει να είναι μεγαλύερη από την ημερομηνία έναρξης\"\r\n      });\r\n  }\r\n\r\n  /* kendo.ui.progress method */\r\n  if (kendo.ui.progress) {\r\n    kendo.ui.progress.messages =\r\n      $.extend(true, kendo.ui.progress.messages, {\r\n        loading: \"Φόρτωση...\"\r\n      });\r\n  }\r\n\r\n  /* Dialog */\r\n\r\n  if (kendo.ui.Dialog) {\r\n    kendo.ui.Dialog.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Dialog.prototype.options.localization, {\r\n        \"close\": \"Κλείσιμο\"\r\n      });\r\n  }\r\n\r\n  /* Alert */\r\n\r\n  if (kendo.ui.Alert) {\r\n    kendo.ui.Alert.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Alert.prototype.options.localization, {\r\n        \"okText\": \"Εντάξει\"\r\n      });\r\n  }\r\n\r\n  /* Confirm */\r\n\r\n  if (kendo.ui.Confirm) {\r\n    kendo.ui.Confirm.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Confirm.prototype.options.localization, {\r\n        \"okText\": \"Εντάξει\",\r\n        \"cancel\": \"Άκυρο\"\r\n      });\r\n  }\r\n\r\n  /* Prompt */\r\n  if (kendo.ui.Prompt) {\r\n    kendo.ui.Prompt.prototype.options.messages =\r\n      $.extend(true, kendo.ui.Prompt.prototype.options.localization, {\r\n        \"okText\": \"Εντάξει\",\r\n        \"cancel\": \"Άκυρο\"\r\n      });\r\n  }\r\n\r\n})(window.kendo.jQuery);\r\n}));"]}