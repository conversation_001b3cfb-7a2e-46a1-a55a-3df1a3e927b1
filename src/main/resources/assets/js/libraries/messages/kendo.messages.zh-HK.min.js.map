{"version": 3, "sources": ["messages/kendo.messages.zh-HK.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "<PERSON><PERSON><PERSON>", "views", "day", "week", "month", "actions", "append", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "last", "next", "previous", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "end", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "editor", "title", "start", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "editor<PERSON><PERSON><PERSON>", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "spreadsheet", "borderPalette", "allBorders", "insideBorders", "insideHorizontalBorders", "insideVerticalBorders", "outsideBorders", "leftBorder", "topBorder", "rightBorder", "bottomBorder", "noBorders", "reset", "customColor", "dialogs", "revert", "okText", "formatCellsDialog", "categories", "currency", "fontFamilyDialog", "fontSizeDialog", "bordersDialog", "alignmentDialog", "buttons", "justtifyLeft", "alignTop", "alignMiddle", "alignBottom", "mergeDialog", "mergeCells", "mergeHorizontally", "mergeVertically", "unmerge", "freezeDialog", "freezePanes", "freezeRows", "freezeColumns", "unfreeze", "validationDialog", "hintMessage", "hintTitle", "criteria", "any", "text", "custom", "list", "comparers", "greaterThan", "lessThan", "between", "notBetween", "equalTo", "notEqualTo", "greaterThanOrEqualTo", "lessThanOrEqualTo", "comparerMessages", "labels", "comparer", "onInvalidData", "rejectInput", "showWarning", "showHint", "ignoreBlank", "placeholders", "typeTitle", "typeMessage", "exportAsDialog", "fileName", "saveAsType", "exportArea", "paperSize", "margins", "orientation", "print", "guidelines", "center", "horizontally", "vertically", "modifyMergedDialog", "errorMessage", "useKeyboardDialog", "forCopy", "forCut", "forPaste", "unsupportedSelectionDialog", "filterMenu", "filterByValue", "filterByCondition", "addToCurrent", "blanks", "operatorNone", "toolbar", "alignment", "alignmentButtons", "backgroundColor", "borders", "colorPicker", "copy", "cut", "excelImport", "fontFamily", "format", "formatTypes", "automatic", "percent", "financial", "time", "dateTime", "duration", "moreFormats", "formatDecreaseDecimal", "formatIncreaseDecimal", "freeze", "freezeButtons", "merge", "mergeButtons", "open", "paste", "quickAccess", "redo", "undo", "saveAs", "sortAsc", "sortDesc", "sortButtons", "sortSheetAsc", "sortSheetDesc", "sortRangeAsc", "sortRangeDesc", "textColor", "textWrap", "validation", "view", "errors", "shiftingNonblankCells", "filterRangeContainingMerges", "validationError", "tabs", "home", "insert", "data", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,KACTC,OAAU,QAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,KACTC,OAAU,QAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,KACjBC,eAAkB,KAClBC,OAAU,KACVC,QAAW,IACXC,KAAQ,KACRC,SAAY,MACZC,KAAQ,KACRC,OAAU,UAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,KACRC,OAAU,KACVC,UAAa,MACbC,cAAiB,MACjBC,YAAe,KACfC,UAAa,KACbC,cAAiB,KACjBC,YAAe,MACfC,aAAgB,MAChBC,YAAe,OACfC,oBAAuB,SACvBC,kBAAqB,SACrBC,OAAU,OACVC,QAAW,OACXC,WAAc,OACdC,OAAU,OACVC,YAAe,OACfC,WAAc,OACdC,WAAc,UACdC,SAAY,UACZC,SAAY,OACZC,gBAAmB,UACnBC,SAAY,OACZC,gBAAmB,UACnBC,YAAe,OACfC,WAAc,MACdC,UAAa,KACbC,UAAa,MACbC,MAAS,KACTC,YAAe,QACfC,WAAc,KACdC,QAAW,QACXC,YAAe,KACfC,YAAe,KACfC,gBAAmB,+BACnBC,WAAc,gBACdC,cAAiB,kCACjBC,kBAAqB,UACrBC,gBAAmB,OACnBC,aAAgB,OAChBC,WAAc,UACdC,YAAe,UACfC,eAAkB,OAClBC,UAAa,KACbC,eAAkB,OAClBC,SAAY,OACZC,YAAe,OACfC,oBAAuB,UACvBC,aAAgB,KAChBC,aAAgB,KAChBC,sBAAyB,IACzBC,aAAgB,KAChBC,YAAe,OACfC,cAAiB,QACjBC,eAAkB,QAClBC,YAAe,QACfC,YAAe,QACfC,UAAa,MACbC,aAAgB,SAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,KACdC,QAAW,OACXE,YAAe,KACfD,YAAe,KACfK,kBAAqB,UACrBR,YAAe,QACfM,WAAc,gBACdD,gBAAmB,+BACnBE,cAAiB,kCACjBwB,cAAiB,cACjBC,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,KACVC,QAAW,KACXvE,OAAU,KACVwE,MAAS,KACTC,SAAY,SAMVtF,MAAMC,GAAGsF,aACbvF,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQC,UACnDmF,KAAQ,aACRL,OAAU,KACVC,QAAW,KACXvE,OAAU,KACVwE,MAAS,KACTI,IAAO,KACPC,GAAM,IACNC,YAAe,OACfL,SAAY,MACZM,MAAS,IACTpF,OAAU,QAMRR,MAAMC,GAAG4F,mBACb7F,MAAMC,GAAG4F,iBAAiB1F,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4F,iBAAiB1F,UAAUC,QAAQC,UACzD4E,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQ0F,UACtChG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQ0F,WACnDC,QACEC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,MAClBC,SAAY,OAEdC,QACEN,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERC,MACEX,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERE,OACEZ,GAAM,KACNC,IAAO,UAOPjG,MAAMC,GAAGsF,aACbvF,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQ0F,UACtChG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQ0F,WACnDC,QACEC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,MAClBC,SAAY,OAEdC,QACEN,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERC,MACEX,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERE,OACEZ,GAAM,KACNC,IAAO,UAQPjG,MAAMC,GAAG4G,QACb7G,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,UAC9CyG,OACEC,IAAO,IACPC,KAAQ,IACRC,MAAS,KAEXC,SACEC,OAAU,OACVC,SAAY,QACZC,aAAgB,QAChBC,YAAe,YAOftH,MAAMC,GAAGsH,OACbvH,MAAMC,GAAGsH,KAAKpH,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsH,KAAKpH,UAAUC,QAAQC,UAC7CmH,UACEhH,OAAU,KACViH,WAAc,KACdC,OAAU,KACVC,QAAW,KACXC,KAAQ,KACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,KACRC,OAAU,KACVC,OAAU,MAEZC,UACEC,aAAgB,KAChBC,aAAgB,UAChBC,cAAiB,SAOjBrI,MAAMC,GAAGqI,YACbtI,MAAMC,GAAGqI,UAAUnI,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqI,UAAUnI,UAAUC,QAAQC,UAClDkI,MAAS,oBAMPvI,MAAMC,GAAGuI,iBACbxI,MAAMC,GAAGuI,eAAerI,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuI,eAAerI,UAAUC,SAC/CqI,YAAe,KACfC,cAAiB,QAMf1I,MAAMC,GAAG0I,QACb3I,MAAMC,GAAG0I,MAAMxI,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0I,MAAMxI,UAAUC,QAAQC,UAC9CuI,SAAY,MACZC,QAAW,uBACXN,MAAS,YACTO,KAAQ,IACRC,GAAM,QACNC,aAAgB,KAChBC,MAAS,KACTC,KAAQ,KACRC,KAAQ,MACRC,SAAY,MACZC,QAAW,KACXC,UAAa,WAMXtJ,MAAMC,GAAGsJ,YACbvJ,MAAMC,GAAGsJ,UAAUpJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsJ,UAAUpJ,UAAUC,QAAQC,UAClDmJ,cAAiB,WACjBC,aAAgB,UAChBC,UAAa,aAMX1J,MAAMC,GAAG0J,mBACb3J,MAAMC,GAAG0J,iBAAiBxJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,iBAAiBxJ,UAAUC,QAAQC,UACzDuJ,aACEC,MAAS,KACTC,OAAU,MACVC,MAAS,KACTC,OAAU,KACVC,QAAW,KACXC,OAAU,MAEZJ,QACEK,YAAe,SACfC,SAAY,OAEdL,OACEI,YAAe,SACfC,SAAY,MAEdJ,QACEI,SAAY,KACZD,YAAe,SACfE,SAAY,QAEdJ,SACEE,YAAe,SACfE,SAAY,OACZD,SAAY,KACZrD,IAAO,MAETmD,QACEC,YAAe,SACfE,SAAY,QACZD,SAAY,KACZrB,GAAM,SAERuB,KACEC,MAAS,QACTC,YAAe,OACfX,MAAS,KACTY,MAAS,MACTC,WAAc,MACdC,GAAM,OAERC,iBACE3B,MAAS,KACT4B,OAAU,KACVC,MAAS,KACTC,OAAU,KACV7B,KAAQ,MAEV8B,UACEjE,IAAO,IACPkE,QAAW,MACXC,QAAW,SAQXlL,MAAMC,GAAGkL,YACbnL,MAAMC,GAAGkL,UAAUhL,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkL,UAAUhL,UAAUC,QAAQC,UAClD+K,MAAS,KACTrD,KAAQ,KACRvH,OAAU,KACVmH,QAAW,KACX0D,kBAAqB,OACrBC,cAAiB,oBACjBC,eAAkB,wBAClBzE,OACEC,IAAO,IACPC,KAAQ,IACRwE,SAAY,MACZC,OAAU,KACVxE,MAAS,KAEXyE,oBACEL,kBAAqB,SACrBM,uBAA0B,SAC1BC,mBAAsB,OACtBC,gBAAmB,SACnBC,qBAAwB,SACxBC,iBAAoB,QAEtBC,QACEC,MAAS,KACTC,MAAS,KACT5B,IAAO,KACP6B,YAAe,OACfC,YAAe,KACfC,OAAU,KACVC,SAAY,IACZC,cAAiB,OACjBC,YAAe,OACfC,kBAAqB,eACrBC,oBAAuB,KACvBC,qBAAwB,KACxBC,cAAiB,OACjBC,WAAc,IACdC,YAAe,SAOf9M,MAAMC,GAAG8M,SACb/M,MAAMC,GAAG8M,OAAO5M,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,OAAO5M,UAAUC,SACvC4M,oBAAuB,KACvBC,oBAAuB,QAMrBjN,MAAMC,GAAGiN,WACblN,MAAMC,GAAGiN,SAAS/M,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiN,SAAS/M,UAAUC,QAAQC,UACjD8M,QAAW,SACXC,cAAiB,OACjBC,MAAS,QAMPrN,MAAMC,GAAGqN,SACbtN,MAAMC,GAAGqN,OAAOnN,UAAUC,QAAQmN,aAClCzN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqN,OAAOnN,UAAUC,QAAQmN,cAC/CvF,OAAU,QACVxH,OAAU,KACV6M,MAAS,KACTG,OAAU,KACVC,oBAAuB,OACvBzI,cAAiB,cACjB0I,gBAAmB,MACnBC,eAAkB,MAClBC,cAAiB,KACjBC,aAAgB,KAChBC,sBAAyB,QACzBC,qBAAwB,QAMtB/N,MAAMC,GAAG+N,YACbhO,MAAMC,GAAG+N,UAAU7N,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+N,UAAU7N,UAAUC,QAAQC,UAClD4N,SAAY,WACZC,QAAW,SACXC,IAAO,kBACPC,IAAO,kBACPC,KAAQ,SACRC,MAAS,gBACTC,IAAO,eACP5H,KAAQ,iBAMN3G,MAAMwO,aAAexO,MAAMwO,YAAYnO,SAASoO,gBAChDzO,MAAMwO,YAAYnO,SAASoO,cACvB3O,EAAEQ,QAAO,EAAMN,MAAMwO,YAAYnO,SAASoO,eACtCC,WAAc,OACdC,cAAiB,MACjBC,wBAA2B,QAC3BC,sBAAyB,QACzBC,eAAkB,MAClBC,WAAc,MACdC,UAAa,MACbC,YAAe,MACfC,aAAgB,MAChBC,UAAa,OACbC,MAAS,OACTC,YAAe,UACf9O,MAAS,KACTC,OAAU,QAIlBR,MAAMwO,aAAexO,MAAMwO,YAAYnO,SAASiP,UAChDtP,MAAMwO,YAAYnO,SAASiP,QACvBxP,EAAEQ,QAAO,EAAMN,MAAMwO,YAAYnO,SAASiP,SACtC/O,MAAS,KACTwH,KAAQ,KACRvH,OAAU,KACVgN,OAAU,KACVH,MAAS,KACTkC,OAAU,KACVC,OAAU,IACVC,mBACIxD,MAAS,KACTyD,YACIpJ,OAAU,KACVqJ,SAAY,KACZhJ,KAAQ,OAGhBiJ,kBACI3D,MAAS,MAEb4D,gBACI5D,MAAS,QAEb6D,eACI7D,MAAS,MAEb8D,iBACI9D,MAAS,KACT+D,SACIC,aAAgB,OAChBvO,cAAiB,SACjBE,aAAgB,OAChBC,YAAe,OACfqO,SAAY,OACZC,YAAe,SACfC,YAAe,SAGvBC,aACIpE,MAAS,QACT+D,SACIM,WAAc,OACdC,kBAAqB,OACrBC,gBAAmB,OACnBC,QAAW,SAGnBC,cACIzE,MAAS,QACT+D,SACIW,YAAe,QACfC,WAAc,MACdC,cAAiB,MACjBC,SAAY,YAGpBC,kBACI9E,MAAS,OACT+E,YAAe,qBACfC,UAAa,SACbC,UACIC,IAAO,OACP7K,OAAU,KACV8K,KAAQ,KACRzK,KAAQ,KACR0K,OAAU,OACVC,KAAQ,MAEZC,WACIC,YAAe,KACfC,SAAY,KACZC,QAAW,WACXC,WAAc,YACdC,QAAW,KACXC,WAAc,MACdC,qBAAwB,QACxBC,kBAAqB,SAEzBC,kBACIR,YAAe,SACfC,SAAY,SACZC,QAAW,iBACXC,WAAc,kBACdC,QAAW,SACXC,WAAc,UACdC,qBAAwB,YACxBC,kBAAqB,YACrBV,OAAU,aAEdY,QACIf,SAAY,KACZgB,SAAY,MACZ/D,IAAO,KACPC,IAAO,KACPxI,MAAS,KACTsG,MAAS,KACT5B,IAAO,KACP6H,cAAiB,SACjBC,YAAe,OACfC,YAAe,OACfC,SAAY,OACZrB,UAAa,OACbD,YAAe,OACfuB,YAAe,QAEnBC,cACIC,UAAa,OACbC,YAAe,SAGvBC,gBACI1G,MAAS,QACTgG,QACIW,SAAY,OACZC,WAAc,OACdC,WAAc,KACdC,UAAa,OACbC,QAAW,KACXC,YAAe,KACfC,MAAS,KACTC,WAAc,MACdC,OAAU,KACVC,aAAgB,MAChBC,WAAc,QAGtBC,oBACIC,aAAgB,iBAEpBC,mBACIxH,MAAS,sBACTuH,aAAgB,+FAChBvB,QACIyB,QAAW,WACXC,OAAU,UACVC,SAAY,cAGpBC,4BACIL,aAAgB,6DAK5BxT,MAAMwO,aAAexO,MAAMwO,YAAYnO,SAASyT,aAChD9T,MAAMwO,YAAYnO,SAASyT,WACvBhU,EAAEQ,QAAO,EAAMN,MAAMwO,YAAYnO,SAASyT,YACtCnT,cAAiB,oBACjBC,eAAkB,oBAClBmT,cAAiB,kBACjBC,kBAAqB,sBACrBzT,MAAS,QACT0E,OAAU,SACVgP,aAAgB,2BAChB5O,MAAS,QACT6O,OAAU,WACVC,aAAgB,OAChB1O,IAAO,MACPC,GAAM,KACNI,WACIC,QACII,SAAY,gBACZC,eAAkB,wBAClBF,WAAc,mBACdG,SAAY,kBAEhBM,MACIX,GAAM,UACNC,IAAO,cACPS,GAAM,iBACNF,GAAM,iBAEVF,QACIN,GAAM,cACNC,IAAO,kBACPM,IAAO,8BACPC,GAAM,kBACNC,IAAO,2BACPC,GAAM,oBAMtB1G,MAAMwO,aAAexO,MAAMwO,YAAYnO,SAAS+T,UAChDpU,MAAMwO,YAAYnO,SAAS+T,QACvBtU,EAAEQ,QAAO,EAAMN,MAAMwO,YAAYnO,SAAS+T,SACtC3P,cAAiB,kBACjBC,eAAkB,mBAClBC,YAAe,gBACfC,YAAe,gBACfyP,UAAa,YACbC,kBACIrE,aAAgB,aAChBvO,cAAiB,SACjBE,aAAgB,cAChBC,YAAe,UACfqO,SAAY,YACZC,YAAe,eACfC,YAAe,gBAEnBmE,gBAAmB,aACnBnT,KAAQ,OACRoT,QAAW,UACXC,aACIrF,MAAS,cACTC,YAAe,mBAEnBqF,KAAQ,OACRC,IAAO,MACP7P,aAAgB,gBAChBD,UAAa,aACb+P,YAAe,uBACf/T,OAAU,SACVgU,WAAc,OACdnS,SAAY,YACZoS,OAAU,mBACVC,aACIC,UAAa,YACb1O,OAAU,SACV2O,QAAW,UACXC,UAAa,YACbvF,SAAY,WACZhJ,KAAQ,OACRwO,KAAQ,OACRC,SAAY,YACZC,SAAY,WACZC,YAAe,mBAEnBC,sBAAyB,mBACzBC,sBAAyB,mBACzBC,OAAU,eACVC,eACI/E,YAAe,eACfC,WAAc,cACdC,cAAiB,iBACjBC,SAAY,kBAEhBzP,OAAU,SACVsU,MAAS,cACTC,cACItF,WAAc,YACdC,kBAAqB,qBACrBC,gBAAmB,mBACnBC,QAAW,WAEfoF,KAAQ,UACRC,MAAS,QACTC,aACIC,KAAQ,OACRC,KAAQ,QAEZC,OAAU,aACVC,QAAW,iBACXC,SAAY,kBACZC,aACIC,aAAgB,oBAChBC,cAAiB,oBACjBC,aAAgB,oBAChBC,cAAiB,qBAErBC,UAAa,aACbC,SAAY,YACZrV,UAAa,YACbsV,WAAc,aAItB5W,MAAMwO,aAAexO,MAAMwO,YAAYnO,SAASwW,OAChD7W,MAAMwO,YAAYnO,SAASwW,KACvB/W,EAAEQ,QAAO,EAAMN,MAAMwO,YAAYnO,SAASwW,MACtCC,QACIC,sBAAyB,sIACzBC,4BAA+B,0DAC/BC,gBAAmB,6EAEvBC,MACIC,KAAQ,KACRC,OAAU,KACVC,KAAQ,UAIrBC,OAAOtX,MAAMuX", "file": "kendo.messages.zh-HK.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"確定\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"確定\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"升序\",\n  \"sortDescending\": \"降序\",\n  \"filter\": \"過濾\",\n  \"columns\": \"列\",\n  \"done\": \"完成\",\n  \"settings\": \"列設置\",\n  \"lock\": \"鎖定\",\n  \"unlock\": \"解除鎖定\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"粗體\",\n  \"italic\": \"斜體\",\n  \"underline\": \"下劃線\",\n  \"strikethrough\": \"刪除線\",\n  \"superscript\": \"上標\",\n  \"subscript\": \"下標\",\n  \"justifyCenter\": \"居中\",\n  \"justifyLeft\": \"左對齊\",\n  \"justifyRight\": \"右對齊\",\n  \"justifyFull\": \"兩端對齊\",\n  \"insertUnorderedList\": \"插入無序列表\",\n  \"insertOrderedList\": \"插入有序列表\",\n  \"indent\": \"增加縮進\",\n  \"outdent\": \"減少縮進\",\n  \"createLink\": \"插入鏈接\",\n  \"unlink\": \"移除鏈接\",\n  \"insertImage\": \"插入圖片\",\n  \"insertFile\": \"插入文件\",\n  \"insertHtml\": \"插入 HTML\",\n  \"viewHtml\": \"查看 HTML\",\n  \"fontName\": \"選擇字體\",\n  \"fontNameInherit\": \"（繼承的字體）\",\n  \"fontSize\": \"選擇字號\",\n  \"fontSizeInherit\": \"（繼承的字號）\",\n  \"formatBlock\": \"格式化塊\",\n  \"formatting\": \"格式化\",\n  \"foreColor\": \"顏色\",\n  \"backColor\": \"背景色\",\n  \"style\": \"風格\",\n  \"emptyFolder\": \"文件夾為空\",\n  \"uploadFile\": \"上傳\",\n  \"orderBy\": \"排序條件:\",\n  \"orderBySize\": \"大小\",\n  \"orderByName\": \"名字\",\n  \"invalidFileType\": \"選中的文件 \\\"{0}\\\" 非法，支持的文件類型為 {1}。\",\n  \"deleteFile\": '您確定要刪除 \\\"{0}\\\"?',\n  \"overwriteFile\": '當前文件夾已存在文件名為 \\\"{0}\\\" 的文件，您確定要覆蓋麽？',\n  \"directoryNotFound\": \"此文件夾未找到\",\n  \"imageWebAddress\": \"圖片地址\",\n  \"imageAltText\": \"替代文本\",\n  \"imageWidth\": \"寬度 (px)\",\n  \"imageHeight\": \"高度 (px)\",\n  \"fileWebAddress\": \"文件地址\",\n  \"fileTitle\": \"標題\",\n  \"linkWebAddress\": \"鏈接地址\",\n  \"linkText\": \"鏈接文字\",\n  \"linkToolTip\": \"鏈接提示\",\n  \"linkOpenInNewWindow\": \"在新窗口中打開\",\n  \"dialogUpdate\": \"上傳\",\n  \"dialogInsert\": \"插入\",\n  \"dialogButtonSeparator\": \"或\",\n  \"dialogCancel\": \"取消\",\n  \"createTable\": \"創建表格\",\n  \"addColumnLeft\": \"左側添加列\",\n  \"addColumnRight\": \"右側添加列\",\n  \"addRowAbove\": \"上方添加行\",\n  \"addRowBelow\": \"下方添加行\",\n  \"deleteRow\": \"刪除行\",\n  \"deleteColumn\": \"刪除列\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"上傳\",\n  \"orderBy\": \"排序條件\",\n  \"orderByName\": \"名稱\",\n  \"orderBySize\": \"大小\",\n  \"directoryNotFound\": \"此文件夾未找到\",\n  \"emptyFolder\": \"文件夾為空\",\n  \"deleteFile\": '您確定要刪除 \\\"{0}\\\"?',\n  \"invalidFileType\": \"選中的文件 \\\"{0}\\\" 非法，支持的文件類型為 {1}。\",\n  \"overwriteFile\": \"當前文件夾已存在文件名為 \\\"{0}\\\" 的文件，您確定要覆蓋麽？\",\n  \"dropFilesHere\": \"拖拽要上傳的文件到此處\",\n  \"search\": \"搜索\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"為真\",\n  \"isFalse\": \"為假\",\n  \"filter\": \"過濾\",\n  \"clear\": \"清除\",\n  \"operator\": \"運算符\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"顯示符合以下條件的行\",\n  \"isTrue\": \"為真\",\n  \"isFalse\": \"為假\",\n  \"filter\": \"過濾\",\n  \"clear\": \"清除\",\n  \"and\": \"並且\",\n  \"or\": \"或\",\n  \"selectValue\": \"-選擇-\",\n  \"operator\": \"運算符\",\n  \"value\": \"值\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"搜索\"\n});\n}\n\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"startswith\": \"開頭為\",\n    \"contains\": \"包含\",\n    \"doesnotcontain\": \"不包含\",\n    \"endswith\": \"結尾為\"\n  },\n  \"number\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"date\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"enums\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"startswith\": \"開頭為\",\n    \"contains\": \"包含\",\n    \"doesnotcontain\": \"不包含\",\n    \"endswith\": \"結尾為\"\n  },\n  \"number\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"date\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"enums\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\"\n  }\n});\n}\n\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"views\": {\n    \"day\": \"日\",\n    \"week\": \"周\",\n    \"month\": \"月\"\n  },\n  \"actions\": {\n    \"append\": \"添加任務\",\n    \"addChild\": \"添加子任務\",\n    \"insertBefore\": \"添加到前面\",\n    \"insertAfter\": \"添加到後面\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"取消\",\n    \"canceledit\": \"取消\",\n    \"create\": \"新增\",\n    \"destroy\": \"刪除\",\n    \"edit\": \"編輯\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"保存\",\n    \"select\": \"選擇\",\n    \"update\": \"更新\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"取消\",\n    \"confirmation\": \"確定要刪除嗎？\",\n    \"confirmDelete\": \"刪除\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"拖拽列標題到此處按列組合顯示\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"增加\",\n  \"downArrowText\": \"減少\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"顯示條目 {0} - {1} 共 {2}\",\n  \"empty\": \"沒有可顯示的記錄。\",\n  \"page\": \"頁\",\n  \"of\": \"共 {0}\",\n  \"itemsPerPage\": \"每頁\",\n  \"first\": \"首頁\",\n  \"last\": \"末頁\",\n  \"next\": \"下一頁\",\n  \"previous\": \"上一頁\",\n  \"refresh\": \"刷新\",\n  \"morePages\": \"更多...\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"拖放數據字段於此\",\n  \"columnFields\": \"拖放列字段於此\",\n  \"rowFields\": \"拖放行字段於此\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"從不\",\n    \"hourly\": \"每小時\",\n    \"daily\": \"每天\",\n    \"weekly\": \"每周\",\n    \"monthly\": \"每月\",\n    \"yearly\": \"每年\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"interval\": \" 小時\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"interval\": \" 天\"\n  },\n  \"weekly\": {\n    \"interval\": \" 周\",\n    \"repeatEvery\": \"重復周期: \",\n    \"repeatOn\": \"重復於:\"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"repeatOn\": \"重復於:\",\n    \"interval\": \" 月\",\n    \"day\": \"日期\"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"repeatOn\": \"重復於: \",\n    \"interval\": \" 年\",\n    \"of\": \" 月份: \"\n  },\n  \"end\": {\n    \"label\": \"截止時間:\",\n    \"mobileLabel\": \"截止時間\",\n    \"never\": \"從不\",\n    \"after\": \"重復 \",\n    \"occurrence\": \" 次後\",\n    \"on\": \"止於 \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"第一\",\n    \"second\": \"第二\",\n    \"third\": \"第三\",\n    \"fourth\": \"第四\",\n    \"last\": \"最後\"\n  },\n  \"weekdays\": {\n    \"day\": \"天\",\n    \"weekday\": \"工作日\",\n    \"weekend\": \"周末\"\n  }\n});\n}\n\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"today\": \"今天\",\n  \"save\": \"保存\",\n  \"cancel\": \"取消\",\n  \"destroy\": \"刪除\",\n  \"deleteWindowTitle\": \"刪除事件\",\n  \"ariaSlotLabel\": \"選擇從 {0:t} 到 {1:t}\",\n  \"ariaEventLabel\": \"{0} on {1:D} at {2:t}\",\n  \"views\": {\n    \"day\": \"日\",\n    \"week\": \"周\",\n    \"workWeek\": \"工作日\",\n    \"agenda\": \"日程\",\n    \"month\": \"月\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"刪除周期條目\",\n    \"deleteWindowOccurrence\": \"刪除當前事件\",\n    \"deleteWindowSeries\": \"刪除序列\",\n    \"editWindowTitle\": \"修改周期條目\",\n    \"editWindowOccurrence\": \"修改當前事件\",\n    \"editWindowSeries\": \"修改序列\"\n  },\n  \"editor\": {\n    \"title\": \"標題\",\n    \"start\": \"起始\",\n    \"end\": \"終止\",\n    \"allDayEvent\": \"全天事件\",\n    \"description\": \"描述\",\n    \"repeat\": \"重復\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"起始時區\",\n    \"endTimezone\": \"終止時區\",\n    \"separateTimezones\": \"使用獨立的起始和終止時區\",\n    \"timezoneEditorTitle\": \"時區\",\n    \"timezoneEditorButton\": \"時區\",\n    \"timezoneTitle\": \"選擇時區\",\n    \"noTimezone\": \"無\",\n    \"editorTitle\": \"事件\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"增加\",\n  \"decreaseButtonTitle\": \"減少\"\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"加載中...\",\n  \"requestFailed\": \"加載失敗\",\n  \"retry\": \"重試\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"選擇...\",\n  \"cancel\": \"取消\",\n  \"retry\": \"重試\",\n  \"remove\": \"移除\",\n  \"uploadSelectedFiles\": \"上傳文件\",\n  \"dropFilesHere\": \"拖拽要上傳的文件到此處\",\n  \"statusUploading\": \"上傳中\",\n  \"statusUploaded\": \"已上傳\",\n  \"statusWarning\": \"警告\",\n  \"statusFailed\": \"失敗\",\n  \"headerStatusUploading\": \"上傳...\",\n  \"headerStatusUploaded\": \"完成\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} 為必填項\",\n  \"pattern\": \"{0} 非法\",\n  \"min\": \"{0} 應該大於或等於 {1}\",\n  \"max\": \"{0} 應該小於或等於 {1}\",\n  \"step\": \"{0} 非法\",\n  \"email\": \"{0} 不是合法的郵件地址\",\n  \"url\": \"{0} 不是合法的URL\",\n  \"date\": \"{0} 不是合法的日期\"\n});\n}\n\n/* Spreadsheet messages */\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.borderPalette) {\n    kendo.spreadsheet.messages.borderPalette =\n        $.extend(true, kendo.spreadsheet.messages.borderPalette, {\n            \"allBorders\": \"所有邊框\",\n            \"insideBorders\": \"內邊框\",\n            \"insideHorizontalBorders\": \"水平內邊框\",\n            \"insideVerticalBorders\": \"垂直內邊框\",\n            \"outsideBorders\": \"外邊框\",\n            \"leftBorder\": \"左邊框\",\n            \"topBorder\": \"頂邊框\",\n            \"rightBorder\": \"右邊框\",\n            \"bottomBorder\": \"底邊框\",\n            \"noBorders\": \"沒有邊框\",\n            \"reset\": \"重設顏色\",\n            \"customColor\": \"自訂顏色...\",\n            \"apply\": \"生效\",\n            \"cancel\": \"取消\"\n        });\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.dialogs) {\n    kendo.spreadsheet.messages.dialogs =\n        $.extend(true, kendo.spreadsheet.messages.dialogs, {\n            \"apply\": \"生效\",\n            \"save\": \"儲存\",\n            \"cancel\": \"取消\",\n            \"remove\": \"移除\",\n            \"retry\": \"重試\",\n            \"revert\": \"還原\",\n            \"okText\": \"是\",\n            \"formatCellsDialog\": {\n                \"title\": \"格式\",\n                \"categories\": {\n                    \"number\": \"數字\",\n                    \"currency\": \"貨幣\",\n                    \"date\": \"日期\"\n                }\n            },\n            \"fontFamilyDialog\": {\n                \"title\": \"字體\"\n            },\n            \"fontSizeDialog\": {\n                \"title\": \"字體大小\"\n            },\n            \"bordersDialog\": {\n                \"title\": \"邊框\"\n            },\n            \"alignmentDialog\": {\n                \"title\": \"對齊\",\n                \"buttons\": {\n                    \"justtifyLeft\": \"向左對齊\",\n                    \"justifyCenter\": \"左右中央對齊\",\n                    \"justifyRight\": \"向右對齊\",\n                    \"justifyFull\": \"兩端對齊\",\n                    \"alignTop\": \"向頂對齊\",\n                    \"alignMiddle\": \"上下中央對齊\",\n                    \"alignBottom\": \"向底對齊\"\n                }\n            },\n            \"mergeDialog\": {\n                \"title\": \"合併儲存格\",\n                \"buttons\": {\n                    \"mergeCells\": \"全部合併\",\n                    \"mergeHorizontally\": \"水平合併\",\n                    \"mergeVertically\": \"垂直合併\",\n                    \"unmerge\": \"取消合併\"\n                }\n            },\n            \"freezeDialog\": {\n                \"title\": \"鎖定儲存格\",\n                \"buttons\": {\n                    \"freezePanes\": \"鎖定儲存格\",\n                    \"freezeRows\": \"鎖定行\",\n                    \"freezeColumns\": \"鎖定列\",\n                    \"unfreeze\": \"取消鎖定儲存格\"\n                }\n            },\n            \"validationDialog\": {\n                \"title\": \"數據驗證\",\n                \"hintMessage\": \"請輪入有效的 {0} 數值 {1}.\",\n                \"hintTitle\": \"驗證 {0}\",\n                \"criteria\": {\n                    \"any\": \"任何數值\",\n                    \"number\": \"數字\",\n                    \"text\": \"文字\",\n                    \"date\": \"日期\",\n                    \"custom\": \"自訂公式\",\n                    \"list\": \"列表\"\n                },\n                \"comparers\": {\n                    \"greaterThan\": \"大於\",\n                    \"lessThan\": \"小於\",\n                    \"between\": \"在 ... 之間\",\n                    \"notBetween\": \"不在 ... 之間\",\n                    \"equalTo\": \"等於\",\n                    \"notEqualTo\": \"不等於\",\n                    \"greaterThanOrEqualTo\": \"大於或等於\",\n                    \"lessThanOrEqualTo\": \"小於或等於\"\n                },\n                \"comparerMessages\": {\n                    \"greaterThan\": \"大於 {0}\",\n                    \"lessThan\": \"小於 {0}\",\n                    \"between\": \"在 {0} 和 {1} 之間\",\n                    \"notBetween\": \"不在 {0} 和 {1} 之間\",\n                    \"equalTo\": \"等於 {0}\",\n                    \"notEqualTo\": \"不等於 {0}\",\n                    \"greaterThanOrEqualTo\": \"大於或等於 {0}\",\n                    \"lessThanOrEqualTo\": \"小於或等於 {0}\",\n                    \"custom\": \"乎合公式: {0}\"\n                },\n                \"labels\": {\n                    \"criteria\": \"准則\",\n                    \"comparer\": \"比較的\",\n                    \"min\": \"最小\",\n                    \"max\": \"最大\",\n                    \"value\": \"數值\",\n                    \"start\": \"開始\",\n                    \"end\": \"完結\",\n                    \"onInvalidData\": \"在數據無效時\",\n                    \"rejectInput\": \"拒絕輸入\",\n                    \"showWarning\": \"顯示警告\",\n                    \"showHint\": \"顯示提示\",\n                    \"hintTitle\": \"提示標題\",\n                    \"hintMessage\": \"提示訊息\",\n                    \"ignoreBlank\": \"忽略空白\"\n                },\n                \"placeholders\": {\n                    \"typeTitle\": \"種類標題\",\n                    \"typeMessage\": \"種類訊息\"\n                }\n            },\n            \"exportAsDialog\": {\n                \"title\": \"滙出...\",\n                \"labels\": {\n                    \"fileName\": \"檔案名稱\",\n                    \"saveAsType\": \"另存種類\",\n                    \"exportArea\": \"滙出\",\n                    \"paperSize\": \"紙張大小\",\n                    \"margins\": \"邊距\",\n                    \"orientation\": \"方向\",\n                    \"print\": \"列印\",\n                    \"guidelines\": \"參考線\",\n                    \"center\": \"置中\",\n                    \"horizontally\": \"水平地\",\n                    \"vertically\": \"垂直地\"\n                }\n            },\n            \"modifyMergedDialog\": {\n                \"errorMessage\": \"不能轉換部份已合併的儲存格\"\n            },\n            \"useKeyboardDialog\": {\n                \"title\": \"Copying and pasting\",\n                \"errorMessage\": \"These actions cannot be invoked through the menu. Please use the keyboard shortcuts instead:\",\n                \"labels\": {\n                    \"forCopy\": \"for copy\",\n                    \"forCut\": \"for cut\",\n                    \"forPaste\": \"for paste\"\n                }\n            },\n            \"unsupportedSelectionDialog\": {\n                \"errorMessage\": \"That action cannot be performed on multiple selection.\"\n            }\n        });\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.filterMenu) {\n    kendo.spreadsheet.messages.filterMenu =\n        $.extend(true, kendo.spreadsheet.messages.filterMenu, {\n            \"sortAscending\": \"Sort range A to Z\",\n            \"sortDescending\": \"Sort range Z to A\",\n            \"filterByValue\": \"Filter by value\",\n            \"filterByCondition\": \"Filter by condition\",\n            \"apply\": \"Apply\",\n            \"search\": \"Search\",\n            \"addToCurrent\": \"Add to current selection\",\n            \"clear\": \"Clear\",\n            \"blanks\": \"(Blanks)\",\n            \"operatorNone\": \"None\",\n            \"and\": \"AND\",\n            \"or\": \"OR\",\n            \"operators\": {\n                \"string\": {\n                    \"contains\": \"Text contains\",\n                    \"doesnotcontain\": \"Text does not contain\",\n                    \"startswith\": \"Text starts with\",\n                    \"endswith\": \"Text ends with\"\n                },\n                \"date\": {\n                    \"eq\": \"Date is\",\n                    \"neq\": \"Date is not\",\n                    \"lt\": \"Date is before\",\n                    \"gt\": \"Date is after\"\n                },\n                \"number\": {\n                    \"eq\": \"Is equal to\",\n                    \"neq\": \"Is not equal to\",\n                    \"gte\": \"Is greater than or equal to\",\n                    \"gt\": \"Is greater than\",\n                    \"lte\": \"Is less than or equal to\",\n                    \"lt\": \"Is less than\"\n                }\n            }\n        });\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.toolbar) {\n    kendo.spreadsheet.messages.toolbar =\n        $.extend(true, kendo.spreadsheet.messages.toolbar, {\n            \"addColumnLeft\": \"Add column left\",\n            \"addColumnRight\": \"Add column right\",\n            \"addRowAbove\": \"Add row above\",\n            \"addRowBelow\": \"Add row below\",\n            \"alignment\": \"Alignment\",\n            \"alignmentButtons\": {\n                \"justtifyLeft\": \"Align left\",\n                \"justifyCenter\": \"Center\",\n                \"justifyRight\": \"Align right\",\n                \"justifyFull\": \"Justify\",\n                \"alignTop\": \"Align top\",\n                \"alignMiddle\": \"Align middle\",\n                \"alignBottom\": \"Align bottom\"\n            },\n            \"backgroundColor\": \"Background\",\n            \"bold\": \"Bold\",\n            \"borders\": \"Borders\",\n            \"colorPicker\": {\n                \"reset\": \"Reset color\",\n                \"customColor\": \"Custom color...\"\n            },\n            \"copy\": \"Copy\",\n            \"cut\": \"Cut\",\n            \"deleteColumn\": \"Delete column\",\n            \"deleteRow\": \"Delete row\",\n            \"excelImport\": \"Import from Excel...\",\n            \"filter\": \"Filter\",\n            \"fontFamily\": \"Font\",\n            \"fontSize\": \"Font size\",\n            \"format\": \"Custom format...\",\n            \"formatTypes\": {\n                \"automatic\": \"Automatic\",\n                \"number\": \"Number\",\n                \"percent\": \"Percent\",\n                \"financial\": \"Financial\",\n                \"currency\": \"Currency\",\n                \"date\": \"Date\",\n                \"time\": \"Time\",\n                \"dateTime\": \"Date time\",\n                \"duration\": \"Duration\",\n                \"moreFormats\": \"More formats...\"\n            },\n            \"formatDecreaseDecimal\": \"Decrease decimal\",\n            \"formatIncreaseDecimal\": \"Increase decimal\",\n            \"freeze\": \"Freeze panes\",\n            \"freezeButtons\": {\n                \"freezePanes\": \"Freeze panes\",\n                \"freezeRows\": \"Freeze rows\",\n                \"freezeColumns\": \"Freeze columns\",\n                \"unfreeze\": \"Unfreeze panes\"\n            },\n            \"italic\": \"Italic\",\n            \"merge\": \"Merge cells\",\n            \"mergeButtons\": {\n                \"mergeCells\": \"Merge all\",\n                \"mergeHorizontally\": \"Merge horizontally\",\n                \"mergeVertically\": \"Merge vertically\",\n                \"unmerge\": \"Unmerge\"\n            },\n            \"open\": \"Open...\",\n            \"paste\": \"Paste\",\n            \"quickAccess\": {\n                \"redo\": \"Redo\",\n                \"undo\": \"Undo\"\n            },\n            \"saveAs\": \"Save As...\",\n            \"sortAsc\": \"Sort ascending\",\n            \"sortDesc\": \"Sort descending\",\n            \"sortButtons\": {\n                \"sortSheetAsc\": \"Sort sheet A to Z\",\n                \"sortSheetDesc\": \"Sort sheet Z to A\",\n                \"sortRangeAsc\": \"Sort range A to Z\",\n                \"sortRangeDesc\": \"Sort range Z to A\"\n            },\n            \"textColor\": \"Text Color\",\n            \"textWrap\": \"Wrap text\",\n            \"underline\": \"Underline\",\n            \"validation\": \"資料驗證...\"\n        });\n}\n\nif (kendo.spreadsheet && kendo.spreadsheet.messages.view) {\n    kendo.spreadsheet.messages.view =\n        $.extend(true, kendo.spreadsheet.messages.view, {\n            \"errors\": {\n                \"shiftingNonblankCells\": \"Cannot insert cells due to data loss possibility. Select another insert location or delete the data from the end of your worksheet.\",\n                \"filterRangeContainingMerges\": \"Cannot create a filter within a range containing merges\",\n                \"validationError\": \"The value that you entered violates the validation rules set on the cell.\"\n            },\n            \"tabs\": {\n                \"home\": \"主頁\",\n                \"insert\": \"插入\",\n                \"data\": \"資料\"\n            }\n        });\n}\n})(window.kendo.jQuery);\n}));"]}