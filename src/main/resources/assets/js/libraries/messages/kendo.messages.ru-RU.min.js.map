{"version": 3, "sources": ["messages/kendo.messages.ru-RU.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gte", "gt", "lte", "lt", "neq", "number", "string", "endswith", "startswith", "contains", "doesnotcontain", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "filter", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "create", "destroy", "canceledit", "update", "edit", "excel", "pdf", "select", "cancel", "save", "editable", "confirmation", "cancelDelete", "confirmDelete", "noRecords", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "clear", "isFalse", "isTrue", "operator", "and", "info", "selectValue", "or", "value", "FilterMultiCheck", "search", "Groupable", "Editor", "bold", "cleanFormatting", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "backColor", "deleteFile", "directoryNotFound", "dropFilesHere", "emptyFolder", "foreColor", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Upload", "localization", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "DateInput", "year", "hour", "minute", "dayperiod", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,QACNC,IAAO,kBACPC,GAAM,QACNC,IAAO,eACPC,GAAM,KACNC,IAAO,YAETC,QACEN,GAAM,QACNC,IAAO,mBACPC,GAAM,SACNC,IAAO,mBACPC,GAAM,SACNC,IAAO,YAETE,QACEC,SAAY,kBACZR,GAAM,QACNK,IAAO,WACPI,WAAc,mBACdC,SAAY,cACZC,eAAkB,eAEpBC,OACEZ,GAAM,QACNK,IAAO,eAOPb,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,QACNC,IAAO,kBACPC,GAAM,QACNC,IAAO,eACPC,GAAM,KACNC,IAAO,YAETC,QACEN,GAAM,QACNC,IAAO,mBACPC,GAAM,SACNC,IAAO,mBACPC,GAAM,SACNC,IAAO,YAETE,QACEC,SAAY,kBACZR,GAAM,QACNK,IAAO,WACPI,WAAc,mBACdC,SAAY,cACZC,eAAkB,eAEpBC,OACEZ,GAAM,QACNK,IAAO,eAOPb,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,UACXC,cAAiB,4BACjBC,eAAkB,yBAClBC,SAAY,qBACZC,KAAQ,SACRC,KAAQ,gBACRC,OAAU,iBACVC,OAAU,iBAMR/B,MAAMC,GAAG+B,mBACbhC,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,UACzDU,OACEC,SAAY,OACZC,YAAe,qBAEjBC,KACEC,MAAS,QACTC,WAAc,SACdC,MAAS,SACTC,MAAS,UACTC,GAAM,IACNC,YAAe,aAEjBC,aACEV,MAAS,YACTW,QAAW,aACXJ,MAAS,UACTK,OAAU,cACVC,OAAU,YAEZF,SACEG,IAAO,OACPb,SAAY,SACZC,YAAe,oBACfa,SAAY,eAEdC,iBACEC,MAAS,SACTC,OAAU,YACVC,KAAQ,YACRC,OAAU,SACVC,MAAS,UAEXT,QACEV,YAAe,oBACfa,SAAY,cACZd,SAAY,UAEdY,QACES,GAAM,KACNpB,YAAe,oBACfa,SAAY,cACZd,SAAY,QAEdsB,UACET,IAAO,OACPU,QAAW,SACXC,QAAW,eAOX1D,MAAMC,GAAG0D,OACb3D,MAAMC,GAAG0D,KAAKxD,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0D,KAAKxD,UAAUC,QAAQmB,UAC7CqC,UACEC,OAAU,WACVC,QAAW,UACXC,WAAc,SACdC,OAAU,WACVC,KAAQ,WACRC,MAAS,kBACTC,IAAO,gBACPC,OAAU,UACVC,OAAU,qBACVC,KAAQ,uBAEVC,UACEC,aAAgB,6CAChBC,aAAgB,SAChBC,cAAiB,WAEnBC,UAAa,2BAMX3E,MAAMC,GAAG2E,QACb5E,MAAMC,GAAG2E,MAAMzE,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2E,MAAMzE,UAAUC,QAAQmB,UAC9CsD,SAAY,MACZC,KAAQ,WACRC,QAAW,qCACXxB,GAAM,SACNyB,MAAS,8BACTC,QAAW,WACX/B,MAAS,+BACTgC,aAAgB,wBAChB9B,KAAQ,uBACR+B,KAAQ,kCACRC,SAAY,iCACZC,UAAa,oBAMXrF,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnDQ,OAAU,cACVuD,MAAS,WACTC,QAAW,OACXC,OAAU,SACVC,SAAY,cAMVzF,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDQ,OAAU,cACV2D,IAAO,IACPJ,MAAS,WACTK,KAAQ,uBACRC,YAAe,aACfL,QAAW,OACXC,OAAU,SACVK,GAAM,MACNxB,OAAU,SACVoB,SAAY,WACZK,MAAS,cAMP9F,MAAMC,GAAG8F,mBACb/F,MAAMC,GAAG8F,iBAAiB5F,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8F,iBAAiB5F,UAAUC,QAAQmB,UACzDyE,OAAU,WAMRhG,MAAMC,GAAGgG,YACbjG,MAAMC,GAAGgG,UAAU9F,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgG,UAAU9F,UAAUC,QAAQmB,UAClDyD,MAAS,oFAMPhF,MAAMC,GAAGiG,SACblG,MAAMC,GAAGiG,OAAO/F,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiG,OAAO/F,UAAUC,QAAQmB,UAC/C4E,KAAQ,aACRC,gBAAmB,kBACnBC,WAAc,uBACdC,SAAY,QACZC,gBAAmB,0BACnBC,SAAY,wBACZC,gBAAmB,2BACnBC,YAAe,oBACfC,OAAU,mBACVC,WAAc,gBACdC,YAAe,cACfC,kBAAqB,sBACrBC,oBAAuB,sBACvBC,OAAU,SACVC,cAAiB,YACjBC,YAAe,YACfC,YAAe,QACfC,aAAgB,SAChBC,QAAW,mBACXC,cAAiB,cACjBC,OAAU,QACVC,UAAa,cACbC,YAAe,cACfC,UAAa,eACbC,OAAU,sBACVC,sBAAyB,MACzBC,aAAgB,SAChBC,aAAgB,WAChBC,aAAgB,uBAChBC,gBAAmB,YACnBC,oBAAuB,uBACvBC,SAAY,QACZC,YAAe,wBACfC,eAAkB,YAClBpC,OAAU,QACVqC,YAAe,mBACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,wBACfC,YAAe,uBACfC,aAAgB,kBAChBC,UAAa,iBACbC,UAAa,YACbC,WAAc,wCACdC,kBAAqB,oCACrBC,cAAiB,qCACjBC,YAAe,eACfC,UAAa,OACbC,gBAAmB,iEACnBC,QAAW,kBACXC,YAAe,MACfC,YAAe,SACfC,cAAiB,iFACjBC,WAAc,YACdC,WAAc,SACdC,SAAY,gBACZC,aAAgB,WAChBC,WAAc,mBAMZ3J,MAAMC,GAAG2J,SACb5J,MAAMC,GAAG2J,OAAOzJ,UAAUC,QAAQyJ,aAClC/J,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,OAAOzJ,UAAUC,QAAQyJ,cAC/CxF,OAAU,oBACV0E,cAAiB,qCACjBe,OAAU,UACVC,MAAS,YACT3F,OAAU,aACV4F,aAAgB,oBAChBC,eAAkB,YAClBC,gBAAmB,cACnBC,oBAAuB,4BACvBC,qBAAwB,SACxBC,sBAAyB,oBAMvBrK,MAAMC,GAAGqK,YACbtK,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqK,UAAUnK,UAAUC,QAAQmB,UAClDgJ,OAAU,YACVlG,OAAU,SACVE,UACEC,aAAgB,+CAElBjE,KAAQ,OACRuD,QAAW,UACX0G,QACEC,YAAe,uBACfC,YAAe,WACfC,YAAe,UACfvI,IAAO,YACPwI,YAAe,yBACfC,OAAU,aACVC,kBAAqB,uDACrBC,MAAS,SACTC,cAAiB,sBACjBC,SAAY,IACZC,qBAAwB,eACxBC,oBAAuB,gBACvBC,MAAS,YACTC,WAAc,sBAEhBC,MAAS,UACTC,oBACEC,gBAAmB,2EACnBC,uBAA0B,sBAC1BC,mBAAsB,mBACtBC,kBAAqB,gCACrBC,cAAiB,yEACjBC,qBAAwB,2BACxBC,iBAAoB,oBACpBC,gBAAmB,yBAErBzH,KAAQ,YACR0H,KAAQ,QACRC,MAAS,UACTC,OACEC,OAAU,WACVpJ,IAAO,OACPqJ,MAAS,QACTC,KAAQ,SACRC,SAAY,kBAEdX,kBAAqB,kBACrBY,YAAe,uBACfC,YAAe,oCAMbxM,MAAMC,GAAGwM,YACbzM,MAAMC,GAAGwM,UAAUtM,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,UAAUtM,UAAUC,QAAQmB,UAClDmL,SAAY,iBACZC,QAAW,eACXC,IAAO,uCACPC,IAAO,uCACPC,KAAQ,eACRC,MAAS,0BACTC,IAAO,wBACPzM,KAAQ,4BAMNP,MAAMC,GAAGgN,SACbjN,MAAMC,GAAGgN,OAAO9M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgN,OAAO9M,UAAUC,QAAQyJ,cAC/CqD,MAAS,aAMPlN,MAAMC,GAAGkN,QACbnN,MAAMC,GAAGkN,MAAMhN,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkN,MAAMhN,UAAUC,QAAQyJ,cAC9CuD,OAAU,QAMRpN,MAAMC,GAAGoN,UACbrN,MAAMC,GAAGoN,QAAQlN,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoN,QAAQlN,UAAUC,QAAQyJ,cAChDuD,OAAU,KACV/I,OAAU,YAKRrE,MAAMC,GAAGqN,SACbtN,MAAMC,GAAGqN,OAAOnN,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqN,OAAOnN,UAAUC,QAAQyJ,cAC/CuD,OAAU,KACV/I,OAAU,YAKRrE,MAAMC,GAAGsN,YACbvN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQmB,UAClDiM,KAAQ,MACRpB,MAAS,QACTrJ,IAAO,OACPU,QAAW,cACXgK,KAAQ,MACRC,OAAU,SACVrK,OAAU,UACVsK,UAAa,YAIZC,OAAO5N,MAAM6N", "file": "kendo.messages.ru-RU.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"равна\",\n    \"gte\": \"после или равна\",\n    \"gt\": \"после\",\n    \"lte\": \"до или равна\",\n    \"lt\": \"до\",\n    \"neq\": \"не равна\"\n  },\n  \"number\": {\n    \"eq\": \"равно\",\n    \"gte\": \"больше или равно\",\n    \"gt\": \"больше\",\n    \"lte\": \"меньше или равно\",\n    \"lt\": \"меньше\",\n    \"neq\": \"не равно\"\n  },\n  \"string\": {\n    \"endswith\": \"оканчивается на\",\n    \"eq\": \"равно\",\n    \"neq\": \"не равно\",\n    \"startswith\": \"начинающимися на\",\n    \"contains\": \"содержащими\",\n    \"doesnotcontain\": \"не содержит\"\n  },\n  \"enums\": {\n    \"eq\": \"равно\",\n    \"neq\": \"не равно\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"равна\",\n    \"gte\": \"после или равна\",\n    \"gt\": \"после\",\n    \"lte\": \"до или равна\",\n    \"lt\": \"до\",\n    \"neq\": \"не равна\"\n  },\n  \"number\": {\n    \"eq\": \"равно\",\n    \"gte\": \"больше или равно\",\n    \"gt\": \"больше\",\n    \"lte\": \"меньше или равно\",\n    \"lt\": \"меньше\",\n    \"neq\": \"не равно\"\n  },\n  \"string\": {\n    \"endswith\": \"оканчивается на\",\n    \"eq\": \"равно\",\n    \"neq\": \"не равно\",\n    \"startswith\": \"начинающимися на\",\n    \"contains\": \"содержащими\",\n    \"doesnotcontain\": \"не содержит\"\n  },\n  \"enums\": {\n    \"eq\": \"равно\",\n    \"neq\": \"не равно\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Столбцы\",\n  \"sortAscending\": \"Сортировка по возрастанию\",\n  \"sortDescending\": \"Сортировка по убыванию\",\n  \"settings\": \"Параметры столбцов\",\n  \"done\": \"Готово\",\n  \"lock\": \"Заблокировать\",\n  \"unlock\": \"Разблокировать\",\n  \"filter\": \"Фильтровать\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"дней\",\n    \"repeatEvery\": \"Повторять каждые:\"\n  },\n  \"end\": {\n    \"after\": \"После\",\n    \"occurrence\": \"входит\",\n    \"label\": \"Конец:\",\n    \"never\": \"Никогда\",\n    \"on\": \"В\",\n    \"mobileLabel\": \"Окончание\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Ежедневно\",\n    \"monthly\": \"Ежемесячно\",\n    \"never\": \"Никогда\",\n    \"weekly\": \"Еженедельно\",\n    \"yearly\": \"Ежегодно\"\n  },\n  \"monthly\": {\n    \"day\": \"День\",\n    \"interval\": \"месяцы\",\n    \"repeatEvery\": \"Повторять каждый:\",\n    \"repeatOn\": \"Повторение:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"первый\",\n    \"fourth\": \"четвертый\",\n    \"last\": \"последний\",\n    \"second\": \"второй\",\n    \"third\": \"третий\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Повторять каждую:\",\n    \"repeatOn\": \"Повторение:\",\n    \"interval\": \"неделя\"\n  },\n  \"yearly\": {\n    \"of\": \"из\",\n    \"repeatEvery\": \"Повторять каждый:\",\n    \"repeatOn\": \"Повторение:\",\n    \"interval\": \"годы\"\n  },\n  \"weekdays\": {\n    \"day\": \"день\",\n    \"weekday\": \"будний\",\n    \"weekend\": \"выходной\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"create\": \"Добавить\",\n    \"destroy\": \"Удалить\",\n    \"canceledit\": \"Отмена\",\n    \"update\": \"Обновить\",\n    \"edit\": \"Изменить\",\n    \"excel\": \"Экспорт в Excel\",\n    \"pdf\": \"Экспорт в PDF\",\n    \"select\": \"Выбрать\",\n    \"cancel\": \"Отменить изменения\",\n    \"save\": \"Сохранить изменения\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Вы уверены, что хотите удалить эту запись?\",\n    \"cancelDelete\": \"Отмена\",\n    \"confirmDelete\": \"Удалить\"\n  },\n  \"noRecords\": \"Нет записей доступны.\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Все\",\n  \"page\": \"Страница\",\n  \"display\": \"Отображены записи {0} - {1} из {2}\",\n  \"of\": \"из {0}\",\n  \"empty\": \"Нет записей для отображения\",\n  \"refresh\": \"Обновить\",\n  \"first\": \"Вернуться на первую страницу\",\n  \"itemsPerPage\": \"элементов на странице\",\n  \"last\": \"К последней странице\",\n  \"next\": \"Перейдите на следующую страницу\",\n  \"previous\": \"Перейти на предыдущую страницу\",\n  \"morePages\": \"Больше страниц\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"фильтровать\",\n  \"clear\": \"очистить\",\n  \"isFalse\": \"ложь\",\n  \"isTrue\": \"истина\",\n  \"operator\": \"Оператор\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"фильтровать\",\n  \"and\": \"И\",\n  \"clear\": \"очистить\",\n  \"info\": \"Строки со значениями\",\n  \"selectValue\": \"-выберите-\",\n  \"isFalse\": \"ложь\",\n  \"isTrue\": \"истина\",\n  \"or\": \"или\",\n  \"cancel\": \"Отмена\",\n  \"operator\": \"Оператор\",\n  \"value\": \"Значение\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Поиск\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Переместите сюда заголовок столбца, чтобы сгрупировать записи по этому столбцу\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Полужирный\",\n  \"cleanFormatting\": \"Очистить формат\",\n  \"createLink\": \"Вставить гиперссылку\",\n  \"fontName\": \"Шрифт\",\n  \"fontNameInherit\": \"(шрифт как в документе)\",\n  \"fontSize\": \"Выбрать размер шрифта\",\n  \"fontSizeInherit\": \"(размер как в документе)\",\n  \"formatBlock\": \"Текст изображения\",\n  \"indent\": \"Увеличить отступ\",\n  \"insertHtml\": \"Вставить HTML\",\n  \"insertImage\": \"Изображение\",\n  \"insertOrderedList\": \"Нумерованный список\",\n  \"insertUnorderedList\": \"Маркированныйсписок\",\n  \"italic\": \"Курсив\",\n  \"justifyCenter\": \"По центру\",\n  \"justifyFull\": \"По ширине\",\n  \"justifyLeft\": \"Влево\",\n  \"justifyRight\": \"Вправо\",\n  \"outdent\": \"Уменьшить отступ\",\n  \"strikethrough\": \"Зачеркнутый\",\n  \"styles\": \"Стиль\",\n  \"subscript\": \"Под строкой\",\n  \"superscript\": \"Над строкой\",\n  \"underline\": \"Подчеркнутый\",\n  \"unlink\": \"Удалить гиперссылку\",\n  \"dialogButtonSeparator\": \"или\",\n  \"dialogCancel\": \"Отмена\",\n  \"dialogInsert\": \"Вставить\",\n  \"imageAltText\": \"Альтернативный текст\",\n  \"imageWebAddress\": \"Веб адрес\",\n  \"linkOpenInNewWindow\": \"Открыть в новом окне\",\n  \"linkText\": \"Текст\",\n  \"linkToolTip\": \"Всплывающая подсказка\",\n  \"linkWebAddress\": \"Веб адрес\",\n  \"search\": \"Поиск\",\n  \"createTable\": \"Вставить таблицу\",\n  \"addColumnLeft\": \"Добавить столбец слева\",\n  \"addColumnRight\": \"Добавить столбец справа\",\n  \"addRowAbove\": \"Добавить стороку выше\",\n  \"addRowBelow\": \"Добавить строку ниже\",\n  \"deleteColumn\": \"Удалить столбец\",\n  \"deleteRow\": \"Удалить строку\",\n  \"backColor\": \"Цвет фона\",\n  \"deleteFile\": \"Вы уверены, что хотите удалить \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"Каталог с таким именем не найден.\",\n  \"dropFilesHere\": \"для загрузки перетащите файлы сюда\",\n  \"emptyFolder\": \"Пустая папка\",\n  \"foreColor\": \"Цвет\",\n  \"invalidFileType\": \"Выбранный файл \\\"{0}\\\" не верен. Поддерживаемые типы файлов {1}.\",\n  \"orderBy\": \"Упорядочить по:\",\n  \"orderByName\": \"Имя\",\n  \"orderBySize\": \"Размер\",\n  \"overwriteFile\": \"'Файл с именем \\\"{0}\\\" уже существует в этой папке. Вы хотите перезаписать его?\",\n  \"uploadFile\": \"Загрузить\",\n  \"formatting\": \"Формат\",\n  \"viewHtml\": \"Просмотр HTML\",\n  \"dialogUpdate\": \"Обновить\",\n  \"insertFile\": \"Вставить файл\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Отменить загрузку\",\n  \"dropFilesHere\": \"перетащите сюда файлы для загрузки\",\n  \"remove\": \"Удалить\",\n  \"retry\": \"Повторить\",\n  \"select\": \"Выбрать...\",\n  \"statusFailed\": \"загрузка прервана\",\n  \"statusUploaded\": \"загружено\",\n  \"statusUploading\": \"загружается\",\n  \"uploadSelectedFiles\": \"Загрузить выбранные файлы\",\n  \"headerStatusUploaded\": \"Готово\",\n  \"headerStatusUploading\": \"Загружается...\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"весь день\",\n  \"cancel\": \"Отмена\",\n  \"editable\": {\n    \"confirmation\": \"Вы уверены, что хотите удалить это событие?\"\n  },\n  \"date\": \"Дата\",\n  \"destroy\": \"Удалить\",\n  \"editor\": {\n    \"allDayEvent\": \"Событие на весь день\",\n    \"description\": \"Описание\",\n    \"editorTitle\": \"Событие\",\n    \"end\": \"Окончание\",\n    \"endTimezone\": \"Часовой поис окончания\",\n    \"repeat\": \"Повторение\",\n    \"separateTimezones\": \"Для начала и окончания используйте свой часовой пояс\",\n    \"start\": \"Начало\",\n    \"startTimezone\": \"Часовой пояс начала\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Часовой пояс\",\n    \"timezoneEditorTitle\": \"Часовые пояса\",\n    \"title\": \"Заголовок\",\n    \"noTimezone\": \"Без часового пояса\"\n  },\n  \"event\": \"Событие\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Вы хотите удалить только это событие или весь ряд повторяющихся событий?\",\n    \"deleteWindowOccurrence\": \"Удалить это событие\",\n    \"deleteWindowSeries\": \"Удалить весь ряд\",\n    \"deleteWindowTitle\": \"Удалить повторяющееся событие\",\n    \"editRecurring\": \"Вы хотите внести изменение только в это событие или изменить весь ряд?\",\n    \"editWindowOccurrence\": \"Изменить текущее событие\",\n    \"editWindowSeries\": \"Изменить весь ряд\",\n    \"editWindowTitle\": \"Изменить одно событие\"\n  },\n  \"save\": \"Сохранить\",\n  \"time\": \"время\",\n  \"today\": \"Сегодня\",\n  \"views\": {\n    \"agenda\": \"Повестка\",\n    \"day\": \"День\",\n    \"month\": \"Месяц\",\n    \"week\": \"Неделя\",\n    \"workWeek\": \"Рабочая неделя\"\n  },\n  \"deleteWindowTitle\": \"Удалить событие\",\n  \"showFullDay\": \"Показывать весь день\",\n  \"showWorkDay\": \"Показывать только рабочие часы\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} обязателен\",\n  \"pattern\": \"{0} не верен\",\n  \"min\": \"{0} должен быть больше или равен {1}\",\n  \"max\": \"{0} должен быть меньше или равен {1}\",\n  \"step\": \"{0} не верен\",\n  \"email\": \"{0} не корректный email\",\n  \"url\": \"{0} не корректный URL\",\n  \"date\": \"{0} не корректная дата\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Закрыть\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"ОК\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"ОК\",\n  \"cancel\": \"Отмена\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"ОК\",\n  \"cancel\": \"Отмена\"\n});\n}\n\n/* DateInput */\nif (kendo.ui.DateInput) {\nkendo.ui.DateInput.prototype.options.messages =\n$.extend(true, kendo.ui.DateInput.prototype.options.messages, {\n  \"year\": \"год\",\n  \"month\": \"месяц\",\n  \"day\": \"день\",\n  \"weekday\": \"день недели\",\n  \"hour\": \"час\",\n  \"minute\": \"минута\",\n  \"second\": \"секунда\",\n  \"dayperiod\": \"AM/PM\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}