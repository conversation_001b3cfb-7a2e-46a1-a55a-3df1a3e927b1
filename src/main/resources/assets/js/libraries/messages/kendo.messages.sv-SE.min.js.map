{"version": 3, "sources": ["messages/kendo.messages.sv-SE.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "clear", "filter", "isFalse", "isTrue", "operator", "and", "info", "or", "selectValue", "cancel", "value", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "Upload", "localization", "retry", "remove", "uploadSelectedFiles", "dropFilesHere", "statusFailed", "statusUploaded", "statusUploading", "headerStatusUploaded", "headerStatusUploading", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "search", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,iBACNC,IAAO,4BACPC,IAAO,oBAETC,QACEN,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,eACNC,IAAO,0BACPC,IAAO,oBAETE,QACEC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,aACZV,GAAM,cACNK,IAAO,mBACPM,WAAc,cAEhBC,OACEZ,GAAM,cACNK,IAAO,uBAOPb,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,iBACNC,IAAO,4BACPC,IAAO,oBAETC,QACEN,GAAM,cACNC,GAAM,eACNC,IAAO,0BACPC,GAAM,eACNC,IAAO,0BACPC,IAAO,oBAETE,QACEC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,aACZV,GAAM,cACNK,IAAO,mBACPM,WAAc,cAEhBC,OACEZ,GAAM,cACNK,IAAO,uBAOPb,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,WACXC,cAAiB,mBACjBC,eAAkB,mBAClBC,SAAY,sBACZC,KAAQ,OACRC,KAAQ,MACRC,OAAU,aAMR9B,MAAMC,GAAG8B,mBACb/B,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,UACzDS,OACEC,SAAY,UACZC,YAAe,iBAEjBC,KACEC,MAAS,QACTC,WAAc,gBACdC,MAAS,OACTC,MAAS,QACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,QACTW,QAAW,UACXJ,MAAS,QACTK,OAAU,SACVC,OAAU,UAEZF,SACEG,IAAO,MACPb,SAAY,WACZC,YAAe,gBACfa,SAAY,cAEdC,iBACEC,MAAS,QACTC,OAAU,SACVC,KAAQ,OACRC,OAAU,SACVC,MAAS,SAEXT,QACEV,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdY,QACES,GAAM,KACNpB,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdsB,UACET,IAAO,MACPU,QAAW,UACXC,QAAW,kBAOXzD,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnDmC,MAAS,QACTC,OAAU,WACVC,QAAW,YACXC,OAAU,UACVC,SAAY,cAMV9D,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDwC,IAAO,MACPL,MAAS,QACTC,OAAU,WACVK,KAAQ,yBACRJ,QAAW,YACXC,OAAU,UACVI,GAAM,QACNC,YAAe,SACfC,OAAU,SACVL,SAAY,WACZM,MAAS,WAMPpE,MAAMC,GAAGoE,OACbrE,MAAMC,GAAGoE,KAAKlE,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoE,KAAKlE,UAAUC,QAAQmB,UAC7C+C,UACEC,WAAc,SACdJ,OAAU,mBACVK,OAAU,iBACVC,QAAW,SACXC,KAAQ,QACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,kBACRC,OAAU,OACVC,OAAU,SAEZC,UACEC,aAAgB,gDAChBC,aAAgB,SAChBC,cAAiB,aAOjBnF,MAAMC,GAAGmF,YACbpF,MAAMC,GAAGmF,UAAUjF,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmF,UAAUjF,UAAUC,QAAQmB,UAClD8D,MAAS,6DAMPrF,MAAMC,GAAGqF,QACbtF,MAAMC,GAAGqF,MAAMnF,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqF,MAAMnF,UAAUC,QAAQmB,UAC9CgE,SAAY,MACZC,QAAW,0BACXH,MAAS,wBACTpC,MAAS,uBACTwC,aAAgB,kBAChBtC,KAAQ,sBACRuC,KAAQ,qBACRpC,GAAM,SACNqC,KAAQ,OACRC,SAAY,0BACZC,QAAW,YACXC,UAAa,gBAMX9F,MAAMC,GAAG8F,SACb/F,MAAMC,GAAG8F,OAAO5F,UAAUC,QAAQ4F,aAClClG,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8F,OAAO5F,UAAUC,QAAQ4F,cAC/C7B,OAAU,SACV8B,MAAS,cACTnB,OAAU,UACVoB,OAAU,UACVC,oBAAuB,kBACvBC,cAAiB,oCACjBC,aAAgB,eAChBC,eAAkB,YAClBC,gBAAmB,aACnBC,qBAAwB,OACxBC,sBAAyB,kBAMvBzG,MAAMC,GAAGyG,SACb1G,MAAMC,GAAGyG,OAAOvG,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyG,OAAOvG,UAAUC,QAAQmB,UAC/CoF,KAAQ,MACRC,WAAc,iBACdC,SAAY,gBACZC,gBAAmB,kBACnBC,SAAY,eACZC,gBAAmB,iBACnBC,YAAe,cACfC,OAAU,aACVC,WAAc,iBACdC,YAAe,iBACfC,kBAAqB,2BACrBC,oBAAuB,uBACvBC,OAAU,SACVC,cAAiB,iBACjBC,YAAe,wBACfC,YAAe,uBACfC,aAAgB,qBAChBC,QAAW,gBACXC,cAAiB,eACjBC,OAAU,OACVC,UAAa,WACbC,YAAe,UACfC,UAAa,eACbC,OAAU,eACVC,WAAc,2CACdC,kBAAqB,sCACrBC,YAAe,WACfC,gBAAmB,wDACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,UACfC,cAAiB,+EACjBC,WAAc,YACdC,UAAa,gBACbC,UAAa,OACbzC,cAAiB,oCACjB0C,sBAAyB,QACzBC,aAAgB,SAChBC,aAAgB,YAChBC,aAAgB,kBAChBC,gBAAmB,aACnBC,oBAAuB,gCACvBC,SAAY,OACZC,YAAe,YACfC,eAAkB,aAClBC,OAAU,SACVC,YAAe,eACfC,cAAiB,0BACjBC,eAAkB,wBAClBC,YAAe,wBACfC,YAAe,sBACfC,aAAgB,iBAChBC,UAAa,cACbC,WAAc,SACdC,SAAY,YACZC,aAAgB,YAChBC,WAAc,cAMZlK,MAAMC,GAAGkK,YACbnK,MAAMC,GAAGkK,UAAUhK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkK,UAAUhK,UAAUC,QAAQmB,UAClD6I,OAAU,aACVjG,OAAU,SACVa,UACEC,aAAgB,kDAElB1E,KAAQ,QACRkE,QAAW,UACX4F,QACEC,YAAe,mBACfC,YAAe,cACfC,YAAe,YACfrI,IAAO,OACPsI,YAAe,cACfC,OAAU,UACVC,kBAAsB,0CACtBC,MAAS,QACTC,cAAiB,eACjBC,SAAY,IACZC,qBAAwB,UACxBC,oBAAuB,YACvBC,MAAS,QACTC,WAAc,iBAEhBC,MAAS,YACTC,oBACEC,gBAAmB,6DACnBC,uBAA0B,+BAC1BC,mBAAsB,iBACtBC,kBAAqB,8BACrBC,cAAiB,6DACjBC,qBAAwB,iCACxBC,iBAAoB,iBACpBC,gBAAmB,gCAErB/G,KAAQ,QACRgH,KAAQ,MACRC,MAAS,OACTC,OACEC,OAAU,SACVlJ,IAAO,MACPmJ,MAAS,QACTC,KAAQ,QACRC,SAAY,eAEdX,kBAAqB,oBACrBY,YAAe,cACfC,YAAe,oBAMbrM,MAAMC,GAAGqM,SACbtM,MAAMC,GAAGqM,OAAOnM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqM,OAAOnM,UAAUC,QAAQ4F,cAC/CuG,MAAS,YAMPvM,MAAMC,GAAGuM,QACbxM,MAAMC,GAAGuM,MAAMrM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuM,MAAMrM,UAAUC,QAAQ4F,cAC9CyG,OAAU,QAMRzM,MAAMC,GAAGyM,UACb1M,MAAMC,GAAGyM,QAAQvM,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,QAAQvM,UAAUC,QAAQ4F,cAChDyG,OAAU,KACVtI,OAAU,YAKRnE,MAAMC,GAAG0M,SACb3M,MAAMC,GAAG0M,OAAOxM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,OAAOxM,UAAUC,QAAQ4F,cAC/CyG,OAAU,KACVtI,OAAU,aAITyI,OAAO5M,MAAM6M", "file": "kendo.messages.sv-SE.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är senare än\",\n    \"gte\": \"Är lika eller senare än\",\n    \"lt\": \"Är tidigare än\",\n    \"lte\": \"Är lika eller tidigare än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"number\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är större än\",\n    \"gte\": \"Är lika eller större än\",\n    \"lt\": \"Är mindre än\",\n    \"lte\": \"Är lika eller mindre än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"string\": {\n    \"contains\": \"Innehåller\",\n    \"doesnotcontain\": \"Innehåller inte\",\n    \"endswith\": \"Slutar med\",\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\",\n    \"startswith\": \"Börjar med\"\n  },\n  \"enums\": {\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är senare än\",\n    \"gte\": \"Är lika eller senare än\",\n    \"lt\": \"Är tidigare än\",\n    \"lte\": \"Är lika eller tidigare än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"number\": {\n    \"eq\": \"Är lika med\",\n    \"gt\": \"Är större än\",\n    \"gte\": \"Är lika eller större än\",\n    \"lt\": \"Är mindre än\",\n    \"lte\": \"Är lika eller mindre än\",\n    \"neq\": \"Är inte lika med\"\n  },\n  \"string\": {\n    \"contains\": \"Innehåller\",\n    \"doesnotcontain\": \"Innehåller inte\",\n    \"endswith\": \"Slutar med\",\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\",\n    \"startswith\": \"Börjar med\"\n  },\n  \"enums\": {\n    \"eq\": \"Är lika med\",\n    \"neq\": \"Är inte lika med\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Kolumner\",\n  \"sortAscending\": \"Sortera stigande\",\n  \"sortDescending\": \"Sortera fallande\",\n  \"settings\": \"Kolumninställningar\",\n  \"done\": \"Klar\",\n  \"lock\": \"Lås\",\n  \"unlock\": \"Lås upp\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"days(s)\",\n    \"repeatEvery\": \"Repeat every:\"\n  },\n  \"end\": {\n    \"after\": \"After\",\n    \"occurrence\": \"occurrence(s)\",\n    \"label\": \"End:\",\n    \"never\": \"Never\",\n    \"on\": \"On\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Daily\",\n    \"monthly\": \"Monthly\",\n    \"never\": \"Never\",\n    \"weekly\": \"Weekly\",\n    \"yearly\": \"Yearly\"\n  },\n  \"monthly\": {\n    \"day\": \"Day\",\n    \"interval\": \"month(s)\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"first\",\n    \"fourth\": \"fourth\",\n    \"last\": \"last\",\n    \"second\": \"second\",\n    \"third\": \"third\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"week(s)\"\n  },\n  \"yearly\": {\n    \"of\": \"of\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"year(s)\"\n  },\n  \"weekdays\": {\n    \"day\": \"day\",\n    \"weekday\": \"weekday\",\n    \"weekend\": \"weekend day\"\n  }\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Rensa\",\n  \"filter\": \"Filtrera\",\n  \"isFalse\": \"är falskt\",\n  \"isTrue\": \"är sant\",\n  \"operator\": \"Operatör\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"Och\",\n  \"clear\": \"Rensa\",\n  \"filter\": \"Filtrera\",\n  \"info\": \"Visa poster med värde:\",\n  \"isFalse\": \"är falskt\",\n  \"isTrue\": \"är sant\",\n  \"or\": \"Eller\",\n  \"selectValue\": \"-Välj-\",\n  \"cancel\": \"Avbryt\",\n  \"operator\": \"Operatör\",\n  \"value\": \"Värde\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"Avbryt\",\n    \"cancel\": \"Avbryt ändringar\",\n    \"create\": \"Lägg till post\",\n    \"destroy\": \"Radera\",\n    \"edit\": \"Ändra\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Spara ändringar\",\n    \"select\": \"Välj\",\n    \"update\": \"Spara\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Är du säker på att du vill radera denna post?\",\n    \"cancelDelete\": \"Avbryt\",\n    \"confirmDelete\": \"Radera\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Dra en kolumnrubrik hit för att sortera på den kolumnen\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} av {2} poster\",\n  \"empty\": \"Det finns inga poster\",\n  \"first\": \"Gå till första sidan\",\n  \"itemsPerPage\": \"poster per sida\",\n  \"last\": \"Gå till sista sidan\",\n  \"next\": \"Gå till nästa sida\",\n  \"of\": \"av {0}\",\n  \"page\": \"Sida\",\n  \"previous\": \"Gå till föregående sida\",\n  \"refresh\": \"Uppdatera\",\n  \"morePages\": \"Fler sidor\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Avbryt\",\n  \"retry\": \"Försök igen\",\n  \"select\": \"Välj...\",\n  \"remove\": \"Ta bort\",\n  \"uploadSelectedFiles\": \"Ladda upp filer\",\n  \"dropFilesHere\": \"släpp filer här för att ladda upp\",\n  \"statusFailed\": \"misslyckades\",\n  \"statusUploaded\": \"uppladdad\",\n  \"statusUploading\": \"laddar upp\",\n  \"headerStatusUploaded\": \"Done\",\n  \"headerStatusUploading\": \"Uploading...\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Fet\",\n  \"createLink\": \"Lägg till länk\",\n  \"fontName\": \"Välj typsnitt\",\n  \"fontNameInherit\": \"(ärvt typsnitt)\",\n  \"fontSize\": \"Välj storlek\",\n  \"fontSizeInherit\": \"(ärvd storlek)\",\n  \"formatBlock\": \"Formatering\",\n  \"indent\": \"Öka indrag\",\n  \"insertHtml\": \"Lägg till HTML\",\n  \"insertImage\": \"Lägg till bild\",\n  \"insertOrderedList\": \"Lägg till numrerad lista\",\n  \"insertUnorderedList\": \"Lägg till punktlista\",\n  \"italic\": \"Kursiv\",\n  \"justifyCenter\": \"Centrerad text\",\n  \"justifyFull\": \"Marginaljusterad text\",\n  \"justifyLeft\": \"Vänsterjusterad text\",\n  \"justifyRight\": \"Högerjusterad text\",\n  \"outdent\": \"Minska indrag\",\n  \"strikethrough\": \"Genomstruken\",\n  \"styles\": \"Stil\",\n  \"subscript\": \"Nedsänkt\",\n  \"superscript\": \"Upphöjd\",\n  \"underline\": \"Understruken\",\n  \"unlink\": \"Ta bort länk\",\n  \"deleteFile\": \"Är du säker på att du vill radera \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"En mapp med detta namn hittades ej.\",\n  \"emptyFolder\": \"Tom mapp\",\n  \"invalidFileType\": \"Filen \\\"{0}\\\" är inte giltig. Tillåtna filtyper är {1}.\",\n  \"orderBy\": \"Sortera på:\",\n  \"orderByName\": \"Namn\",\n  \"orderBySize\": \"Storlek\",\n  \"overwriteFile\": \"'En fil med namn \\\"{0}\\\" finns redan i aktuell mapp. Vill du skriva över den?\",\n  \"uploadFile\": \"Ladda upp\",\n  \"backColor\": \"Bakgrundsfärg\",\n  \"foreColor\": \"Färg\",\n  \"dropFilesHere\": \"släpp filer här för att ladda upp\",\n  \"dialogButtonSeparator\": \"eller\",\n  \"dialogCancel\": \"Avbryt\",\n  \"dialogInsert\": \"Lägg till\",\n  \"imageAltText\": \"Alternativ text\",\n  \"imageWebAddress\": \"Webbadress\",\n  \"linkOpenInNewWindow\": \"Öppna länk i ett nytt fönster\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"Skärmtips\",\n  \"linkWebAddress\": \"Webbadress\",\n  \"search\": \"Search\",\n  \"createTable\": \"Skapa tabell\",\n  \"addColumnLeft\": \"Lägg till vänsterkolumn\",\n  \"addColumnRight\": \"Lägg till högerkolumn\",\n  \"addRowAbove\": \"Lägg till rad ovanför\",\n  \"addRowBelow\": \"Lägg till rad under\",\n  \"deleteColumn\": \"Ta bort kolumn\",\n  \"deleteRow\": \"Ta bort rad\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"Visa HTML\",\n  \"dialogUpdate\": \"Uppdatera\",\n  \"insertFile\": \"Ange fil\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"Hela dagen\",\n  \"cancel\": \"Avbryt\",\n  \"editable\": {\n    \"confirmation\": \"Är du säker på att du vill ta bort tillfället?\"\n  },\n  \"date\": \"Datum\",\n  \"destroy\": \"Ta bort\",\n  \"editor\": {\n    \"allDayEvent\": \"Heldagstillfälle\",\n    \"description\": \"Beskrivning\",\n    \"editorTitle\": \"Tillfälle\",\n    \"end\": \"Slut\",\n    \"endTimezone\": \"Sluttidszon\",\n    \"repeat\": \"Upprepa\",\n    \"separateTimezones\":  \"Använd separata start och sluttidszoner\",\n    \"start\": \"Start\",\n    \"startTimezone\": \"Starttidszon\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Tidszon\",\n    \"timezoneEditorTitle\": \"Tidszoner\",\n    \"title\": \"Titel\",\n    \"noTimezone\": \"Ingen tidszon\"\n  },\n  \"event\": \"Tillfälle\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Vill du ta bort enbart detta tillfället eller hela serien?\",\n    \"deleteWindowOccurrence\": \"Ta bort nuvarande upprepning\",\n    \"deleteWindowSeries\": \"Ta bort serien\",\n    \"deleteWindowTitle\": \"Ta bort återkommande objekt\",\n    \"editRecurring\": \"Vill du redigera enbart detta tillfälle eller hela serien?\",\n    \"editWindowOccurrence\": \"Redigera återkommade tillfälle\",\n    \"editWindowSeries\": \"Redigera serie\",\n    \"editWindowTitle\": \"Redigera återkommande objekt\"\n  },\n  \"save\": \"Spara\",\n  \"time\": \"Tid\",\n  \"today\": \"Idag\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Dag\",\n    \"month\": \"Månad\",\n    \"week\": \"Vecka\",\n    \"workWeek\": \"Arbetsvecka\"\n  },\n  \"deleteWindowTitle\": \"Ta bort tillfälle\",\n  \"showFullDay\": \"Visa heldag\",\n  \"showWorkDay\": \"Visa arbetsdag\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Stänga\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Avbryt\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Avbryt\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}