{"version": 3, "sources": ["messages/kendo.messages.ja-JP.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "<PERSON><PERSON><PERSON>", "actions", "<PERSON><PERSON><PERSON><PERSON>", "append", "insertAfter", "insertBefore", "pdf", "deleteDependencyWindowTitle", "deleteTaskWindowTitle", "destroy", "editor", "assingButton", "editor<PERSON><PERSON><PERSON>", "end", "percentComplete", "resources", "resourcesEditorTitle", "resourcesHeader", "start", "title", "unitsHeader", "save", "views", "day", "month", "week", "year", "Grid", "commands", "canceledit", "create", "edit", "excel", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "previous", "next", "last", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "PivotFieldMenu", "filterFields", "include", "ok", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "allDay", "event", "time", "showFullDay", "showWorkDay", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,KACTC,OAAU,WAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,KACTC,OAAU,WAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,UACjBC,eAAkB,UAClBC,OAAU,OACVC,QAAW,IACXC,KAAQ,KACRC,SAAY,OACZC,KAAQ,MACRC,OAAU,WAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,KACRC,OAAU,KACVC,UAAa,KACbC,cAAiB,QACjBC,YAAe,QACfC,UAAa,QACbC,cAAiB,YACjBC,YAAe,WACfC,aAAgB,WAChBC,YAAe,OACfC,oBAAuB,mBACvBC,kBAAqB,gBACrBC,OAAU,QACVC,QAAW,UACXC,WAAc,aACdC,OAAU,aACVC,YAAe,UACfC,WAAc,UACdC,WAAc,WACdC,SAAY,WACZC,SAAY,eACZC,gBAAmB,cACnBC,SAAY,cACZC,gBAAmB,aACnBC,YAAe,SACfC,WAAc,SACdC,UAAa,MACbC,UAAa,MACbC,MAAS,OACTC,YAAe,SACfC,WAAc,SACdC,QAAW,aACXC,YAAe,MACfC,YAAe,IACfC,gBAAmB,iDACnBC,WAAc,kBACdC,cAAiB,8CACjBC,kBAAqB,0BACrBC,gBAAmB,WACnBC,aAAgB,SAChBC,WAAc,SACdC,YAAe,UACfC,eAAkB,WAClBC,UAAa,OACbC,eAAkB,WAClBC,SAAY,OACZC,YAAe,SACfC,oBAAuB,iBACvBC,aAAgB,KAChBC,aAAgB,KAChBC,sBAAyB,MACzBC,aAAgB,QAChBC,YAAe,UACfC,cAAiB,SACjBC,eAAkB,SAClBC,YAAe,SACfC,YAAe,SACfC,UAAa,OACbC,aAAgB,UAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,SACdC,QAAW,YACXE,YAAe,IACfD,YAAe,MACfK,kBAAqB,0BACrBR,YAAe,SACfM,WAAc,kBACdD,gBAAmB,iDACnBE,cAAiB,8CACjBwB,cAAiB,uBACjBC,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,OACVC,QAAW,OACXvE,OAAU,OACVwE,MAAS,MACTC,SAAY,SAMVtF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,UACNC,IAAO,WACPC,WAAc,SACdC,SAAY,QACZC,eAAkB,UAClBC,SAAY,UAEdC,QACEN,GAAM,UACNC,IAAO,WACPM,IAAO,SACPC,GAAM,YACNC,IAAO,SACPC,GAAM,aAERC,MACEX,GAAM,UACNC,IAAO,WACPM,IAAO,eACPC,GAAM,SACNC,IAAO,eACPC,GAAM,UAERE,OACEZ,GAAM,UACNC,IAAO,eAOP1F,MAAMC,GAAGqG,aACbtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQC,UACnDkG,KAAQ,eACRpB,OAAU,OACVC,QAAW,OACXvE,OAAU,OACVwE,MAAS,MACTmB,IAAO,MACPC,GAAM,KACNC,YAAe,SACfpB,SAAY,MACZqB,MAAS,IACTnG,OAAU,WAMRR,MAAMC,GAAGqG,aACbtG,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,UACtCzF,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqG,WAAWnG,UAAUC,QAAQmF,WACnDC,QACEC,GAAM,UACNC,IAAO,WACPC,WAAc,SACdC,SAAY,QACZC,eAAkB,UAClBC,SAAY,UAEdC,QACEN,GAAM,UACNC,IAAO,WACPM,IAAO,SACPC,GAAM,YACNC,IAAO,SACPC,GAAM,aAERC,MACEX,GAAM,UACNC,IAAO,WACPM,IAAO,eACPC,GAAM,SACNC,IAAO,eACPC,GAAM,UAERE,OACEZ,GAAM,UACNC,IAAO,eAOP1F,MAAMC,GAAG2G,mBACb5G,MAAMC,GAAG2G,iBAAiBzG,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,iBAAiBzG,UAAUC,QAAQC,UACzD4E,OAAU,QAMRjF,MAAMC,GAAG4G,QACb7G,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,UAC9CyG,SACEC,SAAY,OACZC,OAAU,SACVC,YAAe,OACfC,aAAgB,OAChBC,IAAO,eAET3G,OAAU,QACV4G,4BAA+B,UAC/BC,sBAAyB,SACzBC,QAAW,KACXC,QACEC,aAAgB,OAChBC,YAAe,MACfC,IAAO,KACPC,gBAAmB,KACnBC,UAAa,OACbC,qBAAwB,OACxBC,gBAAmB,OACnBC,MAAS,KACTC,MAAS,OACTC,YAAe,MAEjBC,KAAQ,KACRC,OACEC,IAAO,IACPV,IAAO,KACPW,MAAS,IACTN,MAAS,KACTO,KAAQ,IACRC,KAAQ,QAORvI,MAAMC,GAAGuI,OACbxI,MAAMC,GAAGuI,KAAKrI,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuI,KAAKrI,UAAUC,QAAQC,UAC7CoI,UACEjI,OAAU,WACVkI,WAAc,QACdC,OAAU,YACVrB,QAAW,KACXsB,KAAQ,KACRC,MAAS,kBACT1B,IAAO,gBACPe,KAAQ,QACRY,OAAU,KACVC,OAAU,MAEZC,UACEC,aAAgB,QAChBC,aAAgB,oBAChBC,cAAiB,SAOjBnJ,MAAMC,GAAGmJ,YACbpJ,MAAMC,GAAGmJ,UAAUjJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmJ,UAAUjJ,UAAUC,QAAQC,UAClDgJ,MAAS,wCAMPrJ,MAAMC,GAAGqJ,iBACbtJ,MAAMC,GAAGqJ,eAAenJ,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqJ,eAAenJ,UAAUC,SAC/CmJ,YAAe,QACfC,cAAiB,WAMfxJ,MAAMC,GAAGwJ,QACbzJ,MAAMC,GAAGwJ,MAAMtJ,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwJ,MAAMtJ,UAAUC,QAAQC,UAC9CqJ,SAAY,MACZC,QAAW,sBACXN,MAAS,eACTO,KAAQ,MACRC,GAAM,QACNC,aAAgB,gBAChBC,MAAS,YACTC,SAAY,WACZC,KAAQ,WACRC,KAAQ,YACRC,QAAW,KACXC,UAAa,aAMXpK,MAAMC,GAAGoK,YACbrK,MAAMC,GAAGoK,UAAUlK,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoK,UAAUlK,UAAUC,QAAQC,UAClDiK,cAAiB,oBACjBC,aAAgB,iBAChBC,UAAa,oBAMXxK,MAAMC,GAAGwK,iBACbzK,MAAMC,GAAGwK,eAAetK,UAAUC,QAAQC,SAC1CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwK,eAAetK,UAAUC,QAAQC,UACvDkG,KAAQ,eACRmE,aAAgB,aAChB7J,OAAU,OACV8J,QAAW,kBACX3C,MAAS,gBACT3C,MAAS,MACTuF,GAAM,KACNpK,OAAU,QACV+E,WACEK,SAAY,QACZC,eAAkB,UAClBF,WAAc,SACdG,SAAY,SACZL,GAAM,UACNC,IAAO,eAOP1F,MAAMC,GAAG4K,mBACb7K,MAAMC,GAAG4K,iBAAiB1K,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4K,iBAAiB1K,UAAUC,QAAQC,UACzDyK,aACEC,MAAS,KACTC,OAAU,KACVC,MAAS,KACTC,OAAU,KACVC,QAAW,KACXC,OAAU,MAEZJ,QACEK,YAAe,cACfC,SAAY,OAEdL,OACEI,YAAe,cACfC,SAAY,MAEdJ,QACEI,SAAY,MACZD,YAAe,cACfE,SAAY,eAEdJ,SACEE,YAAe,cACfE,SAAY,cACZD,SAAY,MACZlD,IAAO,MAETgD,QACEC,YAAe,cACfE,SAAY,cACZD,SAAY,KACZzB,GAAM,OAERnC,KACE8D,MAAS,MACTC,YAAe,KACfV,MAAS,KACTW,MAAS,KACTC,WAAc,KACdC,GAAM,OAERC,iBACE9B,MAAS,KACT+B,OAAU,OACVC,MAAS,OACTC,OAAU,OACV9B,KAAQ,MAEV+B,UACE7D,IAAO,IACP8D,QAAW,KACXC,QAAW,SAOXnM,MAAMC,GAAGmM,YACbpM,MAAMC,GAAGmM,UAAUjM,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmM,UAAUjM,UAAUC,QAAQC,UAClDgM,OAAU,QACVjG,KAAQ,KACRkG,MAAS,OACTC,KAAQ,KACRC,YAAe,UACfC,YAAe,UACfC,MAAS,KACTxE,KAAQ,KACR1H,OAAU,QACV8G,QAAW,KACXqF,kBAAqB,UACrBC,cAAiB,yBACjBC,eAAkB,yBAClB3D,aAAgB,oBAChBf,OACEC,IAAO,IACPE,KAAQ,IACRwE,SAAY,MACZC,OAAU,KACV1E,MAAS,KAEX2E,oBACEL,kBAAqB,YACrBM,uBAA0B,UAC1BC,mBAAsB,QACtBC,gBAAmB,YACnBC,qBAAwB,UACxBC,iBAAoB,QACpBC,gBAAmB,qCACnBC,cAAiB,sCAEnBhG,QACES,MAAS,OACTD,MAAS,KACTL,IAAO,KACP8F,YAAe,UACfC,YAAe,KACfC,OAAU,OACVC,SAAY,IACZC,cAAiB,WACjBC,YAAe,WACfC,kBAAqB,sBACrBC,oBAAuB,SACvBC,qBAAwB,UACxBC,cAAiB,UACjBC,WAAc,eACdzG,YAAe,WAOfzH,MAAMC,GAAGkO,SACbnO,MAAMC,GAAGkO,OAAOhO,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkO,OAAOhO,UAAUC,SACvCgO,oBAAuB,MACvBC,oBAAuB,SAMrBrO,MAAMC,GAAGqO,WACbtO,MAAMC,GAAGqO,SAASnO,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqO,SAASnO,UAAUC,QAAQC,UACjDkO,QAAW,cACXC,cAAiB,iBACjBC,MAAS,SAMPzO,MAAMC,GAAGyO,SACb1O,MAAMC,GAAGyO,OAAOvO,UAAUC,QAAQuO,aAClC7O,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyO,OAAOvO,UAAUC,QAAQuO,cAC/C7F,OAAU,aACVtI,OAAU,QACViO,MAAS,MACTG,OAAU,KACVC,oBAAuB,cACvB7J,cAAiB,uBACjB8J,gBAAmB,UACnBC,eAAkB,WAClBC,cAAiB,KACjBC,aAAgB,KAChBC,sBAAyB,aACzBC,qBAAwB,QAMtBnP,MAAMC,GAAGmP,YACbpP,MAAMC,GAAGmP,UAAUjP,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmP,UAAUjP,UAAUC,QAAQC,UAClDgP,SAAY,YACZC,QAAW,YACXC,IAAO,6BACPC,IAAO,6BACPC,KAAQ,YACRC,MAAS,kBACTC,IAAO,kBACPvJ,KAAQ,kBAMNpG,MAAMC,GAAG2P,SACb5P,MAAMC,GAAG2P,OAAOzP,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2P,OAAOzP,UAAUC,QAAQuO,cAC/CkB,MAAS,SAMP7P,MAAMC,GAAG6P,QACb9P,MAAMC,GAAG6P,MAAM3P,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6P,MAAM3P,UAAUC,QAAQuO,cAC9CoB,OAAU,UAMR/P,MAAMC,GAAG+P,UACbhQ,MAAMC,GAAG+P,QAAQ7P,UAAUC,QAAQC,SACnCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+P,QAAQ7P,UAAUC,QAAQuO,cAChDoB,OAAU,OACVvP,OAAU,WAKRR,MAAMC,GAAGgQ,SACbjQ,MAAMC,GAAGgQ,OAAO9P,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgQ,OAAO9P,UAAUC,QAAQuO,cAC/CoB,OAAU,OACVvP,OAAU,YAIT0P,OAAOlQ,MAAMmQ", "file": "kendo.messages.ja-JP.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\r\n/* FlatColorPicker messages */\r\n\r\nif (kendo.ui.FlatColorPicker) {\r\nkendo.ui.FlatColorPicker.prototype.options.messages =\r\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\r\n  \"apply\": \"適用\",\r\n  \"cancel\": \"キャンセル\"\r\n});\r\n}\r\n\r\n/* ColorPicker messages */\r\n\r\nif (kendo.ui.ColorPicker) {\r\nkendo.ui.ColorPicker.prototype.options.messages =\r\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\r\n  \"apply\": \"適用\",\r\n  \"cancel\": \"キャンセル\"\r\n});\r\n}\r\n\r\n/* ColumnMenu messages */\r\n\r\nif (kendo.ui.ColumnMenu) {\r\nkendo.ui.ColumnMenu.prototype.options.messages =\r\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\r\n  \"sortAscending\": \"昇順に並べ替え\",\r\n  \"sortDescending\": \"降順に並べ替え\",\r\n  \"filter\": \"フィルタ\",\r\n  \"columns\": \"列\",\r\n  \"done\": \"完了\",\r\n  \"settings\": \"列の設定\",\r\n  \"lock\": \"ロック\",\r\n  \"unlock\": \"ロック解除\"\r\n});\r\n}\r\n\r\n/* Editor messages */\r\n\r\nif (kendo.ui.Editor) {\r\nkendo.ui.Editor.prototype.options.messages =\r\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\r\n  \"bold\": \"太字\",\r\n  \"italic\": \"斜体\",\r\n  \"underline\": \"下線\",\r\n  \"strikethrough\": \"取り消し線\",\r\n  \"superscript\": \"上付き文字\",\r\n  \"subscript\": \"下付き文字\",\r\n  \"justifyCenter\": \"テキストを中央揃え\",\r\n  \"justifyLeft\": \"テキストを左揃え\",\r\n  \"justifyRight\": \"テキストを右揃え\",\r\n  \"justifyFull\": \"両端揃え\",\r\n  \"insertUnorderedList\": \"順序付けされていないリストを挿入\",\r\n  \"insertOrderedList\": \"順序付けされたリストを挿入\",\r\n  \"indent\": \"インデント\",\r\n  \"outdent\": \"インデント解除\",\r\n  \"createLink\": \"ハイパーリンクを挿入\",\r\n  \"unlink\": \"ハイパーリンクを削除\",\r\n  \"insertImage\": \"イメージを挿入\",\r\n  \"insertFile\": \"ファイルを挿入\",\r\n  \"insertHtml\": \"HTML を挿入\",\r\n  \"viewHtml\": \"HTML を表示\",\r\n  \"fontName\": \"フォント ファミリを選択\",\r\n  \"fontNameInherit\": \"(継承されたフォント)\",\r\n  \"fontSize\": \"フォント サイズを選択\",\r\n  \"fontSizeInherit\": \"(継承されたサイズ)\",\r\n  \"formatBlock\": \"フォーマット\",\r\n  \"formatting\": \"フォーマット\",\r\n  \"foreColor\": \"カラー\",\r\n  \"backColor\": \"背景色\",\r\n  \"style\": \"スタイル\",\r\n  \"emptyFolder\": \"空のフォルダ\",\r\n  \"uploadFile\": \"アップロード\",\r\n  \"orderBy\": \"次の要素による整列:\",\r\n  \"orderBySize\": \"サイズ\",\r\n  \"orderByName\": \"名\",\r\n  \"invalidFileType\": \"選択されたファイル {0} は無効です。サポートされているファイル タイプは {1} です。\",\r\n  \"deleteFile\": \"{0} を本当に削除しますか?\",\r\n  \"overwriteFile\": \"{0} という名前のファイルは既にカレント ディレクトリに存在します。上書きしますか?\",\r\n  \"directoryNotFound\": \"この名前のディレクトリが見つかりませんでした。\",\r\n  \"imageWebAddress\": \"Web アドレス\",\r\n  \"imageAltText\": \"代替テキスト\",\r\n  \"imageWidth\": \"幅 (px)\",\r\n  \"imageHeight\": \"高さ (px)\",\r\n  \"fileWebAddress\": \"Web アドレス\",\r\n  \"fileTitle\": \"タイトル\",\r\n  \"linkWebAddress\": \"Web アドレス\",\r\n  \"linkText\": \"テキスト\",\r\n  \"linkToolTip\": \"ツールチップ\",\r\n  \"linkOpenInNewWindow\": \"リンクを新規ウィンドウで開く\",\r\n  \"dialogUpdate\": \"更新\",\r\n  \"dialogInsert\": \"挿入\",\r\n  \"dialogButtonSeparator\": \"または\",\r\n  \"dialogCancel\": \"キャンセル\",\r\n  \"createTable\": \"テーブルの作成\",\r\n  \"addColumnLeft\": \"左に列を追加\",\r\n  \"addColumnRight\": \"右に列を追加\",\r\n  \"addRowAbove\": \"上に行を追加\",\r\n  \"addRowBelow\": \"下に行を追加\",\r\n  \"deleteRow\": \"行を削除\",\r\n  \"deleteColumn\": \"列を削除\"\r\n});\r\n}\r\n\r\n/* FileBrowser messages */\r\n\r\nif (kendo.ui.FileBrowser) {\r\nkendo.ui.FileBrowser.prototype.options.messages =\r\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\r\n  \"uploadFile\": \"アップロード\",\r\n  \"orderBy\": \"次の要素による整列\",\r\n  \"orderByName\": \"名\",\r\n  \"orderBySize\": \"サイズ\",\r\n  \"directoryNotFound\": \"この名前のディレクトリは見つかりませんでした。\",\r\n  \"emptyFolder\": \"空のフォルダ\",\r\n  \"deleteFile\": \"{0} を本当に削除しますか?\",\r\n  \"invalidFileType\": \"選択されたファイル {0} は無効です。サポートされているファイル タイプは {1} です。\",\r\n  \"overwriteFile\": \"{0} という名前のファイルは既にカレント ディレクトリに存在します。上書きしますか?\",\r\n  \"dropFilesHere\": \"ここにファイルをドロップしてアップロード\",\r\n  \"search\": \"検索\"\r\n});\r\n}\r\n\r\n/* FilterCell messages */\r\n\r\nif (kendo.ui.FilterCell) {\r\nkendo.ui.FilterCell.prototype.options.messages =\r\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\r\n  \"isTrue\": \"真である\",\r\n  \"isFalse\": \"偽である\",\r\n  \"filter\": \"フィルタ\",\r\n  \"clear\": \"クリア\",\r\n  \"operator\": \"演算子\"\r\n});\r\n}\r\n\r\n/* FilterCell operators */\r\n\r\nif (kendo.ui.FilterCell) {\r\nkendo.ui.FilterCell.prototype.options.operators =\r\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\r\n  \"string\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\",\r\n    \"startswith\": \"が次で始まる\",\r\n    \"contains\": \"が次を含む\",\r\n    \"doesnotcontain\": \"が次を含まない\",\r\n    \"endswith\": \"が次で終わる\"\r\n  },\r\n  \"number\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\",\r\n    \"gte\": \"が次の値以上\",\r\n    \"gt\": \"が次の値より大きい\",\r\n    \"lte\": \"が次の値以下\",\r\n    \"lt\": \"が次の値より小さい\"\r\n  },\r\n  \"date\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\",\r\n    \"gte\": \"次の値と同じかそれより後\",\r\n    \"gt\": \"次の値より後\",\r\n    \"lte\": \"次の値と同じかそれより前\",\r\n    \"lt\": \"次の値より前\"\r\n  },\r\n  \"enums\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\"\r\n  }\r\n});\r\n}\r\n\r\n/* FilterMenu messages */\r\n\r\nif (kendo.ui.FilterMenu) {\r\nkendo.ui.FilterMenu.prototype.options.messages =\r\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\r\n  \"info\": \"次の値がある項目を表示:\",\r\n  \"isTrue\": \"真である\",\r\n  \"isFalse\": \"偽である\",\r\n  \"filter\": \"フィルタ\",\r\n  \"clear\": \"クリア\",\r\n  \"and\": \"And\",\r\n  \"or\": \"Or\",\r\n  \"selectValue\": \"-値を選択-\",\r\n  \"operator\": \"演算子\",\r\n  \"value\": \"値\",\r\n  \"cancel\": \"キャンセル\"\r\n});\r\n}\r\n\r\n/* FilterMenu operator messages */\r\n\r\nif (kendo.ui.FilterMenu) {\r\nkendo.ui.FilterMenu.prototype.options.operators =\r\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\r\n  \"string\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\",\r\n    \"startswith\": \"が次で始まる\",\r\n    \"contains\": \"が次を含む\",\r\n    \"doesnotcontain\": \"が次を含まない\",\r\n    \"endswith\": \"が次で終わる\"\r\n  },\r\n  \"number\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\",\r\n    \"gte\": \"が次の値以上\",\r\n    \"gt\": \"が次の値より大きい\",\r\n    \"lte\": \"が次の値以下\",\r\n    \"lt\": \"が次の値より小さい\"\r\n  },\r\n  \"date\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\",\r\n    \"gte\": \"次の値と同じかそれより後\",\r\n    \"gt\": \"次の値より後\",\r\n    \"lte\": \"次の値と同じかそれより前\",\r\n    \"lt\": \"次の値より前\"\r\n  },\r\n  \"enums\": {\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\"\r\n  }\r\n});\r\n}\r\n\r\n/* FilterMultiCheck messages */\r\n\r\nif (kendo.ui.FilterMultiCheck) {\r\nkendo.ui.FilterMultiCheck.prototype.options.messages =\r\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\r\n  \"search\": \"検索\"\r\n});\r\n}\r\n\r\n/* Gantt messages */\r\n\r\nif (kendo.ui.Gantt) {\r\nkendo.ui.Gantt.prototype.options.messages =\r\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\r\n  \"actions\": {\r\n    \"addChild\": \"子の追加\",\r\n    \"append\": \"タスクの追加\",\r\n    \"insertAfter\": \"下に追加\",\r\n    \"insertBefore\": \"上に追加\",\r\n    \"pdf\": \"PDF にエクスポート\"\r\n  },\r\n  \"cancel\": \"キャンセル\",\r\n  \"deleteDependencyWindowTitle\": \"依存関係を削除\",\r\n  \"deleteTaskWindowTitle\": \"タスクを削除\",\r\n  \"destroy\": \"削除\",\r\n  \"editor\": {\r\n    \"assingButton\": \"割り当て\",\r\n    \"editorTitle\": \"タスク\",\r\n    \"end\": \"終点\",\r\n    \"percentComplete\": \"完了\",\r\n    \"resources\": \"リソース\",\r\n    \"resourcesEditorTitle\": \"リソース\",\r\n    \"resourcesHeader\": \"リソース\",\r\n    \"start\": \"始点\",\r\n    \"title\": \"タイトル\",\r\n    \"unitsHeader\": \"単位\"\r\n  },\r\n  \"save\": \"保存\",\r\n  \"views\": {\r\n    \"day\": \"日\",\r\n    \"end\": \"終点\",\r\n    \"month\": \"月\",\r\n    \"start\": \"始点\",\r\n    \"week\": \"週\",\r\n    \"year\": \"年\"\r\n  }\r\n});\r\n}\r\n\r\n/* Grid messages */\r\n\r\nif (kendo.ui.Grid) {\r\nkendo.ui.Grid.prototype.options.messages =\r\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\r\n  \"commands\": {\r\n    \"cancel\": \"変更のキャンセル\",\r\n    \"canceledit\": \"キャンセル\",\r\n    \"create\": \"新規レコードを追加\",\r\n    \"destroy\": \"削除\",\r\n    \"edit\": \"編集\",\r\n    \"excel\": \"Export to Excel\",\r\n    \"pdf\": \"Export to PDF\",\r\n    \"save\": \"変更の保存\",\r\n    \"select\": \"選択\",\r\n    \"update\": \"更新\"\r\n  },\r\n  \"editable\": {\r\n    \"cancelDelete\": \"キャンセル\",\r\n    \"confirmation\": \"このレコードを本当に削除しますか?\",\r\n    \"confirmDelete\": \"削除\"\r\n  }\r\n});\r\n}\r\n\r\n/* Groupable messages */\r\n\r\nif (kendo.ui.Groupable) {\r\nkendo.ui.Groupable.prototype.options.messages =\r\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\r\n  \"empty\": \"列ヘッダーをここにドラッグ アンド ドロップして、その列でグループ化\"\r\n});\r\n}\r\n\r\n/* NumericTextBox messages */\r\n\r\nif (kendo.ui.NumericTextBox) {\r\nkendo.ui.NumericTextBox.prototype.options =\r\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\r\n  \"upArrowText\": \"値を増やす\",\r\n  \"downArrowText\": \"値を減らす\"\r\n});\r\n}\r\n\r\n/* Pager messages */\r\n\r\nif (kendo.ui.Pager) {\r\nkendo.ui.Pager.prototype.options.messages =\r\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\r\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} ({2} 項目中)\",\r\n  \"empty\": \"表示する項目がありません\",\r\n  \"page\": \"ページ\",\r\n  \"of\": \"/ {0}\",\r\n  \"itemsPerPage\": \"項目 (1 ページあたり)\",\r\n  \"first\": \"最初のページに移動\",\r\n  \"previous\": \"前のページに移動\",\r\n  \"next\": \"次のページに移動\",\r\n  \"last\": \"最後のページに移動\",\r\n  \"refresh\": \"更新\",\r\n  \"morePages\": \"その他のページ\"\r\n});\r\n}\r\n\r\n/* PivotGrid messages */\r\n\r\nif (kendo.ui.PivotGrid) {\r\nkendo.ui.PivotGrid.prototype.options.messages =\r\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\r\n  \"measureFields\": \"ここにデータ フィールドをドロップ\",\r\n  \"columnFields\": \"ここに列フィールドをドロップ\",\r\n  \"rowFields\": \"ここに行フィールドをドロップ\"\r\n});\r\n}\r\n\r\n/* PivotFieldMenu messages */\r\n\r\nif (kendo.ui.PivotFieldMenu) {\r\nkendo.ui.PivotFieldMenu.prototype.options.messages =\r\n$.extend(true, kendo.ui.PivotFieldMenu.prototype.options.messages,{\r\n  \"info\": \"次の値がある項目を表示:\",\r\n  \"filterFields\": \"フィールド フィルタ\",\r\n  \"filter\": \"フィルタ\",\r\n  \"include\": \"フィールドをインクルード...\",\r\n  \"title\": \"インクルードするフィールド\",\r\n  \"clear\": \"クリア\",\r\n  \"ok\": \"OK\",\r\n  \"cancel\": \"キャンセル\",\r\n  \"operators\": {\r\n    \"contains\": \"が次を含む\",\r\n    \"doesnotcontain\": \"が次を含まない\",\r\n    \"startswith\": \"が次で始まる\",\r\n    \"endswith\": \"が次で終わる\",\r\n    \"eq\": \"が次の値と同じ\",\r\n    \"neq\": \"が次の値と異なる\"\r\n  }\r\n});\r\n}\r\n\r\n/* RecurrenceEditor messages */\r\n\r\nif (kendo.ui.RecurrenceEditor) {\r\nkendo.ui.RecurrenceEditor.prototype.options.messages =\r\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\r\n  \"frequencies\": {\r\n    \"never\": \"なし\",\r\n    \"hourly\": \"毎時\",\r\n    \"daily\": \"毎日\",\r\n    \"weekly\": \"毎週\",\r\n    \"monthly\": \"毎月\",\r\n    \"yearly\": \"毎年\"\r\n  },\r\n  \"hourly\": {\r\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\r\n    \"interval\": \" 時間\"\r\n  },\r\n  \"daily\": {\r\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\r\n    \"interval\": \" 日\"\r\n  },\r\n  \"weekly\": {\r\n    \"interval\": \" 週間\",\r\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\r\n    \"repeatOn\": \"次のときに繰り返す: \"\r\n  },\r\n  \"monthly\": {\r\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\r\n    \"repeatOn\": \"次のときに繰り返す: \",\r\n    \"interval\": \" か月\",\r\n    \"day\": \"日 \"\r\n  },\r\n  \"yearly\": {\r\n    \"repeatEvery\": \"次の間隔で繰り返す: \",\r\n    \"repeatOn\": \"次のときに繰り返す: \",\r\n    \"interval\": \" 年\",\r\n    \"of\": \" の \"\r\n  },\r\n  \"end\": {\r\n    \"label\": \"終了:\",\r\n    \"mobileLabel\": \"終了\",\r\n    \"never\": \"なし\",\r\n    \"after\": \"後 \",\r\n    \"occurrence\": \" 回\",\r\n    \"on\": \"オン \"\r\n  },\r\n  \"offsetPositions\": {\r\n    \"first\": \"最初\",\r\n    \"second\": \"2 番目\",\r\n    \"third\": \"3 番目\",\r\n    \"fourth\": \"4 番目\",\r\n    \"last\": \"最後\"\r\n  },\r\n  \"weekdays\": {\r\n    \"day\": \"日\",\r\n    \"weekday\": \"平日\",\r\n    \"weekend\": \"週末\"\r\n  }\r\n});\r\n}\r\n\r\n/* Scheduler messages */\r\n\r\nif (kendo.ui.Scheduler) {\r\nkendo.ui.Scheduler.prototype.options.messages =\r\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\r\n  \"allDay\": \"すべての日\",\r\n  \"date\": \"日付\",\r\n  \"event\": \"イベント\",\r\n  \"time\": \"時間\",\r\n  \"showFullDay\": \"24 時間表示\",\r\n  \"showWorkDay\": \"営業時間を表示\",\r\n  \"today\": \"今日\",\r\n  \"save\": \"保存\",\r\n  \"cancel\": \"キャンセル\",\r\n  \"destroy\": \"削除\",\r\n  \"deleteWindowTitle\": \"イベントを削除\",\r\n  \"ariaSlotLabel\": \"{0:t} ～ {1:t} 時の範囲から選択\",\r\n  \"ariaEventLabel\": \"{0} ({1:D} 日の {2:t} 時)\",\r\n  \"confirmation\": \"このイベントを本当に削除しますか?\",\r\n  \"views\": {\r\n    \"day\": \"日\",\r\n    \"week\": \"週\",\r\n    \"workWeek\": \"稼働日\",\r\n    \"agenda\": \"予定\",\r\n    \"month\": \"月\"\r\n  },\r\n  \"recurrenceMessages\": {\r\n    \"deleteWindowTitle\": \"定期的な項目を削除\",\r\n    \"deleteWindowOccurrence\": \"現在の回を削除\",\r\n    \"deleteWindowSeries\": \"系列を削除\",\r\n    \"editWindowTitle\": \"定期的な項目を編集\",\r\n    \"editWindowOccurrence\": \"現在の回を編集\",\r\n    \"editWindowSeries\": \"系列を編集\",\r\n    \"deleteRecurring\": \"このイベントの回のみを削除しますか、それとも系列全体を削除しますか?\",\r\n    \"editRecurring\": \"このイベントの回のみを編集しますか、それとも系列全体を編集しますか?\"\r\n  },\r\n  \"editor\": {\r\n    \"title\": \"タイトル\",\r\n    \"start\": \"始点\",\r\n    \"end\": \"終点\",\r\n    \"allDayEvent\": \"終日のイベント\",\r\n    \"description\": \"説明\",\r\n    \"repeat\": \"繰り返す\",\r\n    \"timezone\": \" \",\r\n    \"startTimezone\": \"開始タイムゾーン\",\r\n    \"endTimezone\": \"終了タイムゾーン\",\r\n    \"separateTimezones\": \"開始と終了で別のタイムゾーンを使用する\",\r\n    \"timezoneEditorTitle\": \"タイムゾーン\",\r\n    \"timezoneEditorButton\": \"タイム ゾーン\",\r\n    \"timezoneTitle\": \"タイム ゾーン\",\r\n    \"noTimezone\": \"タイムゾーンがありません\",\r\n    \"editorTitle\": \"イベント\"\r\n  }\r\n});\r\n}\r\n\r\n/* Slider messages */\r\n\r\nif (kendo.ui.Slider) {\r\nkendo.ui.Slider.prototype.options =\r\n$.extend(true, kendo.ui.Slider.prototype.options,{\r\n  \"increaseButtonTitle\": \"増やす\",\r\n  \"decreaseButtonTitle\": \"減らす\"\r\n});\r\n}\r\n\r\n/* TreeView messages */\r\n\r\nif (kendo.ui.TreeView) {\r\nkendo.ui.TreeView.prototype.options.messages =\r\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\r\n  \"loading\": \"ロードしています...\",\r\n  \"requestFailed\": \"要求を実行できませんでした。\",\r\n  \"retry\": \"再試行\"\r\n});\r\n}\r\n\r\n/* Upload messages */\r\n\r\nif (kendo.ui.Upload) {\r\nkendo.ui.Upload.prototype.options.localization=\r\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\r\n  \"select\": \"ファイルを選択...\",\r\n  \"cancel\": \"キャンセル\",\r\n  \"retry\": \"再試行\",\r\n  \"remove\": \"削除\",\r\n  \"uploadSelectedFiles\": \"ファイルをアップロード\",\r\n  \"dropFilesHere\": \"ここにファイルをドロップしてアップロード\",\r\n  \"statusUploading\": \"アップロード中\",\r\n  \"statusUploaded\": \"アップロード済み\",\r\n  \"statusWarning\": \"警告\",\r\n  \"statusFailed\": \"失敗\",\r\n  \"headerStatusUploading\": \"アップロード中...\",\r\n  \"headerStatusUploaded\": \"完了\"\r\n});\r\n}\r\n\r\n/* Validator messages */\r\n\r\nif (kendo.ui.Validator) {\r\nkendo.ui.Validator.prototype.options.messages =\r\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\r\n  \"required\": \"{0} が必要です\",\r\n  \"pattern\": \"{0} は無効です\",\r\n  \"min\": \"{0} は {1} より大きいか同じ値にしてください\",\r\n  \"max\": \"{0} は {1} より小さいか同じ値にしてください\",\r\n  \"step\": \"{0} は無効です\",\r\n  \"email\": \"{0} は無効な電子メールです\",\r\n  \"url\": \"{0} は無効な URL です\",\r\n  \"date\": \"{0} は無効な日付です\"\r\n});\r\n}\r\n\r\n/* Dialog */\r\n\r\nif (kendo.ui.Dialog) {\r\nkendo.ui.Dialog.prototype.options.messages =\r\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\r\n  \"close\": \"閉じる\"\r\n});\r\n}\r\n\r\n/* Alert */\r\n\r\nif (kendo.ui.Alert) {\r\nkendo.ui.Alert.prototype.options.messages =\r\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\r\n  \"okText\": \"オーケー\"\r\n});\r\n}\r\n\r\n/* Confirm */\r\n\r\nif (kendo.ui.Confirm) {\r\nkendo.ui.Confirm.prototype.options.messages =\r\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\r\n  \"okText\": \"オーケー\",\r\n  \"cancel\": \"キャンセル\"\r\n});\r\n}\r\n\r\n/* Prompt */\r\nif (kendo.ui.Prompt) {\r\nkendo.ui.Prompt.prototype.options.messages =\r\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\r\n  \"okText\": \"オーケー\",\r\n  \"cancel\": \"キャンセル\"\r\n});\r\n}\r\n\r\n})(window.kendo.jQuery);\r\n}));"]}