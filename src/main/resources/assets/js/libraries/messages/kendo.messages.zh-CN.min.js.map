{"version": 3, "sources": ["messages/kendo.messages.zh-CN.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "<PERSON><PERSON><PERSON>", "views", "day", "week", "month", "actions", "append", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "noRecords", "Groupable", "empty", "ImageBrowser", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "last", "next", "previous", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "end", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "today", "allDay", "event", "time", "showFullDay", "showWorkDay", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "deleteRecurring", "editR<PERSON><PERSON>ring", "editor", "title", "start", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "editor<PERSON><PERSON><PERSON>", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,KACTC,OAAU,QAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,KACTC,OAAU,QAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,KACjBC,eAAkB,KAClBC,OAAU,KACVC,QAAW,IACXC,KAAQ,KACRC,SAAY,MACZC,KAAQ,KACRC,OAAU,UAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,KACRC,OAAU,KACVC,UAAa,MACbC,cAAiB,MACjBC,YAAe,KACfC,UAAa,KACbC,cAAiB,KACjBC,YAAe,MACfC,aAAgB,MAChBC,YAAe,OACfC,oBAAuB,SACvBC,kBAAqB,SACrBC,OAAU,OACVC,QAAW,OACXC,WAAc,OACdC,OAAU,OACVC,YAAe,OACfC,WAAc,OACdC,WAAc,UACdC,SAAY,UACZC,SAAY,OACZC,gBAAmB,UACnBC,SAAY,OACZC,gBAAmB,UACnBC,YAAe,OACfC,WAAc,MACdC,UAAa,KACbC,UAAa,MACbC,MAAS,KACTC,YAAe,QACfC,WAAc,KACdC,QAAW,QACXC,YAAe,KACfC,YAAe,KACfC,gBAAmB,+BACnBC,WAAc,gBACdC,cAAiB,kCACjBC,kBAAqB,UACrBC,gBAAmB,OACnBC,aAAgB,OAChBC,WAAc,UACdC,YAAe,UACfC,eAAkB,OAClBC,UAAa,KACbC,eAAkB,OAClBC,SAAY,OACZC,YAAe,OACfC,oBAAuB,UACvBC,aAAgB,KAChBC,aAAgB,KAChBC,sBAAyB,IACzBC,aAAgB,KAChBC,YAAe,OACfC,cAAiB,QACjBC,eAAkB,QAClBC,YAAe,QACfC,YAAe,QACfC,UAAa,MACbC,aAAgB,SAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,KACdC,QAAW,OACXE,YAAe,KACfD,YAAe,KACfK,kBAAqB,UACrBR,YAAe,QACfM,WAAc,gBACdD,gBAAmB,+BACnBE,cAAiB,kCACjBwB,cAAiB,cACjBC,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,KACVC,QAAW,KACXvE,OAAU,KACVwE,MAAS,KACTC,SAAY,SAMVtF,MAAMC,GAAGsF,aACbvF,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQC,UACnDmF,KAAQ,aACRL,OAAU,KACVC,QAAW,KACXvE,OAAU,KACVwE,MAAS,KACTI,IAAO,KACPC,GAAM,IACNC,YAAe,OACfL,SAAY,MACZM,MAAS,IACTpF,OAAU,QAMRR,MAAMC,GAAG4F,mBACb7F,MAAMC,GAAG4F,iBAAiB1F,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4F,iBAAiB1F,UAAUC,QAAQC,UACzD4E,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQ0F,UACtChG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQ0F,WACnDC,QACEC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,MAClBC,SAAY,OAEdC,QACEN,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERC,MACEX,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERE,OACEZ,GAAM,KACNC,IAAO,UAQPjG,MAAMC,GAAGsF,aACbvF,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQ0F,UACtChG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQ0F,WACnDC,QACEC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,MAClBC,SAAY,OAEdC,QACEN,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERC,MACEX,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERE,OACEZ,GAAM,KACNC,IAAO,UAQPjG,MAAMC,GAAG4G,QACb7G,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,UAC9CyG,OACEC,IAAO,IACPC,KAAQ,IACRC,MAAS,KAEXC,SACEC,OAAU,OACVC,SAAY,QACZC,aAAgB,QAChBC,YAAe,YAOftH,MAAMC,GAAGsH,OACbvH,MAAMC,GAAGsH,KAAKpH,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsH,KAAKpH,UAAUC,QAAQC,UAC7CmH,UACEhH,OAAU,KACViH,WAAc,KACdC,OAAU,KACVC,QAAW,KACXC,KAAQ,KACRC,MAAS,WACTC,IAAO,SACPC,KAAQ,KACRC,OAAU,KACVC,OAAU,MAEZC,UACEC,aAAgB,KAChBC,aAAgB,UAChBC,cAAiB,MAEnBC,UAAa,cAMXtI,MAAMC,GAAGsI,YACbvI,MAAMC,GAAGsI,UAAUpI,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsI,UAAUpI,UAAUC,QAAQC,UAClDmI,MAAS,oBAMPxI,MAAMC,GAAGwI,eACbzI,MAAMC,GAAGwI,aAAatI,UAAUC,QAAQC,SACxCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwI,aAAatI,UAAUC,QAAQC,UACrD6C,WAAc,KACdC,QAAW,OACXE,YAAe,KACfD,YAAe,KACfK,kBAAqB,UACrBR,YAAe,QACfM,WAAc,gBACdD,gBAAmB,+BACnBE,cAAiB,kCACjBwB,cAAiB,cACjBC,OAAU,QAMRjF,MAAMC,GAAGyI,iBACb1I,MAAMC,GAAGyI,eAAevI,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyI,eAAevI,UAAUC,SAC/CuI,YAAe,KACfC,cAAiB,QAMf5I,MAAMC,GAAG4I,QACb7I,MAAMC,GAAG4I,MAAM1I,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4I,MAAM1I,UAAUC,QAAQC,UAC9CyI,SAAY,MACZC,QAAW,uBACXP,MAAS,YACTQ,KAAQ,IACRC,GAAM,QACNC,aAAgB,KAChBC,MAAS,KACTC,KAAQ,KACRC,KAAQ,MACRC,SAAY,MACZC,QAAW,KACXC,UAAa,WAMXxJ,MAAMC,GAAGwJ,YACbzJ,MAAMC,GAAGwJ,UAAUtJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwJ,UAAUtJ,UAAUC,QAAQC,UAClDqJ,cAAiB,WACjBC,aAAgB,UAChBC,UAAa,aAMX5J,MAAMC,GAAG4J,mBACb7J,MAAMC,GAAG4J,iBAAiB1J,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4J,iBAAiB1J,UAAUC,QAAQC,UACzDyJ,aACEC,MAAS,KACTC,OAAU,MACVC,MAAS,KACTC,OAAU,KACVC,QAAW,KACXC,OAAU,MAEZJ,QACEK,YAAe,SACfC,SAAY,OAEdL,OACEI,YAAe,SACfC,SAAY,MAEdJ,QACEI,SAAY,KACZD,YAAe,SACfE,SAAY,QAEdJ,SACEE,YAAe,SACfE,SAAY,OACZD,SAAY,KACZvD,IAAO,MAETqD,QACEC,YAAe,SACfE,SAAY,QACZD,SAAY,KACZrB,GAAM,SAERuB,KACEC,MAAS,QACTC,YAAe,OACfX,MAAS,KACTY,MAAS,MACTC,WAAc,MACdC,GAAM,OAERC,iBACE3B,MAAS,KACT4B,OAAU,KACVC,MAAS,KACTC,OAAU,KACV7B,KAAQ,MAEV8B,UACEnE,IAAO,IACPoE,QAAW,MACXC,QAAW,SAQXpL,MAAMC,GAAGoL,YACbrL,MAAMC,GAAGoL,UAAUlL,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoL,UAAUlL,UAAUC,QAAQC,UAClDiL,MAAS,KACTvD,KAAQ,KACRvH,OAAU,KACVmH,QAAW,KACX4D,OAAU,KACV5E,KAAQ,KACR6E,MAAS,KACTC,KAAQ,KACRC,YAAe,OACfC,YAAe,SACfC,kBAAqB,OACrBC,cAAiB,oBACjBC,eAAkB,wBAClB5D,UACEE,aAAgB,gBAElBtB,OACEC,IAAO,IACPC,KAAQ,IACR+E,SAAY,MACZC,OAAU,KACV/E,MAAS,KAEXgF,oBACEL,kBAAqB,SACrBM,uBAA0B,SAC1BC,mBAAsB,OACtBC,gBAAmB,SACnBC,qBAAwB,SACxBC,iBAAoB,OACpBC,gBAAmB,mBACnBC,cAAiB,sBAEnBC,QACEC,MAAS,KACTC,MAAS,KACTnC,IAAO,KACPoC,YAAe,OACfC,YAAe,KACfC,OAAU,KACVC,SAAY,IACZC,cAAiB,OACjBC,YAAe,OACfC,kBAAqB,eACrBC,oBAAuB,KACvBC,qBAAwB,KACxBC,cAAiB,OACjBC,WAAc,IACdC,YAAe,SAOfvN,MAAMC,GAAGuN,SACbxN,MAAMC,GAAGuN,OAAOrN,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuN,OAAOrN,UAAUC,SACvCqN,oBAAuB,KACvBC,oBAAuB,QAMrB1N,MAAMC,GAAG0N,WACb3N,MAAMC,GAAG0N,SAASxN,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0N,SAASxN,UAAUC,QAAQC,UACjDuN,QAAW,SACXC,cAAiB,OACjBC,MAAS,QAMP9N,MAAMC,GAAG8N,SACb/N,MAAMC,GAAG8N,OAAO5N,UAAUC,QAAQ4N,aAClClO,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8N,OAAO5N,UAAUC,QAAQ4N,cAC/ChG,OAAU,QACVxH,OAAU,KACVsN,MAAS,KACTG,OAAU,KACVC,oBAAuB,OACvBlJ,cAAiB,cACjBmJ,gBAAmB,MACnBC,eAAkB,MAClBC,cAAiB,KACjBC,aAAgB,KAChBC,sBAAyB,QACzBC,qBAAwB,QAMtBxO,MAAMC,GAAGwO,YACbzO,MAAMC,GAAGwO,UAAUtO,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwO,UAAUtO,UAAUC,QAAQC,UAClDqO,SAAY,WACZC,QAAW,SACXC,IAAO,kBACPC,IAAO,kBACPC,KAAQ,SACRC,MAAS,gBACTC,IAAO,eACPrI,KAAQ,kBAGPsI,OAAOjP,MAAMkP", "file": "kendo.messages.zh-CN.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"确定\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"确定\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"升序\",\n  \"sortDescending\": \"降序\",\n  \"filter\": \"过滤\",\n  \"columns\": \"列\",\n  \"done\": \"完成\",\n  \"settings\": \"列设置\",\n  \"lock\": \"锁定\",\n  \"unlock\": \"解除锁定\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"粗体\",\n  \"italic\": \"斜体\",\n  \"underline\": \"下划线\",\n  \"strikethrough\": \"删除线\",\n  \"superscript\": \"上标\",\n  \"subscript\": \"下标\",\n  \"justifyCenter\": \"居中\",\n  \"justifyLeft\": \"左对齐\",\n  \"justifyRight\": \"右对齐\",\n  \"justifyFull\": \"两端对齐\",\n  \"insertUnorderedList\": \"插入无序列表\",\n  \"insertOrderedList\": \"插入有序列表\",\n  \"indent\": \"增加缩进\",\n  \"outdent\": \"减少缩进\",\n  \"createLink\": \"插入链接\",\n  \"unlink\": \"移除链接\",\n  \"insertImage\": \"插入图片\",\n  \"insertFile\": \"插入文件\",\n  \"insertHtml\": \"插入 HTML\",\n  \"viewHtml\": \"查看 HTML\",\n  \"fontName\": \"选择字体\",\n  \"fontNameInherit\": \"（继承的字体）\",\n  \"fontSize\": \"选择字号\",\n  \"fontSizeInherit\": \"（继承的字号）\",\n  \"formatBlock\": \"格式化块\",\n  \"formatting\": \"格式化\",\n  \"foreColor\": \"颜色\",\n  \"backColor\": \"背景色\",\n  \"style\": \"风格\",\n  \"emptyFolder\": \"文件夹为空\",\n  \"uploadFile\": \"上传\",\n  \"orderBy\": \"排序条件:\",\n  \"orderBySize\": \"大小\",\n  \"orderByName\": \"名字\",\n  \"invalidFileType\": \"选中的文件 \\\"{0}\\\" 非法，支持的文件类型为 {1}。\",\n  \"deleteFile\": '您确定要删除 \\\"{0}\\\"?',\n  \"overwriteFile\": '当前文件夹已存在文件名为 \\\"{0}\\\" 的文件，您确定要覆盖么？',\n  \"directoryNotFound\": \"此文件夹未找到\",\n  \"imageWebAddress\": \"图片地址\",\n  \"imageAltText\": \"替代文本\",\n  \"imageWidth\": \"宽度 (px)\",\n  \"imageHeight\": \"高度 (px)\",\n  \"fileWebAddress\": \"文件地址\",\n  \"fileTitle\": \"标题\",\n  \"linkWebAddress\": \"链接地址\",\n  \"linkText\": \"链接文字\",\n  \"linkToolTip\": \"链接提示\",\n  \"linkOpenInNewWindow\": \"在新窗口中打开\",\n  \"dialogUpdate\": \"上传\",\n  \"dialogInsert\": \"插入\",\n  \"dialogButtonSeparator\": \"或\",\n  \"dialogCancel\": \"取消\",\n  \"createTable\": \"创建表格\",\n  \"addColumnLeft\": \"左侧添加列\",\n  \"addColumnRight\": \"右侧添加列\",\n  \"addRowAbove\": \"上方添加行\",\n  \"addRowBelow\": \"下方添加行\",\n  \"deleteRow\": \"删除行\",\n  \"deleteColumn\": \"删除列\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"上传\",\n  \"orderBy\": \"排序条件\",\n  \"orderByName\": \"名称\",\n  \"orderBySize\": \"大小\",\n  \"directoryNotFound\": \"此文件夹未找到\",\n  \"emptyFolder\": \"文件夹为空\",\n  \"deleteFile\": '您确定要删除 \\\"{0}\\\"?',\n  \"invalidFileType\": \"选中的文件 \\\"{0}\\\" 非法，支持的文件类型为 {1}。\",\n  \"overwriteFile\": \"当前文件夹已存在文件名为 \\\"{0}\\\" 的文件，您确定要覆盖么？\",\n  \"dropFilesHere\": \"拖拽要上传的文件到此处\",\n  \"search\": \"搜索\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"为真\",\n  \"isFalse\": \"为假\",\n  \"filter\": \"过滤\",\n  \"clear\": \"清除\",\n  \"operator\": \"运算符\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"显示符合以下条件的行\",\n  \"isTrue\": \"为真\",\n  \"isFalse\": \"为假\",\n  \"filter\": \"过滤\",\n  \"clear\": \"清除\",\n  \"and\": \"并且\",\n  \"or\": \"或\",\n  \"selectValue\": \"-选择-\",\n  \"operator\": \"运算符\",\n  \"value\": \"值\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"搜索\"\n});\n}\n\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\",\n    \"startswith\": \"开头为\",\n    \"contains\": \"包含\",\n    \"doesnotcontain\": \"不包含\",\n    \"endswith\": \"结尾为\"\n  },\n  \"number\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\",\n    \"gte\": \"大于等于\",\n    \"gt\": \"大于\",\n    \"lte\": \"小于等于\",\n    \"lt\": \"小于\"\n  },\n  \"date\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\",\n    \"gte\": \"大于等于\",\n    \"gt\": \"大于\",\n    \"lte\": \"小于等于\",\n    \"lt\": \"小于\"\n  },\n  \"enums\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\"\n  }\n});\n}\n\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\",\n    \"startswith\": \"开头为\",\n    \"contains\": \"包含\",\n    \"doesnotcontain\": \"不包含\",\n    \"endswith\": \"结尾为\"\n  },\n  \"number\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\",\n    \"gte\": \"大于等于\",\n    \"gt\": \"大于\",\n    \"lte\": \"小于等于\",\n    \"lt\": \"小于\"\n  },\n  \"date\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\",\n    \"gte\": \"大于等于\",\n    \"gt\": \"大于\",\n    \"lte\": \"小于等于\",\n    \"lt\": \"小于\"\n  },\n  \"enums\": {\n    \"eq\": \"等于\",\n    \"neq\": \"不等于\"\n  }\n});\n}\n\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"views\": {\n    \"day\": \"日\",\n    \"week\": \"周\",\n    \"month\": \"月\"\n  },\n  \"actions\": {\n    \"append\": \"添加任务\",\n    \"addChild\": \"添加子任务\",\n    \"insertBefore\": \"添加到前面\",\n    \"insertAfter\": \"添加到后面\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"取消\",\n    \"canceledit\": \"取消\",\n    \"create\": \"新增\",\n    \"destroy\": \"删除\",\n    \"edit\": \"编辑\",\n    \"excel\": \"导出 Excel\",\n    \"pdf\": \"导出 PDF\",\n    \"save\": \"保存\",\n    \"select\": \"选择\",\n    \"update\": \"更新\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"取消\",\n    \"confirmation\": \"确定要删除吗？\",\n    \"confirmDelete\": \"删除\"\n  },\n  \"noRecords\": \"没有可用的记录。\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"拖拽列标题到此处按列组合显示\"\n});\n}\n\n/* ImageBrowser messages */\n\nif (kendo.ui.ImageBrowser) {\nkendo.ui.ImageBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.ImageBrowser.prototype.options.messages,{\n  \"uploadFile\": \"上传\",\n  \"orderBy\": \"排序条件\",\n  \"orderByName\": \"名称\",\n  \"orderBySize\": \"大小\",\n  \"directoryNotFound\": \"此文件夹未找到\",\n  \"emptyFolder\": \"文件夹为空\",\n  \"deleteFile\": '您确定要删除 \\\"{0}\\\"?',\n  \"invalidFileType\": \"选中的文件 \\\"{0}\\\" 非法，支持的文件类型为 {1}。\",\n  \"overwriteFile\": \"当前文件夹已存在文件名为 \\\"{0}\\\" 的文件，您确定要覆盖么？\",\n  \"dropFilesHere\": \"拖拽要上传的文件到此处\",\n  \"search\": \"搜索\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"增加\",\n  \"downArrowText\": \"减少\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"显示条目 {0} - {1} 共 {2}\",\n  \"empty\": \"没有可显示的记录。\",\n  \"page\": \"页\",\n  \"of\": \"共 {0}\",\n  \"itemsPerPage\": \"每页\",\n  \"first\": \"首页\",\n  \"last\": \"末页\",\n  \"next\": \"下一页\",\n  \"previous\": \"上一页\",\n  \"refresh\": \"刷新\",\n  \"morePages\": \"更多...\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"拖放数据字段于此\",\n  \"columnFields\": \"拖放列字段于此\",\n  \"rowFields\": \"拖放行字段于此\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"从不\",\n    \"hourly\": \"每小时\",\n    \"daily\": \"每天\",\n    \"weekly\": \"每周\",\n    \"monthly\": \"每月\",\n    \"yearly\": \"每年\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"重复周期: \",\n    \"interval\": \" 小时\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"重复周期: \",\n    \"interval\": \" 天\"\n  },\n  \"weekly\": {\n    \"interval\": \" 周\",\n    \"repeatEvery\": \"重复周期: \",\n    \"repeatOn\": \"重复于:\"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"重复周期: \",\n    \"repeatOn\": \"重复于:\",\n    \"interval\": \" 月\",\n    \"day\": \"日期\"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"重复周期: \",\n    \"repeatOn\": \"重复于: \",\n    \"interval\": \" 年\",\n    \"of\": \" 月份: \"\n  },\n  \"end\": {\n    \"label\": \"截止时间:\",\n    \"mobileLabel\": \"截止时间\",\n    \"never\": \"从不\",\n    \"after\": \"重复 \",\n    \"occurrence\": \" 次后\",\n    \"on\": \"止于 \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"第一\",\n    \"second\": \"第二\",\n    \"third\": \"第三\",\n    \"fourth\": \"第四\",\n    \"last\": \"最后\"\n  },\n  \"weekdays\": {\n    \"day\": \"天\",\n    \"weekday\": \"工作日\",\n    \"weekend\": \"周末\"\n  }\n});\n}\n\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"today\": \"今天\",\n  \"save\": \"保存\",\n  \"cancel\": \"取消\",\n  \"destroy\": \"删除\",\n  \"allDay\": \"整天\",\n  \"date\": \"日期\",\n  \"event\": \"事件\",\n  \"time\": \"时间\",\n  \"showFullDay\": \"显示整天\",\n  \"showWorkDay\": \"显示营业时间\",\n  \"deleteWindowTitle\": \"删除事件\",\n  \"ariaSlotLabel\": \"选择从 {0:t} 到 {1:t}\",\n  \"ariaEventLabel\": \"{0} on {1:D} at {2:t}\",\n  \"editable\": {\n    \"confirmation\": \"你确定你要删除这个活动？\"\n  },\n  \"views\": {\n    \"day\": \"日\",\n    \"week\": \"周\",\n    \"workWeek\": \"工作日\",\n    \"agenda\": \"日程\",\n    \"month\": \"月\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"删除周期条目\",\n    \"deleteWindowOccurrence\": \"删除当前事件\",\n    \"deleteWindowSeries\": \"删除序列\",\n    \"editWindowTitle\": \"修改周期条目\",\n    \"editWindowOccurrence\": \"修改当前事件\",\n    \"editWindowSeries\": \"修改序列\",\n    \"deleteRecurring\": \"你想删除仅此事件发生或整个系列？\",\n    \"editRecurring\": \"你想，仅编辑此次事件发生或整个系列？\"\n  },\n  \"editor\": {\n    \"title\": \"标题\",\n    \"start\": \"起始\",\n    \"end\": \"终止\",\n    \"allDayEvent\": \"全天事件\",\n    \"description\": \"描述\",\n    \"repeat\": \"重复\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"起始时区\",\n    \"endTimezone\": \"终止时区\",\n    \"separateTimezones\": \"使用独立的起始和终止时区\",\n    \"timezoneEditorTitle\": \"时区\",\n    \"timezoneEditorButton\": \"时区\",\n    \"timezoneTitle\": \"选择时区\",\n    \"noTimezone\": \"无\",\n    \"editorTitle\": \"事件\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"增加\",\n  \"decreaseButtonTitle\": \"减少\"\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"加载中...\",\n  \"requestFailed\": \"加载失败\",\n  \"retry\": \"重试\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"选择...\",\n  \"cancel\": \"取消\",\n  \"retry\": \"重试\",\n  \"remove\": \"移除\",\n  \"uploadSelectedFiles\": \"上传文件\",\n  \"dropFilesHere\": \"拖拽要上传的文件到此处\",\n  \"statusUploading\": \"上传中\",\n  \"statusUploaded\": \"已上传\",\n  \"statusWarning\": \"警告\",\n  \"statusFailed\": \"失败\",\n  \"headerStatusUploading\": \"上传...\",\n  \"headerStatusUploaded\": \"完成\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} 为必填项\",\n  \"pattern\": \"{0} 非法\",\n  \"min\": \"{0} 应该大于或等于 {1}\",\n  \"max\": \"{0} 应该小于或等于 {1}\",\n  \"step\": \"{0} 非法\",\n  \"email\": \"{0} 不是合法的邮件地址\",\n  \"url\": \"{0} 不是合法的URL\",\n  \"date\": \"{0} 不是合法的日期\"\n});\n}\n})(window.kendo.jQuery);\n}));"]}