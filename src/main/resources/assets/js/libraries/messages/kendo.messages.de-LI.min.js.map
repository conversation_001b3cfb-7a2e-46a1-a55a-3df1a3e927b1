{"version": 3, "sources": ["messages/kendo.messages.de-LI.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "enums", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "FilterMenu", "FilterMultiCheck", "messages", "search", "ColumnMenu", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "filter", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Editor", "backColor", "bold", "createLink", "deleteFile", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "directoryNotFound", "emptyFolder", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "foreColor", "formatBlock", "imageAltText", "imageWebAddress", "imageWidth", "imageHeight", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "invalidFileType", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "orderBy", "orderByName", "orderBySize", "outdent", "overwriteFile", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "uploadFile", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "dropFilesHere", "formatting", "viewHtml", "dialogUpdate", "insertFile", "dialogOk", "tableWizard", "tableTab", "cellTab", "accessibilityTab", "caption", "summary", "width", "height", "cellSpacing", "cellPadding", "cellMargin", "alignment", "background", "cssClass", "id", "border", "borderStyle", "collapseBorders", "wrapText", "associateCellsWithHeaders", "alignLeft", "alignCenter", "alignRight", "alignLeftTop", "alignCenterTop", "alignRightTop", "alignLeftMiddle", "alignCenterMiddle", "alignRightMiddle", "alignLeftBottom", "alignCenterBottom", "alignRightBottom", "align<PERSON><PERSON>ove", "rows", "selectAllCells", "browserMessages", "FileBrowser", "ImageBrowser", "clear", "isFalse", "isTrue", "operator", "and", "info", "or", "selectValue", "cancel", "value", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "noRecords", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "TreeList", "noRows", "loading", "requestFailed", "retry", "createchild", "Upload", "localization", "remove", "statusFailed", "statusWarning", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "timezoneTitle", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "timeline", "timelineWeek", "timelineWorkWeek", "timelineMonth", "defaultRowText", "showFullDay", "showWorkDay", "ariaSlotLabel", "ariaEventLabel", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,cACNC,GAAM,kBACNC,IAAO,8BACPC,GAAM,kBACNC,IAAO,0BACPC,IAAO,qBAETC,OACEN,GAAM,cACNK,IAAO,qBAETE,QACEP,GAAM,cACNC,GAAM,kBACNC,IAAO,8BACPC,GAAM,eACNC,IAAO,+BACPC,IAAO,qBAETG,QACEC,SAAY,aACZC,eAAkB,mBAClBC,SAAY,YACZX,GAAM,cACNK,IAAO,oBACPO,WAAc,mBAOdpB,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACjDE,MACIC,GAAM,cACNC,GAAM,kBACNC,IAAO,8BACPC,GAAM,kBACNC,IAAO,0BACPC,IAAO,qBAEXC,OACIN,GAAM,cACNK,IAAO,qBAEXE,QACIP,GAAM,cACNC,GAAM,kBACNC,IAAO,8BACPC,GAAM,eACNC,IAAO,+BACPC,IAAO,qBAEXG,QACIC,SAAY,aACZC,eAAkB,mBAClBC,SAAY,YACZX,GAAM,cACNK,IAAO,oBACPO,WAAc,mBAOlBpB,MAAMC,GAAGqB,mBACbtB,MAAMC,GAAGqB,iBAAiBnB,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,iBAAiBnB,UAAUC,QAAQmB,UACzDC,OAAU,YAMRxB,MAAMC,GAAGwB,aACbzB,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwB,WAAWtB,UAAUC,QAAQmB,UACnDG,QAAW,UACXC,cAAiB,wBACjBC,eAAkB,uBAClBC,SAAY,2BACZC,KAAQ,WACRC,KAAQ,UACRC,OAAU,aACVC,OAAU,aAMRjC,MAAMC,GAAGiC,mBACblC,MAAMC,GAAGiC,iBAAiB/B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiC,iBAAiB/B,UAAUC,QAAQmB,UACzDY,OACEC,SAAY,SACZC,YAAe,yBAEjBC,KACEC,MAAS,OACTC,WAAc,wBACdC,MAAS,WACTC,MAAS,MACTC,GAAM,KACNC,YAAe,SAEjBC,aACEV,MAAS,UACTW,QAAW,YACXJ,MAAS,MACTK,OAAU,cACVC,OAAU,YAEZF,SACEG,IAAO,MACPb,SAAY,WACZC,YAAe,wBACfa,SAAY,mBAEdC,iBACEC,MAAS,SACTC,OAAU,UACVC,KAAQ,UACRC,OAAU,UACVC,MAAS,WAEXT,QACEV,YAAe,wBACfa,SAAY,kBACZd,SAAY,YAEdY,QACES,GAAM,MACNpB,YAAe,wBACfa,SAAY,kBACZd,SAAY,WAEdsB,UACET,IAAO,MACPU,QAAW,YACXC,QAAW,wBAOX5D,MAAMC,GAAG4D,SACb7D,MAAMC,GAAG4D,OAAO1D,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4D,OAAO1D,UAAUC,QAAQmB,UAC/CuC,UAAa,mBACbC,KAAQ,OACRC,WAAc,qBACdC,WAAc,mDACdC,sBAAyB,OACzBC,aAAgB,YAChBC,aAAgB,WAChBC,kBAAqB,6CACrBC,YAAe,qBACfC,SAAY,iBACZC,gBAAmB,uBACnBC,SAAY,QACZC,gBAAmB,qBACnBC,UAAa,QACbC,YAAe,aACfC,aAAgB,qBAChBC,gBAAmB,cACnBC,WAAc,cACdC,YAAe,YACfC,OAAU,oBACVC,WAAc,gBACdC,YAAe,gBACfC,kBAAqB,mBACrBC,oBAAuB,eACvBC,gBAAmB,+EACnBC,OAAU,SACVC,cAAiB,YACjBC,YAAe,aACfC,YAAe,cACfC,aAAgB,eAChBC,oBAAuB,qCACvBC,SAAY,OACZC,YAAe,UACfC,eAAkB,cAClBC,QAAW,iBACXC,YAAe,OACfC,YAAe,QACfC,QAAW,qBACXC,cAAiB,6GACjB5E,OAAU,SACV6E,cAAiB,kBACjBC,OAAU,OACVC,UAAa,eACbC,YAAe,eACfC,UAAa,gBACbC,OAAU,sBACVC,WAAc,YACdC,YAAe,mBACfC,cAAiB,wBACjBC,eAAkB,yBAClBC,YAAe,0BACfC,YAAe,2BACfC,aAAgB,iBAChBC,UAAa,gBACbC,cAAiB,2CACjBC,WAAc,SACdC,SAAY,YACZC,aAAgB,gBAChBC,WAAc,iBACdC,SAAY,KACZC,YAAe,qBACfC,SAAY,UACZC,QAAW,gBACXC,iBAAoB,iBACpBC,QAAW,YACXC,QAAW,kBACXC,MAAS,SACTC,OAAU,OACVC,YAAe,cACfC,YAAe,iBACfC,WAAc,aACdC,UAAa,cACbC,WAAc,cACdC,SAAY,aACZC,GAAM,KACNC,OAAU,SACVC,YAAe,aACfC,gBAAmB,kBACnBC,SAAY,YACZC,0BAA6B,8BAC7BC,UAAa,mBACbC,YAAe,qBACfC,WAAc,mBACdC,aAAgB,4BAChBC,eAAkB,8BAClBC,cAAiB,4BACjBC,gBAAmB,gCACnBC,kBAAqB,kCACrBC,iBAAoB,gCACpBC,gBAAmB,6BACnBC,kBAAqB,+BACrBC,iBAAoB,6BACpBC,YAAe,wBACf/H,QAAW,UACXgI,KAAQ,SACRC,eAAkB,+BAMpB,IAAIC,IACFjD,WAAe,YACfX,QAAY,iBACZC,YAAgB,OAChBC,YAAgB,QAChB7B,kBAAsB,wCACtBC,YAAgB,qBAChBL,WAAe,2DACfqB,gBAAoB,8EACpBc,cAAkB,gFAClBe,cAAkB,2BAClB3F,OAAU,SAGRxB,OAAMC,GAAG4J,cACb7J,MAAMC,GAAG4J,YAAY1J,UAAUC,QAAQmB,SACvCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4J,YAAY1J,UAAUC,QAAQmB,SAAUqI,IAG5D5J,MAAMC,GAAG6J,eACb9J,MAAMC,GAAG6J,aAAa3J,UAAUC,QAAQmB,SACxCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6J,aAAa3J,UAAUC,QAAQmB,SAAUqI,IAK7D5J,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnDwI,MAAS,UACT9H,OAAU,SACV+H,QAAW,aACXC,OAAU,cACVC,SAAY,cAKVlK,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnD4I,IAAO,MACPJ,MAAS,UACT9H,OAAU,UACVmI,KAAQ,kCACRJ,QAAW,SACXC,OAAU,UACVI,GAAM,OACNC,YAAe,eACfC,OAAU,YACVL,SAAY,WACZM,MAAS,UAMPxK,MAAMC,GAAGwK,OACbzK,MAAMC,GAAGwK,KAAKtK,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwK,KAAKtK,UAAUC,QAAQmB,UAC7CmJ,UACEC,WAAc,YACdJ,OAAU,uBACVK,OAAU,6BACVC,QAAW,UACXC,KAAQ,aACRC,MAAS,oBACTC,IAAO,iBACPC,KAAQ,uBACRC,OAAU,QACVC,OAAU,iBAEZC,UACEC,aAAgB,6DAChBC,aAAgB,YAChBC,cAAiB,WAEnBC,UAAa,yCAMXxL,MAAMC,GAAGwL,YACbzL,MAAMC,GAAGwL,UAAUtL,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwL,UAAUtL,UAAUC,QAAQmB,UAClDmK,MAAS,qFAMP1L,MAAMC,GAAG0L,iBACb3L,MAAMC,GAAG0L,eAAexL,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0L,eAAexL,UAAUC,SAC/CwL,YAAe,eACfC,cAAiB,qBAMf7L,MAAMC,GAAG6L,QACb9L,MAAMC,GAAG6L,MAAM3L,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6L,MAAM3L,UAAUC,QAAQmB,UAC9CwK,SAAY,MACZC,QAAW,6BACXN,MAAS,cACTtI,MAAS,mBACT6I,aAAgB,qBAChB3I,KAAQ,oBACR4I,KAAQ,qBACRzI,GAAM,UACN0I,KAAQ,QACRC,SAAY,uBACZC,QAAW,gBACXC,UAAa,oBAMXtM,MAAMC,GAAGsM,WACbvM,MAAMC,GAAGsM,SAASpM,UAAUC,QAAQmB,SACpCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,SAASpM,UAAUC,QAAQmB,UAC/CiL,OAAQ,gCACRC,QAAS,gBACTC,cAAe,uBACfC,MAAO,YACPjC,UACII,KAAM,aACNK,OAAQ,gBACRR,WAAY,YACZC,OAAQ,6BACRgC,YAAa,4BACb/B,QAAS,UACTE,MAAO,wBACPC,IAAK,0BAOThL,MAAMC,GAAG4M,SACb7M,MAAMC,GAAG4M,OAAO1M,UAAUC,QAAQ0M,aAClChN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4M,OAAO1M,UAAUC,QAAQ0M,cAC/CvC,OAAU,UACVpD,cAAiB,2CACjB4F,OAAU,UACVJ,MAAS,cACTzB,OAAU,gBACV8B,aAAgB,oBAChBC,cAAiB,UACjBC,eAAkB,cAClBC,gBAAmB,YACnBC,oBAAuB,oBACvBC,qBAAwB,cACxBC,sBAAyB,kBAMvBtN,MAAMC,GAAGsN,YACbvN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsN,UAAUpN,UAAUC,QAAQmB,UAClDiM,OAAU,aACVjD,OAAU,YACVhK,KAAQ,QACRsK,QAAW,UACXG,IAAO,sBACPI,UACEC,aAAgB,+CAElBoC,QACEC,YAAe,qBACfC,YAAe,eACfC,YAAe,SACftL,IAAO,UACPuL,cAAiB,WACjBC,YAAe,gBACfC,OAAU,cACVC,kBAAqB,oDACrBC,MAAS,UACTC,cAAiB,iBACjBC,SAAY,uBACZC,qBAAwB,WACxBC,oBAAuB,YACvBC,MAAS,QACTC,WAAc,kBAEhBC,MAAS,SACTC,oBACEC,gBAAmB,kEACnBC,uBAA0B,wBAC1BC,mBAAsB,0CACtBC,kBAAqB,gDACrBC,cAAiB,qEACjBC,qBAAwB,gCACxBC,iBAAoB,mBACpBC,gBAAmB,mCAErBhE,KAAQ,YACRiE,KAAQ,OACRC,MAAS,QACTC,OACEC,OAAU,SACVpM,IAAO,MACPqM,MAAS,QACTC,KAAQ,QACRC,SAAY,eACZC,SAAY,aACZC,aAAgB,mBAChBC,iBAAoB,0BACpBC,cAAiB,oBAEnBf,kBAAqB,iBACrBgB,eAAkB,eAClBC,YAAe,sBACfC,YAAe,2BACfC,cAAiB,iCACjBC,eAAkB,2BAMhBjQ,MAAMC,GAAGiQ,YACblQ,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiQ,UAAU/P,UAAUC,QAAQmB,UAClD4O,SAAY,oBACZC,QAAW,mBACXC,IAAO,2CACPC,IAAO,4CACPC,KAAQ,mBACRC,MAAS,+BACTC,IAAO,4BACPlQ,KAAQ,iCAMNP,MAAMC,GAAGyQ,SACb1Q,MAAMC,GAAGyQ,OAAOvQ,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyQ,OAAOvQ,UAAUC,QAAQ0M,cAC/C6D,MAAS,eAMP3Q,MAAMC,GAAG2Q,QACb5Q,MAAMC,GAAG2Q,MAAMzQ,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2Q,MAAMzQ,UAAUC,QAAQ0M,cAC9C+D,OAAU,QAMR7Q,MAAMC,GAAG6Q,UACb9Q,MAAMC,GAAG6Q,QAAQ3Q,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6Q,QAAQ3Q,UAAUC,QAAQ0M,cAChD+D,OAAU,KACVtG,OAAU,eAKRvK,MAAMC,GAAG8Q,SACb/Q,MAAMC,GAAG8Q,OAAO5Q,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8Q,OAAO5Q,UAAUC,QAAQ0M,cAC/C+D,OAAU,KACVtG,OAAU,gBAITyG,OAAOhR,MAAMiR", "file": "kendo.messages.de-LI.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"gleich sind\",\n    \"gt\": \"später sind als\",\n    \"gte\": \"gleich oder später sind als\",\n    \"lt\": \"früher sind als\",\n    \"lte\": \"früher oder gleich sind\",\n    \"neq\": \"nicht gleich sind\"\n  },\n  \"enums\": {\n    \"eq\": \"gleich sind\",\n    \"neq\": \"nicht gleich sind\"\n  },\n  \"number\": {\n    \"eq\": \"gleich sind\",\n    \"gt\": \"größer als sind\",\n    \"gte\": \"größer als oder gleich sind\",\n    \"lt\": \"kleiner sind\",\n    \"lte\": \"kleiner als oder gleich sind\",\n    \"neq\": \"nicht gleich sind\"\n  },\n  \"string\": {\n    \"contains\": \"beinhalten\",\n    \"doesnotcontain\": \"beinhalten nicht\",\n    \"endswith\": \"enden mit\",\n    \"eq\": \"gleich sind\",\n    \"neq\": \"nicht gleich sind\",\n    \"startswith\": \"beginnen mit\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n    \"date\": {\n        \"eq\": \"gleich sind\",\n        \"gt\": \"später sind als\",\n        \"gte\": \"gleich oder später sind als\",\n        \"lt\": \"früher sind als\",\n        \"lte\": \"früher oder gleich sind\",\n        \"neq\": \"nicht gleich sind\"\n    },\n    \"enums\": {\n        \"eq\": \"gleich sind\",\n        \"neq\": \"nicht gleich sind\"\n    },\n    \"number\": {\n        \"eq\": \"gleich sind\",\n        \"gt\": \"größer als sind\",\n        \"gte\": \"größer als oder gleich sind\",\n        \"lt\": \"kleiner sind\",\n        \"lte\": \"kleiner als oder gleich sind\",\n        \"neq\": \"nicht gleich sind\"\n    },\n    \"string\": {\n        \"contains\": \"beinhalten\",\n        \"doesnotcontain\": \"beinhalten nicht\",\n        \"endswith\": \"enden mit\",\n        \"eq\": \"gleich sind\",\n        \"neq\": \"nicht gleich sind\",\n        \"startswith\": \"beginnen mit\"\n    }\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Suchen\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Spalten\",\n  \"sortAscending\": \"Aufsteigend sortieren\",\n  \"sortDescending\": \"Absteigend sortieren\",\n  \"settings\": \"Einstellungen zu Spalten\",\n  \"done\": \"Erledigt\",\n  \"lock\": \"Sperren\",\n  \"unlock\": \"Entsperren\",\n  \"filter\": \"Filtern\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"Tag(e)\",\n    \"repeatEvery\": \"Wiederholen an jedem:\"\n  },\n  \"end\": {\n    \"after\": \"Nach\",\n    \"occurrence\": \"Anzahl Wiederholungen\",\n    \"label\": \"Beenden:\",\n    \"never\": \"Nie\",\n    \"on\": \"Am\",\n    \"mobileLabel\": \"Endet\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Täglich\",\n    \"monthly\": \"Monatlich\",\n    \"never\": \"Nie\",\n    \"weekly\": \"Wöchentlich\",\n    \"yearly\": \"Jährlich\"\n  },\n  \"monthly\": {\n    \"day\": \"Tag\",\n    \"interval\": \"Monat(e)\",\n    \"repeatEvery\": \"Wiederholen an jedem:\",\n    \"repeatOn\": \"Wiederholen am:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"ersten\",\n    \"fourth\": \"vierten\",\n    \"last\": \"letzten\",\n    \"second\": \"zweiten\",\n    \"third\": \"dritten\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Wiederholen an jedem:\",\n    \"repeatOn\": \"Wiederholen am:\",\n    \"interval\": \"Woche(n)\"\n  },\n  \"yearly\": {\n    \"of\": \"von\",\n    \"repeatEvery\": \"Wiederholen an jedem:\",\n    \"repeatOn\": \"Wiederholen am:\",\n    \"interval\": \"Jahr(e)\"\n  },\n  \"weekdays\": {\n    \"day\": \"Tag\",\n    \"weekday\": \"Wochentag\",\n    \"weekend\": \"Tag am Wochenende\"\n  }\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"backColor\": \"Hintergrundfarbe\",\n  \"bold\": \"Fett\",\n  \"createLink\": \"Hyperlink einfügen\",\n  \"deleteFile\": \"Sind Sie sicher, dass Sie  \\\"{0}\\\" löschen wollen?\",\n  \"dialogButtonSeparator\": \"oder\",\n  \"dialogCancel\": \"Abbrechen\",\n  \"dialogInsert\": \"Einfügen\",\n  \"directoryNotFound\": \"Kein Verzeichnis mit diesem Namen gefunden\",\n  \"emptyFolder\": \"Leeres Verzeichnis\",\n  \"fontName\": \"Schriftfamilie\",\n  \"fontNameInherit\": \"(Schrift übernehmen)\",\n  \"fontSize\": \"Größe\",\n  \"fontSizeInherit\": \"(Größe übernehmen)\",\n  \"foreColor\": \"Farbe\",\n  \"formatBlock\": \"Absatzstil\",\n  \"imageAltText\": \"Abwechselnder Text\",\n  \"imageWebAddress\": \"Web-Adresse\",\n  \"imageWidth\": \"Breite (px)\",\n  \"imageHeight\": \"Höhe (px)\",\n  \"indent\": \"Einzug vergrößern\",\n  \"insertHtml\": \"HTML einfügen\",\n  \"insertImage\": \"Einfügen Bild\",\n  \"insertOrderedList\": \"Numerierte Liste\",\n  \"insertUnorderedList\": \"Aufzählliste\",\n  \"invalidFileType\": \"Die ausgewählte Datei  \\\"{0}\\\" ist ungültig. Unterstützte Dateitypen sind {1}.\",\n  \"italic\": \"Kursiv\",\n  \"justifyCenter\": \"Zentriert\",\n  \"justifyFull\": \"Ausrichten\",\n  \"justifyLeft\": \"Linksbündig\",\n  \"justifyRight\": \"Rechtsbündig\",\n  \"linkOpenInNewWindow\": \"Link in einem neuen Fenster öffnen\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Web-Adresse\",\n  \"orderBy\": \"Sortiert nach:\",\n  \"orderByName\": \"Name\",\n  \"orderBySize\": \"Größe\",\n  \"outdent\": \"Einzug verkleinern\",\n  \"overwriteFile\": \"Eine Datei mit dem Namen \\\"{0}\\\" existiert bereits im aktuellen Verzeichnis. Wollen Sie diese überschreiben?\",\n  \"search\": \"Suchen\",\n  \"strikethrough\": \"Durchgestrichen\",\n  \"styles\": \"Stil\",\n  \"subscript\": \"Tiefgestellt\",\n  \"superscript\": \"Hochgestellt\",\n  \"underline\": \"Unterstrichen\",\n  \"unlink\": \"Hyperlink entfernen\",\n  \"uploadFile\": \"Hochladen\",\n  \"createTable\": \"Tabelle einfügen\",\n  \"addColumnLeft\": \"Spalte links einfügen\",\n  \"addColumnRight\": \"Spalte rechts einfügen\",\n  \"addRowAbove\": \"Zeile oberhalb einfügen\",\n  \"addRowBelow\": \"Zeile unterhalb einfügen\",\n  \"deleteColumn\": \"Spalte löschen\",\n  \"deleteRow\": \"Zeile löschen\",\n  \"dropFilesHere\": \"Dateien hier fallen lassen zum hochladen\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Aktualisieren\",\n  \"insertFile\": \"Datei einfügen\",\n  \"dialogOk\": \"OK\",\n  \"tableWizard\": \"Tabellen-Assistent\",\n  \"tableTab\": \"Tabelle\",\n  \"cellTab\": \"Tabellenzelle\",\n  \"accessibilityTab\": \"Zugänglichkeit\",\n  \"caption\": \"Erklärung\",\n  \"summary\": \"Zusammenfassung\",\n  \"width\": \"Breite\",\n  \"height\": \"Höhe\",\n  \"cellSpacing\": \"Zellabstand\",\n  \"cellPadding\": \"Zellauffüllung\",\n  \"cellMargin\": \"Zellenrand\",\n  \"alignment\": \"Ausrichtung\",\n  \"background\": \"Hintergrund\",\n  \"cssClass\": \"CSS Klasse\",\n  \"id\": \"Id\",\n  \"border\": \"Rahmen\",\n  \"borderStyle\": \"Rahmenstil\",\n  \"collapseBorders\": \"Collapse rahmen\",\n  \"wrapText\": \"Texthülle\",\n  \"associateCellsWithHeaders\": \"Zellen mit header verbinden\",\n  \"alignLeft\": \"Ausrichten links\",\n  \"alignCenter\": \"Ausrichten zentrum\",\n  \"alignRight\": \"Ausrichten recht\",\n  \"alignLeftTop\": \"Ausrichten links und oben\",\n  \"alignCenterTop\": \"Ausrichten zentrum und oben\",\n  \"alignRightTop\": \"Ausrichten recht und oben\",\n  \"alignLeftMiddle\": \"Ausrichten links und mittlere\",\n  \"alignCenterMiddle\": \"Ausrichten zentrum und mittlere\",\n  \"alignRightMiddle\": \"Ausrichten recht und mittlere\",\n  \"alignLeftBottom\": \"Ausrichten links und unten\",\n  \"alignCenterBottom\": \"Ausrichten zentrum und unten\",\n  \"alignRightBottom\": \"Ausrichten recht und unten\",\n  \"alignRemove\": \"Entfernen ausrichtung\",\n  \"columns\": \"Spalten\",\n  \"rows\": \"Reihen\",\n  \"selectAllCells\": \"Wählen alle tabellenzellen\"\n});\n}\n\n/* FileBrowser and ImageBrowser messages */\n\nvar browserMessages = {\n  \"uploadFile\" : \"Hochladen\",\n  \"orderBy\" : \"Sortieren nach\",\n  \"orderByName\" : \"Name\",\n  \"orderBySize\" : \"Größe\",\n  \"directoryNotFound\" : \"Das Verzeichnis wurde nicht gefunden.\",\n  \"emptyFolder\" : \"Leeres Verzeichnis\",\n  \"deleteFile\" : 'Sind Sie sicher, dass Sie \"{0}\" wirklich löschen wollen?',\n  \"invalidFileType\" : \"Die ausgewählte Datei \\\"{0}\\\" ist ungültig. Unterstützte Dateitypen sind {1}.\",\n  \"overwriteFile\" : \"Eine Datei namens \\\"{0}\\\" existiert bereits im aktuellen Ordner. Überschreiben?\",\n  \"dropFilesHere\" : \"Dateien hier verschieben\",\n  \"search\": \"Suchen\"\n};\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages, browserMessages);\n}\n\nif (kendo.ui.ImageBrowser) {\nkendo.ui.ImageBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.ImageBrowser.prototype.options.messages, browserMessages);\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Löschen\",\n  \"filter\": \"Filter\",\n  \"isFalse\": \"ist falsch\",\n  \"isTrue\": \"ist richtig\",\n  \"operator\": \"Operator\"\n});\n}\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"Und\",\n  \"clear\": \"Löschen\",\n  \"filter\": \"Filtern\",\n  \"info\": \"Zeilen mit Werten anzeigen, die\",\n  \"isFalse\": \"falsch\",\n  \"isTrue\": \"richtig\",\n  \"or\": \"Oder\",\n  \"selectValue\": \"-Wählen Sie-\",\n  \"cancel\": \"Abbrechen\",\n  \"operator\": \"Operator\",\n  \"value\": \"Wert\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"Abbrechen\",\n    \"cancel\": \"Änderungen verwerfen\",\n    \"create\": \"Neuen Datensatz hinzufügen\",\n    \"destroy\": \"Löschen\",\n    \"edit\": \"Bearbeiten\",\n    \"excel\": \"Export nach Excel\",\n    \"pdf\": \"Export als PDF\",\n    \"save\": \"Änderungen speichern\",\n    \"select\": \"Wähle\",\n    \"update\": \"Aktualisieren\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Sind Sie sicher, dass Sie diesen Datensatz löschen wollen?\",\n    \"cancelDelete\": \"Abbrechen\",\n    \"confirmDelete\": \"Löschen\"\n  },\n  \"noRecords\": \"Keine Aufzeichnungen zur Verfügung.\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Ziehen Sie eine Spaltenüberschrift hierher, um nach dieser Spalte zu gruppieren\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"Wert erhöhen\",\n  \"downArrowText\": \"Wert verringern\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"Einträge {0} - {1} von {2}\",\n  \"empty\": \"keine Daten\",\n  \"first\": \"Zur ersten Seite\",\n  \"itemsPerPage\": \"Elemente pro Seite\",\n  \"last\": \"Zur letzten Seite\",\n  \"next\": \"Zur nächsten Seite\",\n  \"of\": \"von {0}\",\n  \"page\": \"Seite\",\n  \"previous\": \"Zur vorherigen Seite\",\n  \"refresh\": \"Aktualisieren\",\n  \"morePages\": \"Weitere Seiten\"\n});\n}\n\n/* TreeList messages */\n\nif (kendo.ui.TreeList) {\nkendo.ui.TreeList.prototype.options.messages =\n$.extend(true, kendo.ui.TreeList.prototype.options.messages, {\n    noRows: \"Es sind keine Daten vorhanden\",\n    loading: \"Lade Daten...\",\n    requestFailed: \"Laden fehlgeschlagen\",\n    retry: \"Neu laden\",\n    commands: {\n        edit: \"Bearbeiten\",\n        update: \"Aktualisieren\",\n        canceledit: \"Abbrechen\",\n        create: \"Neuen Datensatz hinzufügen\",\n        createchild: \"Kind-Datensatz hinzufügen\",\n        destroy: \"Löschen\",\n        excel: \"Als Excel exportieren\",\n        pdf: \"Als PDF exportieren\"\n    }\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Beenden\",\n  \"dropFilesHere\": \"Dateien hier fallen lassen zum Hochladen\",\n  \"remove\": \"Löschen\",\n  \"retry\": \"Wiederholen\",\n  \"select\": \"Wählen Sie...\",\n  \"statusFailed\": \"nicht erfolgreich\",\n  \"statusWarning\": \"Warnung\",\n  \"statusUploaded\": \"hochgeladen\",\n  \"statusUploading\": \"hochladen\",\n  \"uploadSelectedFiles\": \"Dateien hochladen\",\n  \"headerStatusUploaded\": \"Hochgeladen\",\n  \"headerStatusUploading\": \"Hochladen...\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"Ganzer Tag\",\n  \"cancel\": \"Abbrechen\",\n  \"date\": \"Datum\",\n  \"destroy\": \"Löschen\",\n  \"pdf\": \"Exportieren als PDF\",\n  \"editable\": {\n    \"confirmation\": \"Möchten Sie diesen Termin wirklich löschen?\"\n  },\n  \"editor\": {\n    \"allDayEvent\": \"Ganztägiger Termin\",\n    \"description\": \"Beschreibung\",\n    \"editorTitle\": \"Termin\",\n    \"end\": \"Beenden\",\n    \"timezoneTitle\": \"Zeitzone\",\n    \"endTimezone\": \"Zeitzone Ende\",\n    \"repeat\": \"Wiederholen\",\n    \"separateTimezones\": \"Unterschiedliche Start- und Endzeitzonen benutzen\",\n    \"start\": \"Starten\",\n    \"startTimezone\": \"Zeitzone Start\",\n    \"timezone\": \"Zeitzonen bearbeiten\",\n    \"timezoneEditorButton\": \"Zeitzone\",\n    \"timezoneEditorTitle\": \"Zeitzonen\",\n    \"title\": \"Titel\",\n    \"noTimezone\": \"Keine Zeitzone\"\n  },\n  \"event\": \"Termin\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Möchten Sie nur diesen Termin oder alle Wiederholungen löschen?\",\n    \"deleteWindowOccurrence\": \"Diesen Termin löschen\",\n    \"deleteWindowSeries\": \"Alle Wiederholungen des Termins löschen\",\n    \"deleteWindowTitle\": \"Diesen Termin und alle Wiederholungen löschen\",\n    \"editRecurring\": \"Möchten Sie nur diesen Termin oder alle Wiederholungen bearbeiten?\",\n    \"editWindowOccurrence\": \"Aktuelles Ereignis bearbeiten\",\n    \"editWindowSeries\": \"Serie bearbeiten\",\n    \"editWindowTitle\": \"Wiederholungseintrag bearbeiten\"\n  },\n  \"save\": \"Speichern\",\n  \"time\": \"Zeit\",\n  \"today\": \"Heute\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Tag\",\n    \"month\": \"Monat\",\n    \"week\": \"Woche\",\n    \"workWeek\": \"Arbeitswoche\",\n    \"timeline\": \"Zeitstrahl\",\n    \"timelineWeek\": \"Zeitstrahl Woche\",\n    \"timelineWorkWeek\": \"Zeitstrahl Arbeitswoche\",\n    \"timelineMonth\": \"Zeitstrahl Monat\"\n  },\n  \"deleteWindowTitle\": \"Termin löschen\",\n  \"defaultRowText\": \"Alle Termine\",\n  \"showFullDay\": \"Ganzen Tag anzeigen\",\n  \"showWorkDay\": \"Geschäftszeiten anzeigen\",\n  \"ariaSlotLabel\": \"Ausgewählt von {0:t} bis {1:t}\",\n  \"ariaEventLabel\": \"{0} am {1:D} um {2:t}\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} ist notwendig\",\n  \"pattern\": \"{0} ist ungültig\",\n  \"min\": \"{0} muss größer oder gleich sein als {1}\",\n  \"max\": \"{0} muss kleiner oder gleich sein als {1}\",\n  \"step\": \"{0} ist ungültig\",\n  \"email\": \"{0} ist keine gültige E-Mail\",\n  \"url\": \"{0} ist keine gültige URL\",\n  \"date\": \"{0} ist kein gültiges Datum\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Schließen\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Abbrechen\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Abbrechen\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}