{"version": 3, "sources": ["messages/kendo.messages.nb-NO.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "clear", "filter", "isFalse", "isTrue", "operator", "and", "info", "or", "selectValue", "cancel", "value", "FilterMultiCheck", "search", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "confirmation", "cancelDelete", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "next", "page", "previous", "refresh", "morePages", "Upload", "localization", "retry", "remove", "uploadSelectedFiles", "dropFilesHere", "statusFailed", "statusUploaded", "statusUploading", "headerStatusUploaded", "headerStatusUploading", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACTF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACjDE,MACIC,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,6BACPC,IAAO,mBAEXC,QACIN,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,gBACNC,IAAO,0BACPC,IAAO,mBAEXE,QACIC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,cACZV,GAAM,aACNK,IAAO,kBACPM,WAAc,eAElBC,OACIZ,GAAM,aACNK,IAAO,sBAOfb,MAAMC,GAAGoB,aACTrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACjDE,MACIC,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,mBACNC,IAAO,4BACPC,IAAO,mBAEXC,QACIN,GAAM,aACNC,GAAM,gBACNC,IAAO,0BACPC,GAAM,gBACNC,IAAO,0BACPC,IAAO,mBAEXE,QACIC,SAAY,aACZC,eAAkB,kBAClBC,SAAY,cACZV,GAAM,aACNK,IAAO,kBACPM,WAAc,eAElBC,OACIZ,GAAM,aACNK,IAAO,sBAOfb,MAAMC,GAAGqB,aACTtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACjDC,QAAW,WACXC,cAAiB,mBACjBC,eAAkB,mBAClBC,SAAY,sBACZC,KAAQ,OACRC,KAAQ,MACRC,OAAU,aAMd9B,MAAMC,GAAG8B,mBACT/B,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,UACvDS,OACIC,SAAY,UACZC,YAAe,gBAEnBC,KACIC,MAAS,QACTC,WAAc,gBACdC,MAAS,SACTC,MAAS,QACTC,GAAM,KACNC,YAAe,WAEnBC,aACIV,MAAS,SACTW,QAAW,WACXJ,MAAS,QACTK,OAAU,WACVC,OAAU,SAEdF,SACIG,IAAO,MACPb,SAAY,YACZC,YAAe,eACfa,SAAY,cAEhBC,iBACIC,MAAS,SACTC,OAAU,SACVC,KAAQ,QACRC,OAAU,QACVC,MAAS,UAEbT,QACIV,YAAe,eACfa,SAAY,aACZd,SAAY,UAEhBY,QACIS,GAAM,KACNpB,YAAe,eACfa,SAAY,aACZd,SAAY,MAEhBsB,UACIT,IAAO,MACPU,QAAW,SACXC,QAAW,eAOnBzD,MAAMC,GAAGC,aACTF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACjDmC,MAAS,QACTC,OAAU,WACVC,QAAW,WACXC,OAAU,UACVC,SAAY,cAMhB9D,MAAMC,GAAGoB,aACTrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACjDwC,IAAO,KACPL,MAAS,QACTC,OAAU,WACVK,KAAQ,wBACRJ,QAAW,WACXC,OAAU,UACVI,GAAM,QACNC,YAAe,SACfC,OAAU,SACVL,SAAY,WACZM,MAAS,WAMbpE,MAAMC,GAAGoE,mBACbrE,MAAMC,GAAGoE,iBAAiBlE,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoE,iBAAiBlE,UAAUC,QAAQmB,UACzD+C,OAAU,SAMRtE,MAAMC,GAAGsE,OACTvE,MAAMC,GAAGsE,KAAKpE,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsE,KAAKpE,UAAUC,QAAQmB,UAC3CiD,UACIC,WAAc,SACdN,OAAU,mBACVO,OAAU,gBACVC,QAAW,QACXC,KAAQ,QACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,QACRC,OAAU,OACVC,OAAU,YAEdC,UACIC,aAAgB,iDAChBC,aAAgB,SAChBC,cAAiB,YAOzBrF,MAAMC,GAAGqF,YACTtF,MAAMC,GAAGqF,UAAUnF,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqF,UAAUnF,UAAUC,QAAQmB,UAChDgE,MAAS,sDAMbvF,MAAMC,GAAGuF,QACTxF,MAAMC,GAAGuF,MAAMrF,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuF,MAAMrF,UAAUC,QAAQmB,UAC5CkE,SAAY,OACZC,QAAW,0BACXH,MAAS,0BACTtC,MAAS,qBACT0C,aAAgB,kBAChBxC,KAAQ,qBACRyC,KAAQ,oBACRtC,GAAM,SACNuC,KAAQ,OACRC,SAAY,sBACZC,QAAW,YACXC,UAAa,iBAMjBhG,MAAMC,GAAGgG,SACTjG,MAAMC,GAAGgG,OAAO9F,UAAUC,QAAQ8F,aAClCpG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgG,OAAO9F,UAAUC,QAAQ8F,cAC7C/B,OAAU,SACVgC,MAAS,eACTnB,OAAU,UACVoB,OAAU,QACVC,oBAAuB,iBACvBC,cAAiB,kCACjBC,aAAgB,aAChBC,eAAkB,YAClBC,gBAAmB,aACnBC,qBAAwB,SACxBC,sBAAyB,mBAM7B3G,MAAMC,GAAG2G,SACT5G,MAAMC,GAAG2G,OAAOzG,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2G,OAAOzG,UAAUC,QAAQmB,UAC7CsF,KAAQ,UACRC,WAAc,gBACdC,SAAY,gBACZC,gBAAmB,mBACnBC,SAAY,iBACZC,gBAAmB,oBACnBC,YAAe,cACfC,OAAU,iBACVC,WAAc,gBACdC,YAAe,iBACfC,kBAAqB,0BACrBC,oBAAuB,sBACvBC,OAAU,SACVC,cAAiB,iBACjBC,YAAe,wBACfC,YAAe,uBACfC,aAAgB,qBAChBC,QAAW,oBACXC,cAAiB,iBACjBC,OAAU,OACVC,UAAa,YACbC,YAAe,UACfC,UAAa,eACbC,OAAU,cACVC,WAAc,0CACdC,kBAAqB,yCACrBC,YAAe,YACfC,gBAAmB,uDACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,YACfC,cAAiB,mFACjBC,WAAc,WACdC,UAAa,iBACbC,UAAa,QACbzC,cAAiB,kCACjB0C,sBAAyB,QACzBC,aAAgB,SAChBC,aAAgB,WAChBC,aAAgB,mBAChBC,gBAAmB,cACnBC,oBAAuB,0BACvBC,SAAY,QACZC,YAAe,aACfC,eAAkB,cAClBlF,OAAU,MACVmF,YAAe,aACfC,cAAiB,8BACjBC,eAAkB,4BAClBC,YAAe,oBACfC,YAAe,qBACfC,aAAgB,gBAChBC,UAAa,YACbC,WAAc,SACdC,SAAY,WACZC,aAAgB,WAChBC,WAAc,kBAMlBnK,MAAMC,GAAGmK,YACTpK,MAAMC,GAAGmK,UAAUjK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmK,UAAUjK,UAAUC,QAAQmB,UAChD8I,OAAU,aACVlG,OAAU,SACVe,UACIC,aAAgB,oDAEpB5E,KAAQ,OACRoE,QAAW,QACX2F,QACIC,YAAe,kBACfC,YAAe,cACfC,YAAe,UACftI,IAAO,QACPuI,YAAe,iBACfC,OAAU,SACVC,kBAAqB,4CACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,WACZC,qBAAwB,WACxBC,oBAAuB,YACvBC,MAAS,SACTC,WAAc,kBAElBC,MAAS,UACTC,oBACIC,gBAAmB,iFACnBC,uBAA0B,0BAC1BC,mBAAsB,yBACtBC,kBAAqB,8BACrBC,cAAiB,mFACjBC,qBAAwB,4BACxBC,iBAAoB,2BACpBC,gBAAmB,+BAEvB9G,KAAQ,QACR+G,KAAQ,MACRC,MAAS,QACTC,OACIC,OAAU,SACVnJ,IAAO,MACPoJ,MAAS,QACTC,KAAQ,MACRC,SAAY,cAEhBX,kBAAqB,gBACrBY,YAAe,eACfC,YAAe,sBAMnBtM,MAAMC,GAAGsM,SACbvM,MAAMC,GAAGsM,OAAOpM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,OAAOpM,UAAUC,QAAQ8F,cAC/CsG,MAAS,WAMPxM,MAAMC,GAAGwM,QACbzM,MAAMC,GAAGwM,MAAMtM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,MAAMtM,UAAUC,QAAQ8F,cAC9CwG,OAAU,QAMR1M,MAAMC,GAAG0M,UACb3M,MAAMC,GAAG0M,QAAQxM,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,QAAQxM,UAAUC,QAAQ8F,cAChDwG,OAAU,KACVvI,OAAU,YAKRnE,MAAMC,GAAG2M,SACb5M,MAAMC,GAAG2M,OAAOzM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,OAAOzM,UAAUC,QAAQ8F,cAC/CwG,OAAU,KACVvI,OAAU,aAIT0I,OAAO7M,MAAM8M", "file": "kendo.messages.nb-NO.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\r\n/* Filter cell operator messages */\r\n\r\nif (kendo.ui.FilterCell) {\r\n    kendo.ui.FilterCell.prototype.options.operators =\r\n    $.extend(true, kendo.ui.FilterCell.prototype.options.operators, {\r\n        \"date\": {\r\n            \"eq\": \"Er lik med\",\r\n            \"gt\": \"Er senere enn\",\r\n            \"gte\": \"Er lik eller senere enn\",\r\n            \"lt\": \"Er tidligere enn\",\r\n            \"lte\": \"Er lik eller tidligere enn\",\r\n            \"neq\": \"Er ikke lik med\"\r\n        },\r\n        \"number\": {\r\n            \"eq\": \"Er lik med\",\r\n            \"gt\": \"Er større enn\",\r\n            \"gte\": \"Er lik eller større enn\",\r\n            \"lt\": \"Er mindre enn\",\r\n            \"lte\": \"Er lik eller mindre enn\",\r\n            \"neq\": \"Er ikke lik med\"\r\n        },\r\n        \"string\": {\r\n            \"contains\": \"Inneholder\",\r\n            \"doesnotcontain\": \"Inneholder ikke\",\r\n            \"endswith\": \"Slutter med\",\r\n            \"eq\": \"Er lik med\",\r\n            \"neq\": \"Er ikke lik med\",\r\n            \"startswith\": \"Starter med\"\r\n        },\r\n        \"enums\": {\r\n            \"eq\": \"Er lik med\",\r\n            \"neq\": \"Er ikke lik med\"\r\n        }\r\n    });\r\n}\r\n\r\n/* Filter menu operator messages */\r\n\r\nif (kendo.ui.FilterMenu) {\r\n    kendo.ui.FilterMenu.prototype.options.operators =\r\n    $.extend(true, kendo.ui.FilterMenu.prototype.options.operators, {\r\n        \"date\": {\r\n            \"eq\": \"Er lik med\",\r\n            \"gt\": \"Er senere enn\",\r\n            \"gte\": \"Er lik eller senere enn\",\r\n            \"lt\": \"Er tidligere enn\",\r\n            \"lte\": \"Er lik eller tidigere enn\",\r\n            \"neq\": \"Er ikke lik med\"\r\n        },\r\n        \"number\": {\r\n            \"eq\": \"Er lik med\",\r\n            \"gt\": \"Er større enn\",\r\n            \"gte\": \"Er lik eller større enn\",\r\n            \"lt\": \"Er mindre enn\",\r\n            \"lte\": \"Er lik eller mindre enn\",\r\n            \"neq\": \"Er ikke lik med\"\r\n        },\r\n        \"string\": {\r\n            \"contains\": \"Inneholder\",\r\n            \"doesnotcontain\": \"Inneholder ikke\",\r\n            \"endswith\": \"Slutter med\",\r\n            \"eq\": \"Er lik med\",\r\n            \"neq\": \"Er ikke lik med\",\r\n            \"startswith\": \"Starter med\"\r\n        },\r\n        \"enums\": {\r\n            \"eq\": \"Er lik med\",\r\n            \"neq\": \"Er ikke lik med\"\r\n        }\r\n    });\r\n}\r\n\r\n/* ColumnMenu messages */\r\n\r\nif (kendo.ui.ColumnMenu) {\r\n    kendo.ui.ColumnMenu.prototype.options.messages =\r\n    $.extend(true, kendo.ui.ColumnMenu.prototype.options.messages, {\r\n        \"columns\": \"Kolonner\",\r\n        \"sortAscending\": \"Sortere fallende\",\r\n        \"sortDescending\": \"Sortere stigende\",\r\n        \"settings\": \"Kolonneinstillinger\",\r\n        \"done\": \"Klar\",\r\n        \"lock\": \"Lås\",\r\n        \"unlock\": \"Lås opp\"\r\n    });\r\n}\r\n\r\n/* RecurrenceEditor messages */\r\n\r\nif (kendo.ui.RecurrenceEditor) {\r\n    kendo.ui.RecurrenceEditor.prototype.options.messages =\r\n    $.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages, {\r\n        \"daily\": {\r\n            \"interval\": \"dag(er)\",\r\n            \"repeatEvery\": \"Gjenta hver:\"\r\n        },\r\n        \"end\": {\r\n            \"after\": \"Etter\",\r\n            \"occurrence\": \"forekomst(er)\",\r\n            \"label\": \"Slutt:\",\r\n            \"never\": \"Aldri\",\r\n            \"on\": \"På\",\r\n            \"mobileLabel\": \"Slutter\"\r\n        },\r\n        \"frequencies\": {\r\n            \"daily\": \"Daglig\",\r\n            \"monthly\": \"Månedlig\",\r\n            \"never\": \"Aldri\",\r\n            \"weekly\": \"Ukentlig\",\r\n            \"yearly\": \"Årlig\"\r\n        },\r\n        \"monthly\": {\r\n            \"day\": \"Dag\",\r\n            \"interval\": \"måned(er)\",\r\n            \"repeatEvery\": \"Gjenta hver:\",\r\n            \"repeatOn\": \"Gjenta på:\"\r\n        },\r\n        \"offsetPositions\": {\r\n            \"first\": \"første\",\r\n            \"fourth\": \"fjerde\",\r\n            \"last\": \"siste\",\r\n            \"second\": \"andre\",\r\n            \"third\": \"tredje\"\r\n        },\r\n        \"weekly\": {\r\n            \"repeatEvery\": \"Gjenta hver:\",\r\n            \"repeatOn\": \"Gjenta på:\",\r\n            \"interval\": \"uke(r)\"\r\n        },\r\n        \"yearly\": {\r\n            \"of\": \"av\",\r\n            \"repeatEvery\": \"Gjenta hver:\",\r\n            \"repeatOn\": \"Gjenta på:\",\r\n            \"interval\": \"år\"\r\n        },\r\n        \"weekdays\": {\r\n            \"day\": \"dag\",\r\n            \"weekday\": \"ukedag\",\r\n            \"weekend\": \"helgedag\"\r\n        }\r\n    });\r\n}\r\n\r\n/* FilterCell messages */\r\n\r\nif (kendo.ui.FilterCell) {\r\n    kendo.ui.FilterCell.prototype.options.messages =\r\n    $.extend(true, kendo.ui.FilterCell.prototype.options.messages, {\r\n        \"clear\": \"Fjern\",\r\n        \"filter\": \"Filtrere\",\r\n        \"isFalse\": \"Er usann\",\r\n        \"isTrue\": \"Er sant\",\r\n        \"operator\": \"Operator\"\r\n    });\r\n}\r\n\r\n/* FilterMenu messages */\r\n\r\nif (kendo.ui.FilterMenu) {\r\n    kendo.ui.FilterMenu.prototype.options.messages =\r\n    $.extend(true, kendo.ui.FilterMenu.prototype.options.messages, {\r\n        \"and\": \"Og\",\r\n        \"clear\": \"Fjern\",\r\n        \"filter\": \"Filtrere\",\r\n        \"info\": \"Vis poster med verdi:\",\r\n        \"isFalse\": \"Er usann\",\r\n        \"isTrue\": \"Er sant\",\r\n        \"or\": \"Eller\",\r\n        \"selectValue\": \"-Velg-\",\r\n        \"cancel\": \"Avbryt\",\r\n        \"operator\": \"Operator\",\r\n        \"value\": \"Verdi\"\r\n    });\r\n}\r\n\r\n/* FilterMultiCheck messages */\r\n\r\nif (kendo.ui.FilterMultiCheck) {\r\nkendo.ui.FilterMultiCheck.prototype.options.messages =\r\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\r\n  \"search\": \"Søk\"\r\n});\r\n}\r\n\r\n/* Grid messages */\r\n\r\nif (kendo.ui.Grid) {\r\n    kendo.ui.Grid.prototype.options.messages =\r\n    $.extend(true, kendo.ui.Grid.prototype.options.messages, {\r\n        \"commands\": {\r\n            \"canceledit\": \"Avbryt\",\r\n            \"cancel\": \"Avbryt endringer\",\r\n            \"create\": \"Legg til post\",\r\n            \"destroy\": \"Slett\",\r\n            \"edit\": \"Endre\",\r\n            \"excel\": \"Export to Excel\",\r\n            \"pdf\": \"Export to PDF\",\r\n            \"save\": \"Lagre\",\r\n            \"select\": \"Velg\",\r\n            \"update\": \"Oppdater\"\r\n        },\r\n        \"editable\": {\r\n            \"confirmation\": \"Er du sikker på at du vil slette denna posten?\",\r\n            \"cancelDelete\": \"Avbryt\",\r\n            \"confirmDelete\": \"Slett\"\r\n        }\r\n    });\r\n}\r\n\r\n/* Groupable messages */\r\n\r\nif (kendo.ui.Groupable) {\r\n    kendo.ui.Groupable.prototype.options.messages =\r\n    $.extend(true, kendo.ui.Groupable.prototype.options.messages, {\r\n        \"empty\": \"Dra en kolonne hit for å sortere på den kolonnen\"\r\n    });\r\n}\r\n\r\n/* Pager messages */\r\n\r\nif (kendo.ui.Pager) {\r\n    kendo.ui.Pager.prototype.options.messages =\r\n    $.extend(true, kendo.ui.Pager.prototype.options.messages, {\r\n        \"allPages\": \"Alle\",\r\n        \"display\": \"{0} - {1} av {2} poster\",\r\n        \"empty\": \"Det finnes ingen poster\",\r\n        \"first\": \"Gå til første side\",\r\n        \"itemsPerPage\": \"poster per side\",\r\n        \"last\": \"Gå til siste siden\",\r\n        \"next\": \"Gå til neste side\",\r\n        \"of\": \"av {0}\",\r\n        \"page\": \"Side\",\r\n        \"previous\": \"Gå til forrige side\",\r\n        \"refresh\": \"Oppdatere\",\r\n        \"morePages\": \"Flere sider\"\r\n    });\r\n}\r\n\r\n/* Upload messages */\r\n\r\nif (kendo.ui.Upload) {\r\n    kendo.ui.Upload.prototype.options.localization =\r\n    $.extend(true, kendo.ui.Upload.prototype.options.localization, {\r\n        \"cancel\": \"Avbryt\",\r\n        \"retry\": \"Forsøk igjen\",\r\n        \"select\": \"Velg...\",\r\n        \"remove\": \"Fjern\",\r\n        \"uploadSelectedFiles\": \"Last opp filer\",\r\n        \"dropFilesHere\": \"slipp filer her for å laste opp\",\r\n        \"statusFailed\": \"misslyktes\",\r\n        \"statusUploaded\": \"opplastet\",\r\n        \"statusUploading\": \"laster opp\",\r\n        \"headerStatusUploaded\": \"Ferdig\",\r\n        \"headerStatusUploading\": \"Laster opp...\"\r\n    });\r\n}\r\n\r\n/* Editor messages */\r\n\r\nif (kendo.ui.Editor) {\r\n    kendo.ui.Editor.prototype.options.messages =\r\n    $.extend(true, kendo.ui.Editor.prototype.options.messages, {\r\n        \"bold\": \"Uthevet\",\r\n        \"createLink\": \"Legg til link\",\r\n        \"fontName\": \"Velg fontnavn\",\r\n        \"fontNameInherit\": \"(arvet fontnavn)\",\r\n        \"fontSize\": \"Velg størrelse\",\r\n        \"fontSizeInherit\": \"(arvet størrelse)\",\r\n        \"formatBlock\": \"Formatering\",\r\n        \"indent\": \"Øk indentasjon\",\r\n        \"insertHtml\": \"Legg til HTML\",\r\n        \"insertImage\": \"Legg til bilde\",\r\n        \"insertOrderedList\": \"Legg til numerert liste\",\r\n        \"insertUnorderedList\": \"Legg til punktliste\",\r\n        \"italic\": \"Kursiv\",\r\n        \"justifyCenter\": \"Sentrert tekst\",\r\n        \"justifyFull\": \"Marginaljustert tekst\",\r\n        \"justifyLeft\": \"Venstrejustert tekst\",\r\n        \"justifyRight\": \"Høyrejustert tekst\",\r\n        \"outdent\": \"Minsk indentasjon\",\r\n        \"strikethrough\": \"Gjennomstreket\",\r\n        \"styles\": \"Stil\",\r\n        \"subscript\": \"Nedsenket\",\r\n        \"superscript\": \"Opphøyd\",\r\n        \"underline\": \"Understreket\",\r\n        \"unlink\": \"Fjern lenke\",\r\n        \"deleteFile\": \"Er du sikker på at du vil slette \\\"{0}\\\"?\",\r\n        \"directoryNotFound\": \"En mappe med dette navnet finnes ikke.\",\r\n        \"emptyFolder\": \"Tom mappe\",\r\n        \"invalidFileType\": \"Filen \\\"{0}\\\" er ikke gyldig. Tilatte filtyper er {1}.\",\r\n        \"orderBy\": \"Sortere på:\",\r\n        \"orderByName\": \"Navn\",\r\n        \"orderBySize\": \"Størrelse\",\r\n        \"overwriteFile\": \"'En fil med navn \\\"{0}\\\" finnes allerede i aktuell mappe. Vil du skrive over den?\",\r\n        \"uploadFile\": \"Last opp\",\r\n        \"backColor\": \"Bakgrunnsfarge\",\r\n        \"foreColor\": \"Farge\",\r\n        \"dropFilesHere\": \"slipp filer her for å laste opp\",\r\n        \"dialogButtonSeparator\": \"eller\",\r\n        \"dialogCancel\": \"Avbryt\",\r\n        \"dialogInsert\": \"Legg til\",\r\n        \"imageAltText\": \"Alternativ tekst\",\r\n        \"imageWebAddress\": \"Webbadresse\",\r\n        \"linkOpenInNewWindow\": \"Åpne lenke i nytt vindu\",\r\n        \"linkText\": \"Tekst\",\r\n        \"linkToolTip\": \"Skjermtips\",\r\n        \"linkWebAddress\": \"Webbadresse\",\r\n        \"search\": \"Søk\",\r\n        \"createTable\": \"Lag tabell\",\r\n        \"addColumnLeft\": \"Legg til kolonne på venstre\",\r\n        \"addColumnRight\": \"Legg til kolonne på høyre\",\r\n        \"addRowAbove\": \"Legg til rad over\",\r\n        \"addRowBelow\": \"Legg til rad under\",\r\n        \"deleteColumn\": \"Slett kolonne\",\r\n        \"deleteRow\": \"Slett rad\",\r\n        \"formatting\": \"Format\",\r\n        \"viewHtml\": \"Vis HTML\",\r\n        \"dialogUpdate\": \"Oppdater\",\r\n        \"insertFile\": \"Sett inn fil\"\r\n    });\r\n}\r\n\r\n/* Scheduler messages */\r\n\r\nif (kendo.ui.Scheduler) {\r\n    kendo.ui.Scheduler.prototype.options.messages =\r\n    $.extend(true, kendo.ui.Scheduler.prototype.options.messages, {\r\n        \"allDay\": \"hele dagen\",\r\n        \"cancel\": \"Avbryt\",\r\n        \"editable\": {\r\n            \"confirmation\": \"Er du sikker på at du vil slette denne oppgaven?\"\r\n        },\r\n        \"date\": \"Dato\",\r\n        \"destroy\": \"Slett\",\r\n        \"editor\": {\r\n            \"allDayEvent\": \"Heldags oppgave\",\r\n            \"description\": \"Beskrivelse\",\r\n            \"editorTitle\": \"Oppgave\",\r\n            \"end\": \"Slutt\",\r\n            \"endTimezone\": \"Slutt tidssone\",\r\n            \"repeat\": \"Gjenta\",\r\n            \"separateTimezones\": \"Bruk forskjellig start og slutt tidssoner\",\r\n            \"start\": \"Start\",\r\n            \"startTimezone\": \"Start tidssone\",\r\n            \"timezone\": \"tidssone\",\r\n            \"timezoneEditorButton\": \"Tidssone\",\r\n            \"timezoneEditorTitle\": \"Tidssoner\",\r\n            \"title\": \"Tittel\",\r\n            \"noTimezone\": \"Ingen tidssone\"\r\n        },\r\n        \"event\": \"Oppgave\",\r\n        \"recurrenceMessages\": {\r\n            \"deleteRecurring\": \"Vil du slette bare denne forekomsten eller alle forekomster av denne oppgaven?\",\r\n            \"deleteWindowOccurrence\": \"Slett denne forekomsten\",\r\n            \"deleteWindowSeries\": \"Slett alle forekomster\",\r\n            \"deleteWindowTitle\": \"Slett gjentagende forekomst\",\r\n            \"editRecurring\": \"Vil du redigere bare denne forekomsten eller alle forekomster av denne oppgaven?\",\r\n            \"editWindowOccurrence\": \"Rediger denne forekomsten\",\r\n            \"editWindowSeries\": \"Rediger alle forekomster\",\r\n            \"editWindowTitle\": \"Rediger gjentagende oppgave\"\r\n        },\r\n        \"save\": \"Lagre\",\r\n        \"time\": \"Tid\",\r\n        \"today\": \"I dag\",\r\n        \"views\": {\r\n            \"agenda\": \"Agenda\",\r\n            \"day\": \"Dag\",\r\n            \"month\": \"Måned\",\r\n            \"week\": \"Uke\",\r\n            \"workWeek\": \"Arbeidsuke\"\r\n        },\r\n        \"deleteWindowTitle\": \"Slett oppgave\",\r\n        \"showFullDay\": \"Vis full dag\",\r\n        \"showWorkDay\": \"Vis arbeidstimer\"\r\n    });\r\n}\r\n\r\n/* Dialog */\r\n\r\nif (kendo.ui.Dialog) {\r\nkendo.ui.Dialog.prototype.options.messages =\r\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\r\n  \"close\": \"Lukke\"\r\n});\r\n}\r\n\r\n/* Alert */\r\n\r\nif (kendo.ui.Alert) {\r\nkendo.ui.Alert.prototype.options.messages =\r\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\r\n  \"okText\": \"OK\"\r\n});\r\n}\r\n\r\n/* Confirm */\r\n\r\nif (kendo.ui.Confirm) {\r\nkendo.ui.Confirm.prototype.options.messages =\r\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\r\n  \"okText\": \"OK\",\r\n  \"cancel\": \"Avbryt\"\r\n});\r\n}\r\n\r\n/* Prompt */\r\nif (kendo.ui.Prompt) {\r\nkendo.ui.Prompt.prototype.options.messages =\r\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\r\n  \"okText\": \"OK\",\r\n  \"cancel\": \"Avbryt\"\r\n});\r\n}\r\n\r\n})(window.kendo.jQuery);\r\n}));"]}