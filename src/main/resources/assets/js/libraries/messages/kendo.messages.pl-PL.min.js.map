{"version": 3, "sources": ["messages/kendo.messages.pl-PL.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gte", "gt", "lte", "lt", "neq", "number", "string", "endswith", "startswith", "contains", "doesnotcontain", "enums", "FilterMenu", "ColumnMenu", "messages", "filter", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "create", "destroy", "canceledit", "update", "edit", "excel", "pdf", "select", "cancel", "save", "editable", "confirmation", "cancelDelete", "confirmDelete", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "clear", "isFalse", "isTrue", "operator", "and", "info", "selectValue", "or", "value", "FilterMultiCheck", "search", "Groupable", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "deleteFile", "directoryNotFound", "emptyFolder", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "backColor", "foreColor", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "dropFilesHere", "formatting", "viewHtml", "dialogUpdate", "insertFile", "browserMessages", "FileBrowser", "ImageBrowser", "Upload", "localization", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,WACNC,IAAO,0BACPC,GAAM,oBACNC,IAAO,4BACPC,GAAM,sBACNC,IAAO,eAETC,QACEN,GAAM,WACNC,IAAO,uBACPC,GAAM,iBACNC,IAAO,wBACPC,GAAM,kBACNC,IAAO,eAETE,QACEC,SAAY,gBACZR,GAAM,WACNK,IAAO,cACPI,WAAc,mBACdC,SAAY,YACZC,eAAkB,iBAEpBC,OACEZ,GAAM,WACNK,IAAO,kBAOPb,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,WACNC,IAAO,0BACPC,GAAM,oBACNC,IAAO,4BACPC,GAAM,sBACNC,IAAO,eAETC,QACEN,GAAM,WACNC,IAAO,uBACPC,GAAM,iBACNC,IAAO,wBACPC,GAAM,kBACNC,IAAO,eAETE,QACEC,SAAY,gBACZR,GAAM,WACNK,IAAO,cACPI,WAAc,mBACdC,SAAY,YACZC,eAAkB,iBAEpBC,OACEZ,GAAM,WACNK,IAAO,kBAOPb,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,OAAU,QACVC,QAAW,UACXC,cAAiB,iBACjBC,eAAkB,kBAClBC,SAAY,oBACZC,KAAQ,cACRC,KAAQ,aACRC,OAAU,gBAMR/B,MAAMC,GAAG+B,mBACbhC,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,UACzDU,OACEC,SAAY,UACZC,YAAe,iBAEjBC,KACEC,MAAS,QACTC,WAAc,gBACdC,MAAS,OACTC,MAAS,QACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,QACTW,QAAW,UACXJ,MAAS,QACTK,OAAU,SACVC,OAAU,UAEZF,SACEG,IAAO,MACPb,SAAY,WACZC,YAAe,gBACfa,SAAY,cAEdC,iBACEC,MAAS,QACTC,OAAU,SACVC,KAAQ,OACRC,OAAU,SACVC,MAAS,SAEXT,QACEV,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdY,QACES,GAAM,KACNpB,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdsB,UACET,IAAO,MACPU,QAAW,UACXC,QAAW,kBAOX1D,MAAMC,GAAG0D,OACb3D,MAAMC,GAAG0D,KAAKxD,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0D,KAAKxD,UAAUC,QAAQmB,UAC7CqC,UACEC,OAAU,QACVC,QAAW,OACXC,WAAc,SACdC,OAAU,aACVC,KAAQ,SACRC,MAAS,kBACTC,IAAO,gBACPC,OAAU,UACVC,OAAU,gBACVC,KAAQ,iBAEVC,UACEC,aAAgB,yCAChBC,aAAgB,SAChBC,cAAiB,WAOjB1E,MAAMC,GAAG0E,QACb3E,MAAMC,GAAG0E,MAAMxE,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0E,MAAMxE,UAAUC,QAAQmB,UAC9CqD,SAAY,MACZC,KAAQ,SACRC,QAAW,yCACXvB,GAAM,QACNwB,MAAS,cACTC,QAAW,UACX9B,MAAS,0BACT+B,aAAgB,YAChB7B,KAAQ,8BACR8B,KAAQ,8BACRC,SAAY,gCACZC,UAAa,kBAMXpF,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnDC,OAAU,QACV6D,MAAS,gBACTC,QAAW,QACXC,OAAU,SACVC,SAAY,cAMVxF,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDC,OAAU,QACViE,IAAO,OACPJ,MAAS,gBACTK,KAAQ,oCACRC,YAAe,oBACfL,QAAW,QACXC,OAAU,SACVK,GAAM,MACNvB,OAAU,SACVmB,SAAY,WACZK,MAAS,aAMP7F,MAAMC,GAAG6F,mBACb9F,MAAMC,GAAG6F,iBAAiB3F,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6F,iBAAiB3F,UAAUC,QAAQmB,UACzDwE,OAAU,YAMR/F,MAAMC,GAAG+F,YACbhG,MAAMC,GAAG+F,UAAU7F,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+F,UAAU7F,UAAUC,QAAQmB,UAClDwD,MAAS,qFAMP/E,MAAMC,GAAGgG,SACbjG,MAAMC,GAAGgG,OAAO9F,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgG,OAAO9F,UAAUC,QAAQmB,UAC/C2E,KAAQ,gBACRC,WAAc,aACdC,SAAY,mBACZC,gBAAmB,2BACnBC,SAAY,2BACZC,gBAAmB,0BACnBC,YAAe,wBACfC,OAAU,UACVC,WAAc,aACdC,YAAe,cACfC,kBAAqB,yBACrBC,oBAAuB,2BACvBC,OAAU,UACVC,cAAiB,gBACjBC,YAAe,iBACfC,YAAe,0BACfC,aAAgB,2BAChBC,QAAW,mBACXC,cAAiB,gBACjBC,OAAU,OACVC,UAAa,YACbC,YAAe,cACfC,UAAa,eACbC,OAAU,YACVC,WAAc,oCACdC,kBAAqB,6CACrBC,YAAe,kBACfC,gBAAmB,2EACnBC,QAAW,iBACXC,YAAe,QACfC,YAAe,UACfC,cAAiB,gFACjBC,WAAc,UACdC,UAAa,YACbC,UAAa,QACbC,sBAAyB,KACzBC,aAAgB,SAChBC,aAAgB,SAChBC,aAAgB,iBAChBC,gBAAmB,cACnBC,oBAAuB,0BACvBC,SAAY,OACZC,YAAe,UACfC,eAAkB,cAClB9C,OAAU,SACV+C,YAAe,mBACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,aAAgB,gBAChBC,UAAa,aACbC,cAAiB,4BACjBC,WAAc,SACdC,SAAY,YACZC,aAAgB,SAChBC,WAAc,gBAMhB,IAAIC,IACFxB,WAAe,SACfJ,QAAY,YACZC,YAAgB,QAChBC,YAAgB,WAChBL,kBAAsB,kDACtBC,YAAgB,eAChBC,gBAAoB,gEACpBH,WAAe,wCACfO,cAAkB,sEAClBoB,cAAkB,oCAClBtD,OAAW,SAGT/F,OAAMC,GAAG0J,cACb3J,MAAMC,GAAG0J,YAAYxJ,UAAUC,QAAQmB,SACvCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,YAAYxJ,UAAUC,QAAQmB,SAAUmI,IAG5D1J,MAAMC,GAAG2J,eACb5J,MAAMC,GAAG2J,aAAazJ,UAAUC,QAAQmB,SACxCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2J,aAAazJ,UAAUC,QAAQmB,SAAUmI,IAK7D1J,MAAMC,GAAG4J,SACb7J,MAAMC,GAAG4J,OAAO1J,UAAUC,QAAQ0J,aAClChK,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4J,OAAO1J,UAAUC,QAAQ0J,cAC/CzF,OAAU,SACVgF,cAAiB,wCACjBU,OAAU,OACVC,MAAS,QACT5F,OAAU,aACV6F,aAAgB,gBAChBC,eAAkB,aAClBC,gBAAmB,iBACnBC,oBAAuB,gBACvBC,qBAAwB,OACxBC,sBAAyB,kBAMvBtK,MAAMC,GAAGsK,YACbvK,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsK,UAAUpK,UAAUC,QAAQmB,UAClDiJ,OAAU,UACVnG,OAAU,SACVE,UACEC,aAAgB,+CAElBjE,KAAQ,OACRuD,QAAW,SACX2G,QACEC,YAAe,gBACfC,YAAe,cACfC,YAAe,QACfxI,IAAO,MACPyI,YAAe,eACfC,OAAU,SACVC,kBAAqB,wCACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,IACZC,qBAAwB,YACxBC,oBAAuB,YACvBC,MAAS,QACTC,WAAc,eAEhBC,MAAS,QACTC,oBACEC,gBAAmB,wEACnBC,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,kBAAqB,wBACrBC,cAAiB,sEACjBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,uBAErB1H,KAAQ,OACR2H,KAAQ,OACRC,MAAS,QACTC,OACEC,OAAU,SACVrJ,IAAO,MACPsJ,MAAS,QACTC,KAAQ,OACRC,SAAY,aAEdX,kBAAqB,eACrBY,YAAe,gBACfC,YAAe,yBAMbzM,MAAMC,GAAGyM,SACb1M,MAAMC,GAAGyM,OAAOvM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,OAAOvM,UAAUC,QAAQ0J,cAC/C6C,MAAS,aAMP3M,MAAMC,GAAG2M,QACb5M,MAAMC,GAAG2M,MAAMzM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,MAAMzM,UAAUC,QAAQ0J,cAC9C+C,OAAU,QAMR7M,MAAMC,GAAG6M,UACb9M,MAAMC,GAAG6M,QAAQ3M,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6M,QAAQ3M,UAAUC,QAAQ0J,cAChD+C,OAAU,KACVxI,OAAU,YAKRrE,MAAMC,GAAG8M,SACb/M,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQ0J,cAC/C+C,OAAU,KACVxI,OAAU,aAIT2I,OAAOhN,MAAMiN", "file": "kendo.messages.pl-PL.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"są równe\",\n    \"gte\": \"są późniejsze lub równe\",\n    \"gt\": \"są późniejsze niż\",\n    \"lte\": \"są wcześniejsze lub równe\",\n    \"lt\": \"są wcześniejsze niż\",\n    \"neq\": \"są inne niż\"\n  },\n  \"number\": {\n    \"eq\": \"są równe\",\n    \"gte\": \"są większe lub równe\",\n    \"gt\": \"są większe niż\",\n    \"lte\": \"są mniejsze lub równe\",\n    \"lt\": \"są mniejsze niż\",\n    \"neq\": \"są inne niż\"\n  },\n  \"string\": {\n    \"endswith\": \"kończą się na\",\n    \"eq\": \"są równe\",\n    \"neq\": \"są inne niż\",\n    \"startswith\": \"zaczynają się od\",\n    \"contains\": \"zawierają\",\n    \"doesnotcontain\": \"nie zawierają\"\n  },\n  \"enums\": {\n    \"eq\": \"są równe\",\n    \"neq\": \"są inne niż\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"są równe\",\n    \"gte\": \"są późniejsze lub równe\",\n    \"gt\": \"są późniejsze niż\",\n    \"lte\": \"są wcześniejsze lub równe\",\n    \"lt\": \"są wcześniejsze niż\",\n    \"neq\": \"są inne niż\"\n  },\n  \"number\": {\n    \"eq\": \"są równe\",\n    \"gte\": \"są większe lub równe\",\n    \"gt\": \"są większe niż\",\n    \"lte\": \"są mniejsze lub równe\",\n    \"lt\": \"są mniejsze niż\",\n    \"neq\": \"są inne niż\"\n  },\n  \"string\": {\n    \"endswith\": \"kończą się na\",\n    \"eq\": \"są równe\",\n    \"neq\": \"są inne niż\",\n    \"startswith\": \"zaczynają się od\",\n    \"contains\": \"zawierają\",\n    \"doesnotcontain\": \"nie zawierają\"\n  },\n  \"enums\": {\n    \"eq\": \"są równe\",\n    \"neq\": \"są inne niż\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"filter\": \"Filtr\",\n  \"columns\": \"Kolumny\",\n  \"sortAscending\": \"Sortuj Rosnąco\",\n  \"sortDescending\": \"Sortuj malejąco\",\n  \"settings\": \"Ustawienia kolumn\",\n  \"done\": \"Sporządzono\",\n  \"lock\": \"Zablokować\",\n  \"unlock\": \"Odblokować\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"days(s)\",\n    \"repeatEvery\": \"Repeat every:\"\n  },\n  \"end\": {\n    \"after\": \"After\",\n    \"occurrence\": \"occurrence(s)\",\n    \"label\": \"End:\",\n    \"never\": \"Never\",\n    \"on\": \"On\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Daily\",\n    \"monthly\": \"Monthly\",\n    \"never\": \"Never\",\n    \"weekly\": \"Weekly\",\n    \"yearly\": \"Yearly\"\n  },\n  \"monthly\": {\n    \"day\": \"Day\",\n    \"interval\": \"month(s)\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"first\",\n    \"fourth\": \"fourth\",\n    \"last\": \"last\",\n    \"second\": \"second\",\n    \"third\": \"third\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"week(s)\"\n  },\n  \"yearly\": {\n    \"of\": \"of\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"year(s)\"\n  },\n  \"weekdays\": {\n    \"day\": \"day\",\n    \"weekday\": \"weekday\",\n    \"weekend\": \"weekend day\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"create\": \"Wstaw\",\n    \"destroy\": \"Usuń\",\n    \"canceledit\": \"Anuluj\",\n    \"update\": \"Aktualizuj\",\n    \"edit\": \"Edycja\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"select\": \"Zaznacz\",\n    \"cancel\": \"Anuluj zmiany\",\n    \"save\": \"Zapisz zmiany\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Czy na pewno chcesz usunąć ten rekord?\",\n    \"cancelDelete\": \"Anuluj\",\n    \"confirmDelete\": \"Usuń\"\n  }\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"page\": \"Strona\",\n  \"display\": \"Wyświetlanie elementów {0} - {1} z {2}\",\n  \"of\": \"z {0}\",\n  \"empty\": \"Brak danych\",\n  \"refresh\": \"Odśwież\",\n  \"first\": \"Idź do pierwszej strony\",\n  \"itemsPerPage\": \"na stronę\",\n  \"last\": \"Przejdź do ostatniej strony\",\n  \"next\": \"Przejdź do następnej strony\",\n  \"previous\": \"Przejdź do poprzedniej strony\",\n  \"morePages\": \"Więcej stron\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"Filtr\",\n  \"clear\": \"Wyczyść filtr\",\n  \"isFalse\": \"fałsz\",\n  \"isTrue\": \"prawda\",\n  \"operator\": \"Operator\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"Filtr\",\n  \"and\": \"Oraz\",\n  \"clear\": \"Wyczyść filtr\",\n  \"info\": \"Pokaż wiersze o wartościach które\",\n  \"selectValue\": \"-Wybierz wartość-\",\n  \"isFalse\": \"fałsz\",\n  \"isTrue\": \"prawda\",\n  \"or\": \"lub\",\n  \"cancel\": \"Anuluj\",\n  \"operator\": \"Operator\",\n  \"value\": \"Wartość\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Szukaj\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Przeciągnij nagłówek kolumny i upuść go tutaj aby pogrupować według tej kolumny\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Wytłuszczenie\",\n  \"createLink\": \"Wstaw link\",\n  \"fontName\": \"Wybierz czcionkę\",\n  \"fontNameInherit\": \"(czcionka odziedziczona)\",\n  \"fontSize\": \"Wybierz rozmiar czcionki\",\n  \"fontSizeInherit\": \"(rozmiar odziedziczony)\",\n  \"formatBlock\": \"Wybierz rozmiar bloku\",\n  \"indent\": \"Wcięcie\",\n  \"insertHtml\": \"Wstaw HTML\",\n  \"insertImage\": \"Wstaw obraz\",\n  \"insertOrderedList\": \"Wstaw listę numerowaną\",\n  \"insertUnorderedList\": \"Wstaw listę wypunktowaną\",\n  \"italic\": \"Kursywa\",\n  \"justifyCenter\": \"Centruj tekst\",\n  \"justifyFull\": \"Wyrównaj tekst\",\n  \"justifyLeft\": \"Wyrównaj tekst do lewej\",\n  \"justifyRight\": \"Wyrównaj tekst do prawej\",\n  \"outdent\": \"Zmniejsz wcięcie\",\n  \"strikethrough\": \"Przekreślenie\",\n  \"styles\": \"Styl\",\n  \"subscript\": \"Subscript\",\n  \"superscript\": \"Superscript\",\n  \"underline\": \"Podkreślenie\",\n  \"unlink\": \"Usuń link\",\n  \"deleteFile\": \"Czy na pewno chcesz usunąć \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"Folder o tej nazwie nie został znaleziony.\",\n  \"emptyFolder\": \"Opróżnij folder\",\n  \"invalidFileType\": \"Wybrany plik \\\"{0}\\\" nie jest prawidłowy. Obsługiwane typy plików to: {1}.\",\n  \"orderBy\": \"Uporządkuj wg:\",\n  \"orderByName\": \"Nazwa\",\n  \"orderBySize\": \"Rozmiar\",\n  \"overwriteFile\": \"Plik o nazwie \\\"{0}\\\" istnieje już w bieżącym folderze. Czy chcesz go zastąpić?\",\n  \"uploadFile\": \"Załaduj\",\n  \"backColor\": \"Kolor tła\",\n  \"foreColor\": \"Kolor\",\n  \"dialogButtonSeparator\": \"or\",\n  \"dialogCancel\": \"Cancel\",\n  \"dialogInsert\": \"Insert\",\n  \"imageAltText\": \"Alternate text\",\n  \"imageWebAddress\": \"Web address\",\n  \"linkOpenInNewWindow\": \"Open link in new window\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Web address\",\n  \"search\": \"Search\",\n  \"createTable\": \"Tworzenie tabeli\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteColumn\": \"Delete column\",\n  \"deleteRow\": \"Delete row\",\n  \"dropFilesHere\": \"drop files here to upload\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Update\",\n  \"insertFile\": \"Insert file\"\n});\n}\n\n/* FileBrowser and ImageBrowser messages */\n\nvar browserMessages = {\n  \"uploadFile\" : \"Wyślij\",\n  \"orderBy\" : \"Sortuj wg\",\n  \"orderByName\" : \"Nazwy\",\n  \"orderBySize\" : \"Rozmiaru\",\n  \"directoryNotFound\" : \"Folder o podanej nazwie nie został odnaleziony.\",\n  \"emptyFolder\" : \"Pusty folder\",\n  \"invalidFileType\" : \"Wybrany plik \\\"{0}\\\" jest nieprawidłowy. Obsługiwane pliki {1}.\",\n  \"deleteFile\" : 'Czy napewno chcesz usunąć plik \"{0}\"?',\n  \"overwriteFile\" : 'Plik o nazwie \"{0}\" już istnieje w bieżącym folderze. Czy zastąpić?',\n  \"dropFilesHere\" : \"umieść pliki tutaj, aby je wysłać\",\n  \"search\" : \"Szukaj\"\n};\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages, browserMessages);\n}\n\nif (kendo.ui.ImageBrowser) {\nkendo.ui.ImageBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.ImageBrowser.prototype.options.messages, browserMessages);\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Anuluj\",\n  \"dropFilesHere\": \"przeciągnij tu pliki aby je załadować\",\n  \"remove\": \"Usuń\",\n  \"retry\": \"Ponów\",\n  \"select\": \"Wybierz...\",\n  \"statusFailed\": \"niepowodzenie\",\n  \"statusUploaded\": \"załadowane\",\n  \"statusUploading\": \"trwa ładowanie\",\n  \"uploadSelectedFiles\": \"Załaduj pliki\",\n  \"headerStatusUploaded\": \"Done\",\n  \"headerStatusUploading\": \"Uploading...\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"all day\",\n  \"cancel\": \"Anuluj\",\n  \"editable\": {\n    \"confirmation\": \"Are you sure you want to delete this event?\"\n  },\n  \"date\": \"Date\",\n  \"destroy\": \"Delete\",\n  \"editor\": {\n    \"allDayEvent\": \"All day event\",\n    \"description\": \"Description\",\n    \"editorTitle\": \"Event\",\n    \"end\": \"End\",\n    \"endTimezone\": \"End timezone\",\n    \"repeat\": \"Repeat\",\n    \"separateTimezones\": \"Use separate start and end time zones\",\n    \"start\": \"Start\",\n    \"startTimezone\": \"Start timezone\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Time zone\",\n    \"timezoneEditorTitle\": \"Timezones\",\n    \"title\": \"Title\",\n    \"noTimezone\": \"No timezone\"\n  },\n  \"event\": \"Event\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\n    \"deleteWindowOccurrence\": \"Delete current occurrence\",\n    \"deleteWindowSeries\": \"Delete the series\",\n    \"deleteWindowTitle\": \"Delete Recurring Item\",\n    \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\",\n    \"editWindowOccurrence\": \"Edit current occurrence\",\n    \"editWindowSeries\": \"Edit the series\",\n    \"editWindowTitle\": \"Edit Recurring Item\"\n  },\n  \"save\": \"Save\",\n  \"time\": \"Time\",\n  \"today\": \"Today\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Day\",\n    \"month\": \"Month\",\n    \"week\": \"Week\",\n    \"workWeek\": \"Work Week\"\n  },\n  \"deleteWindowTitle\": \"Delete event\",\n  \"showFullDay\": \"Show full day\",\n  \"showWorkDay\": \"Show business hours\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Zamknąć\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Anuluj\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Anuluj\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}