{"version": 3, "sources": ["messages/kendo.messages.uk-UA.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gte", "gt", "lte", "lt", "neq", "number", "string", "endswith", "startswith", "contains", "doesnotcontain", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "create", "destroy", "canceledit", "update", "edit", "excel", "pdf", "select", "cancel", "save", "editable", "confirmation", "cancelDelete", "confirmDelete", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "filter", "clear", "isFalse", "isTrue", "operator", "and", "info", "selectValue", "or", "value", "Groupable", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "search", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "backColor", "deleteFile", "deleteRow1", "dialogButtonSeparator1", "dialogCancel1", "dialogInsert1", "directoryNotFound", "dropFilesHere", "emptyFolder", "foreColor", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Upload", "localization", "headerStatusUploaded", "headerStatusUploading", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,UACNC,IAAO,kBACPC,GAAM,QACNC,IAAO,iBACPC,GAAM,KACNC,IAAO,YAETC,QACEN,GAAM,QACNC,IAAO,qBACPC,GAAM,SACNC,IAAO,oBACPC,GAAM,QACNC,IAAO,cAETE,QACEC,SAAY,kBACZR,GAAM,QACNK,IAAO,WACPI,WAAc,iBACdC,SAAY,UACZC,eAAkB,oBAEpBC,OACEZ,GAAM,UACNK,IAAO,iBAOPb,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,UACNC,IAAO,kBACPC,GAAM,QACNC,IAAO,iBACPC,GAAM,KACNC,IAAO,YAETC,QACEN,GAAM,QACNC,IAAO,qBACPC,GAAM,SACNC,IAAO,oBACPC,GAAM,QACNC,IAAO,cAETE,QACEC,SAAY,kBACZR,GAAM,QACNK,IAAO,WACPI,WAAc,iBACdC,SAAY,UACZC,eAAkB,oBAEpBC,OACEZ,GAAM,UACNK,IAAO,iBAOPb,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,SACXC,cAAiB,2BACjBC,eAAkB,0BAClBC,SAAY,qBACZC,KAAQ,YACRC,KAAQ,WACRC,OAAU,gBAMR9B,MAAMC,GAAG8B,mBACb/B,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,UACzDS,OACEC,SAAY,UACZC,YAAe,iBAEjBC,KACEC,MAAS,QACTC,WAAc,gBACdC,MAAS,OACTC,MAAS,QACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,QACTW,QAAW,UACXJ,MAAS,QACTK,OAAU,SACVC,OAAU,UAEZF,SACEG,IAAO,MACPb,SAAY,WACZC,YAAe,gBACfa,SAAY,cAEdC,iBACEC,MAAS,QACTC,OAAU,SACVC,KAAQ,OACRC,OAAU,SACVC,MAAS,SAEXT,QACEV,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdY,QACES,GAAM,KACNpB,YAAe,gBACfa,SAAY,aACZd,SAAY,WAEdsB,UACET,IAAO,MACPU,QAAW,UACXC,QAAW,kBAOXzD,MAAMC,GAAGyD,OACb1D,MAAMC,GAAGyD,KAAKvD,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyD,KAAKvD,UAAUC,QAAQmB,UAC7CoC,UACEC,OAAU,SACVC,QAAW,WACXC,WAAc,YACdC,OAAU,UACVC,KAAQ,aACRC,MAAS,kBACTC,IAAO,gBACPC,OAAU,UACVC,OAAU,iBACVC,KAAQ,gBAEVC,UACEC,aAAgB,gDAChBC,aAAgB,YAChBC,cAAiB,eAOjBzE,MAAMC,GAAGyE,QACb1E,MAAMC,GAAGyE,MAAMvE,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyE,MAAMvE,UAAUC,QAAQmB,UAC9CoD,SAAY,MACZC,KAAQ,WACRC,QAAW,mCACXvB,GAAM,QACNwB,MAAS,gBACTC,QAAW,UACX9B,MAAS,gCACT+B,aAAgB,wBAChB7B,KAAQ,wBACR8B,KAAQ,iCACRC,SAAY,gCACZC,UAAa,qBAMXnF,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnD6D,OAAU,cACVC,MAAS,WACTC,QAAW,OACXC,OAAU,SACVC,SAAY,cAMVxF,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnD6D,OAAU,cACVK,IAAO,IACPJ,MAAS,WACTK,KAAQ,oBACRC,YAAe,aACfL,QAAW,OACXC,OAAU,SACVK,GAAM,KACNxB,OAAU,YACVoB,SAAY,WACZK,MAAS,cAMP7F,MAAMC,GAAG6F,YACb9F,MAAMC,GAAG6F,UAAU3F,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6F,UAAU3F,UAAUC,QAAQmB,UAClDuD,MAAS,+EAMP9E,MAAMC,GAAG8F,SACb/F,MAAMC,GAAG8F,OAAO5F,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8F,OAAO5F,UAAUC,QAAQmB,UAC/CyE,KAAQ,SACRC,WAAc,mBACdC,SAAY,QACZC,gBAAmB,mBACnBC,SAAY,gBACZC,gBAAmB,mBACnBC,YAAe,eACfC,OAAU,oBACVC,WAAc,cACdC,YAAe,oBACfC,kBAAqB,qBACrBC,oBAAuB,oBACvBC,OAAU,SACVC,cAAiB,YACjBC,YAAe,YACfC,YAAe,iBACfC,aAAgB,kBAChBC,QAAW,mBACXC,cAAiB,cACjBC,OAAU,QACVC,UAAa,YACbC,YAAe,cACfC,UAAa,eACbC,OAAU,qBACVC,sBAAyB,KACzBC,aAAgB,SAChBC,aAAgB,SAChBC,aAAgB,iBAChBC,gBAAmB,cACnBC,oBAAuB,0BACvBC,SAAY,OACZC,YAAe,UACfC,eAAkB,cAClBC,OAAU,SACVC,YAAe,mBACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,aAAgB,gBAChBC,UAAa,aACbC,UAAa,mBACbC,WAAc,yCACdC,WAAc,aACdC,uBAA0B,KAC1BC,cAAiB,SACjBC,cAAiB,SACjBC,kBAAqB,4CACrBC,cAAiB,4BACjBC,YAAe,eACfC,UAAa,QACbC,gBAAmB,sEACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,OACfC,cAAiB,iGACjBC,WAAc,SACdC,WAAc,SACdC,SAAY,YACZC,aAAgB,SAChBC,WAAc,iBAMZ5J,MAAMC,GAAG4J,YACb7J,MAAMC,GAAG4J,UAAU1J,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4J,UAAU1J,UAAUC,QAAQmB,UAClDuI,OAAU,UACV1F,OAAU,YACVE,UACEC,aAAgB,+CAElBhE,KAAQ,OACRsD,QAAW,SACXkG,QACEC,YAAe,gBACfC,YAAe,cACfC,YAAe,QACf/H,IAAO,MACPgI,YAAe,eACfC,OAAU,SACVC,kBAAqB,wCACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,IACZC,qBAAwB,YACxBC,oBAAuB,YACvBC,MAAS,QACTC,WAAc,eAEhBC,MAAS,QACTC,oBACEC,gBAAmB,wEACnBC,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,kBAAqB,wBACrBC,cAAiB,sEACjBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,uBAErBjH,KAAQ,OACRkH,KAAQ,OACRC,MAAS,QACTC,OACEC,OAAU,SACV5I,IAAO,MACP6I,MAAS,QACTC,KAAQ,OACRC,SAAY,aAEdX,kBAAqB,eACrBY,YAAe,gBACfC,YAAe,yBAMb/L,MAAMC,GAAG+L,SACbhM,MAAMC,GAAG+L,OAAO7L,UAAUC,QAAQ6L,aAClCnM,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+L,OAAO7L,UAAUC,QAAQ6L,cAC/C7H,OAAU,SACV4E,cAAiB,4BACjBkD,qBAAwB,OACxBC,sBAAyB,eACzBC,OAAU,SACVC,MAAS,QACTlI,OAAU,YACVmI,aAAgB,SAChBC,eAAkB,WAClBC,gBAAmB,YACnBC,oBAAuB,kBAMrBzM,MAAMC,GAAGyM,SACb1M,MAAMC,GAAGyM,OAAOvM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyM,OAAOvM,UAAUC,QAAQ6L,cAC/CU,MAAS,eAMP3M,MAAMC,GAAG2M,QACb5M,MAAMC,GAAG2M,MAAMzM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,MAAMzM,UAAUC,QAAQ6L,cAC9CY,OAAU,QAMR7M,MAAMC,GAAG6M,UACb9M,MAAMC,GAAG6M,QAAQ3M,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG6M,QAAQ3M,UAAUC,QAAQ6L,cAChDY,OAAU,KACVzI,OAAU,eAKRpE,MAAMC,GAAG8M,SACb/M,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQ6L,cAC/CY,OAAU,KACVzI,OAAU,gBAIT4I,OAAOhN,MAAMiN", "file": "kendo.messages.uk-UA.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"рівними\",\n    \"gte\": \"після або рівна\",\n    \"gt\": \"після\",\n    \"lte\": \"до або рівними\",\n    \"lt\": \"до\",\n    \"neq\": \"не рівна\"\n  },\n  \"number\": {\n    \"eq\": \"рівне\",\n    \"gte\": \"більше або рівними\",\n    \"gt\": \"більше\",\n    \"lte\": \"менше або рівними\",\n    \"lt\": \"менше\",\n    \"neq\": \"не рівними\"\n  },\n  \"string\": {\n    \"endswith\": \"закінчуються на\",\n    \"eq\": \"рівні\",\n    \"neq\": \"не рівні\",\n    \"startswith\": \"починаються на\",\n    \"contains\": \"містять\",\n    \"doesnotcontain\": \"Does not contain\"\n  },\n  \"enums\": {\n    \"eq\": \"рівними\",\n    \"neq\": \"не рівними\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"рівними\",\n    \"gte\": \"після або рівна\",\n    \"gt\": \"після\",\n    \"lte\": \"до або рівними\",\n    \"lt\": \"до\",\n    \"neq\": \"не рівна\"\n  },\n  \"number\": {\n    \"eq\": \"рівне\",\n    \"gte\": \"більше або рівними\",\n    \"gt\": \"більше\",\n    \"lte\": \"менше або рівними\",\n    \"lt\": \"менше\",\n    \"neq\": \"не рівними\"\n  },\n  \"string\": {\n    \"endswith\": \"закінчуються на\",\n    \"eq\": \"рівні\",\n    \"neq\": \"не рівні\",\n    \"startswith\": \"починаються на\",\n    \"contains\": \"містять\",\n    \"doesnotcontain\": \"Does not contain\"\n  },\n  \"enums\": {\n    \"eq\": \"рівними\",\n    \"neq\": \"не рівними\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Kолони\",\n  \"sortAscending\": \"Сортування за зростанням\",\n  \"sortDescending\": \"Сортування за спаданням\",\n  \"settings\": \"Параметри стовпців\",\n  \"done\": \"Зроблений\",\n  \"lock\": \"Замикати\",\n  \"unlock\": \"Відімкнути\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"days(s)\",\n    \"repeatEvery\": \"Repeat every:\"\n  },\n  \"end\": {\n    \"after\": \"After\",\n    \"occurrence\": \"occurrence(s)\",\n    \"label\": \"End:\",\n    \"never\": \"Never\",\n    \"on\": \"On\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Daily\",\n    \"monthly\": \"Monthly\",\n    \"never\": \"Never\",\n    \"weekly\": \"Weekly\",\n    \"yearly\": \"Yearly\"\n  },\n  \"monthly\": {\n    \"day\": \"Day\",\n    \"interval\": \"month(s)\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"first\",\n    \"fourth\": \"fourth\",\n    \"last\": \"last\",\n    \"second\": \"second\",\n    \"third\": \"third\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"week(s)\"\n  },\n  \"yearly\": {\n    \"of\": \"of\",\n    \"repeatEvery\": \"Repeat every:\",\n    \"repeatOn\": \"Repeat on:\",\n    \"interval\": \"year(s)\"\n  },\n  \"weekdays\": {\n    \"day\": \"day\",\n    \"weekday\": \"weekday\",\n    \"weekend\": \"weekend day\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"create\": \"Додати\",\n    \"destroy\": \"Видалити\",\n    \"canceledit\": \"Скасувати\",\n    \"update\": \"Оновити\",\n    \"edit\": \"Редагувати\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"select\": \"Вибрати\",\n    \"cancel\": \"Cancel Changes\",\n    \"save\": \"Save Changes\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Ви впевнені, що бажаєте видалити даний запис?\",\n    \"cancelDelete\": \"Скасувати\",\n    \"confirmDelete\": \"Видалити\"\n  }\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"page\": \"Сторінка\",\n  \"display\": \"Зображено записи {0} - {1} з {2}\",\n  \"of\": \"з {0}\",\n  \"empty\": \"немає записів\",\n  \"refresh\": \"Оновити\",\n  \"first\": \"Повернутися на першу сторінку\",\n  \"itemsPerPage\": \"елементів на сторінці\",\n  \"last\": \"До останньої сторінки\",\n  \"next\": \"Перейдіть на наступну сторінку\",\n  \"previous\": \"Перейти на попередню сторінку\",\n  \"morePages\": \"Більше сторінок\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"фільтрувати\",\n  \"clear\": \"очистити\",\n  \"isFalse\": \"хиба\",\n  \"isTrue\": \"істина\",\n  \"operator\": \"Oператор\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"фільтрувати\",\n  \"and\": \"І\",\n  \"clear\": \"очистити\",\n  \"info\": \"Рядки із записами\",\n  \"selectValue\": \"-виберіть-\",\n  \"isFalse\": \"хиба\",\n  \"isTrue\": \"істина\",\n  \"or\": \"Or\",\n  \"cancel\": \"Скасувати\",\n  \"operator\": \"Oператор\",\n  \"value\": \"Значення\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Перетягніть сюди заголовок стовпця, щоб згрупувати записи з цього стовпця\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Жирний\",\n  \"createLink\": \"Додати посилання\",\n  \"fontName\": \"Шрифт\",\n  \"fontNameInherit\": \"(inherited font)\",\n  \"fontSize\": \"Розмір шрифта\",\n  \"fontSizeInherit\": \"(inherited size)\",\n  \"formatBlock\": \"Форматування\",\n  \"indent\": \"Збільшити відступ\",\n  \"insertHtml\": \"Додати HTML\",\n  \"insertImage\": \"Додати зображення\",\n  \"insertOrderedList\": \"Нумерований список\",\n  \"insertUnorderedList\": \"Маркований список\",\n  \"italic\": \"Курсив\",\n  \"justifyCenter\": \"По центру\",\n  \"justifyFull\": \"По ширині\",\n  \"justifyLeft\": \"По лівому краю\",\n  \"justifyRight\": \"По правому краю\",\n  \"outdent\": \"Зменшити відступ\",\n  \"strikethrough\": \"Закреслений\",\n  \"styles\": \"Стиль\",\n  \"subscript\": \"Subscript\",\n  \"superscript\": \"Superscript\",\n  \"underline\": \"Підкреслений\",\n  \"unlink\": \"Видалити посилання\",\n  \"dialogButtonSeparator\": \"or\",\n  \"dialogCancel\": \"Cancel\",\n  \"dialogInsert\": \"Insert\",\n  \"imageAltText\": \"Alternate text\",\n  \"imageWebAddress\": \"Web address\",\n  \"linkOpenInNewWindow\": \"Open link in new window\",\n  \"linkText\": \"Text\",\n  \"linkToolTip\": \"ToolTip\",\n  \"linkWebAddress\": \"Web address\",\n  \"search\": \"Search\",\n  \"createTable\": \"Створити таблицю\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteColumn\": \"Delete column\",\n  \"deleteRow\": \"Delete row\",\n  \"backColor\": \"Background color\",\n  \"deleteFile\": \"Are you sure you want to delete \\\"{0}\\\"?\",\n  \"deleteRow1\": \"Delete row\",\n  \"dialogButtonSeparator1\": \"or\",\n  \"dialogCancel1\": \"Cancel\",\n  \"dialogInsert1\": \"Insert\",\n  \"directoryNotFound\": \"A directory with this name was not found.\",\n  \"dropFilesHere\": \"drop files here to upload\",\n  \"emptyFolder\": \"Empty Folder\",\n  \"foreColor\": \"Color\",\n  \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\n  \"orderBy\": \"Arrange by:\",\n  \"orderByName\": \"Name\",\n  \"orderBySize\": \"Size\",\n  \"overwriteFile\": \"'A file with name \\\"{0}\\\" already exists in the current directory. Do you want to overwrite it?\",\n  \"uploadFile\": \"Upload\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Update\",\n  \"insertFile\": \"Insert file\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"all day\",\n  \"cancel\": \"Скасувати\",\n  \"editable\": {\n    \"confirmation\": \"Are you sure you want to delete this event?\"\n  },\n  \"date\": \"Date\",\n  \"destroy\": \"Delete\",\n  \"editor\": {\n    \"allDayEvent\": \"All day event\",\n    \"description\": \"Description\",\n    \"editorTitle\": \"Event\",\n    \"end\": \"End\",\n    \"endTimezone\": \"End timezone\",\n    \"repeat\": \"Repeat\",\n    \"separateTimezones\": \"Use separate start and end time zones\",\n    \"start\": \"Start\",\n    \"startTimezone\": \"Start timezone\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Time zone\",\n    \"timezoneEditorTitle\": \"Timezones\",\n    \"title\": \"Title\",\n    \"noTimezone\": \"No timezone\"\n  },\n  \"event\": \"Event\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\n    \"deleteWindowOccurrence\": \"Delete current occurrence\",\n    \"deleteWindowSeries\": \"Delete the series\",\n    \"deleteWindowTitle\": \"Delete Recurring Item\",\n    \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\",\n    \"editWindowOccurrence\": \"Edit current occurrence\",\n    \"editWindowSeries\": \"Edit the series\",\n    \"editWindowTitle\": \"Edit Recurring Item\"\n  },\n  \"save\": \"Save\",\n  \"time\": \"Time\",\n  \"today\": \"Today\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Day\",\n    \"month\": \"Month\",\n    \"week\": \"Week\",\n    \"workWeek\": \"Work Week\"\n  },\n  \"deleteWindowTitle\": \"Delete event\",\n  \"showFullDay\": \"Show full day\",\n  \"showWorkDay\": \"Show business hours\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Cancel\",\n  \"dropFilesHere\": \"drop files here to upload\",\n  \"headerStatusUploaded\": \"Done\",\n  \"headerStatusUploading\": \"Uploading...\",\n  \"remove\": \"Remove\",\n  \"retry\": \"Retry\",\n  \"select\": \"Select...\",\n  \"statusFailed\": \"failed\",\n  \"statusUploaded\": \"uploaded\",\n  \"statusUploading\": \"uploading\",\n  \"uploadSelectedFiles\": \"Upload files\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"закінчати\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"OK\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Скасувати\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"OK\",\n  \"cancel\": \"Скасувати\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}