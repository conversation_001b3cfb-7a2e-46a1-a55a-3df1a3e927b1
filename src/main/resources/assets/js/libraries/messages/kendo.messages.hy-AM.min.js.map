{"version": 3, "sources": ["messages/kendo.messages.hy-AM.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gte", "gt", "lte", "lt", "neq", "number", "string", "endswith", "startswith", "contains", "doesnotcontain", "enums", "FilterMenu", "ColumnMenu", "messages", "columns", "sortAscending", "sortDescending", "settings", "done", "lock", "unlock", "filter", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "occurrence", "label", "never", "on", "mobileLabel", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "of", "weekdays", "weekday", "weekend", "Grid", "commands", "create", "destroy", "canceledit", "update", "edit", "excel", "pdf", "select", "cancel", "save", "editable", "confirmation", "cancelDelete", "confirmDelete", "noRecords", "Pager", "allPages", "page", "display", "empty", "refresh", "itemsPerPage", "next", "previous", "morePages", "clear", "isFalse", "isTrue", "operator", "and", "info", "selectValue", "or", "value", "Groupable", "Editor", "bold", "createLink", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "outdent", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "imageAltText", "imageWebAddress", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "search", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteColumn", "deleteRow", "backColor", "deleteFile", "directoryNotFound", "dropFilesHere", "emptyFolder", "foreColor", "invalidFileType", "orderBy", "orderByName", "orderBySize", "overwriteFile", "uploadFile", "formatting", "viewHtml", "dialogUpdate", "insertFile", "Upload", "localization", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "headerStatusUploaded", "headerStatusUploading", "Scheduler", "allDay", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "noTimezone", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "deleteWindowTitle", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "time", "today", "views", "agenda", "month", "week", "workWeek", "showFullDay", "showWorkDay", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,YACNC,IAAO,oBACPC,GAAM,QACNC,IAAO,qBACPC,GAAM,SACNC,IAAO,cAETC,QACEN,GAAM,YACNC,IAAO,oBACPC,GAAM,QACNC,IAAO,qBACPC,GAAM,SACNC,IAAO,cAETE,QACEC,SAAY,cACZR,GAAM,YACNK,IAAO,aACPI,WAAc,aACdC,SAAY,gBACZC,eAAkB,kBAEpBC,OACEZ,GAAM,YACNK,IAAO,iBAOPb,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,YACNC,IAAO,oBACPC,GAAM,QACNC,IAAO,qBACPC,GAAM,SACNC,IAAO,cAETC,QACEN,GAAM,YACNC,IAAO,oBACPC,GAAM,QACNC,IAAO,qBACPC,GAAM,SACNC,IAAO,cAETE,QACEC,SAAY,cACZR,GAAM,YACNK,IAAO,aACPI,WAAc,aACdC,SAAY,gBACZC,eAAkB,kBAEpBC,OACEZ,GAAM,YACNK,IAAO,iBAOPb,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,UACXC,cAAiB,yBACjBC,eAAkB,2BAClBC,SAAY,qBACZC,KAAQ,WACRC,KAAQ,QACRC,OAAU,QACVC,OAAU,aAMR/B,MAAMC,GAAG+B,mBACbhC,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+B,iBAAiB7B,UAAUC,QAAQmB,UACzDU,OACEC,SAAY,SACZC,YAAe,gBAEjBC,KACEC,MAAS,OACTC,WAAc,gBACdC,MAAS,QACTC,MAAS,QACTC,GAAM,KACNC,YAAe,QAEjBC,aACEV,MAAS,SACTW,QAAW,UACXJ,MAAS,QACTK,OAAU,YACVC,OAAU,WAEZF,SACEG,IAAO,KACPb,SAAY,YACZC,YAAe,eACfa,SAAY,WAEdC,iBACEC,MAAS,SACTC,OAAU,UACVC,KAAQ,SACRC,OAAU,UACVC,MAAS,UAEXT,QACEV,YAAe,eACfa,SAAY,UACZd,SAAY,cAEdY,QACES,GAAM,KACNpB,YAAe,eACfa,SAAY,UACZd,SAAY,aAEdsB,UACET,IAAO,KACPU,QAAW,kBACXC,QAAW,yBAOX1D,MAAMC,GAAG0D,OACb3D,MAAMC,GAAG0D,KAAKxD,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0D,KAAKxD,UAAUC,QAAQmB,UAC7CqC,UACEC,OAAU,YACVC,QAAW,WACXC,WAAc,WACdC,OAAU,YACVC,KAAQ,WACRC,MAAS,kBACTC,IAAO,gBACPC,OAAU,SACVC,OAAU,WACVC,KAAQ,YAEVC,UACEC,aAAgB,wBAChBC,aAAgB,WAChBC,cAAiB,YAEnBC,UAAa,6BAMX3E,MAAMC,GAAG2E,QACb5E,MAAMC,GAAG2E,MAAMzE,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2E,MAAMzE,UAAUC,QAAQmB,UAC9CsD,SAAY,SACZC,KAAQ,KACRC,QAAW,sCACXxB,GAAM,SACNyB,MAAS,gBACTC,QAAW,YACX/B,MAAS,uBACTgC,aAAgB,qBAChB9B,KAAQ,mBACR+B,KAAQ,mBACRC,SAAY,uBACZC,UAAa,oBAMXrF,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnDQ,OAAU,UACVuD,MAAS,SACTC,QAAW,SACXC,OAAU,SACVC,SAAY,cAMVzF,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDQ,OAAU,UACV2D,IAAO,IACPJ,MAAS,SACTK,KAAQ,0BACRC,YAAe,WACfL,QAAW,SACXC,OAAU,SACVK,GAAM,MACNxB,OAAU,WACVoB,SAAY,WACZK,MAAS,WAMP9F,MAAMC,GAAG8F,YACb/F,MAAMC,GAAG8F,UAAU5F,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8F,UAAU5F,UAAUC,QAAQmB,UAClDyD,MAAS,mEAMPhF,MAAMC,GAAG+F,SACbhG,MAAMC,GAAG+F,OAAO7F,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+F,OAAO7F,UAAUC,QAAQmB,UAC/C0E,KAAQ,aACRC,WAAc,gBACdC,SAAY,YACZC,gBAAmB,0BACnBC,SAAY,iBACZC,gBAAmB,2BACnBC,YAAe,oBACfC,OAAU,mBACVC,WAAc,gBACdC,YAAe,SACfC,kBAAqB,sBACrBC,oBAAuB,sBACvBC,OAAU,SACVC,cAAiB,YACjBC,YAAe,YACfC,YAAe,QACfC,aAAgB,SAChBC,QAAW,mBACXC,cAAiB,cACjBC,OAAU,QACVC,UAAa,cACbC,YAAe,cACfC,UAAa,eACbC,OAAU,sBACVC,sBAAyB,MACzBC,aAAgB,SAChBC,aAAgB,WAChBC,aAAgB,iBAChBC,gBAAmB,YACnBC,oBAAuB,uBACvBC,SAAY,QACZC,YAAe,wBACfC,eAAkB,YAClBC,OAAU,QACVC,YAAe,mBACfC,cAAiB,yBACjBC,eAAkB,0BAClBC,YAAe,gBACfC,YAAe,gBACfC,aAAgB,gBAChBC,UAAa,aACbC,UAAa,mBACbC,WAAc,yCACdC,kBAAqB,4CACrBC,cAAiB,4BACjBC,YAAe,eACfC,UAAa,QACbC,gBAAmB,sEACnBC,QAAW,cACXC,YAAe,OACfC,YAAe,OACfC,cAAiB,iGACjBC,WAAc,SACdC,WAAc,SACdC,SAAY,YACZC,aAAgB,SAChBC,WAAc,iBAMZzJ,MAAMC,GAAGyJ,SACb1J,MAAMC,GAAGyJ,OAAOvJ,UAAUC,QAAQuJ,aAClC7J,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,OAAOvJ,UAAUC,QAAQuJ,cAC/CtF,OAAU,yBACVwE,cAAiB,yCACjBe,OAAU,WACVC,MAAS,SACTzF,OAAU,YACV0F,aAAgB,yBAChBC,eAAkB,yBAClBC,gBAAmB,aACnBC,oBAAuB,4BACvBC,qBAAwB,aACxBC,sBAAyB,mBAMvBnK,MAAMC,GAAGmK,YACbpK,MAAMC,GAAGmK,UAAUjK,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmK,UAAUjK,UAAUC,QAAQmB,UAClD8I,OAAU,UACVhG,OAAU,WACVE,UACEC,aAAgB,2BAElBjE,KAAQ,UACRuD,QAAW,WACXwG,QACEC,YAAe,0BACfC,YAAe,iBACfC,YAAe,iBACfrI,IAAO,OACPsI,YAAe,eACfC,OAAU,SACVC,kBAAqB,wCACrBC,MAAS,QACTC,cAAiB,iBACjBC,SAAY,IACZC,qBAAwB,YACxBC,oBAAuB,YACvBC,MAAS,QACTC,WAAc,eAEhBC,MAAS,QACTC,oBACEC,gBAAmB,wEACnBC,uBAA0B,4BAC1BC,mBAAsB,oBACtBC,kBAAqB,wBACrBC,cAAiB,sEACjBC,qBAAwB,0BACxBC,iBAAoB,kBACpBC,gBAAmB,uBAErBvH,KAAQ,OACRwH,KAAQ,OACRC,MAAS,QACTC,OACEC,OAAU,SACVlJ,IAAO,MACPmJ,MAAS,QACTC,KAAQ,OACRC,SAAY,aAEdX,kBAAqB,eACrBY,YAAe,gBACfC,YAAe,yBAMbtM,MAAMC,GAAGsM,YACbvM,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,UAAUpM,UAAUC,QAAQmB,UAClDiL,SAAY,iBACZC,QAAW,aACXC,IAAO,2CACPC,IAAO,4CACPC,KAAQ,aACRC,MAAS,gCACTC,IAAO,iBACPvM,KAAQ,wBAMNP,MAAMC,GAAG8M,SACb/M,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,OAAO5M,UAAUC,QAAQuJ,cAC/CqD,MAAS,UAMPhN,MAAMC,GAAGgN,QACbjN,MAAMC,GAAGgN,MAAM9M,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgN,MAAM9M,UAAUC,QAAQuJ,cAC9CuD,OAAU,SAMRlN,MAAMC,GAAGkN,UACbnN,MAAMC,GAAGkN,QAAQhN,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkN,QAAQhN,UAAUC,QAAQuJ,cAChDuD,OAAU,MACV7I,OAAU,cAKRrE,MAAMC,GAAGmN,SACbpN,MAAMC,GAAGmN,OAAOjN,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGmN,OAAOjN,UAAUC,QAAQuJ,cAC/CuD,OAAU,MACV7I,OAAU,eAITgJ,OAAOrN,MAAMsN", "file": "kendo.messages.hy-AM.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"հավասար է\",\n    \"gte\": \"մեծ է կամ հավասար\",\n    \"gt\": \"մեծ է\",\n    \"lte\": \"փոքր է կամ հավասար\",\n    \"lt\": \"փոքր է\",\n    \"neq\": \"հավասար չէ\"\n  },\n  \"number\": {\n    \"eq\": \"հավասար է\",\n    \"gte\": \"մեծ է կամ հավասար\",\n    \"gt\": \"մեծ է\",\n    \"lte\": \"փոքր է կամ հավասար\",\n    \"lt\": \"փոքր է\",\n    \"neq\": \"հավասար չէ\"\n  },\n  \"string\": {\n    \"endswith\": \"ավարտվում է\",\n    \"eq\": \"հավասար է\",\n    \"neq\": \"հավասար չէ\",\n    \"startswith\": \"սկասվում է\",\n    \"contains\": \"պարունակում է\",\n    \"doesnotcontain\": \"չի պարունակում\"\n  },\n  \"enums\": {\n    \"eq\": \"հավասար է\",\n    \"neq\": \"հավասար չէ\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"հավասար է\",\n    \"gte\": \"մեծ է կամ հավասար\",\n    \"gt\": \"մեծ է\",\n    \"lte\": \"փոքր է կամ հավասար\",\n    \"lt\": \"փոքր է\",\n    \"neq\": \"հավասար չէ\"\n  },\n  \"number\": {\n    \"eq\": \"հավասար է\",\n    \"gte\": \"մեծ է կամ հավասար\",\n    \"gt\": \"մեծ է\",\n    \"lte\": \"փոքր է կամ հավասար\",\n    \"lt\": \"փոքր է\",\n    \"neq\": \"հավասար չէ\"\n  },\n  \"string\": {\n    \"endswith\": \"ավարտվում է\",\n    \"eq\": \"հավասար է\",\n    \"neq\": \"հավասար չէ\",\n    \"startswith\": \"սկասվում է\",\n    \"contains\": \"պարունակում է\",\n    \"doesnotcontain\": \"չի պարունակում\"\n  },\n  \"enums\": {\n    \"eq\": \"հավասար է\",\n    \"neq\": \"հավասար չէ\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Սյուներ\",\n  \"sortAscending\": \"Դասավորել աճման կարգով\",\n  \"sortDescending\": \"Դասավորել նվազման կարգով\",\n  \"settings\": \"Սյուների տվյալները\",\n  \"done\": \"Կատարված\",\n  \"lock\": \"Փակել\",\n  \"unlock\": \"Բացել\",\n  \"filter\": \"Ֆիլտրեր\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"օր(եր)\",\n    \"repeatEvery\": \"Կրկնել ամեն:\"\n  },\n  \"end\": {\n    \"after\": \"Հետո\",\n    \"occurrence\": \"occurrence(s)\",\n    \"label\": \"Վերջ:\",\n    \"never\": \"Երբեք\",\n    \"on\": \"On\",\n    \"mobileLabel\": \"Ends\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Օրական\",\n    \"monthly\": \"Ամսական\",\n    \"never\": \"Երբեք\",\n    \"weekly\": \"Շաբաթական\",\n    \"yearly\": \"Տարեկան\"\n  },\n  \"monthly\": {\n    \"day\": \"Օր\",\n    \"interval\": \"ամիս(ներ)\",\n    \"repeatEvery\": \"Կրկնել ամեն:\",\n    \"repeatOn\": \"Կրկնել:\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"առաջին\",\n    \"fourth\": \"չորրորդ\",\n    \"last\": \"վերջին\",\n    \"second\": \"երկրորդ\",\n    \"third\": \"երրորդ\"\n  },\n  \"weekly\": {\n    \"repeatEvery\": \"Կրկնել ամեն:\",\n    \"repeatOn\": \"Կրկնել:\",\n    \"interval\": \"շաբաթ(ներ)\"\n  },\n  \"yearly\": {\n    \"of\": \"of\",\n    \"repeatEvery\": \"Կրկնել ամեն:\",\n    \"repeatOn\": \"Կրկնել:\",\n    \"interval\": \"տարի(ներ)\"\n  },\n  \"weekdays\": {\n    \"day\": \"օր\",\n    \"weekday\": \"աշխատանքային օր\",\n    \"weekend\": \"ոչ աշխատանքային օր\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"create\": \"Ավելացնել\",\n    \"destroy\": \"Հեռացնել\",\n    \"canceledit\": \"Չեղարկել\",\n    \"update\": \"Թարմացնել\",\n    \"edit\": \"Խմբագրել\",\n    \"excel\": \"Արտահանել Excel\",\n    \"pdf\": \"Արտահանել PDF\",\n    \"select\": \"Ընտրել\",\n    \"cancel\": \"Չեղարկել\",\n    \"save\": \"Պահպանել\"\n  },\n  \"editable\": {\n    \"confirmation\": \"Հեռացնե՞լ նշված տողը։\",\n    \"cancelDelete\": \"Չեղարկել\",\n    \"confirmDelete\": \"Հեռացնել\"\n  },\n  \"noRecords\": \"Հասանելի տվյալներ չկան։\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"Բոլորը\",\n  \"page\": \"Էջ\",\n  \"display\": \"Ցուցադրված են {0}-{1} տողերը {2}-ից\",\n  \"of\": \"{0}-ից\",\n  \"empty\": \"Տվյալներ չկան\",\n  \"refresh\": \"Թարմացնել\",\n  \"first\": \"Վերադառնալ առաջին էջ\",\n  \"itemsPerPage\": \"տողերի քանակ էջում\",\n  \"last\": \"Անցնել վերջին էջ\",\n  \"next\": \"Անցնել հաջորդ էջ\",\n  \"previous\": \"Վերադառնալ նախորդ էջ\",\n  \"morePages\": \"Ավելի շատ էջեր\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"filter\": \"Ֆիլտրել\",\n  \"clear\": \"Մաքրել\",\n  \"isFalse\": \"սխալ է\",\n  \"isTrue\": \"ճիշտ է\",\n  \"operator\": \"Օպերատոր\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"filter\": \"Ֆիլտրել\",\n  \"and\": \"և\",\n  \"clear\": \"Մաքրել\",\n  \"info\": \"Նշված արժեքներով տողերը\",\n  \"selectValue\": \"-ընտրել-\",\n  \"isFalse\": \"սխալ է\",\n  \"isTrue\": \"ճիշտ է\",\n  \"or\": \"կամ\",\n  \"cancel\": \"Չեղարկել\",\n  \"operator\": \"Օպերատոր\",\n  \"value\": \"Արժեք\"\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Սյան արժեքները խմբավորելու համար սյան վերնագիրը քաշեք այստեղ։\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"Полужирный\",\n  \"createLink\": \"Դարձնել հղում\",\n  \"fontName\": \"Տառատեսակ\",\n  \"fontNameInherit\": \"(шрифт как в документе)\",\n  \"fontSize\": \"Տառատեսակի չափ\",\n  \"fontSizeInherit\": \"(размер как в документе)\",\n  \"formatBlock\": \"Текст изображения\",\n  \"indent\": \"Увеличить отступ\",\n  \"insertHtml\": \"Вставить HTML\",\n  \"insertImage\": \"Պատկեր\",\n  \"insertOrderedList\": \"Нумерованный список\",\n  \"insertUnorderedList\": \"Маркированныйсписок\",\n  \"italic\": \"Курсив\",\n  \"justifyCenter\": \"По центру\",\n  \"justifyFull\": \"По ширине\",\n  \"justifyLeft\": \"Влево\",\n  \"justifyRight\": \"Вправо\",\n  \"outdent\": \"Уменьшить отступ\",\n  \"strikethrough\": \"Зачеркнутый\",\n  \"styles\": \"Стиль\",\n  \"subscript\": \"Под строкой\",\n  \"superscript\": \"Над строкой\",\n  \"underline\": \"Подчеркнутый\",\n  \"unlink\": \"Удалить гиперссылку\",\n  \"dialogButtonSeparator\": \"или\",\n  \"dialogCancel\": \"Отмена\",\n  \"dialogInsert\": \"Вставить\",\n  \"imageAltText\": \"Alternate text\",\n  \"imageWebAddress\": \"Веб адрес\",\n  \"linkOpenInNewWindow\": \"Открыть в новом окне\",\n  \"linkText\": \"Текст\",\n  \"linkToolTip\": \"Всплывающая подсказка\",\n  \"linkWebAddress\": \"Веб адрес\",\n  \"search\": \"Поиск\",\n  \"createTable\": \"Вставить таблицу\",\n  \"addColumnLeft\": \"Add column on the left\",\n  \"addColumnRight\": \"Add column on the right\",\n  \"addRowAbove\": \"Add row above\",\n  \"addRowBelow\": \"Add row below\",\n  \"deleteColumn\": \"Delete column\",\n  \"deleteRow\": \"Delete row\",\n  \"backColor\": \"Background color\",\n  \"deleteFile\": \"Are you sure you want to delete \\\"{0}\\\"?\",\n  \"directoryNotFound\": \"A directory with this name was not found.\",\n  \"dropFilesHere\": \"drop files here to upload\",\n  \"emptyFolder\": \"Empty Folder\",\n  \"foreColor\": \"Color\",\n  \"invalidFileType\": \"The selected file \\\"{0}\\\" is not valid. Supported file types are {1}.\",\n  \"orderBy\": \"Arrange by:\",\n  \"orderByName\": \"Name\",\n  \"orderBySize\": \"Size\",\n  \"overwriteFile\": \"'A file with name \\\"{0}\\\" already exists in the current directory. Do you want to overwrite it?\",\n  \"uploadFile\": \"Upload\",\n  \"formatting\": \"Format\",\n  \"viewHtml\": \"View HTML\",\n  \"dialogUpdate\": \"Update\",\n  \"insertFile\": \"Insert file\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"Դադարեցնել ներբեռնումը\",\n  \"dropFilesHere\": \"Ներբեռնելու համար քաշեք ֆայլերը այստեղ\",\n  \"remove\": \"Հեռացնել\",\n  \"retry\": \"Կրկնել\",\n  \"select\": \"Ընտրել...\",\n  \"statusFailed\": \"Ներբեռնումը կասեցված է\",\n  \"statusUploaded\": \"Ներբեռնումը ավարտված է\",\n  \"statusUploading\": \"բեռնվում է\",\n  \"uploadSelectedFiles\": \"Ներբեռնել ընտրված ֆայլերը\",\n  \"headerStatusUploaded\": \"Կատարված է\",\n  \"headerStatusUploading\": \"Բեռնվում է...\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"ամեն օր\",\n  \"cancel\": \"Չեղարկել\",\n  \"editable\": {\n    \"confirmation\": \"Ցանկանու՞մ եք հեռացնել։\"\n  },\n  \"date\": \"Ամսաթիվ\",\n  \"destroy\": \"Հեռացնել\",\n  \"editor\": {\n    \"allDayEvent\": \"Ամենօրյա իրադարձություն\",\n    \"description\": \"Նկարագրություն\",\n    \"editorTitle\": \"Իրադարձություն\",\n    \"end\": \"Վերջ\",\n    \"endTimezone\": \"End timezone\",\n    \"repeat\": \"Կրկնել\",\n    \"separateTimezones\": \"Use separate start and end time zones\",\n    \"start\": \"Սկիզբ\",\n    \"startTimezone\": \"Start timezone\",\n    \"timezone\": \" \",\n    \"timezoneEditorButton\": \"Time zone\",\n    \"timezoneEditorTitle\": \"Timezones\",\n    \"title\": \"Title\",\n    \"noTimezone\": \"No timezone\"\n  },\n  \"event\": \"Event\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Do you want to delete only this event occurrence or the whole series?\",\n    \"deleteWindowOccurrence\": \"Delete current occurrence\",\n    \"deleteWindowSeries\": \"Delete the series\",\n    \"deleteWindowTitle\": \"Delete Recurring Item\",\n    \"editRecurring\": \"Do you want to edit only this event occurrence or the whole series?\",\n    \"editWindowOccurrence\": \"Edit current occurrence\",\n    \"editWindowSeries\": \"Edit the series\",\n    \"editWindowTitle\": \"Edit Recurring Item\"\n  },\n  \"save\": \"Save\",\n  \"time\": \"Time\",\n  \"today\": \"Today\",\n  \"views\": {\n    \"agenda\": \"Agenda\",\n    \"day\": \"Day\",\n    \"month\": \"Month\",\n    \"week\": \"Week\",\n    \"workWeek\": \"Work Week\"\n  },\n  \"deleteWindowTitle\": \"Delete event\",\n  \"showFullDay\": \"Show full day\",\n  \"showWorkDay\": \"Show business hours\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} պարտադիր է\",\n  \"pattern\": \"{0} սխալ է\",\n  \"min\": \"{0}-ը պետք է լինի մեծ կամ հավասար {1}-ից\",\n  \"max\": \"{0}-ը պետք է լինի փոքր կամ հավասար {1}-ից\",\n  \"step\": \"{0} սխալ է\",\n  \"email\": \"{0} սխալ էլեկտրոնային հասցե է\",\n  \"url\": \"{0} սխալ URL է\",\n  \"date\": \"{0} սխալ ամսաթիվ է\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"սերտ\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"լավ\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"լավ\",\n  \"cancel\": \"Չեղարկել\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"լավ\",\n  \"cancel\": \"Չեղարկել\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}