{"version": 3, "sources": ["messages/kendo.messages.zh-TW.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "FlatColorPicker", "prototype", "options", "messages", "extend", "apply", "cancel", "ColorPicker", "ColumnMenu", "sortAscending", "sortDescending", "filter", "columns", "done", "settings", "lock", "unlock", "Editor", "bold", "italic", "underline", "strikethrough", "superscript", "subscript", "justifyCenter", "justifyLeft", "justifyRight", "justifyFull", "insertUnorderedList", "insertOrderedList", "indent", "outdent", "createLink", "unlink", "insertImage", "insertFile", "insertHtml", "viewHtml", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "formatBlock", "formatting", "foreColor", "backColor", "style", "emptyFolder", "uploadFile", "orderBy", "orderBySize", "orderByName", "invalidFileType", "deleteFile", "overwriteFile", "directoryNotFound", "imageWebAddress", "imageAltText", "imageWidth", "imageHeight", "fileWebAddress", "fileTitle", "linkWebAddress", "linkText", "linkToolTip", "linkOpenInNewWindow", "dialogUpdate", "dialogInsert", "dialogButtonSeparator", "dialogCancel", "createTable", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "deleteRow", "deleteColumn", "FileBrowser", "dropFilesHere", "search", "<PERSON><PERSON><PERSON>ell", "isTrue", "isFalse", "clear", "operator", "FilterMenu", "info", "and", "or", "selectValue", "value", "FilterMultiCheck", "operators", "string", "eq", "neq", "startswith", "contains", "doesnotcontain", "endswith", "number", "gte", "gt", "lte", "lt", "date", "enums", "<PERSON><PERSON><PERSON>", "views", "day", "week", "month", "actions", "append", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Groupable", "empty", "NumericTextBox", "upArrowText", "downArrowText", "Pager", "allPages", "display", "page", "of", "itemsPerPage", "first", "last", "next", "previous", "refresh", "morePages", "PivotGrid", "measureFields", "columnFields", "rowFields", "RecurrenceEditor", "frequencies", "never", "hourly", "daily", "weekly", "monthly", "yearly", "repeatEvery", "interval", "repeatOn", "end", "label", "mobileLabel", "after", "occurrence", "on", "offsetPositions", "second", "third", "fourth", "weekdays", "weekday", "weekend", "Scheduler", "today", "deleteWindowTitle", "ariaSlotLabel", "ariaEventLabel", "workWeek", "agenda", "recurrenceMessages", "deleteWindowOccurrence", "deleteWindowSeries", "editWindowTitle", "editWindowOccurrence", "editWindowSeries", "editor", "title", "start", "allDayEvent", "description", "repeat", "timezone", "startTimezone", "endTimezone", "separateTimezones", "timezoneEditorTitle", "timezoneEditorButton", "timezoneTitle", "noTimezone", "editor<PERSON><PERSON><PERSON>", "Slide<PERSON>", "increaseButtonTitle", "decreaseButtonTitle", "TreeView", "loading", "requestFailed", "retry", "Upload", "localization", "remove", "uploadSelectedFiles", "statusUploading", "statusUploaded", "statusWarning", "statusFailed", "headerStatusUploading", "headerStatusUploaded", "Validator", "required", "pattern", "min", "max", "step", "email", "url", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,kBACbF,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,SAC3CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,gBAAgBC,UAAUC,QAAQC,UACxDE,MAAS,KACTC,OAAU,QAMRR,MAAMC,GAAGQ,cACbT,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGQ,YAAYN,UAAUC,QAAQC,UACpDE,MAAS,KACTC,OAAU,QAMRR,MAAMC,GAAGS,aACbV,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGS,WAAWP,UAAUC,QAAQC,UACnDM,cAAiB,KACjBC,eAAkB,KAClBC,OAAU,KACVC,QAAW,IACXC,KAAQ,KACRC,SAAY,MACZC,KAAQ,KACRC,OAAU,UAMRlB,MAAMC,GAAGkB,SACbnB,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,SAClCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkB,OAAOhB,UAAUC,QAAQC,UAC/Ce,KAAQ,KACRC,OAAU,KACVC,UAAa,MACbC,cAAiB,MACjBC,YAAe,KACfC,UAAa,KACbC,cAAiB,KACjBC,YAAe,MACfC,aAAgB,MAChBC,YAAe,OACfC,oBAAuB,SACvBC,kBAAqB,SACrBC,OAAU,OACVC,QAAW,OACXC,WAAc,OACdC,OAAU,OACVC,YAAe,OACfC,WAAc,OACdC,WAAc,UACdC,SAAY,UACZC,SAAY,OACZC,gBAAmB,UACnBC,SAAY,OACZC,gBAAmB,UACnBC,YAAe,OACfC,WAAc,MACdC,UAAa,KACbC,UAAa,MACbC,MAAS,KACTC,YAAe,QACfC,WAAc,KACdC,QAAW,QACXC,YAAe,KACfC,YAAe,KACfC,gBAAmB,+BACnBC,WAAc,gBACdC,cAAiB,kCACjBC,kBAAqB,UACrBC,gBAAmB,OACnBC,aAAgB,OAChBC,WAAc,UACdC,YAAe,UACfC,eAAkB,OAClBC,UAAa,KACbC,eAAkB,OAClBC,SAAY,OACZC,YAAe,OACfC,oBAAuB,UACvBC,aAAgB,KAChBC,aAAgB,KAChBC,sBAAyB,IACzBC,aAAgB,KAChBC,YAAe,OACfC,cAAiB,QACjBC,eAAkB,QAClBC,YAAe,QACfC,YAAe,QACfC,UAAa,MACbC,aAAgB,SAMd9E,MAAMC,GAAG8E,cACb/E,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,SACvCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8E,YAAY5E,UAAUC,QAAQC,UACpD6C,WAAc,KACdC,QAAW,OACXE,YAAe,KACfD,YAAe,KACfK,kBAAqB,UACrBR,YAAe,QACfM,WAAc,gBACdD,gBAAmB,+BACnBE,cAAiB,kCACjBwB,cAAiB,cACjBC,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQC,UACnD8E,OAAU,KACVC,QAAW,KACXvE,OAAU,KACVwE,MAAS,KACTC,SAAY,SAMVtF,MAAMC,GAAGsF,aACbvF,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQC,SACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQC,UACnDmF,KAAQ,aACRL,OAAU,KACVC,QAAW,KACXvE,OAAU,KACVwE,MAAS,KACTI,IAAO,KACPC,GAAM,IACNC,YAAe,OACfL,SAAY,MACZM,MAAS,IACTpF,OAAU,QAMRR,MAAMC,GAAG4F,mBACb7F,MAAMC,GAAG4F,iBAAiB1F,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4F,iBAAiB1F,UAAUC,QAAQC,UACzD4E,OAAU,QAMRjF,MAAMC,GAAGiF,aACblF,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQ0F,UACtChG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiF,WAAW/E,UAAUC,QAAQ0F,WACnDC,QACEC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,MAClBC,SAAY,OAEdC,QACEN,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERC,MACEX,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERE,OACEZ,GAAM,KACNC,IAAO,UAOPjG,MAAMC,GAAGsF,aACbvF,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQ0F,UACtChG,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsF,WAAWpF,UAAUC,QAAQ0F,WACnDC,QACEC,GAAM,KACNC,IAAO,MACPC,WAAc,MACdC,SAAY,KACZC,eAAkB,MAClBC,SAAY,OAEdC,QACEN,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERC,MACEX,GAAM,KACNC,IAAO,MACPM,IAAO,OACPC,GAAM,KACNC,IAAO,OACPC,GAAM,MAERE,OACEZ,GAAM,KACNC,IAAO,UAQPjG,MAAMC,GAAG4G,QACb7G,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4G,MAAM1G,UAAUC,QAAQC,UAC9CyG,OACEC,IAAO,IACPC,KAAQ,IACRC,MAAS,KAEXC,SACEC,OAAU,OACVC,SAAY,QACZC,aAAgB,QAChBC,YAAe,YAOftH,MAAMC,GAAGsH,OACbvH,MAAMC,GAAGsH,KAAKpH,UAAUC,QAAQC,SAChCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsH,KAAKpH,UAAUC,QAAQC,UAC7CmH,UACEhH,OAAU,KACViH,WAAc,KACdC,OAAU,KACVC,QAAW,KACXC,KAAQ,KACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,KACRC,OAAU,KACVC,OAAU,MAEZC,UACEC,aAAgB,KAChBC,aAAgB,UAChBC,cAAiB,SAOjBrI,MAAMC,GAAGqI,YACbtI,MAAMC,GAAGqI,UAAUnI,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqI,UAAUnI,UAAUC,QAAQC,UAClDkI,MAAS,oBAMPvI,MAAMC,GAAGuI,iBACbxI,MAAMC,GAAGuI,eAAerI,UAAUC,QAClCN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGuI,eAAerI,UAAUC,SAC/CqI,YAAe,KACfC,cAAiB,QAMf1I,MAAMC,GAAG0I,QACb3I,MAAMC,GAAG0I,MAAMxI,UAAUC,QAAQC,SACjCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0I,MAAMxI,UAAUC,QAAQC,UAC9CuI,SAAY,MACZC,QAAW,uBACXN,MAAS,YACTO,KAAQ,IACRC,GAAM,QACNC,aAAgB,KAChBC,MAAS,KACTC,KAAQ,KACRC,KAAQ,MACRC,SAAY,MACZC,QAAW,KACXC,UAAa,WAMXtJ,MAAMC,GAAGsJ,YACbvJ,MAAMC,GAAGsJ,UAAUpJ,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsJ,UAAUpJ,UAAUC,QAAQC,UAClDmJ,cAAiB,WACjBC,aAAgB,UAChBC,UAAa,aAMX1J,MAAMC,GAAG0J,mBACb3J,MAAMC,GAAG0J,iBAAiBxJ,UAAUC,QAAQC,SAC5CP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0J,iBAAiBxJ,UAAUC,QAAQC,UACzDuJ,aACEC,MAAS,KACTC,OAAU,MACVC,MAAS,KACTC,OAAU,KACVC,QAAW,KACXC,OAAU,MAEZJ,QACEK,YAAe,SACfC,SAAY,OAEdL,OACEI,YAAe,SACfC,SAAY,MAEdJ,QACEI,SAAY,KACZD,YAAe,SACfE,SAAY,QAEdJ,SACEE,YAAe,SACfE,SAAY,OACZD,SAAY,KACZrD,IAAO,MAETmD,QACEC,YAAe,SACfE,SAAY,QACZD,SAAY,KACZrB,GAAM,SAERuB,KACEC,MAAS,QACTC,YAAe,OACfX,MAAS,KACTY,MAAS,MACTC,WAAc,MACdC,GAAM,OAERC,iBACE3B,MAAS,KACT4B,OAAU,KACVC,MAAS,KACTC,OAAU,KACV7B,KAAQ,MAEV8B,UACEjE,IAAO,IACPkE,QAAW,MACXC,QAAW,SAQXlL,MAAMC,GAAGkL,YACbnL,MAAMC,GAAGkL,UAAUhL,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGkL,UAAUhL,UAAUC,QAAQC,UAClD+K,MAAS,KACTrD,KAAQ,KACRvH,OAAU,KACVmH,QAAW,KACX0D,kBAAqB,OACrBC,cAAiB,oBACjBC,eAAkB,wBAClBzE,OACEC,IAAO,IACPC,KAAQ,IACRwE,SAAY,MACZC,OAAU,KACVxE,MAAS,KAEXyE,oBACEL,kBAAqB,SACrBM,uBAA0B,SAC1BC,mBAAsB,OACtBC,gBAAmB,SACnBC,qBAAwB,SACxBC,iBAAoB,QAEtBC,QACEC,MAAS,KACTC,MAAS,KACT5B,IAAO,KACP6B,YAAe,OACfC,YAAe,KACfC,OAAU,KACVC,SAAY,IACZC,cAAiB,OACjBC,YAAe,OACfC,kBAAqB,eACrBC,oBAAuB,KACvBC,qBAAwB,KACxBC,cAAiB,OACjBC,WAAc,IACdC,YAAe,SAOf9M,MAAMC,GAAG8M,SACb/M,MAAMC,GAAG8M,OAAO5M,UAAUC,QAC1BN,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8M,OAAO5M,UAAUC,SACvC4M,oBAAuB,KACvBC,oBAAuB,QAMrBjN,MAAMC,GAAGiN,WACblN,MAAMC,GAAGiN,SAAS/M,UAAUC,QAAQC,SACpCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGiN,SAAS/M,UAAUC,QAAQC,UACjD8M,QAAW,SACXC,cAAiB,OACjBC,MAAS,QAMPrN,MAAMC,GAAGqN,SACbtN,MAAMC,GAAGqN,OAAOnN,UAAUC,QAAQmN,aAClCzN,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqN,OAAOnN,UAAUC,QAAQmN,cAC/CvF,OAAU,QACVxH,OAAU,KACV6M,MAAS,KACTG,OAAU,KACVC,oBAAuB,OACvBzI,cAAiB,cACjB0I,gBAAmB,MACnBC,eAAkB,MAClBC,cAAiB,KACjBC,aAAgB,KAChBC,sBAAyB,QACzBC,qBAAwB,QAMtB/N,MAAMC,GAAG+N,YACbhO,MAAMC,GAAG+N,UAAU7N,UAAUC,QAAQC,SACrCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+N,UAAU7N,UAAUC,QAAQC,UAClD4N,SAAY,WACZC,QAAW,SACXC,IAAO,kBACPC,IAAO,kBACPC,KAAQ,SACRC,MAAS,gBACTC,IAAO,eACP5H,KAAQ,kBAGP6H,OAAOxO,MAAMyO", "file": "kendo.messages.zh-TW.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* FlatColorPicker messages */\n\nif (kendo.ui.FlatColorPicker) {\nkendo.ui.FlatColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.FlatColorPicker.prototype.options.messages,{\n  \"apply\": \"確定\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* ColorPicker messages */\n\nif (kendo.ui.ColorPicker) {\nkendo.ui.ColorPicker.prototype.options.messages =\n$.extend(true, kendo.ui.ColorPicker.prototype.options.messages,{\n  \"apply\": \"確定\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"sortAscending\": \"升序\",\n  \"sortDescending\": \"降序\",\n  \"filter\": \"過濾\",\n  \"columns\": \"列\",\n  \"done\": \"完成\",\n  \"settings\": \"列設置\",\n  \"lock\": \"鎖定\",\n  \"unlock\": \"解除鎖定\"\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"bold\": \"粗體\",\n  \"italic\": \"斜體\",\n  \"underline\": \"下劃線\",\n  \"strikethrough\": \"刪除線\",\n  \"superscript\": \"上標\",\n  \"subscript\": \"下標\",\n  \"justifyCenter\": \"居中\",\n  \"justifyLeft\": \"左對齊\",\n  \"justifyRight\": \"右對齊\",\n  \"justifyFull\": \"兩端對齊\",\n  \"insertUnorderedList\": \"插入無序列表\",\n  \"insertOrderedList\": \"插入有序列表\",\n  \"indent\": \"增加縮進\",\n  \"outdent\": \"減少縮進\",\n  \"createLink\": \"插入鏈接\",\n  \"unlink\": \"移除鏈接\",\n  \"insertImage\": \"插入圖片\",\n  \"insertFile\": \"插入文件\",\n  \"insertHtml\": \"插入 HTML\",\n  \"viewHtml\": \"查看 HTML\",\n  \"fontName\": \"選擇字體\",\n  \"fontNameInherit\": \"（繼承的字體）\",\n  \"fontSize\": \"選擇字號\",\n  \"fontSizeInherit\": \"（繼承的字號）\",\n  \"formatBlock\": \"格式化塊\",\n  \"formatting\": \"格式化\",\n  \"foreColor\": \"顏色\",\n  \"backColor\": \"背景色\",\n  \"style\": \"風格\",\n  \"emptyFolder\": \"文件夾為空\",\n  \"uploadFile\": \"上傳\",\n  \"orderBy\": \"排序條件:\",\n  \"orderBySize\": \"大小\",\n  \"orderByName\": \"名字\",\n  \"invalidFileType\": \"選中的文件 \\\"{0}\\\" 非法，支持的文件類型為 {1}。\",\n  \"deleteFile\": '您確定要刪除 \\\"{0}\\\"?',\n  \"overwriteFile\": '當前文件夾已存在文件名為 \\\"{0}\\\" 的文件，您確定要覆蓋麽？',\n  \"directoryNotFound\": \"此文件夾未找到\",\n  \"imageWebAddress\": \"圖片地址\",\n  \"imageAltText\": \"替代文本\",\n  \"imageWidth\": \"寬度 (px)\",\n  \"imageHeight\": \"高度 (px)\",\n  \"fileWebAddress\": \"文件地址\",\n  \"fileTitle\": \"標題\",\n  \"linkWebAddress\": \"鏈接地址\",\n  \"linkText\": \"鏈接文字\",\n  \"linkToolTip\": \"鏈接提示\",\n  \"linkOpenInNewWindow\": \"在新窗口中打開\",\n  \"dialogUpdate\": \"上傳\",\n  \"dialogInsert\": \"插入\",\n  \"dialogButtonSeparator\": \"或\",\n  \"dialogCancel\": \"取消\",\n  \"createTable\": \"創建表格\",\n  \"addColumnLeft\": \"左側添加列\",\n  \"addColumnRight\": \"右側添加列\",\n  \"addRowAbove\": \"上方添加行\",\n  \"addRowBelow\": \"下方添加行\",\n  \"deleteRow\": \"刪除行\",\n  \"deleteColumn\": \"刪除列\"\n});\n}\n\n/* FileBrowser messages */\n\nif (kendo.ui.FileBrowser) {\nkendo.ui.FileBrowser.prototype.options.messages =\n$.extend(true, kendo.ui.FileBrowser.prototype.options.messages,{\n  \"uploadFile\": \"上傳\",\n  \"orderBy\": \"排序條件\",\n  \"orderByName\": \"名稱\",\n  \"orderBySize\": \"大小\",\n  \"directoryNotFound\": \"此文件夾未找到\",\n  \"emptyFolder\": \"文件夾為空\",\n  \"deleteFile\": '您確定要刪除 \\\"{0}\\\"?',\n  \"invalidFileType\": \"選中的文件 \\\"{0}\\\" 非法，支持的文件類型為 {1}。\",\n  \"overwriteFile\": \"當前文件夾已存在文件名為 \\\"{0}\\\" 的文件，您確定要覆蓋麽？\",\n  \"dropFilesHere\": \"拖拽要上傳的文件到此處\",\n  \"search\": \"搜索\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"isTrue\": \"為真\",\n  \"isFalse\": \"為假\",\n  \"filter\": \"過濾\",\n  \"clear\": \"清除\",\n  \"operator\": \"運算符\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"info\": \"顯示符合以下條件的行\",\n  \"isTrue\": \"為真\",\n  \"isFalse\": \"為假\",\n  \"filter\": \"過濾\",\n  \"clear\": \"清除\",\n  \"and\": \"並且\",\n  \"or\": \"或\",\n  \"selectValue\": \"-選擇-\",\n  \"operator\": \"運算符\",\n  \"value\": \"值\",\n  \"cancel\": \"取消\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"搜索\"\n});\n}\n\n/* Filter cell operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"startswith\": \"開頭為\",\n    \"contains\": \"包含\",\n    \"doesnotcontain\": \"不包含\",\n    \"endswith\": \"結尾為\"\n  },\n  \"number\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"date\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"enums\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"string\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"startswith\": \"開頭為\",\n    \"contains\": \"包含\",\n    \"doesnotcontain\": \"不包含\",\n    \"endswith\": \"結尾為\"\n  },\n  \"number\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"date\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\",\n    \"gte\": \"大於等於\",\n    \"gt\": \"大於\",\n    \"lte\": \"小於等於\",\n    \"lt\": \"小於\"\n  },\n  \"enums\": {\n    \"eq\": \"等於\",\n    \"neq\": \"不等於\"\n  }\n});\n}\n\n\n/* Gantt messages */\n\nif (kendo.ui.Gantt) {\nkendo.ui.Gantt.prototype.options.messages =\n$.extend(true, kendo.ui.Gantt.prototype.options.messages,{\n  \"views\": {\n    \"day\": \"日\",\n    \"week\": \"周\",\n    \"month\": \"月\"\n  },\n  \"actions\": {\n    \"append\": \"添加任務\",\n    \"addChild\": \"添加子任務\",\n    \"insertBefore\": \"添加到前面\",\n    \"insertAfter\": \"添加到後面\"\n  }\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"cancel\": \"取消\",\n    \"canceledit\": \"取消\",\n    \"create\": \"新增\",\n    \"destroy\": \"刪除\",\n    \"edit\": \"編輯\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"保存\",\n    \"select\": \"選擇\",\n    \"update\": \"更新\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"取消\",\n    \"confirmation\": \"確定要刪除嗎？\",\n    \"confirmDelete\": \"刪除\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"拖拽列標題到此處按列組合顯示\"\n});\n}\n\n/* NumericTextBox messages */\n\nif (kendo.ui.NumericTextBox) {\nkendo.ui.NumericTextBox.prototype.options =\n$.extend(true, kendo.ui.NumericTextBox.prototype.options,{\n  \"upArrowText\": \"增加\",\n  \"downArrowText\": \"減少\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"顯示條目 {0} - {1} 共 {2}\",\n  \"empty\": \"沒有可顯示的記錄。\",\n  \"page\": \"頁\",\n  \"of\": \"共 {0}\",\n  \"itemsPerPage\": \"每頁\",\n  \"first\": \"首頁\",\n  \"last\": \"末頁\",\n  \"next\": \"下一頁\",\n  \"previous\": \"上一頁\",\n  \"refresh\": \"刷新\",\n  \"morePages\": \"更多...\"\n});\n}\n\n/* PivotGrid messages */\n\nif (kendo.ui.PivotGrid) {\nkendo.ui.PivotGrid.prototype.options.messages =\n$.extend(true, kendo.ui.PivotGrid.prototype.options.messages,{\n  \"measureFields\": \"拖放數據字段於此\",\n  \"columnFields\": \"拖放列字段於此\",\n  \"rowFields\": \"拖放行字段於此\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"frequencies\": {\n    \"never\": \"從不\",\n    \"hourly\": \"每小時\",\n    \"daily\": \"每天\",\n    \"weekly\": \"每周\",\n    \"monthly\": \"每月\",\n    \"yearly\": \"每年\"\n  },\n  \"hourly\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"interval\": \" 小時\"\n  },\n  \"daily\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"interval\": \" 天\"\n  },\n  \"weekly\": {\n    \"interval\": \" 周\",\n    \"repeatEvery\": \"重復周期: \",\n    \"repeatOn\": \"重復於:\"\n  },\n  \"monthly\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"repeatOn\": \"重復於:\",\n    \"interval\": \" 月\",\n    \"day\": \"日期\"\n  },\n  \"yearly\": {\n    \"repeatEvery\": \"重復周期: \",\n    \"repeatOn\": \"重復於: \",\n    \"interval\": \" 年\",\n    \"of\": \" 月份: \"\n  },\n  \"end\": {\n    \"label\": \"截止時間:\",\n    \"mobileLabel\": \"截止時間\",\n    \"never\": \"從不\",\n    \"after\": \"重復 \",\n    \"occurrence\": \" 次後\",\n    \"on\": \"止於 \"\n  },\n  \"offsetPositions\": {\n    \"first\": \"第一\",\n    \"second\": \"第二\",\n    \"third\": \"第三\",\n    \"fourth\": \"第四\",\n    \"last\": \"最後\"\n  },\n  \"weekdays\": {\n    \"day\": \"天\",\n    \"weekday\": \"工作日\",\n    \"weekend\": \"周末\"\n  }\n});\n}\n\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"today\": \"今天\",\n  \"save\": \"保存\",\n  \"cancel\": \"取消\",\n  \"destroy\": \"刪除\",\n  \"deleteWindowTitle\": \"刪除事件\",\n  \"ariaSlotLabel\": \"選擇從 {0:t} 到 {1:t}\",\n  \"ariaEventLabel\": \"{0} on {1:D} at {2:t}\",\n  \"views\": {\n    \"day\": \"日\",\n    \"week\": \"周\",\n    \"workWeek\": \"工作日\",\n    \"agenda\": \"日程\",\n    \"month\": \"月\"\n  },\n  \"recurrenceMessages\": {\n    \"deleteWindowTitle\": \"刪除周期條目\",\n    \"deleteWindowOccurrence\": \"刪除當前事件\",\n    \"deleteWindowSeries\": \"刪除序列\",\n    \"editWindowTitle\": \"修改周期條目\",\n    \"editWindowOccurrence\": \"修改當前事件\",\n    \"editWindowSeries\": \"修改序列\"\n  },\n  \"editor\": {\n    \"title\": \"標題\",\n    \"start\": \"起始\",\n    \"end\": \"終止\",\n    \"allDayEvent\": \"全天事件\",\n    \"description\": \"描述\",\n    \"repeat\": \"重復\",\n    \"timezone\": \" \",\n    \"startTimezone\": \"起始時區\",\n    \"endTimezone\": \"終止時區\",\n    \"separateTimezones\": \"使用獨立的起始和終止時區\",\n    \"timezoneEditorTitle\": \"時區\",\n    \"timezoneEditorButton\": \"時區\",\n    \"timezoneTitle\": \"選擇時區\",\n    \"noTimezone\": \"無\",\n    \"editorTitle\": \"事件\"\n  }\n});\n}\n\n/* Slider messages */\n\nif (kendo.ui.Slider) {\nkendo.ui.Slider.prototype.options =\n$.extend(true, kendo.ui.Slider.prototype.options,{\n  \"increaseButtonTitle\": \"增加\",\n  \"decreaseButtonTitle\": \"減少\"\n});\n}\n\n/* TreeView messages */\n\nif (kendo.ui.TreeView) {\nkendo.ui.TreeView.prototype.options.messages =\n$.extend(true, kendo.ui.TreeView.prototype.options.messages,{\n  \"loading\": \"加載中...\",\n  \"requestFailed\": \"加載失敗\",\n  \"retry\": \"重試\"\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"select\": \"選擇...\",\n  \"cancel\": \"取消\",\n  \"retry\": \"重試\",\n  \"remove\": \"移除\",\n  \"uploadSelectedFiles\": \"上傳文件\",\n  \"dropFilesHere\": \"拖拽要上傳的文件到此處\",\n  \"statusUploading\": \"上傳中\",\n  \"statusUploaded\": \"已上傳\",\n  \"statusWarning\": \"警告\",\n  \"statusFailed\": \"失敗\",\n  \"headerStatusUploading\": \"上傳...\",\n  \"headerStatusUploaded\": \"完成\"\n});\n}\n\n/* Validator messages */\n\nif (kendo.ui.Validator) {\nkendo.ui.Validator.prototype.options.messages =\n$.extend(true, kendo.ui.Validator.prototype.options.messages,{\n  \"required\": \"{0} 為必填項\",\n  \"pattern\": \"{0} 非法\",\n  \"min\": \"{0} 應該大於或等於 {1}\",\n  \"max\": \"{0} 應該小於或等於 {1}\",\n  \"step\": \"{0} 非法\",\n  \"email\": \"{0} 不是合法的郵件地址\",\n  \"url\": \"{0} 不是合法的URL\",\n  \"date\": \"{0} 不是合法的日期\"\n});\n}\n})(window.kendo.jQuery);\n}));"]}