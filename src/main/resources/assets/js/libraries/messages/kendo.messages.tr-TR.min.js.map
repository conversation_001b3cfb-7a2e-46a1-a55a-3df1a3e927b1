{"version": 3, "sources": ["messages/kendo.messages.tr-TR.js"], "names": ["f", "define", "amd", "$", "undefined", "kendo", "ui", "<PERSON><PERSON><PERSON>ell", "prototype", "options", "operators", "extend", "date", "eq", "gt", "gte", "lt", "lte", "neq", "enums", "number", "string", "contains", "doesnotcontain", "endswith", "startswith", "FilterMenu", "ColumnMenu", "messages", "columns", "settings", "done", "lock", "sortAscending", "sortDescending", "unlock", "RecurrenceEditor", "daily", "interval", "repeatEvery", "end", "after", "label", "mobileLabel", "never", "occurrence", "on", "frequencies", "monthly", "weekly", "yearly", "day", "repeatOn", "offsetPositions", "first", "fourth", "last", "second", "third", "weekdays", "weekday", "weekend", "of", "Editor", "addColumnLeft", "addColumnRight", "addRowAbove", "addRowBelow", "backColor", "bold", "createLink", "createTable", "deleteColumn", "deleteFile", "deleteRow", "dialogButtonSeparator", "dialogCancel", "dialogInsert", "dialogUpdate", "directoryNotFound", "dropFilesHere", "emptyFolder", "fontName", "fontNameInherit", "fontSize", "fontSizeInherit", "foreColor", "formatBlock", "formatting", "imageAltText", "imageWebAddress", "indent", "insertHtml", "insertImage", "insertOrderedList", "insertUnorderedList", "invalidFileType", "italic", "justifyCenter", "justifyFull", "justifyLeft", "justifyRight", "linkOpenInNewWindow", "linkText", "linkToolTip", "linkWebAddress", "orderBy", "orderByName", "orderBySize", "outdent", "overwriteFile", "search", "strikethrough", "styles", "subscript", "superscript", "underline", "unlink", "uploadFile", "viewHtml", "insertFile", "clear", "filter", "isFalse", "isTrue", "operator", "and", "cancel", "info", "or", "selectValue", "value", "FilterMultiCheck", "Grid", "commands", "canceledit", "create", "destroy", "edit", "excel", "pdf", "save", "select", "update", "editable", "cancelDelete", "confirmation", "confirmDelete", "Groupable", "empty", "Pager", "allPages", "display", "itemsPerPage", "morePages", "next", "page", "previous", "refresh", "Scheduler", "allDay", "deleteWindowTitle", "editor", "allDayEvent", "description", "editor<PERSON><PERSON><PERSON>", "endTimezone", "noTimezone", "repeat", "separateTimezones", "start", "startTimezone", "timezone", "timezoneEditorButton", "timezoneEditorTitle", "title", "event", "recurrenceMessages", "deleteRecurring", "deleteWindowOccurrence", "deleteWindowSeries", "editR<PERSON><PERSON>ring", "editWindowOccurrence", "editWindowSeries", "editWindowTitle", "showFullDay", "showWorkDay", "time", "today", "views", "agenda", "month", "week", "workWeek", "Upload", "localization", "headerStatusUploaded", "headerStatusUploading", "remove", "retry", "statusFailed", "statusUploaded", "statusUploading", "uploadSelectedFiles", "Dialog", "close", "<PERSON><PERSON>", "okText", "Confirm", "Prompt", "window", "j<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;CAyBC,SAASA,GACgB,kBAAXC,SAAyBA,OAAOC,IACvCD,QAAQ,cAAeD,GAEvBA,KAEN,YACF,SAAWG,EAAGC,GAGVC,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQC,WACnDE,MACEC,GAAM,UACNC,GAAM,QACNC,IAAO,mBACPC,GAAM,OACNC,IAAO,kBACPC,IAAO,iBAETC,OACEN,GAAM,UACNK,IAAO,iBAETE,QACEP,GAAM,UACNC,GAAM,WACNC,IAAO,0BACPC,GAAM,aACNC,IAAO,uBACPC,IAAO,iBAETG,QACEC,SAAY,WACZC,eAAkB,YAClBC,SAAY,YACZX,GAAM,UACNK,IAAO,gBACPO,WAAc,iBAOdpB,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,UACtCP,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQC,WACnDE,MACEC,GAAM,UACNC,GAAM,QACNC,IAAO,mBACPC,GAAM,OACNC,IAAO,kBACPC,IAAO,iBAETC,OACEN,GAAM,UACNK,IAAO,iBAETE,QACEP,GAAM,UACNC,GAAM,WACNC,IAAO,0BACPC,GAAM,aACNC,IAAO,uBACPC,IAAO,iBAETG,QACEC,SAAY,WACZC,eAAkB,YAClBC,SAAY,YACZX,GAAM,UACNK,IAAO,gBACPO,WAAc,iBAOdpB,MAAMC,GAAGqB,aACbtB,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGqB,WAAWnB,UAAUC,QAAQmB,UACnDC,QAAW,WACXC,SAAY,iBACZC,KAAQ,QACRC,KAAQ,UACRC,cAAiB,iBACjBC,eAAkB,kBAClBC,OAAU,iBAMR9B,MAAMC,GAAG8B,mBACb/B,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8B,iBAAiB5B,UAAUC,QAAQmB,UACzDS,OACEC,SAAY,SACZC,YAAe,oBAEjBC,KACEC,MAAS,QACTC,MAAS,QACTC,YAAe,QACfC,MAAS,WACTC,WAAc,OACdC,GAAM,SAERC,aACEV,MAAS,SACTW,QAAW,QACXJ,MAAS,WACTK,OAAU,WACVC,OAAU,UAEZF,SACEG,IAAO,MACPb,SAAY,QACZC,YAAe,kBACfa,SAAY,YAEdC,iBACEC,MAAS,MACTC,OAAU,WACVC,KAAQ,MACRC,OAAU,SACVC,MAAS,UAEXC,UACER,IAAO,MACPS,QAAW,UACXC,QAAW,aAEbZ,QACEX,SAAY,WACZC,YAAe,qBACfa,SAAY,YAEdF,QACEZ,SAAY,SACZwB,GAAM,WACNvB,YAAe,mBACfa,SAAY,eAOZ/C,MAAMC,GAAGyD,SACb1D,MAAMC,GAAGyD,OAAOvD,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyD,OAAOvD,UAAUC,QAAQmB,UAC/CoC,cAAiB,kBACjBC,eAAkB,kBAClBC,YAAe,wBACfC,YAAe,uBACfC,UAAa,kBACbC,KAAQ,SACRC,WAAc,eACdC,YAAe,gBACfC,aAAgB,cAChBC,WAAc,uCACdC,UAAa,YACbC,sBAAyB,QACzBC,aAAgB,QAChBC,aAAgB,OAChBC,aAAgB,WAChBC,kBAAqB,kCACrBC,cAAiB,yCACjBC,YAAe,aACfC,SAAY,sBACZC,gBAAmB,sBACnBC,SAAY,sBACZC,gBAAmB,mBACnBC,UAAa,OACbC,YAAe,QACfC,WAAc,gBACdC,aAAgB,mBAChBC,gBAAmB,aACnBC,OAAU,YACVC,WAAc,YACdC,YAAe,aACfC,kBAAqB,sBACrBC,oBAAuB,uBACvBC,gBAAmB,+EACnBC,OAAU,kBACVC,cAAiB,gBACjBC,YAAe,YACfC,YAAe,mBACfC,aAAgB,mBAChBC,oBAAuB,oBACvBC,SAAY,QACZC,YAAe,aACfC,eAAkB,cAClBC,QAAW,oBACXC,YAAe,OACfC,YAAe,QACfC,QAAW,UACXC,cAAiB,oFACjBC,OAAU,QACVC,cAAiB,cACjBC,OAAU,UACVC,UAAa,QACbC,YAAe,UACfC,UAAa,gBACbC,OAAU,iBACVC,WAAc,QACdC,SAAY,iBACZC,WAAc,iBAMZnH,MAAMC,GAAGC,aACbF,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGC,WAAWC,UAAUC,QAAQmB,UACnD6F,MAAS,UACTC,OAAU,SACVC,QAAW,SACXC,OAAU,SACVC,SAAY,wBAMVxH,MAAMC,GAAGoB,aACbrB,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,SACtCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGoB,WAAWlB,UAAUC,QAAQmB,UACnDkG,IAAO,KACPC,OAAU,QACVN,MAAS,UACTC,OAAU,SACVM,KAAQ,wCACRL,QAAW,SACXC,OAAU,SACVC,SAAY,qBACZI,GAAM,QACNC,YAAe,gBACfC,MAAS,WAMP9H,MAAMC,GAAG8H,mBACb/H,MAAMC,GAAG8H,iBAAiB5H,UAAUC,QAAQmB,SAC5CzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8H,iBAAiB5H,UAAUC,QAAQmB,UACzDmF,OAAU,WAMR1G,MAAMC,GAAG+H,OACbhI,MAAMC,GAAG+H,KAAK7H,UAAUC,QAAQmB,SAChCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG+H,KAAK7H,UAAUC,QAAQmB,UAC7C0G,UACEC,WAAc,QACdR,OAAU,0BACVS,OAAU,kBACVC,QAAW,MACXC,KAAQ,UACRC,MAAS,kBACTC,IAAO,gBACPC,KAAQ,wBACRC,OAAU,UACVC,OAAU,YAEZC,UACEC,aAAgB,QAChBC,aAAgB,iDAChBC,cAAiB,UAOjB9I,MAAMC,GAAG8I,YACb/I,MAAMC,GAAG8I,UAAU5I,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG8I,UAAU5I,UAAUC,QAAQmB,UAClDyH,MAAS,wFAMPhJ,MAAMC,GAAGgJ,QACbjJ,MAAMC,GAAGgJ,MAAM9I,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGgJ,MAAM9I,UAAUC,QAAQmB,UAC9C2H,SAAY,MACZC,QAAW,qDACXH,MAAS,yBACT/F,MAAS,kBACTmG,aAAgB,oBAChBjG,KAAQ,kBACRkG,UAAa,mBACbC,KAAQ,0BACR7F,GAAM,MACN8F,KAAQ,QACRC,SAAY,mBACZC,QAAW,cAMTzJ,MAAMC,GAAGyJ,YACb1J,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQmB,SACrCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGyJ,UAAUvJ,UAAUC,QAAQmB,UAClDoI,OAAU,UACVjC,OAAU,WACViB,UACEE,aAAgB,oDAElBtI,KAAQ,QACRqJ,kBAAqB,gBACrBxB,QAAW,MACXyB,QACEC,YAAe,qBACfC,YAAe,QACfC,YAAe,OACf7H,IAAO,QACP8H,YAAe,cACfC,WAAc,8BACdC,OAAU,SACVC,kBAAqB,mDACrBC,MAAS,YACTC,cAAiB,kBACjBC,SAAY,GACZC,qBAAwB,gBACxBC,oBAAuB,gBACvBC,MAAS,SAEXC,MAAS,OACTC,oBACEC,gBAAmB,gEACnBC,uBAA0B,yBAC1BC,mBAAsB,aACtBnB,kBAAqB,wBACrBoB,cAAiB,uEACjBC,qBAAwB,wBACxBC,iBAAoB,iBACpBC,gBAAmB,6BAErB3C,KAAQ,SACR4C,YAAe,iBACfC,YAAe,uBACfC,KAAQ,QACRC,MAAS,QACTC,OACEC,OAAU,SACV3I,IAAO,MACP4I,MAAS,KACTC,KAAQ,QACRC,SAAY,sBAOZ5L,MAAMC,GAAG4L,SACb7L,MAAMC,GAAG4L,OAAO1L,UAAUC,QAAQ0L,aAClChM,EAAEQ,QAAO,EAAMN,MAAMC,GAAG4L,OAAO1L,UAAUC,QAAQ0L,cAC/CpE,OAAU,WACV/C,cAAiB,yCACjBoH,qBAAwB,aACxBC,sBAAyB,aACzBC,OAAU,SACVC,MAAS,cACTzD,OAAU,UACV0D,aAAgB,eAChBC,eAAkB,WAClBC,gBAAmB,aACnBC,oBAAuB,6BAMrBtM,MAAMC,GAAGsM,SACbvM,MAAMC,GAAGsM,OAAOpM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGsM,OAAOpM,UAAUC,QAAQ0L,cAC/CU,MAAS,cAMPxM,MAAMC,GAAGwM,QACbzM,MAAMC,GAAGwM,MAAMtM,UAAUC,QAAQmB,SACjCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAGwM,MAAMtM,UAAUC,QAAQ0L,cAC9CY,OAAU,WAMR1M,MAAMC,GAAG0M,UACb3M,MAAMC,GAAG0M,QAAQxM,UAAUC,QAAQmB,SACnCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG0M,QAAQxM,UAAUC,QAAQ0L,cAChDY,OAAU,QACVhF,OAAU,WAKR1H,MAAMC,GAAG2M,SACb5M,MAAMC,GAAG2M,OAAOzM,UAAUC,QAAQmB,SAClCzB,EAAEQ,QAAO,EAAMN,MAAMC,GAAG2M,OAAOzM,UAAUC,QAAQ0L,cAC/CY,OAAU,QACVhF,OAAU,YAITmF,OAAO7M,MAAM8M", "file": "kendo.messages.tr-TR.min.js", "sourcesContent": ["/*!\n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n                                                                                                                                                                                                       \n\n*/\n\n(function(f){\n    if (typeof define === 'function' && define.amd) {\n        define([\"kendo.core\"], f);\n    } else {\n        f();\n    }\n}(function(){\n(function ($, undefined) {\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.operators =\n$.extend(true, kendo.ui.FilterCell.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Eşittir\",\n    \"gt\": \"Sonra\",\n    \"gte\": \"Sonra ya da eşit\",\n    \"lt\": \"Önce\",\n    \"lte\": \"Önce ya da eşit\",\n    \"neq\": \"Eşit değildir\"\n  },\n  \"enums\": {\n    \"eq\": \"Eşittir\",\n    \"neq\": \"Eşit değildir\"\n  },\n  \"number\": {\n    \"eq\": \"Eşittir\",\n    \"gt\": \"Büyüktür\",\n    \"gte\": \"Daha büyük veya eşittir\",\n    \"lt\": \"<PERSON><PERSON>üçük\",\n    \"lte\": \"<PERSON>ha küçük veya eşit\",\n    \"neq\": \"Eşit değildir\"\n  },\n  \"string\": {\n    \"contains\": \"İçeriyor\",\n    \"doesnotcontain\": \"İçermiyor\",\n    \"endswith\": \"İle biter\",\n    \"eq\": \"Eşittir\",\n    \"neq\": \"Eşit değildir\",\n    \"startswith\": \"İle başlar\"\n  }\n});\n}\n\n/* Filter menu operator messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.operators =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.operators,{\n  \"date\": {\n    \"eq\": \"Eşittir\",\n    \"gt\": \"Sonra\",\n    \"gte\": \"Sonra ya da eşit\",\n    \"lt\": \"Önce\",\n    \"lte\": \"Önce ya da eşit\",\n    \"neq\": \"Eşit değildir\"\n  },\n  \"enums\": {\n    \"eq\": \"Eşittir\",\n    \"neq\": \"Eşit değildir\"\n  },\n  \"number\": {\n    \"eq\": \"Eşittir\",\n    \"gt\": \"Büyüktür\",\n    \"gte\": \"Daha büyük veya eşittir\",\n    \"lt\": \"Daha küçük\",\n    \"lte\": \"Daha küçük veya eşit\",\n    \"neq\": \"Eşit değildir\"\n  },\n  \"string\": {\n    \"contains\": \"İçeriyor\",\n    \"doesnotcontain\": \"İçermiyor\",\n    \"endswith\": \"İle biter\",\n    \"eq\": \"Eşittir\",\n    \"neq\": \"Eşit değildir\",\n    \"startswith\": \"İle başlar\"\n  }\n});\n}\n\n/* ColumnMenu messages */\n\nif (kendo.ui.ColumnMenu) {\nkendo.ui.ColumnMenu.prototype.options.messages =\n$.extend(true, kendo.ui.ColumnMenu.prototype.options.messages,{\n  \"columns\": \"Sütunlar\",\n  \"settings\": \"Sütun ayarları\",\n  \"done\": \"Tamam\",\n  \"lock\": \"Kilitle\",\n  \"sortAscending\": \"Artan Sıralama\",\n  \"sortDescending\": \"Azalan Sıralama\",\n  \"unlock\": \"Kilidini Aç\"\n});\n}\n\n/* RecurrenceEditor messages */\n\nif (kendo.ui.RecurrenceEditor) {\nkendo.ui.RecurrenceEditor.prototype.options.messages =\n$.extend(true, kendo.ui.RecurrenceEditor.prototype.options.messages,{\n  \"daily\": {\n    \"interval\": \"Günler\",\n    \"repeatEvery\": \"Her gün tekrarla\"\n  },\n  \"end\": {\n    \"after\": \"Sonra\",\n    \"label\": \"Bitiş\",\n    \"mobileLabel\": \"Bitiş\",\n    \"never\": \"Asla/Hiç\",\n    \"occurrence\": \"Olay\",\n    \"on\": \"Anlık\"\n  },\n  \"frequencies\": {\n    \"daily\": \"Günlük\",\n    \"monthly\": \"Aylık\",\n    \"never\": \"Asla/Hiç\",\n    \"weekly\": \"Haftalık\",\n    \"yearly\": \"Yıllık\"\n  },\n  \"monthly\": {\n    \"day\": \"Gün\",\n    \"interval\": \"Aylar\",\n    \"repeatEvery\": \"Her ay tekrarla\",\n    \"repeatOn\": \"Tekrarla\"\n  },\n  \"offsetPositions\": {\n    \"first\": \"İlk\",\n    \"fourth\": \"Dördüncü\",\n    \"last\": \"Son\",\n    \"second\": \"İkinci\",\n    \"third\": \"Üçüncü\"\n  },\n  \"weekdays\": {\n    \"day\": \"Gün\",\n    \"weekday\": \"İş günü\",\n    \"weekend\": \"Haftasonu\"\n  },\n  \"weekly\": {\n    \"interval\": \"Haftalar\",\n    \"repeatEvery\": \"Her hafta tekrarla\",\n    \"repeatOn\": \"Tekrarla\"\n  },\n  \"yearly\": {\n    \"interval\": \"Yıllar\",\n    \"of\": \"Arasında\",\n    \"repeatEvery\": \"Her Yıl Tekrarla\",\n    \"repeatOn\": \"Tekrarla\"\n  }\n});\n}\n\n/* Editor messages */\n\nif (kendo.ui.Editor) {\nkendo.ui.Editor.prototype.options.messages =\n$.extend(true, kendo.ui.Editor.prototype.options.messages,{\n  \"addColumnLeft\": \"Sola kolon ekle\",\n  \"addColumnRight\": \"Sağa kolon ekle\",\n  \"addRowAbove\": \"Yukarıdaki satır ekle\",\n  \"addRowBelow\": \"Aşağıdaki satır ekle\",\n  \"backColor\": \"Arka plan rengi\",\n  \"bold\": \"Kalın \",\n  \"createLink\": \"Köprü ekleme\",\n  \"createTable\": \"Tablo oluştur\",\n  \"deleteColumn\": \"Sütun silme\",\n  \"deleteFile\": \"Silmek istediğinizden emin misiniz ?\",\n  \"deleteRow\": \"Satır sil\",\n  \"dialogButtonSeparator\": \"ya da\",\n  \"dialogCancel\": \"İptal\",\n  \"dialogInsert\": \"Ekle\",\n  \"dialogUpdate\": \"Güncelle\",\n  \"directoryNotFound\": \"Bu isimde bir dizin bulunamadı.\",\n  \"dropFilesHere\": \"Yüklemek için dosyaları buraya bırakın\",\n  \"emptyFolder\": \"Boş klasör\",\n  \"fontName\": \"Font ailesi Seçiniz\",\n  \"fontNameInherit\": \"Devralınan Karakter\",\n  \"fontSize\": \"Font boyutu Seçiniz\",\n  \"fontSizeInherit\": \"Devralınan Boyut\",\n  \"foreColor\": \"Renk\",\n  \"formatBlock\": \"Biçim\",\n  \"formatting\": \"Biçimlendirme\",\n  \"imageAltText\": \"Alternatif metin\",\n  \"imageWebAddress\": \"Web adresi\",\n  \"indent\": \"Aatırbaşı\",\n  \"insertHtml\": \"HTML ekle\",\n  \"insertImage\": \"Resim ekle\",\n  \"insertOrderedList\": \"Sıralı liste ekleme\",\n  \"insertUnorderedList\": \"Sırasız liste ekleme\",\n  \"invalidFileType\": \"Seçinizilen dosya \\\"{0}\\\" geçerli değil. Desteklenen dosya türleri {1} vardır.\",\n  \"italic\": \"İtalik karakter\",\n  \"justifyCenter\": \"Merkezi metin\",\n  \"justifyFull\": \"Doğrulama\",\n  \"justifyLeft\": \"Metni sola yasla\",\n  \"justifyRight\": \"Metni sağa yasla\",\n  \"linkOpenInNewWindow\": \"Yeni pencerede aç\",\n  \"linkText\": \"Metin\",\n  \"linkToolTip\": \"Araç İpucu\",\n  \"linkWebAddress\": \"Web address\",\n  \"orderBy\": \"Düzenleme ölçütü:\",\n  \"orderByName\": \"İsim\",\n  \"orderBySize\": \"Boyut\",\n  \"outdent\": \"Çıkıntı\",\n  \"overwriteFile\": \"İsimde bir dosya \\\"{0}\\\" zaten dizinde mevcut. Bunu üzerine yazmak istiyor musunuz?\",\n  \"search\": \"Arama\",\n  \"strikethrough\": \"Üstü çizili\",\n  \"styles\": \"Stiller\",\n  \"subscript\": \"İndis\",\n  \"superscript\": \"Üstyazı\",\n  \"underline\": \"Altını çizmek\",\n  \"unlink\": \"Köprüyü Kaldır\",\n  \"uploadFile\": \"Yükle\",\n  \"viewHtml\": \"HTML Görünümü \",\n  \"insertFile\": \"Insert file\"\n});\n}\n\n/* FilterCell messages */\n\nif (kendo.ui.FilterCell) {\nkendo.ui.FilterCell.prototype.options.messages =\n$.extend(true, kendo.ui.FilterCell.prototype.options.messages,{\n  \"clear\": \"Temizle\",\n  \"filter\": \"Filtre\",\n  \"isFalse\": \"Yanlış\",\n  \"isTrue\": \"Doğru \",\n  \"operator\": \"Operatör(işletmen)\"\n});\n}\n\n/* FilterMenu messages */\n\nif (kendo.ui.FilterMenu) {\nkendo.ui.FilterMenu.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMenu.prototype.options.messages,{\n  \"and\": \"Ve\",\n  \"cancel\": \"İptal\",\n  \"clear\": \"Temizle\",\n  \"filter\": \"Filtre\",\n  \"info\": \"bu ile bu arasındaki değerleri göster\",\n  \"isFalse\": \"Yanlış\",\n  \"isTrue\": \"Doğru \",\n  \"operator\": \"Operatör(işletmen)\",\n  \"or\": \"ya da\",\n  \"selectValue\": \"Değer Seçiniz\",\n  \"value\": \"Değer\"\n});\n}\n\n/* FilterMultiCheck messages */\n\nif (kendo.ui.FilterMultiCheck) {\nkendo.ui.FilterMultiCheck.prototype.options.messages =\n$.extend(true, kendo.ui.FilterMultiCheck.prototype.options.messages,{\n  \"search\": \"Arama\"\n});\n}\n\n/* Grid messages */\n\nif (kendo.ui.Grid) {\nkendo.ui.Grid.prototype.options.messages =\n$.extend(true, kendo.ui.Grid.prototype.options.messages,{\n  \"commands\": {\n    \"canceledit\": \"İptal\",\n    \"cancel\": \"Değişiklikleri iptal et\",\n    \"create\": \"Yeni Kayıt Ekle\",\n    \"destroy\": \"Sil\",\n    \"edit\": \"Düzenle\",\n    \"excel\": \"Export to Excel\",\n    \"pdf\": \"Export to PDF\",\n    \"save\": \"Değişiklikleri Kaydet\",\n    \"select\": \"Seçiniz\",\n    \"update\": \"Güncelle\"\n  },\n  \"editable\": {\n    \"cancelDelete\": \"İptal\",\n    \"confirmation\": \"Kayıtları silmek istediğinizden emin misiniz ?\",\n    \"confirmDelete\": \"Sil\"\n  }\n});\n}\n\n/* Groupable messages */\n\nif (kendo.ui.Groupable) {\nkendo.ui.Groupable.prototype.options.messages =\n$.extend(true, kendo.ui.Groupable.prototype.options.messages,{\n  \"empty\": \"Bir sütun başlığını sürükleyin ve bu sütuna göre gruplandırmak için buraya bırakın\"\n});\n}\n\n/* Pager messages */\n\nif (kendo.ui.Pager) {\nkendo.ui.Pager.prototype.options.messages =\n$.extend(true, kendo.ui.Pager.prototype.options.messages,{\n  \"allPages\": \"All\",\n  \"display\": \"{0} - {1} aralığı gösteriliyor. Toplam {2} öğe var\",\n  \"empty\": \"Görüntülenecek öğe yok\",\n  \"first\": \"İlk sayfaya git\",\n  \"itemsPerPage\": \"Sayfa başına ürün\",\n  \"last\": \"Son sayfaya git\",\n  \"morePages\": \"Daha fazla sayfa\",\n  \"next\": \"Bir sonraki sayfaya git\",\n  \"of\": \"{0}\",\n  \"page\": \"Sayfa\",\n  \"previous\": \"Sayfaları İncele\",\n  \"refresh\": \"Güncelle\"\n});\n}\n\n/* Scheduler messages */\n\nif (kendo.ui.Scheduler) {\nkendo.ui.Scheduler.prototype.options.messages =\n$.extend(true, kendo.ui.Scheduler.prototype.options.messages,{\n  \"allDay\": \"Tüm gün\",\n  \"cancel\": \"İptal Et\",\n  \"editable\": {\n    \"confirmation\": \"Bu etkinliği silmek istediğinizden emin misiniz?\"\n  },\n  \"date\": \"Tarih\",\n  \"deleteWindowTitle\": \"Etkinliği sil\",\n  \"destroy\": \"Sil\",\n  \"editor\": {\n    \"allDayEvent\": \"Tüm gün süren olay\",\n    \"description\": \"Tanım\",\n    \"editorTitle\": \"Olay\",\n    \"end\": \"Bitiş\",\n    \"endTimezone\": \"Bitiş saati\",\n    \"noTimezone\": \"Zaman Aralığı belirtilmemiş\",\n    \"repeat\": \"Tekrar\",\n    \"separateTimezones\": \"Ayrı bir başlangıç ve bitiş Zaman aralığı kullan\",\n    \"start\": \"Başlangıç\",\n    \"startTimezone\": \"Başlangıç Saati\",\n    \"timezone\": \"\",\n    \"timezoneEditorButton\": \"Zaman Aralığı\",\n    \"timezoneEditorTitle\": \"Zaman Aralığı\",\n    \"title\": \"Tanım\"\n  },\n  \"event\": \"Olay\",\n  \"recurrenceMessages\": {\n    \"deleteRecurring\": \"Sadece bu olayı ya da bütün dizini mi silmek istiyor musunuz?\",\n    \"deleteWindowOccurrence\": \"Geçerli yinelemeyi Sil\",\n    \"deleteWindowSeries\": \"Seriyi Sil\",\n    \"deleteWindowTitle\": \"Tekrarlanan Öğeyi Sil\",\n    \"editRecurring\": \"Sadece bu olay oluşumunu veya tüm dizini düzenlemek istiyor musunuz?\",\n    \"editWindowOccurrence\": \"Geçerli Olayı Düzenle\",\n    \"editWindowSeries\": \"Seriyi düzenle\",\n    \"editWindowTitle\": \"Tekrarlanan Öğeyi Düzenle\"\n  },\n  \"save\": \"Kaydet\",\n  \"showFullDay\": \"Tüm gün göster\",\n  \"showWorkDay\": \"İş saatlerini göster\",\n  \"time\": \"Zaman\",\n  \"today\": \"Bugün\",\n  \"views\": {\n    \"agenda\": \"Gündem\",\n    \"day\": \"Gün\",\n    \"month\": \"Ay\",\n    \"week\": \"Hafta\",\n    \"workWeek\": \"Çalışma Haftası\"\n  }\n});\n}\n\n/* Upload messages */\n\nif (kendo.ui.Upload) {\nkendo.ui.Upload.prototype.options.localization =\n$.extend(true, kendo.ui.Upload.prototype.options.localization,{\n  \"cancel\": \"İptal Et\",\n  \"dropFilesHere\": \"Yüklemek için dosyaları buraya bırakın\",\n  \"headerStatusUploaded\": \"Tamamlandı\",\n  \"headerStatusUploading\": \"Yükleniyor\",\n  \"remove\": \"Kaldır\",\n  \"retry\": \"Tekrar Dene\",\n  \"select\": \"Seçiniz\",\n  \"statusFailed\": \"Başarız Oldu\",\n  \"statusUploaded\": \"Yüklendi\",\n  \"statusUploading\": \"Yükleniyor\",\n  \"uploadSelectedFiles\": \"seçilen dosyaları Yükle\"\n});\n}\n\n/* Dialog */\n\nif (kendo.ui.Dialog) {\nkendo.ui.Dialog.prototype.options.messages =\n$.extend(true, kendo.ui.Dialog.prototype.options.localization, {\n  \"close\": \"Kapatmak\"\n});\n}\n\n/* Alert */\n\nif (kendo.ui.Alert) {\nkendo.ui.Alert.prototype.options.messages =\n$.extend(true, kendo.ui.Alert.prototype.options.localization, {\n  \"okText\": \"Tamam\"\n});\n}\n\n/* Confirm */\n\nif (kendo.ui.Confirm) {\nkendo.ui.Confirm.prototype.options.messages =\n$.extend(true, kendo.ui.Confirm.prototype.options.localization, {\n  \"okText\": \"Tamam\",\n  \"cancel\": \"İptal\"\n});\n}\n\n/* Prompt */\nif (kendo.ui.Prompt) {\nkendo.ui.Prompt.prototype.options.messages =\n$.extend(true, kendo.ui.Prompt.prototype.options.localization, {\n  \"okText\": \"Tamam\",\n  \"cancel\": \"İptal\"\n});\n}\n\n})(window.kendo.jQuery);\n}));"]}