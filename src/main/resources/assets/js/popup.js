function openPopup(detailUrl) {
    var myWindow = $("#window");

    myWindow.kendoWindow({
        width: "1000px",
        height: "666px",
        content: detailUrl,
        iframe: true,
        modal: true,
        resizable: false,
        draggable: false
    }).data("kendoWindow").center().open();

    $('.k-overlay').on('click', function() {
        myWindow.data('kendoWindow').close();
    });

    myWindow.data('kendoWindow').bind('close', function(e) {
        myWindow.data('kendoWindow').content("");
        if (typeof $('#issueScheduler').data('kendoScheduler') != 'undefined' && typeof $('#assistantScheduler').data('kendoScheduler') != 'undefined') {
            $('#issueScheduler').data('kendoScheduler').dataSource.read()
            $('#assistantScheduler').data('kendoScheduler').dataSource.read()
            $('#issuelist').data('kendoListView').dataSource.read()
        }

        if (typeof $('#full-issue-list').data('kendoGrid') != 'undefined') {
            $('#full-issue-list').data('kendoGrid').dataSource.read()
       }
    });
}