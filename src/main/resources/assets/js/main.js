$(document).ready(function() {

    window.selectedDate = null

    const element = $('body').find('#issueScheduler')
    var $ele = $(element)
    if ($ele.length > 0) {
        var config = configuration[$ele.data('config')]
        let issueScheduler = null
        if (typeof config != "undefined") {
            issueScheduler = new Scheduler($ele, config)
        } else {
            throw "Scheduler config not found."
        }

        $('#issueScheduler .k-scheduler-refresh').click(function() {
            $('#issuelist').data('kendoListView').dataSource.read()
        })
    }

    const elementAssist = $('body').find('#assistantScheduler')
    var $ele = $(elementAssist)
    if ($ele.length > 0) {
        var config = configuration[$ele.data('config')]
        let assistantScheduler = null
        if (typeof config != "undefined") {
            assistantScheduler = new Scheduler($ele, config)
        } else {
            throw "Scheduler config not found."
        }
    }

    const elementIssueList = $('body').find('#issuelist')
    var $ele = $(elementIssueList)
    if ($ele.length > 0) {
        var $pager = $($ele.data('pager'))
        var config = configuration[$ele.data('config')]
        var listName = "TECHNICIAN"
        let issueList = null
        if (typeof config != "undefined") {
            issueList = new List($ele, $pager, config, listName)
        } else {
            throw "Issuelist config not found."
        }
    }

    const elementAssistantList = $('body').find('#assistantlist')
    var $ele = $(elementAssistantList)
    if ($ele.length > 0) {
        var $pager = $($ele.data('pager'))
        var config = configuration[$ele.data('config')]
        var listName = "ASSISTANT"
        let assistantList = null
        if (typeof config != "undefined") {
            assistantList = new List($ele, $pager, config, listName)
        } else {
            throw "Assistantlist config not found."
        }
    }

    $('body').find('.absenceList').each(function(i, element) {
        var $ele = $(element)
        var $pager = $($ele.data('pager'))
        var config = configuration[$ele.data('config')]
        var listName = "ABSENCE"
        let absenceList = null
        if (typeof config != "undefined") {
            absenceList = new List($ele, $pager, config, listName)
        } else {
            throw "AssistantAbsenceList config not found."
        }
    })

    $('body').find('.gridlist').each(function(i, element) {
        var $ele = $(element)
        var config = configuration[$ele.data('config')]
        if (typeof config != "undefined") {
            Grid.__constructor($ele, config)
        } else {
            throw "Gridlist config not found."
        }
    })

})
