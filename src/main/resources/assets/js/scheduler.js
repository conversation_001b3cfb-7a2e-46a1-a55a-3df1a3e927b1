function Scheduler($element, config) {
    this.scheduler = {
        $element: null,

        config: null,

        resourceDataSource: null,

        dataSource: null,

        scheduler: null,

        __constructor: function($element, config) {
            kendo.culture("de-AT");
            this.$element = $element
            this.config = config
            var date = $('body').data('date');
            if (date === undefined){
               window.selectedDate = moment().startOf('day').tz(dateTime.ZONE).toISOString()
            } else {
                window.selectedDate = new Date(date).toISOString()
            }
            this.resourceDataSource = this.setupResourceDataSource()
            this.dataSource = this.setupDataSource()
            this.scheduler = this.setupScheduler()
            this.setupSchedulerEvents()
            this.setupLogger()
        },

        setupResourceDataSource: function() {
            return new kendo.data.SchedulerDataSource({
                transport: {
                    read: {
                        url: this.config.endpoint.RESOURCES,
                        dataType: "json"
                    }
                },
                schema: {
                    model: {
                        fields: {
                            text: { from: "displayName" },
                            value: { from: "id" }
                        }
                    }
                }
            });
        },

        setupDataSource: function() {
            return new kendo.data.SchedulerDataSource({
                serverPaging: true,
                transport: {
                    read: {
                        url: this.config.endpoint.READ,
                        dataType: "json",
                        data: {
                            from: function() {
                                return window.selectedDate
                            }.bind(this),
                            mode: this.config.view.mode
                        }
                    },
                    update: {
                        url: this.config.endpoint.UPDATE,
                        contentType: "application/json",
                        dataType: "json",
                        type: "POST"
                    },
                    create: {
                        url: this.config.endpoint.CREATE,
                        contentType: "application/json",
                        dataType: "json",
                        type: "POST"
                    },
                    destroy: {
                        url: this.config.endpoint.DELETE,
                        contentType: "application/json",
                        dataType: "json",
                        type: "POST"
                    },
                    parameterMap: function (options, operation) {
                        if (operation !== "read") {
                            options.start = new Date(options.start).toISOString()
                            options.end = new Date(options.end).toISOString()
                            return JSON.stringify(options)
                        }
                        return options
                    }
                },
                schema: {
                    timezone: dateTime.ZONE
                },
                requestEnd: function(e) {
                    if(typeof e.type == "undefined") {
                        $('#issuelist').data('kendoListView').dataSource.read()
                        $('#issueScheduler').data('kendoScheduler').dataSource.read()
                        $('#assistantScheduler').data('kendoScheduler').dataSource.read()
                    }
                }
            });
        },

        setupScheduler: function() {
            return this.$element.kendoScheduler({
                date: window.selectedDate,
                workDayStart: workHours.START,
                workDayEnd: workHours.END,
                eventTemplate: $(config.eventTemplate).html(),
                editable: this.config.editable,
                views: this.config.view.views,
                dataBound: function() {
                    $('.k-event-actions > .k-icon.k-i-arrow-60-left').parent().next('.neb-event-with-secondary-button').css('padding-left', '16px')
                    $('.k-event-actions > .k-icon.k-i-arrow-60-left').parent().next('.neb-event-without-secondary-button').css('padding-left', '24px')

                    $(".assignment-detail-action-icon").bind("click", function(e) {
                        var detailUrl = e.target.getAttribute('assignmentDetailUrl')
                        openPopup(detailUrl)
                    });
                    this.createDropArea()
                }.bind(this),
                dataSource: this.dataSource,
                group: {
                    resources: [this.config.resource.title],
                    orientation: "vertical"
                },
                resources: [{
                    field: this.config.resource.foreignField,
                    name: this.config.resource.title,
                    dataSource: this.resourceDataSource,
                    title: this.config.resource.title
                }]
            }).data("kendoScheduler");
        },

        setupSchedulerEvents: function() {
            this.scheduler.bind("navigate", function(e) {
                if (e.action == "changeDate" || e.action == "next" || e.action == "previous" || e.action == "today") {

                    $('.scheduler').each(function(i, element) {
                        $ele = $(element)
                        window.selectedDate = moment(e.date).startOf('day').tz(dateTime.ZONE).toISOString()
                        var scheduler = $ele.data('kendoScheduler')
                        scheduler.date(e.date)
                    })
                }
            }.bind(this));

            this.scheduler.bind("edit", function(e) {
                e.preventDefault()
                var detailUrl = e.event.detailUrl
                if (typeof detailUrl != "undefined" && detailUrl != "") {
                    openPopup(detailUrl)
                }

                return true
            });
        },

        setupLogger() {
            this.dataSource.bind('requestStart', function(e) {
                console.log("Request: start", 'Type: ' + e.type)
            })
            this.dataSource.bind('requestEnd', function(e) {
                var response = e.response
                var type = e.type
                console.log('Request: end', 'Type: ' + type)

                if (type == "create" && response.eventType == "ABSENCE" && typeof response.detailUrl != "undefined") {
                    openPopup(response.detailUrl)
                }
            })
        },

        createDropArea() {
            this.scheduler.view().content.kendoDropTargetArea({
                filter: ".k-scheduler-table td, .k-event",
                drop: function(e){
                    var offset = $(e.dropTarget).offset()
                    $(e.dropTarget[0]).removeClass('dropTarget')

                    var slot = this.scheduler.slotByPosition(offset.left, offset.top)
                    if(window.dropDataItem && slot) {
                        if(window.dropDataItem.listName == this.scheduler.view().title || window.dropDataItem.listName == "ABSENCE") {
                            var userId = this.resourceDataSource.data()[slot.groupIndex].value
                            var title = ""
                            var description = ""
                            if(window.dropDataItem.listName == "TECHNICIAN") {
                                title = window.dropDataItem.externalId
                                description = window.dropDataItem
                            } else {
                                title = window.dropDataItem.displayName
                            }

                            var newEvent = {
                                title: title,
                                end: new Date(slot.startDate.getTime() + (3600000*3)),
                                start: slot.startDate,
                                isAllDay: slot.isDaySlot,
                                description: description,
                                userId: userId,
                                plannedResourceId: window.dropDataItem.id,
                                gradientStartColor: "#D3D3D3",
                                gradientEndColor: "#000000",
                                eventType: window.dropDataItem.eventType,
                                detailUrl: "",
                                secondaryDetailUrl: "",
                                recurringDetailUrl: "",
                                isRecurringIssue: false,
                            };

                            this.dataSource.add(newEvent)
                            this.dataSource.sync().then(function() {
                                $(config.listId).data('kendoListView').dataSource.read()
                            })

                            window.dropDataItem = null
                        }
                    }
                }.bind(this),
                dragenter: function(e) {
                    $(e.dropTarget[0]).addClass('dropTarget')
                }.bind(this),
                dragleave: function(e) {
                    $(e.dropTarget[0]).removeClass('dropTarget')
                }.bind(this)
            });
        }

    }
    this.scheduler.__constructor($element, config)
}
