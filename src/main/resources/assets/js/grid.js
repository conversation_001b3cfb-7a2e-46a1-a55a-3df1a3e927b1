var Grid = {

    $element: null,

    $pager: null,

    config: null,

    dataSource: null,

    __constructor: function($element, config) {
        'use strict';
        this.$element = $element;
        this.config = config;
        this.dataSource = this.setupDataSource();
        this.setupGridView();
    },
    
    setupDataSource: function() {
        'use strict';
        return new kendo.data.DataSource({
            serverPaging: this.config.serverPaging,
            serverSorting: this.config.serverSorting,
            serverFiltering: this.config.serverFiltering,
            transport: {
                read: {
                    url: this.config.endpoint.READ,
                    dataType: 'json',
                    data: {
                        
                    }
                }
            },
            sort: this.config.sort,
            schema: this.config.schema,
            pageSize: 20
        });
    },

    setupGridView: function() {
        'use strict';
        this.$element.kendoGrid({
            selectable: this.config.selectable,
            sortable: this.config.sortable,
            dataSource: this.dataSource,
            pageable: this.config.pageable,
            filterable: this.config.filterable,
            columns: this.config.columns,
            dataBound: function() {
                $('.text-popover-more').on('click', function() {
                    $('.text-popover-window.active').not($(this).next()).removeClass('active');
                    $(this).next().toggleClass('active');
                });
            }
        });
    }
};