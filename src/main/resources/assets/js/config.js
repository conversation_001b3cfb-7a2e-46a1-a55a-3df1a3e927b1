var dateTime = Object.freeze({
    ZONE: 'Europe/Vienna',
});

var workHours = Object.freeze({
    START: new Date('1970/1/1 07:00 AM'),
    END: new Date('1970/1/1 05:00 PM')
});

var calendarMode = Object.freeze({
    DAILY: 'DAILY',
    WEEKLY: 'WEEKLY'
});

var calendarView = Object.freeze({
    TECHNICIAN_DAILY: {
        mode: calendarMode.DAILY,
        views: [{
            type: 'timeline',
            selected: true,
            majorTick: 60,
            minorTickCount: 4,
            columnWidth: 25,
            showWorkHours: true,
            majorTimeHeaderTemplate: kendo.template('#=kendo.toString(date, "HH:mm")#'),
            title: "TECHNICIAN"
        }]
    },
    ASSISTANT_DAILY: {
        mode: calendarMode.DAILY,
        views: [{
            type: 'timeline',
            selected: true,
            majorTick: 60,
            minorTickCount: 4,
            columnWidth: 25,
            showWorkHours: true,
            majorTimeHeaderTemplate: kendo.template('#=kendo.toString(date, "HH:mm")#'),
            title: "ASSISTANT"
        }]
    }
});

var eventStatus = Object.freeze({
    NEW: 'NEW',
    REOPENED: 'REOPENED',
    PLANNED: 'PLANNED',
    ACCEPTED: 'ACCEPTED',
    NOT_STARTED: 'NOT_STARTED',
    DONE_TECHNICIAN: 'DONE_TECHNICIAN',
    CLOSED: 'CLOSED'
});

var baseURL = '/admin/api/v1';

var endpoint = Object.freeze({
    TECHNICIAN: {
        RESOURCES: baseURL + '/technicians',
        READ: baseURL + '/technicians/assignments',
        CREATE: baseURL + '/technicians/assignments/create',
        UPDATE: baseURL + '/technicians/assignments/update',
        DELETE: baseURL + '/technicians/assignments/delete'
    },
    OPEN_ISSUES: {
        READ: baseURL + '/issues/open'
    },
    ASSISTANT: {
        RESOURCES: baseURL + '/assistants',
        READ: baseURL + '/assistants/assignments',
        CREATE: baseURL + '/assistants/assignments/create',
        UPDATE: baseURL + '/assistants/assignments/update',
        DELETE: baseURL + '/assistants/assignments/delete'
    },
    ACTIVE_TECHNICIANS: {
        READ: baseURL + '/technicians'
    },
    ALL_ISSUES: {
        READ: baseURL + '/issues/all'
    },
    USERS: {
        READ: baseURL + '/users'
    }
});


var textBoxViaAutoComplete = function(args) {
    'use strict';
    args.element.kendoAutoComplete({
        dataSource: [],
        noDataTemplate: ''
    });
};

var configuration = Object.freeze({

    TECHNICIAN_DAILY: {
        endpoint: endpoint.TECHNICIAN,
        view: calendarView.TECHNICIAN_DAILY,
        resource: {
            title: 'Mitarbeiter',
            foreignField: 'userId'
        },
        editable: {
            create: false,
            destroy: false,
            resize: true
        },
        listId: '#issuelist',
        eventTemplate: '#event-template'
    },

    OPEN_ISSUES_LIST: {
        endpoint: endpoint.OPEN_ISSUES,
        listTemplate: "#list-template",
        listFilter: "#neb-issue",
        searchId: '#issuelistsearch',
        filters: function(value) {
            return [
               { field: "address", operator: "contains", value: value },
               { field: "contactPerson", operator: "contains", value: value },
               { field: "description", operator: "contains", value: value },
               { field: "externalId", operator: "contains", value: value },
               { field: "note", operator: "contains", value: value },
               { field: "status", operator: "contains", value: value },
               { field: "suggestedDate", operator: "contains", value: value },
               { field: "eventType", operator: "contains", value: value }
            ]
        },
        hint: function (e) {
            var dataItem = this.element.data("kendoListView").dataItem(e)
            var tooltipHtml = "<div class='k-event neb-event' id='dragTooltip'>" +
                            "<div class='neb-event-wrapper'>" + dataItem.externalId + "</div>"
                        "</div>";
            return $(tooltipHtml).css("width", 300);
        },
        dataBound: function() {
            this.element.find('.neb-listitem').dblclick(function(e){
               var id = $(e.currentTarget).data("id")
               openPopup('/issue/'+id)
            }.bind(this))
        }
    },

    ASSISTANT_DAILY: {
        endpoint: endpoint.ASSISTANT,
        view: calendarView.ASSISTANT_DAILY,
        resource: {
            title: 'Helfer',
            foreignField: 'userId'
        },
        editable: {
            create: false,
            destroy: true,
            resize: true
        },
        listId: '#assistantlist',
        eventTemplate: '#technician-event-template'
    },

    ACTIVE_TECHNICIAN_LIST: {
        endpoint: endpoint.ACTIVE_TECHNICIANS,
        listTemplate: "#small-list-template",
        listFilter: "#neb-item",
        searchId: '#technicianlistsearch',
        filters: function(value) {
           return [
                { field: "displayName", operator: "contains", value: value },
                { field: "eventType", operator: "contains", value: value }
           ]
        },
        hint: function (e) {
                  var dataItem = this.element.data("kendoListView").dataItem(e)
                  var tooltipHtml = "<div class='k-event neb-event' id='dragTooltip'>" +
                                  "<div class='neb-event-wrapper'>" + dataItem.displayName + "</div>"
                              "</div>";
                  return $(tooltipHtml).css("width", 300);
              }
    },

    ABSENCE_LIST: {
        data: [
            { eventType: "ABSENCE", displayName: translation.absence }
        ],
        listTemplate: "#absence-template",
        listFilter: "#neb-absence",
        hint: function (e) {
                  var dataItem = this.element.data("kendoListView").dataItem(e)
                  var tooltipHtml = "<div class='k-event neb-event' id='dragTooltip'>" +
                                  "<div class='neb-event-wrapper'>" + dataItem.displayName + "</div>"
                              "</div>";
                  return $(tooltipHtml).css("width", 300);
              }
    },

    ISSUES_LIST: {
        endpoint: endpoint.ALL_ISSUES,
        detailUrl: '/issue/',
        serverPaging: true,
        serverSorting: false,
        serverFiltering: true,
        selectable: false,
        sortable: false,
        pageable: {
            refresh: false,
            buttonCount: 5,
            pageSizes: [10, 20, 50]
        },
        sort: { field: 'externalId', dir: 'desc' },
        filterable: {
            mode: 'row',
        },
        dataBound: function () {
            'use strict';
            this.element.find('.k-filtercell .k-autocomplete .k-clear-value').remove();
        },
        columns: [
            {
                field: 'externalId',
                title: translation.externalId,
                template: '<div onclick="openPopup(`/issue/#: id#`)"><b style="cursor: pointer;">#: externalId # </b></div>',
                width: '130px',
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: 115,
                        template: textBoxViaAutoComplete,
                    }
                }
            },
            {
                field: 'address',
                title: translation.address,
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: '100%',
                        template: textBoxViaAutoComplete,
                    }
                }
            },
            {
                field: 'contactPerson',
                title: translation.contactPerson,
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: '100%',
                        template: textBoxViaAutoComplete,
                    }
                }
            },
            {
                field: 'description',
                title: translation.description,
                filterable: {
                    cell: {
                        showOperators: false,
                        template: textBoxViaAutoComplete,
                        inputWidth: '100%',
                    }
                }
            },
            {
                field: 'status',
                title: translation.status,
                template: '<span class="badge" style="background: linear-gradient(to right, #: gradientStartColor #, #: gradientEndColor #);">#: status # </span>',
                width: '230px',
                filterable: {
                    cell: {
                        template: function (args) {
                            if (typeof window.issueStatusDataSource === 'undefined') {
                                window.issueStatusDataSource = [];
                            }
                            args.element.kendoDropDownList({
                                dataSource: window.issueStatusDataSource,
                                dataTextField: 'name',
                                dataValueField: 'key',
                                valuePrimitive: true
                            });
                        },
                        inputWidth: 170,
                        showOperators: false
                    }
                }
            },
            {
                field: 'latestAssignmentDateStart',
                title: translation.latestAssignmentDateStart,
                width: '200px',
                template: function (dataItem) {
                    'use strict';
                    var html = [];
                    html.push('<div class="text-popover-more">');
                    html.push('<span>' + dataItem.latestAssignmentDateStart + '</span>');
                    if (dataItem.assignmentDates.length > 1) {
                        html.push('<span class="text-popover"><img src="/assets/images/<EMAIL>" width="4" height="11">');
                    }
                    html.push('</div>');

                    if (dataItem.assignmentDates.length > 1) {
                        html.push('<div class="text-popover text-popover-window">');
                        html.push('<div class="text-popover-table-wrapper">');
                        html.push('<table>');
                        $.each(dataItem.assignmentDates, function (i, data) {
                            html.push('<tr><td class="start">' + data.start + '</td><td class="end">' + data.end + '</td></tr>');
                        });
                        html.push('</table>');
                        html.push('</div>');
                        html.push('</div>');
                    }
                    return html.join('');
                },
                filterable: false,
            },
        ],
        schema: {
            total: 'total',
            data: 'data'
        }
    },

    USERS_LIST: {
        endpoint: endpoint.USERS,
        serverPaging: false,
        serverSorting: false,
        serverFiltering: false,
        selectable: false,
        sortable: {
            initialDirection: 'asc'
        },
        sort: { field: 'username', dir: 'asc' },
        pageable: {
            refresh: false,
            buttonCount: 5,
            pageSizes: [10, 20, 50]
        },
        filterable: {
            mode: 'row',
            operators: {
                string: {
                    'contains': '~'
                }
            }
        },
        columns: [
            {
                field: 'username',
                title: translation.username,
                template: '<a href="/admin/users/#: id#"><b>#: username#</b></a>',
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: '100%',
                        template: textBoxViaAutoComplete,
                    }
                }
            }, {
                field: 'firstName',
                title: translation.firstName,
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: '100%',
                        template: textBoxViaAutoComplete,
                    }
                }
            }, {
                field: 'lastName',
                title: translation.lastName,
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: '100%',
                        template: textBoxViaAutoComplete,
                    }
                }
            }, {
                field: 'phoneNumber',
                title: translation.phoneNumber,
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: '100%',
                        template: textBoxViaAutoComplete,
                    }
                }
            }, {
                field: 'status',
                title: translation.disabled,
                template: function (dataItem) {
                    'use strict';
                    if (dataItem.status === 'DISABLED') {
                        return '<span class="status inactive"> ' + translation.inactive + '</span>';
                    } else {
                        return '<span class="status active">' + translation.active + '</span>';
                    }
                },
                filterable: {
                    cell: {
                        template: function (args) {
                            'use strict';
                            args.element.kendoDropDownList({
                                dataSource: [
                                    {
                                        'key': 'ABLED',
                                        'name': translation.activeAndInactive
                                    },
                                    {
                                        'key': 'ENABLED',
                                        'name': translation.active
                                    },
                                    {
                                        'key': 'DISABLED',
                                        'name': translation.inactive
                                    }
                                ],
                                dataTextField: 'name',
                                dataValueField: 'key',
                                valuePrimitive: true
                            });
                        },
                        inputWidth: '100%',
                        showOperators: false
                    }
                }
            }, {
                field: 'role',
                title: translation.role,
                filterable: {
                    cell: {
                        showOperators: false,
                        inputWidth: '100%',
                        template: textBoxViaAutoComplete,
                    }
                }
            },
        ],
        schema: {}
    }

});