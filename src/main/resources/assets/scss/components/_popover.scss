.gridlist td:last-child {
    overflow: visible;
    position: relative;
}

.text-popover-more {
    cursor: pointer;
    img {
        margin-left: 10px;
    }
}

.text-popover {
    position: relative;
    display: inline-block;

    &.text-popover-window  {
        position: absolute;
        top: 43px;
        left: -151px;
        width: 320px;
        background-color: white;
        border-radius: 10px;
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -o-transform: scale(0);
        -ms-transform: scale(0);
        transform: scale(0);
        -webkit-transform-origin: 90% 0%;
        -moz-transform-origin: 90% 0%;
        -o-transform-origin: 90% 0%;
        -ms-transform-origin: 90% 0%;
        transform-origin: 90% 0%;
        -webkit-transition: all 0.2s ease-out;
        -moz-transition: all 0.2s ease-out;
        -o-transition: all 0.2s ease-out;
        transition: all 0.2s ease-out;
        z-index: 999;
        -webkit-box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.2);
        -moz-box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.2);
        box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.2);
        text-transform: uppercase;
        color: #787878;
        .text-popover-table-wrapper {
            //max-height: 162px;
            //overflow: scroll;
            table {
                padding: 20px 0px;
                td {
                    padding: 4px 0px 4px 13px;
                    color: #505A64;
                    font-weight: 600;
                    &.start {
                        padding: 4px 0px 4px 26px;
                    }
                }
            }
        }
        &.active {
            -webkit-transform: scale(1);
            -moz-transform: scale(1);
            -o-transform: scale(1);
            -ms-transform: scale(1);
            transform: scale(1);
        }
        &::before {
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 10px 10px 10px;
            border-color: transparent transparent #ffffff;
            content: "";
            position: absolute;
            top: -10px;
            right: 17px;
        
        }
    }
}
  