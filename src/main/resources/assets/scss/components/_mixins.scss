@import "variables";

@mixin gradient-background() {
    background: $primary;
    background: -moz-linear-gradient(left, rgba(167,195,255,1) 0%, rgba(38,107,204,1) 100%);
    background: -webkit-linear-gradient(left, rgba(167,195,255,1) 0%,rgba(38,107,204,1) 100%);
    background: linear-gradient(to right, rgba(167,195,255,1) 0%,rgba(38,107,204,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a7c3ff', endColorstr='#266bcc',GradientType=1 );
}

@mixin background-gradient($start, $end) {
    background: $end;
    background: -moz-linear-gradient(left, $start 0%, $end 100%); 
    background: -webkit-linear-gradient(left, $start 0%, $end 100%); 
    background: linear-gradient(to right, $start 0%, $end 100%); 
}

@mixin reset() {
    list-style:none;
    margin: 0;
    padding:0;
    text-decoration: none;
}