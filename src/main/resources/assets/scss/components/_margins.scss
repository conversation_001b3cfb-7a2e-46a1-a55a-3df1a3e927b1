$max: 101;
$i: 0; 
$step: 5;
@while $i < $max { 
    $i: $i + $step; 
    .margin-#{$i} { margin: 1px * $i; }
}

$i: 0; 
@while $i < $max { 
    $i: $i + $step; 
    .margin-top-#{$i} { margin-top: 1px * $i; }
}

$i: 0; 
@while $i < $max { 
    $i: $i + $step; 
    .margin-bottom-#{$i} { margin-bottom: 1px * $i; }
}

$i: 0;
@while $i < $max {
    $i: $i + $step;
    .margin-left-#{$i} { margin-left: 1px * $i; }
}

$i: 0;
@while $i < $max {
    $i: $i + $step;
    .margin-right-#{$i} { margin-right: 1px * $i; }
}

@media (max-width: 1140px) {
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .margin-#{$i}\@tablet { margin: 1px * $i; }
    }
    
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .margin-top-#{$i}\@tablet { margin-top: 1px * $i; }
    }
    
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .margin-bottom-#{$i}\@tablet { margin-bottom: 1px * $i; }
    }
    
    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .margin-left-#{$i}\@tablet { margin-left: 1px * $i; }
    }
    
    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .margin-right-#{$i}\@tablet { margin-right: 1px * $i; }
    }
}

@media (max-width: 720px) {
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .margin-#{$i}\@mobile { margin: 1px * $i; }
    }
    
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .margin-top-#{$i}\@mobile { margin-top: 1px * $i; }
    }
    
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .margin-bottom-#{$i}\@mobile { margin-bottom: 1px * $i; }
    }
    
    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .margin-left-#{$i}\@mobile { margin-left: 1px * $i; }
    }
    
    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .margin-right-#{$i}\@mobile { margin-right: 1px * $i; }
    }
}


.margin-none {
    margin: 0 !important;
}

.margin-bottom-none {
    margin-bottom: 0 !important;
}

.margin-top-none {
    margin-top: 0 !important;
}

.margin-right-none {
     margin-right: 0 !important;
 }

.margin-left-none {
    margin-left: 0 !important;
}