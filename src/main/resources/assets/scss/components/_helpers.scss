@import "variables";

.container {
    max-width: 1200px;
    margin: 0 auto;
 }

 .container-fluid {
    max-width: 100%;
    margin-left: 150px;
    margin-right: 80px;
 }
 
 .background-white {
     background: white;
 }

 .box-shadow {
    box-shadow: 0 0 25px rgba(0,0,0,0.301);
 }

 .border-radius {
     border-radius: 10px;
 }
 
.block-center {
    margin: 0 auto;
    float: none;
    display: block;
}

.block-inline {
    display: inline-block
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-italic {
    font-style: italic;
}

.font-regular {
    font-weight: normal;
    font-size: 16px;
    letter-spacing: 0.5;
    line-height: 18px;
}

.font-14 {
    font-size: 14px;
}

.img-responsive {
    max-width: 100%;
}

.img-round {
    border-radius: 50%;
    overflow: hidden;
    display: inline-block !important;
}

.max-height {
    height: 100%;
}

.max-width {
    width: 100%;
}

.vertical-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pull-right {
    float: right;
}

.reset {
    margin: 0;
    padding: 0;
    list-style: none;
    li {
        margin: 0;
        padding: 0;
    }
}

.clearfix {
    clear: both;
    float: none;
}

.badge {
    padding-left: 20px;
    padding-right: 20px;
    border-radius: 5px;
    border: 1px solid;
    line-height: 2;
    color: white;
    display: inline-block;
    font-weight: 600;
}

hr {
    border: 0.5px solid #dddddd;
}

.k-state-hover {
    box-shadow: none;
    background-color: $primary;
    color: white;
}
.k-list-scroller li:hover {
    box-shadow: none;
    background-color: $primary;
    color: white;
}

.k-autocomplete ~ button {
    display: none;
}

.k-dropdown ~ button {
    display: none;
}

.k-filtercell {
    padding-bottom: 2px;
}

.k-combobox {
    .k-input {
        padding-top: 5px;
        padding-bottom: 5px;
        padding-left: 14px;
        box-shadow: none !important;
    }

    .k-dropdown-wrap {
        border: 1px solid rgba(0,0,0,0.2);
    }

    .k-i-arrow-60-down {
        margin-top: 4px;
    }
}

@supports (-moz-appearance:none) {
    .k-combobox {
        line-height: 35px;
    }

    .k-scheduler {
        .k-event-actions {
            .k-i-arrow-60-left {
                margin-top: -3px;
            }
        }
    }
}

body .gridlist .k-grid-header {
    padding-right: 0 !important;
}

body .gridlist .k-filtercell > .k-operator-hidden {
    padding-right: 0;
}

.position-fixed {
    position: fixed;
    bottom: 9px;
    left: 0;
    right: 0;
    border-top: 1px solid rgb(221, 221, 221);
    z-index: 1000 !important;
}

.position-fixed-iframe {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid rgb(221, 221, 221);
    z-index: 1000 !important;
}

.full-width {
    width: 100%;
}

.cursor-pointer {
    cursor: pointer;
}
