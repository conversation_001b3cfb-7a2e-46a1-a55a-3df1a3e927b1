@import "variables";

#navigation {
    background: white;
    font-size: 12px;
    color: $trout;
    box-shadow: 0px 2px 4px #B0B0B0;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999999;
    position: fixed;
    &.relative {
        position: relative;
        background: $primary;
    }
    .logo {
        transform: translateY(-2px);
    }
    ul {
        @include reset;
        display: table;
        height: 64px;
        li {
            @include reset;
            position: relative;
            display: table-cell;
            vertical-align: middle;
            a {
                @include reset;
                font-size: 12px;
                color: $silver-chalice;
                font-weight: 400;
                margin-right: 22px;
                display: block;
                letter-spacing: 2px;
                &:hover {
                    color: $primary;
                }
            }
        }
    }
}