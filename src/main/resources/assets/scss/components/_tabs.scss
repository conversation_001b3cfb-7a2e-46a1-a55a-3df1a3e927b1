@import "variables";

.tabs {
  list-style: none;
  margin: 0;
  padding: 0;
  font-size: 16px;
  margin-top: 20px;

  li {
    display: inline-block;
    padding: 0px;
    margin-right: 20px;
    font-style: italic;

    a {
      opacity: 0.7;
      font-weight: 600;

      &.active {
        opacity: 1;
      }
    }
  }
}

.tab {
  display: none;

  &.active {
    display: block;
  }
}

.issue-detail .tab.active {
  height: 498px;
  overflow-y: scroll;
}

.issue-detail .tab-iframe.active {
  height: 566px;
  overflow-y: hidden;
}

.issue-detail .full-height {
  height: 100%;
}

.issue-detail .header {
  white-space: nowrap;
}

.x-hidden {
  overflow-x: hidden;
}

.y-hidden {
  overflow-y: hidden;
}

.flexible-container {
  display: flex;
  align-items: center;
}

#issuescan {

  display: flex;
  flex-direction: row;

  padding-left: 4px;

  .scan-active {
    background-image: url("../images/<EMAIL>");
  }

  .scan-manual-active {
    background-image: url("../images/<EMAIL>");
  }

  a, span {
    width: 24px;
    height: 24px;
    display: block;
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.context-menu {
  position: absolute;
  text-align: center;
  background: white;
  border: 1px solid $silver-chalice;
  border-radius: 6px;
  overflow: hidden;

  a {
    display: block;
  }

  ul {
    padding: 0;
    margin: 0;
    min-width: 150px;
    list-style: none;

    li {
      cursor: pointer;
      padding: 7px 0;
      font-weight: 600;

      &:hover {
        background: $grey94;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

.red-text {
  color: $danger;
}

#scan-upload-button {
  img {
    height: 24px;
    width: 24px;
  }
}

.grey-background {
  background-color: #FAFAFA;
}

.tab5-sticky-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border-bottom: 1px solid #ddd;
  margin-top: 98px;
}

.tab5-content {
  margin-top: 135px;
}
