.grid_header {
    font-weight: normal;
    color: #787878;
    font-size: 14px;
}

.grid_cell {
    font-size: 14px;
    padding: 14px 10px;
    line-height: 1.6;
    min-height: 51px;
    overflow-x: hidden;
    overflow-y: hidden;
    text-overflow: ellipsis;
    word-break: keep-all;

    &.grid_badge_padding {
        padding: 12px 10px;
    }
}

.grid_row_border {
    border-left-color: transparent !important;
    border-bottom: 1px solid #E8EDF6 !important;
}

.badge {
    &.badge_border {
        border: 1px solid rgba(0,0,0,0.2);
    }

    &.badge_spacing {
        font-size: 12px;
        width: 120px;
        text-align: center;
        letter-spacing: 0.5px;
    }
}

.bold {
    font-weight: bold;
}

.col-xs-5ths,
.col-xs-2-5ths {
    position: relative;
    min-height: 1px;
    padding-right: 10px;
    padding-left: 10px;
    display: inline-block;
    line-height: 1;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align: top;
}

.col-xs-5ths {
    width: 20%;
    float: left;
}

.col-xs-2-5ths {
    width: 40%;
    float: left;
}