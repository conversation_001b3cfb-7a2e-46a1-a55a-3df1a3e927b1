$max: 101;
$i: 0; 
$step: 5;
@while $i < $max { 
    $i: $i + $step; 
    .padding-#{$i} { padding: 1px * $i; }
}

$i: 0; 
@while $i < $max { 
    $i: $i + $step; 
    .padding-top-#{$i} { padding-top: 1px * $i; }
}

$i: 0; 
@while $i < $max { 
    $i: $i + $step; 
    .padding-bottom-#{$i} { padding-bottom: 1px * $i; }
}

$i: 0;
@while $i < $max {
    $i: $i + $step;
    .padding-left-#{$i} { padding-left: 1px * $i; }
}
$i: 0;
@while $i < $max {
    $i: $i + $step;
    .padding-right-#{$i} { padding-right: 1px * $i; }
}

@media (max-width: 1140px) {
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .padding-#{$i}\@tablet { padding: 1px * $i; }
    }

    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .padding-top-#{$i}\@tablet { padding-top: 1px * $i; }
    }

    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .padding-bottom-#{$i}\@tablet { padding-bottom: 1px * $i; }
    }

    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .padding-left-#{$i}\@tablet { padding-left: 1px * $i; }
    }
    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .padding-right-#{$i}\@tablet { padding-right: 1px * $i; }
    }
}


@media (max-width: 720px) {
    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .padding-#{$i}\@mobile { padding: 1px * $i; }
    }

    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .padding-top-#{$i}\@mobile { padding-top: 1px * $i; }
    }

    $i: 0; 
    @while $i < $max { 
        $i: $i + $step; 
        .padding-bottom-#{$i}\@mobile { padding-bottom: 1px * $i; }
    }

    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .padding-left-#{$i}\@mobile { padding-left: 1px * $i; }
    }
    $i: 0;
    @while $i < $max {
        $i: $i + $step;
        .padding-right-#{$i}\@mobile { padding-right: 1px * $i; }
    }
}

.padding-none {
    padding: 0 !important;
}

.padding-bottom-none {
    padding-bottom: 0 !important;
}

.padding-top-none {
    padding-top: 0 !important;
}

.padding-left-none {
    padding-left: 0 !important;
}

.padding-right-none {
    padding-right: 0 !important;
}

.padding-top-36 {
    padding-top: 36px;
}

.padding-tb-8 {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
}