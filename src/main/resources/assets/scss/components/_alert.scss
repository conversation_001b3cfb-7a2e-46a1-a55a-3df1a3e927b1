@import "variables";
@import "mixins";

.alert {
    border-radius: 5px;
    background: $athens-gray;
    width: 100%;
    font-size: 14px;
    padding: 13px 16px;
    border: 0px;
    box-shadow: none;
    color: $primary;
    box-sizing: border-box;
    &.alert-danger {
        color: white;
        @include background-gradient(#F8703F, #CD1212);
    }
    &.alert-success {
        @include background-gradient(#9FB83E, #1B8F22);
        color: white;
    }
    &.alert-box {
        border-radius: 0px;
        padding: 10px 30px;
        a {
            color: white;
        }
    }
}

.alert-fixed {
   position: fixed;
   top: 46px;
   left: 50px;
   right: 47px;
   z-index: 9999;
   &.users {
        top: 30px;
        left: 10px;
        right: 10px;
   }
   &.popup {
        top: 6px;
        left: 48px;
        right: 48px;
   }
}

.alert-top {
    position: absolute;
    z-index: 999;
    left: -1px;
    right: -1px;
    top: -11px;
}