<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      xmlns:bpo="http://www.w3.org/1999/xhtml"
      layout:decorate="~{popup-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{absences.title}"></title>
    <script th:src="@{/assets/js/libraries/cultures/kendo.culture.de-AT.min.js}"></script>
</head>

<body layout:fragment="content">

<div class="container padding-left-50 padding-right-50 padding-top-40 padding-bottom-30">
    <div class="row">
        <div class="gr-12">
            <h1 th:text="#{absence.title}">ABWESENHEIT</h1>
        </div>
    </div>
</div>

<hr>

<div class="container padding-50">
    <form class="form-horizontal" role="form" th:action="@{/absence/{plannedResourceId}(plannedResourceId=${plannedResourceId})}" th:object="${absenceDetail}" method="POST" bpo:disabled-without-permission="EDIT_ABSENCE">
        <div class="row margin-bottom-30">
            <div class="gr-12">
                <small th:text="#{absence.type.title}">#{absence.type.title}</small>
                <select class="select2 margin-top-5 kendo-select" th:field="*{absenceType}">
                    <option th:each="type : ${absenceTypes}"
                            th:value="${type}" th:text="#{${type.localizationKey}}"
                            th:selected="${type} == *{absenceType}">
                    </option>
                </select>
            </div>
        </div>
        <div class="row margin-bottom-30">
            <div class="gr-12">
                <small th:text="#{absence.note}">#{absence.note}</small>
                <textarea class="form-control margin-top-5" th:field="*{note}" rows="3"></textarea>
                <small th:if="${#fields.hasErrors('note')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{note}"></small>
            </div>
        </div>

        <div class="row margin-bottom-30">
            <div class="gr-12">
                <div class="gr-6 no-gutter">
                    <small th:text="#{absence.startDate}">#{absence.startDate}</small>
                    <div class="row">
                        <div class="gr-12 no-gutter margin-top-5">
                            <input class="datePicker" th:field="*{startDate}"/>
                            <small th:if="${#fields.hasErrors('startDate')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{startDate}"></small>
                        </div>
                    </div>
                </div>

                <div class="gr-6 no-gutter">
                    <small th:text="#{absence.endDate}">#{absence.endDate}</small>
                    <div class="row">
                        <div class="gr-12 no-gutter margin-top-5">
                            <input class="datePicker" th:field="*{endDate}"/>
                            <small th:if="${#fields.hasErrors('endDate')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{endDate}"></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row margin-bottom-30">
            <div class="gr-12">
                <div class="gr-6 no-gutter">
                    <small th:text="#{absence.startTime}">#{absence.startTime}</small>
                    <div class="row">
                        <div class="gr-12 no-gutter margin-top-5">
                            <input class="timePicker" th:field="*{startTime}"/>
                            <small th:if="${#fields.hasErrors('startTime')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{startTime}"></small>
                        </div>
                    </div>
                </div>
                <div class="gr-6 no-gutter">
                    <small th:text="#{absence.endTime}">#{absence.endTime}</small>
                    <div class="row">
                        <div class="gr-12 no-gutter margin-top-5">
                            <input class="timePicker" th:field="*{endTime}"/>
                            <small th:if="${#fields.hasErrors('endTime')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{endTime}"></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="position-fixed background-white">
            <div class="gr-12 no-gutter text-right padding-right-50 padding-left-50 padding-top-20 padding-bottom-10">
                <button type="submit" class="btn primary " th:text="#{save}"></button>
            </div>
        </div>
    </form>
</div>

</body>
</html>



