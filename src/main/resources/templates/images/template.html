<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org" layout:decorate="~{${layoutToRender}}">
<head>
    <style type="text/css" th:inline="text">
        @font-face { font-family: Roboto; src: url("[[${fontBaseUrl}]]Roboto-Regular.ttf"); font-weight: normal; font-style: normal; -fs-pdf-font-embed: embed; -fs-pdf-font-encoding: Identity-H; }

        @page {
            size: A4;
            margin-bottom: 40px;
            counter-increment: page;

            @top-left {
                content: "[[${title}]]";
                font-family: 'Roboto', sans-serif;
                font-size: 12px;
                padding-top: 30px;
            }
            @bottom-right {
                content: "[[#{print.footer.page}]] " counter(page) " [[#{print.footer.of}]] " counter(pages);
                font-family: 'Roboto', sans-serif;
                font-size: 12px;
                padding-bottom: 30px;
            }
            @bottom-left {
                content: "[[#{print.footer.description}]] [[${dateForFooter}]]";
                font-family: 'Roboto', sans-serif;
                font-size: 12px;
                padding-bottom: 30px;
            }
        }
    </style>
</head>
<body layout:fragment="content">
    <div th:each="chunk, iterStat : ${chunkedUrls}">
        <div class="row">
            <div class="col" th:each="src : ${chunk}">
                <div><img th:src="${'file://' + src}" alt="" /></div>
            </div>
        </div>
        <div class="break" th:unless="${iterStat.last}"></div>
    </div>
</body>
</html>