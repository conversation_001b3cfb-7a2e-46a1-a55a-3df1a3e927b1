<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:bpo="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0">
    <link rel="stylesheet" th:href="@{/assets/css/kendo.common.min.css}">
    <link rel="stylesheet" th:href="@{/assets/css/kendo.custom.css}">
    <link rel="stylesheet" th:href="@{/assets/css/grid.min.css}">
    <link rel="stylesheet" th:href="@{/assets/css/styles.css}">
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,400i,600,600i,700,700i" rel="stylesheet">
    <title>Title</title>
    <script th:inline="javascript">
        var translation = {
            imagesDeleteConfirmation: /*[[#{images.delete.confirmation}]]*/,
            deleteIssuePlaceholder: /*[[#{recurringissues.placeholder.delete}]]*/,
            unassignSubIssue: /*[[#{recurringissues.subissue.unassign}]]*/,
            deleteAssignment: /*[[#{recurringissues.subissue.delete}]]*/
        };
    </script>
    <script th:src="@{/assets/js/libraries/jquery.js}"></script>
    <script th:src="@{/assets/js/libraries/select2.js}"></script>
    <script th:src="@{/assets/js/libraries/datatables.js}"></script>
    <script th:src="@{/assets/js/libraries/kendo.all.min.js}"></script>
    <script th:src="@{/assets/js/libraries/kendo.timezones.min.js}"></script>
    <script th:src="@{/assets/js/config.js}"></script>
    <script th:src="@{/assets/js/datepicker.js}"></script>
    <script th:src="@{/assets/js/tabs.js}"></script>
</head>

<body class="x-hidden">

<div class="alert-top" th:if="${success}">
    <div class="padding-left-50 padding-right-50 padding-top-20">
        <div class="container">
            <div class="row">
                <div class="gr-12">
                    <div class="alert alert-success">
                        <div class="gr-12 no-gutter" th:text="${success}">
                            Lorem ipsum dolor sit amet, consetetur sadipscing
                        </div>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="alert-top" th:if="${error}">
    <div class="padding-left-50 padding-right-50 padding-top-20">
        <div class="container">
            <div class="row">
                <div class="gr-12">
                    <div class="alert alert-danger">
                        <div class="gr-12 no-gutter" th:text="${error}">
                            Lorem ipsum dolor sit amet, consetetur sadipscing
                        </div>
                        <div class="clearfix"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div layout:fragment="content"></div>

<script>
    if(typeof window.jsHook != "undefined") {
        window.jsHook();
    }
    if($('.alert-top').length > 0) {
        setTimeout(function() {
            $('.alert-top').fadeOut();
        }, 3000)
    }
     $(".kendo-select").kendoDropDownList();
</script>
</body>
</html>
