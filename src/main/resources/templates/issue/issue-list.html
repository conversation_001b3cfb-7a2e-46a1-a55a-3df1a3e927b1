<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org" layout:decorate="~{default-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{issues.list.title}">Title</title>
</head>

<body>
    <section layout:fragment="content">

        <div class="container-fluid issue-table">
            <div class="row">

                <div class="gr-12">
                    <div class="margin-bottom-30">
                        <h1 th:text="#{issues.list.title}">#{issues.list.title}</h1>
                    </div>
                </div>


                <div class="gr-12">
                    <div id="full-issue-list" class="gridlist" data-config="ISSUES_LIST"></div>
                </div>
            </div>

        </div>

    </section>

    <section layout:fragment="beforeScripts">
        <script th:inline="javascript">
            window.issueStatusDataSource = [];
            window.issueStatusDataSource.push({
                'key': 'ALL',
                'name': /*[[#{status.all}]]*/
            });
        </script>
        <script th:each="issueStatus : ${issueStatusList}" th:inline="javascript">
            window.issueStatusDataSource.push({
                'key': /*[[${issueStatus.name}]]*/,
                'name': /*[[#{${issueStatus.localizationKey}}]]*/
            });
        </script>
    </section>
</body>

</html>