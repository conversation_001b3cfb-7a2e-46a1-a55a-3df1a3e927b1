<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document</title>
    <style type="text/css" th:inline="text">
        @font-face { font-family: Roboto; src: url("[[${fontBaseUrl}]]Roboto-Regular.ttf"); font-weight: normal; font-style: normal; -fs-pdf-font-embed: embed; -fs-pdf-font-encoding: Identity-H; }
        @font-face { font-family: Roboto; src: url("[[${fontBaseUrl}]]Roboto-Light.ttf"); font-weight: 300; font-style: 300; -fs-pdf-font-embed: embed; -fs-pdf-font-encoding: Identity-H; }
        @font-face { font-family: Roboto; src: url("[[${fontBaseUrl}]]Roboto-Bold.ttf"); font-weight: bold; font-style: bold; -fs-pdf-font-embed: embed; -fs-pdf-font-encoding: Identity-H; }

        @page {
            size: A4;
            margin-bottom: 50px;
            counter-increment: page;

            @bottom-right {
                content: "[[#{print.footer.page}]] " counter(page) " [[#{print.footer.of}]] " counter(pages);
                font-family: 'Roboto', sans-serif;
                font-weight: normal;
                font-size: 11px;
                padding-bottom: 30px;
            }
            @bottom-left {
                content: "[[#{print.footer.description}]] [[${footerDescriptionDate}]]";
                font-family: 'Roboto', sans-serif;
                font-weight: normal;
                font-size: 11px;
                padding-bottom: 30px;
            }
        }

        body {
            margin-top: 6px;
            font-family: 'Roboto', sans-serif;
            font-weight: normal;
            font-size: 11px;
        }
        .titlebar {
            text-align: center;
            margin-bottom: 45px;
        }
        .title {
            font-size: 1.6em;
        }
        .subtitle {
            font-size: 1.5em;
            margin-top: -5px;
        }
        .font-light {
            font-family: 'Roboto', sans-serif;
            font-weight: 300;
        }
        .font-bold {
            font-family: 'Roboto', sans-serif;
            font-weight: bold;
        }
        img {
            margin-bottom: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            -fs-table-paginate: paginate;
        }
        thead {
            display: table-header-group;
        }
        th {
            font-weight: normal;
            color: white;
            text-align: left;
            padding-left: 10px;
            padding-right: 10px;
            padding-top: 4px;
            padding-bottom: 4px;
        }
        tr {
            line-height: 1.7;
        }
        tr.technician {
            background-color: #002b80;
        }
        tr.assistant {
            background-color: #CD1013;
        }
        td {
            border-bottom: 1px solid #181716;
            padding: 10px 10px;
            line-height: 1.6em;
        }
        td.multi-line {
            border-bottom: 1px solid #181716;
            /* disabled for now because it causes a mixed line (first part is thick, last part is thin) when a page break is inserted in the middle of a technician / assistant */
            /* border-bottom: 0.5px solid rgba(24,23,22,0.5) !important; */
        }

    </style>
</head>
<body>
<div class="titlebar">
    <img th:src="${'data:image/png;base64,'+ logo}" alt="Neubauer" width="109" height="73" class="logo" />
    <div class="title font-light" th:text="#{print.title}">Tageseinsatzplan</div>
    <div class="subtitle font-bold" th:text="${title}">Mittwoch, 21.Februar 2018</div>
</div>

<table>
    <thead>
    <tr class="technician">
        <th align="left" style="width:20%" th:text="#{print.technician}">Monteur</th>
        <th align="left" style="width:13%" th:text="#{print.time}">Zeit</th>
        <th align="left" style="width:12%" th:text="#{print.absence}">Abwesenheit</th>
        <th align="left" style="width:13%" th:text="#{print.issue}">Auftrag</th>
        <th align="left" style="width:42%" th:text="#{print.address}">Einsatzort</th>
    </tr>
    </thead>
    <tbody>
    <th:block th:each="ta : ${technicianAssignments}">
        <tr th:each="ass,iter : ${ta.assignments}">
            <td valign="top" th:rowspan="${iter.size}" th:if="${iter.index == 0}"
                th:text="${ta.name}"></td>
            <td valign="top" th:text="${ass.time}" th:classappend="${iter.size > 1 && iter.index < (iter.size - 1)}? 'multi-line'"></td>
            <td valign="top" th:colspan="${ass.hasAbsence? 3 : 1}" th:text="${ass.absence}" th:classappend="${iter.size > 1 && iter.index < (iter.size - 1)}? 'multi-line'"></td>
            <td valign="top" th:if="${!ass.hasAbsence}" th:text="${ass.assignmentTitle}" th:classappend="${iter.size > 1 && iter.index < (iter.size - 1)}? 'multi-line'"></td>
            <td valign="top" th:if="${!ass.hasAbsence}" th:text="${ass.assignmentDescription}" class="font-light" th:classappend="${iter.size > 1 && iter.index < (iter.size - 1)}? 'multi-line'"></td>
        </tr>
    </th:block>
    </tbody>
</table>
<br />
<table>
    <thead>
    <tr class="assistant">
        <th align="left" style="width:20%" th:text="#{print.assistant}">Helfer</th>
        <th align="left" style="width:13%" th:text="#{print.time}">Zeit</th>
        <th align="left" style="width:12%" th:text="#{print.absence}">Abwesenheit</th>
        <th align="left" style="width:55%" th:text="#{print.technician}">Monteur</th>
    </tr>
    </thead>
    <tbody>
    <th:block th:each="aa : ${assistantAssignments}">
        <tr th:each="ass,iter : ${aa.assignments}">
            <td valign="top" th:rowspan="${iter.size}" th:if="${iter.index == 0}"
                th:text="${aa.name}"></td>
            <td valign="top" th:text="${ass.time}" th:classappend="${iter.size > 1 && iter.index < (iter.size - 1)}? 'multi-line'"></td>
            <td valign="top" th:colspan="${ass.hasAbsence? 2 : 1}" th:text="${ass.absence}" th:classappend="${iter.size > 1 && iter.index < (iter.size - 1)}? 'multi-line'"></td>
            <td valign="top" th:if="${!ass.hasAbsence}" th:text="${ass.assignmentTitle}" class="font-light" th:classappend="${iter.size > 1 && iter.index < (iter.size - 1)}? 'multi-line'"></td>
        </tr>
    </th:block>
    </tbody>
</table>
</body>
</html>