<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document</title>
    <style type="text/css" th:inline="text">
        @font-face { font-family: Roboto; src: url("[[${fontBaseUrl}]]Roboto-Regular.ttf"); font-weight: normal; font-style: normal; -fs-pdf-font-embed: embed; -fs-pdf-font-encoding: Identity-H; }
        @font-face { font-family: Roboto; src: url("[[${fontBaseUrl}]]Roboto-Light.ttf"); font-weight: 300; font-style: 300; -fs-pdf-font-embed: embed; -fs-pdf-font-encoding: Identity-H; }
        @font-face { font-family: Roboto; src: url("[[${fontBaseUrl}]]Roboto-Bold.ttf"); font-weight: bold; font-style: bold; -fs-pdf-font-embed: embed; -fs-pdf-font-encoding: Identity-H; }

        @page {
            size: A4;
            margin-bottom: 50px;
            counter-increment: page;

            @bottom-right {
                content: "[[#{print.footer.page}]] " counter(page) " [[#{print.footer.of}]] " counter(pages);
                font-family: 'Roboto', sans-serif;
                font-weight: normal;
                font-size: 11px;
                padding-bottom: 30px;
            }
            @bottom-left {
                content: "[[#{print.footer.description}]] [[${footerDescriptionDate}]]";
                font-family: 'Roboto', sans-serif;
                font-weight: normal;
                font-size: 11px;
                padding-bottom: 30px;
            }
        }

        body {
            margin-top: 6px;
            font-family: 'Roboto', sans-serif;
            font-weight: normal;
            font-size: 11px;
        }
        .titlebar {
            text-align: center;
            margin-bottom: 45px;
        }
        .title {
            font-size: 1.6em;
        }
        .subtitle {
            font-size: 1.5em;
            margin-top: -5px;
        }
        .font-light {
            font-family: 'Roboto', sans-serif;
            font-weight: 300;
        }
        .font-bold {
            font-family: 'Roboto', sans-serif;
            font-weight: bold;
        }
        img {
            margin-bottom: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            -fs-table-paginate: paginate;
        }
        thead {
            display: table-header-group;
        }
        th {
            font-weight: normal;
            color: white;
            text-align: left;
            padding-left: 10px;
            padding-right: 10px;
            padding-top: 4px;
            padding-bottom: 4px;
        }
        .lt {
            font-weight: normal;
            font-size: 11px;
            color: black;
            text-align: left;
            padding-left: 10px;
            padding-right: 10px;
            padding-bottom: 4px;
        }
        .lt-title {
            font-weight: bold;
            font-size: 11px;
            color: black;
            text-align: left;
            padding-left: 10px;
            padding-right: 10px;
            padding-top: 4px;
            padding-bottom: 4px;
        }
        tr {
            line-height: 1.7;
        }
        tr.technician {
            background-color: #002b80;
        }
        tr.assistant {
            background-color: #CD1013;
        }
        td {
            border-bottom: 1px solid #181716;
            padding: 10px 10px;
            line-height: 1.6em;
        }
        td.multi-line {
            border-bottom: 1px solid #181716;
        }
        #data tbody > tr:last-child > td {
             border-bottom: 0;
        }
        .notes {
            white-space: pre-wrap;
        }

    </style>
</head>
<body>
<div class="titlebar">
    <img th:src="${'data:image/png;base64,'+ logo}" alt="Neubauer" width="109" height="73" class="logo" />
    <div class="title font-bold" th:text="${issue.externalId}">externalId</div>
    <div class="subtitle font-light" th:text="#{${issue.status.localizationKey}}">Status</div>
</div>

<table>
    <thead>
    <tr class="technician">
        <th align="left" style="width:54%" th:text="#{print.technician}">Monteur</th>
        <th align="left" style="width:23%" th:text="#{print.date}">Datum</th>
        <th align="left" style="width:23%" th:text="#{print.time}">Zeit</th>
    </tr>
    </thead>
    <tbody>
        <th:block>
            <tr th:each="assign : ${assignment}">
                <td valign="top"
                    th:text="${assign.technician}"></td>
                <td valign="top" th:text="${assign.date}"></td>
                <td valign="top" th:text="${assign.time}"></td>
            </tr>
        </th:block>
    </tbody>
</table>
<br />
<br />
<div class="lt-title" th:text="#{issue.detail.contactPerson}">Störmelder</div>
<div class="lt" th:text="${issue.contactPerson}"></div>
<br />
<div class="lt-title" th:text="#{issue.detail.address}">Adresse</div>
<div class="lt" th:text="${issue.address}"></div>
<br />
<div class="lt-title" th:text="#{issue.detail.description}">Auszuführende Arbeit</div>
<div class="lt" th:text="${issue.description}"></div>
<br />
<div class="lt-title" th:text="#{issue.detail.note}">Notiz</div>
<div class="lt notes" th:text="${issue.note}"></div>
<br />
</body>
</html>