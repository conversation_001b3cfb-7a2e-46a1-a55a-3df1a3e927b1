<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0">
    <link rel="stylesheet" th:href="@{/assets/css/grid.min.css}">
    <link rel="stylesheet" th:href="@{/assets/css/styles.css}">
    <script th:src="@{/assets/js/pace.min.js}"></script>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,600i" rel="stylesheet">
    <title th:text="#{neubauer} + ' - ' + #{login.title}"></title>
</head>

<body id="login">
    <div class="container max-height" layout:fragment="content">
        <div class="row max-height vertical-center">
            <div class="gr-4 gr-10@tablet gr-10@mobile">
                <form class="form-horizontal" role="form" method="POST" th:action="@{/login}">
                    <div class="margin-bottom-40 text-center">
                        <img th:src="@{/assets/images/<EMAIL>}" alt="Neubauer" width="218" height="146">
                    </div>

                    <div th:if="${param.error}" th:text="#{errors.login.invalidCredentials}"
                        class="alert alert-danger margin-bottom-15">Lorem ipsum dolor sit amet, consetetur sadipscing</div>

                    <div th:if="${param.logout}" th:text="#{logout.message}"
                        class="alert alert-success margin-bottom-15">Lorem ipsum dolor sit amet, consetetur sadipscing</div>

                    <div class="margin-bottom-15">
                        <input type="text" name="username" class="form-control"
                            th:placeholder="#{login.username}">
                    </div>

                    <div class="margin-bottom-15"> 
                        <input type="password" name="password" class="form-control"
                            th:placeholder="#{login.password}">
                    </div>
                    <div class="text-center">
                        <button type="submit" class="btn primary login"  th:text="#{login.title}"></button>
                    </div>
                </form>
            </div>
        </div>
        <div id="version" th:text="${version}" style="display: none;"></div>
    </div>
</body>
</html>
