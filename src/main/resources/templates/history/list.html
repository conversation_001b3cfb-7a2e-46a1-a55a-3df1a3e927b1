<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org" layout:decorate="~{popup-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{issues.list.title}">Title</title>
</head>

<body>
<section layout:fragment="content" class="container padding-left-50 padding-right-50 padding-top-20">
    <div class="gr-12 no-gutter">
        <div class="row grid_header grid_row_border">
            <div class="gr-3 grid_cell">
                <small th:text="#{issue.history.employee}"></small>
            </div>
            <div class="gr-2 grid_cell">
                <small th:text="#{issue.history.date}"></small>
            </div>
            <div class="gr-3 grid_cell">
                <small th:text="#{issue.history.status}"></small>
            </div>
            <div class="gr-4 grid_cell">
                <small th:text="#{issue.history.note}"></small>
            </div>
        </div>

        <div class="gr-12 no-gutter" th:each="historyListItem : ${historyList}">
            <div class="row grid_row_border">
                <div class="gr-3 grid_cell bold">
                    <div th:text="${historyListItem.userDisplayName}"></div>
                </div>
                <div class="gr-2 grid_cell">
                    <div th:text="${historyListItem.timestamp}"></div>
                </div>
                <div class="gr-3 grid_cell grid_badge_padding">
                    <span th:if="${historyListItem.issueStatus != null}" class="badge badge_border badge_spacing" th:style="'background: linear-gradient(to right, '+ ${historyListItem.gradientStartColor}+', '+${historyListItem.gradientEndColor}+');'" th:text="${historyListItem.issueStatus}"></span>
                </div>
                <div class="gr-4 grid_cell">
                    <div th:text="${historyListItem.actionText}"></div>
                </div>
            </div>
        </div>
    </div>
</section>

</body>
</html>


