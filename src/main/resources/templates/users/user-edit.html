<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org" xmlns:bpo="http://www.w3.org/1999/xhtml"
    layout:decorate="~{default-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{user.edit.title}"></title>
</head>

<body layout:fragment="content">
    <div class="margin-bottom-100">
        <div class="container-fluid">
            <div class="row">
                <div class="gr-12">
                    <div class="row">
                        <div class="gr-6">
                            <h1 th:text="#{user.edit.title}">#{user.edit.title}</h1>
                        </div>
                        <div class="gr-6" bpo:required-permission="EDIT_USER">
                            <div class="text-right">
                                <div th:if="${userDisabled}">
                                    <form class="form-horizontal" role="form" th:action="@{/admin/users/{userId}/enable(userId=${userId})}" method="POST">
                                        <button type="submit" class="btn secondary" th:text="#{user.enable}"></button>
                                    </form>
                                </div>
                                <div th:if="${!userDisabled}">
                                    <form
                                    class="form-horizontal"
                                    role="form"
                                    th:action="@{/admin/users/{userId}/disable(userId=${userId})}"
                                    method="POST"
                                    th:onsubmit="'return confirm(\'' + #{user.disable.confirm} + '\');'">
                                        <button type="submit" class="btn secondary disable" th:text="#{user.disable}"></button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="gr-12">
                            <ul class="tabs">
                                <li><a href="#tab1" class="active" th:text="#{tab.detail}">Details</a></li>
                                <li><a href="#tab2" th:text="#{tab.images}">Bilder</a></li>
                            </ul>
                            <hr class="margin-top-20 margin-bottom-0">
                        </div>
                    </div>
                </div>
                <div id="tab1" class="tab active">
                    <form class="form-horizontal" role="form" th:action="@{/admin/users/{userId}(userId=${userId})}" th:object="${userEditForm}"
                    method="POST">

                        <div th:replace="~{/users/partials/form-fields :: form-fields}"></div>

                        <div class="gr-6 margin-bottom-20">
                            <a th:href="@{/admin/users}" class="btn secondary" th:text="#{back}">#{back}</a>
                        </div>

                        <div class="gr-6 margin-bottom-20 text-right">
                            <button type="submit" class="btn primary" th:text="#{save}" bpo:required-permission="EDIT_USER"></button>
                        </div>
                    </form>
                </div>
                <div id="tab2" class="tab">
                    <div class="gr-12">
                        <iframe th:src="@{/admin/users/{userId}/images(userId=${userId})}" frameborder="0" width="100%" height="800" class="image-iframe"></iframe>
                    </div>
                </div>

            </div>
        </div>

        <script th:inline="javascript">
            window.jsHook = function () {
                $('.select2').select2();
            }
        </script>
    </div>
</body>

</html>