<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      layout:decorate="~{default-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{user.create.title}"></title>
</head>

<body layout:fragment="content">
<div class="margin-bottom-100">
    <div class="container-fluid">
        <div class="row">
            <form class="form-horizontal" role="form" th:action="@{/admin/users/create}" th:object="${userForm}" method="POST">

                <div class="gr-12">
                    <h1 class="margin-bottom-50" th:text="#{user.create.title}">#{user.create.title}</h1>
                </div>

                <div th:replace="~{/users/partials/form-fields :: form-fields}"></div>

                <div class="gr-6 margin-bottom-20">
                    <a th:href="@{/admin/users}" class="btn secondary" th:text="#{back}">#{back}</a>
                </div>

                <div class="gr-6 margin-bottom-20 text-right">
                    <button type="submit" class="btn primary"  th:text="#{save}"></button>
                </div>

            </form>
        </div>
    </div>
    <script th:inline="javascript">
        window.jsHook = function () {
            $('.select2').select2();
        }
    </script>
</div>
</body>
</html>
