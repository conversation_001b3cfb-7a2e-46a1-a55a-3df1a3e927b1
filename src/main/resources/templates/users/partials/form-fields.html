<!DOCTYPE html>
<html xmlns:th="http://www.w3.org/1999/xhtml" xmlns:bpo="http://www.w3.org/1999/xhtml">
<head>
</head>
<body>

<div class="gr-12 margin-bottom-10 padding-top-40" th:fragment="form-fields">
 
    <div class="row">
        <div class="gr-6 margin-bottom-40">
            <small th:text="#{user.username}">#{user.username}</small>
            <small class="help-block black padding-top-10 padding-bottom-10" th:text="#{user.usernameCriteria}"></small>
            <div class="margin-top-10">
                <input type="text" class="form-control" th:field="*{username}" th:name="username" bpo:disabled-without-permission="EDIT_USER" />
                <small th:if="${#fields.hasErrors('username')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{username}"></small>
            </div>
        </div>

        <div class="gr-6 margin-bottom-40">
            <small th:text="#{user.password}">#{user.password}</small>
            <small class="help-block black padding-top-10 padding-bottom-10" th:text="#{user.passwordCriteria}"></small>
            <div class="margin-top-10">
                <input type="password" class="form-control" th:field="*{password}" th:name="password" bpo:disabled-without-permission="EDIT_USER"/>
                <small th:if="${#fields.hasErrors('password')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{password}"></small>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="gr-6 margin-bottom-40">
            <small th:text="#{user.firstName}">#{user.firstName}</small>
            <div class="margin-top-10">
                <input type="text" class="form-control" th:field="*{firstName}" th:name="firstName" bpo:disabled-without-permission="EDIT_USER"/>
                <small th:if="${#fields.hasErrors('firstName')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{firstName}"></small>
            </div>
        </div>
        <div class="gr-6 margin-bottom-40">
            <small th:text="#{user.lastName}">#{user.lastName}</small>
            <div class="margin-top-10">
                <input type="text" class="form-control" th:field="*{lastName}" th:name="lastName" bpo:disabled-without-permission="EDIT_USER"/>
                <small th:if="${#fields.hasErrors('lastName')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{lastName}"></small>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="gr-6 margin-bottom-40">
            <small th:text="#{user.phoneNumber}">#{user.phoneNumber}</small>
            <div class="margin-top-10">
                <input type="text" class="form-control" th:field="*{phoneNumber}" th:name="phoneNumber" bpo:disabled-without-permission="EDIT_USER"/>
                <small th:if="${#fields.hasErrors('phoneNumber')}" class="help-block padding-top-10 padding-bottom-10" th:errors="*{phoneNumber}"></small>
            </div>
        </div>
        <div class="gr-6 margin-bottom-40">
            <small th:text="#{user.role}">#{user.role}</small>
            <div class="margin-top-10">
                <select id="roles" class="select2" th:field="*{role}" bpo:disabled-without-permission="EDIT_USER">
                    <option th:each="role : ${roles}"
                            th:value="${role.name}" th:text="#{${role.localizationKey}}"
                            th:selected="${role.name} == *{role}">TECHNICIAN
                    </option>
                </select>
            </div>
        </div>
        
    </div>
</div>
</body>
</html>
