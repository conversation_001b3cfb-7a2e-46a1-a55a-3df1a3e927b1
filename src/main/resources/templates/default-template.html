<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org" xmlns:bpo="http://www.w3.org/1999/xhtml">

<head>
    <!-- Standard Meta -->
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0">
    <link rel="stylesheet" th:href="@{/assets/css/kendo.common.min.css}">
    <link rel="stylesheet" th:href="@{/assets/css/kendo.custom.css}">
    <link rel="stylesheet" th:href="@{/assets/css/grid.min.css}">
    <link rel="stylesheet" th:href="@{/assets/css/styles.css}">
    <script th:src="@{/assets/js/libraries/pace.min.js}"></script>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,400i,600,600i,700,700i" rel="stylesheet">
    <!-- Site Properties -->
    <title>Title</title>
</head>

<body>
    <div id="navigation">
        <div class="container-fluid">
            <div class="row">
                <div class="gr-8">
                    <ul>
                        <li>
                            <a th:href="@{/dashboard}">
                                <img th:src="@{/assets/images/<EMAIL>}" alt="Neubauer" width="78" height="56" class="logo">
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="gr-4">
                    <ul class="pull-right">
                        <li>
                            <div class="gr-12 padding-right-30 font-14">
                                <span th:text="${T(org.springframework.security.core.context.SecurityContextHolder).getContext().getAuthentication().getPrincipal().getUsername()}"></span>

                            </div>
                        </li>
                        <li>
                            <div class="gr-12 no-gutter">
                                <form method="post" th:action="@{/logout}">
                                    <input type="submit" class="btn secondary logout" th:value="#{logout.title}">
                                </form>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="sidebar">
        <ul class="reset">
            <li bpo:required-permission="VIEW_USER_LIST" bpo:mark-if-active-nav-item="/admin/users">
                <a href="/admin/users" class="mitarbeiter">&nbsp;</a>
            </li>
            <li bpo:required-permission="VIEW_RESOURCE_PLAN_TECHNICIAN" bpo:mark-if-active-nav-item="/scheduler/technician">
                <a class="zeiten" href="/scheduler/technician">&nbsp;</a>
            </li>
            <li bpo:required-permission="VIEW_ISSUE_LIST" bpo:mark-if-active-nav-item="/admin/issues">
                <a class="auftrag" href="/admin/issues">&nbsp;</a>
            </li>
        </ul>
    </div>

    <div id="window" style="display:none"></div>

    <div class="padding-top-100">
        <div th:if="${success}">
            <div class="container-fluid">
                <div class="row">
                    <div class="gr-12 margin-bottom-50">
                        <div class="alert alert-success">
                            <div class="gr-12" th:text="${success}">
                                Lorem ipsum dolor sit amet, consetetur sadipscing
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div th:if="${error}">
            <div class="container-fluid">
                <div class="row">
                    <div class="gr-12 margin-bottom-50">
                        <div class="alert alert-danger">
                            <div class="gr-12" th:text="${error}">
                                Lorem ipsum dolor sit amet, consetetur sadipscing
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div layout:fragment="content"></div>
    </div>

    <script th:inline="javascript">
        var translation = {
            externalId: /*[[#{issues.externalId}]]*/,
            address: /*[[#{issues.address}]]*/,
            contactPerson:  /*[[#{issues.contactPerson}]]*/,
            description:  /*[[#{issues.description}]]*/,
            status:  /*[[#{issues.status}]]*/,
            latestAssignmentDateStart:  /*[[#{issues.latestAssignmentDateStart}]]*/,
            username:  /*[[#{user.username}]]*/,
            firstName:  /*[[#{user.firstName}]]*/,
            lastName:  /*[[#{user.lastName}]]*/,
            phoneNumber:  /*[[#{user.phoneNumber}]]*/,
            disabled:  /*[[#{user.status}]]*/,
            role:  /*[[#{user.role}]]*/,
            active:  /*[[#{user.status.active}]]*/,
            inactive:  /*[[#{user.status.inactive}]]*/,
            activeAndInactive:  /*[[#{user.status.all}]]*/,
            imagesDeleteConfirmation: /*[[#{images.delete.confirmation}]]*/,
            absence: /*[[#{absence.title}]]*/
        };
    </script>

    <section layout:fragment="beforeScripts"></section>

    <script th:src="@{/assets/js/libraries/jquery.js}"></script>
    <script th:src="@{/assets/js/libraries/select2.js}"></script>
    <script th:src="@{/assets/js/libraries/kendo.all.min.js}"></script>
    <script th:src="@{/assets/js/libraries/kendo.timezones.min.js}"></script>
    <script th:src="@{/assets/js/libraries/messages/kendo.messages.de-AT.min.js}"></script>
    <script th:src="@{/assets/js/libraries/cultures/kendo.culture.de-AT.min.js}"></script>
    <script th:src="@{/assets/js/libraries/moment.js}"></script>
    <script th:src="@{/assets/js/libraries/moment-timezone.js}"></script>
    <script th:src="@{/assets/js/config.js}"></script>
    <script th:src="@{/assets/js/popup.js}"></script>
    <script th:src="@{/assets/js/list.js}"></script>
    <script th:src="@{/assets/js/grid.js}"></script>
    <script th:src="@{/assets/js/scheduler.js}"></script>
    <script th:src="@{/assets/js/tabs.js}"></script>
    <script th:src="@{/assets/js/print.js}"></script>
    <script th:src="@{/assets/js/main.js}"></script>

    <section layout:fragment="afterScripts"></section>

    <script>
        if (typeof window.jsHook != "undefined") {
            window.jsHook();
        }
    </script>
</body>

</html>