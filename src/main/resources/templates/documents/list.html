<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      layout:decorate="~{popup-template}">

<head>
    <title th:text="#{neubauer} + ' - ' + #{issues.list.title}">Title</title>
</head>

<body>

<section layout:fragment="content" class="container padding-left-50 padding-right-50 padding-top-20">
    <div class="gr-12 no-gutter">
        <div class="row grid_header grid_row_border">
            <div class="gr-2 grid_cell">
                <small th:text="#{issue.documents.type}"></small>
            </div>
            <div class="gr-6 grid_cell">
                <small th:text="#{issue.documents.title}"></small>
            </div>
            <div class="gr-2 grid_cell">
                <small th:text="#{issue.documents.date}"></small>
            </div>
            <div class="gr-2 grid_cell text-right">
                <small th:text="#{issue.documents.download}"></small>
            </div>
        </div>

        <div class="gr-12 no-gutter" th:if="${documentList.size() > 0}" th:each="documentListItem : ${documentList}">
            <div class="row grid_row_border vertical-center">
                <div class="gr-2 grid_cell">
                    <span th:text="#{'issue.documents.type.'+${documentListItem.documentType}}"></span>
                </div>
                <div class="gr-6 grid_cell bold">
                    <span th:text="${documentListItem.filename}"></span>
                </div>
                <div class="gr-2 grid_cell">
                    <span th:text="${documentListItem.updatedAt}"></span>
                </div>
                <div class="gr-2 grid_cell text-right full-line-height">
                    <form method="GET" th:action="'/documents/' + ${documentListItem.id}">
                        <img src="/assets/images/<EMAIL>" alt="download" height="32px" class="cursor-pointer" onclick="submit()">
                    </form>
                </div>
            </div>
        </div>

        <div class="gr-12 no-gutter" th:unless="${documentList.size() > 0}">
            <div class="row grid_row_border">
                <div class="gr-12 grid_cell">
                    <span th:text="#{issue.documents.emptyList}"></span>
                </div>
            </div>
        </div>
    </div>
</section>

</body>
</html>
