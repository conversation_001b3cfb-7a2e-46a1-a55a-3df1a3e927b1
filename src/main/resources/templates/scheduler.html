<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" xmlns:th="http://www.thymeleaf.org"
      layout:decorate="~{default-template}" xmlns:bpo="http://www.w3.org/1999/xhtml">

<head>
    <title th:text="#{neubauer} + ' - ' + #{scheduler.technician.navigation}"></title>
</head>

<body th:attr="data-date=${date}">
<section layout:fragment="content">
    <div class="container-fluid">
        <div class="row">
            <div class="gr-12 margin-bottom-30">
                <div class="gr-10 no-gutter">
                    <h1 th:text="#{scheduler.title}">#{scheduler.title}</h1>
                </div>
                <div class="gr-2 no-gutter text-right">
                    <button bpo:required-permission="PRINT_RESOURCE_PLAN" id="print" class="btn primary small"
                            th:text="#{print}">&nbsp;
                    </button>
                </div>
            </div>
        </div>

        <div class="row">

            <div class="gr-12">
                <div class="margin-bottom-30">
                    <h3 class="header-bar" th:text="#{scheduler.technician.title}">#{scheduler.technician.title}</h3>
                </div>
            </div>

            <div class="gr-2 padding-right-30">
                <script id="list-template" type="text/x-kendo-template">
                    <div class="neb-listitem" id="neb-issue" data-id="#: id #">
                        <div class="neb-listview-wrapper">
                            <div class="neb-listview-main">
                                <b>#: externalId #</b>
                                # if (isRecurringIssue) { #
                                <img class="neb-list-recurring-icon" src="/assets/images/ic_serientermin_grey.png" />
                                # } #
                                <span>[#: status #]</span>
                            </div>
                            <div>
                                <small>#: address #</small>
                            </div>
                        </div>
                    </div>
                </script>
                <input type="text" class="listsearch" id="issuelistsearch" th:placeholder="#{issues.list.placeholder}">
                <div class="listview" id="issuelist" data-config="OPEN_ISSUES_LIST" data-pager="#issue-pager"></div>
                <div id="issue-pager"></div>

                <script id="absence-template" type="text/x-kendo-template">
                    <div class="neb-listitem" id="neb-absence" data-id="#: eventType #">
                        <div class="k-event max-width" style="height: 25px;">
                            <div class="neb-event neb-event-draggable">
                                <b>#: displayName #</b> &nbsp;
                            </div>
                        </div>
                    </div>
                </script>
                <div class="margin-top-20 margin-bottom-30">
                    <div class="absenceList border-top-zero" data-config="ABSENCE_LIST" data-pager=""></div>
                </div>
            </div>

            <div class="gr-10">
                <script id="event-template" type="text/x-kendo-template">
                    <div class="neb-event neb-event-with-secondary-button"
                         style="background: linear-gradient(to right, #= gradientStartColor # , #= gradientEndColor #);">
                        <div class="neb-event-wrapper">
                            <img class='assignment-detail-action-icon' src="/assets/images/zeiten.png"
                                 assignmentDetailUrl='#: secondaryDetailUrl #' eventType='#: eventType #'>
                            # if (eventType != "ABSENCE" && isRecurringIssue) { #
                            <img class='assignment-detail-action-icon margin-right-5' src="/assets/images/ic_serientermin_white.png"
                                 assignmentDetailUrl='#: recurringDetailUrl #' eventType='#: eventType #'>
                            # } #
                            # if (!isRecurringIssue) { #
                            <span class="k-event-actions">
                                 <a href="\\\#" class="k-link k-event-delete" title="Löschen" aria-label="Löschen">
                                    <span class="k-icon k-i-close padding-top-10"></span>
                                 </a>
                            </span>
                            # } #
                            <b>#: title #</b>
                            #if(description){#
                            #: description #
                            #}#
                        </div>
                    </div>
                </script>
                <div class="scheduler" id="issueScheduler" data-config="TECHNICIAN_DAILY"></div>
            </div>
        </div>

        <div class="row padding-top-50 padding-bottom-50">
            <div class="gr-12">
                <div class="margin-bottom-30">
                    <h3 class="header-bar" th:text="#{scheduler.assistant.title}">#{scheduler.assistant.title}</h3>
                </div>
            </div>

            <div class="gr-2 padding-right-30">
                <script id="small-list-template" type="text/x-kendo-template">
                    <div class="neb-listitem" id="neb-item" data-id="#: id #">
                        <div class="neb-listview-wrapper-small">
                            <div>
                                <b>#: displayName #</b> &nbsp;
                            </div>
                        </div>
                    </div>
                </script>
                <input type="text" class="listsearch" id="technicianlistsearch"
                       th:placeholder="#{technicians.list.placeholder}">
                <div class="listview" id="assistantlist" data-config="ACTIVE_TECHNICIAN_LIST"
                     data-pager="#assistant-pager"></div>
                <div id="assistant-pager"></div>

                <div class="listview margin-top-30">
                    <div class="absenceList border-top-zero" data-config="ABSENCE_LIST" data-pager=""></div>
                </div>
            </div>

            <div class="gr-10">
                <script id="technician-event-template" type="text/x-kendo-template">
                    # if (eventType == "ABSENCE") { #
                        <div class="neb-event neb-event-with-secondary-button"
                             style="background: linear-gradient(to right, #= gradientStartColor # , #= gradientEndColor #);">
                            <div class="neb-event-wrapper">
                                <img class='assignment-detail-action-icon' src="/assets/images/zeiten.png"
                                     assignmentDetailUrl='#: secondaryDetailUrl #' eventType='#: eventType #'>
                                <b>#: title #</b>
                            </div>
                        </div>
                    # } else { #
                        <div class="neb-event neb-event-without-secondary-button"
                             style="background: linear-gradient(to right, #= gradientStartColor # , #= gradientEndColor #);">
                            <div class="neb-event-wrapper">
                                <b>#: title #</b>
                            </div>
                        </div>
                    # } #
                </script>
                <div class="scheduler" id="assistantScheduler" data-config="ASSISTANT_DAILY"></div>
            </div>
        </div>
    </div>
</section>

</body>
</html>
