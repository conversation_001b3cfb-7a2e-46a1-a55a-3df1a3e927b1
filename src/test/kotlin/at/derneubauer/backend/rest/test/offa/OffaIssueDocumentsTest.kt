package at.derneubauer.backend.rest.test.offa

import at.derneubauer.backend.offa.db.OffaIssueInsertDto
import at.derneubauer.backend.offa.rest.OffaApiResponse
import at.derneubauer.backend.offa.unmarshal.OffaDocumentInfoDto
import at.derneubauer.backend.offa.unmarshal.OffaDocumentType
import at.derneubauer.backend.offa.unmarshal.OffaProjectDto
import at.derneubauer.backend.offa.unmarshal.OffaWorksheetDto
import at.derneubauer.backend.rest.LoginRequest
import at.derneubauer.backend.rest.client.NeubauerRestTestClient
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.util.NeubauerTestConfig
import at.derneubauer.backend.util.TestConfigLoader
import com.natpryce.hamkrest.assertion.assertThat
import com.natpryce.hamkrest.equalTo
import jakarta.xml.bind.JAXBContext
import jakarta.xml.bind.Marshaller
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import java.io.StringWriter
import java.time.OffsetDateTime
import java.util.UUID

@Tag("end2end")
class OffaIssueDocumentsTest {
    private lateinit var config: NeubauerTestConfig
    private lateinit var restClient: NeubauerRestTestClient

    companion object {
        const val DOCUMENT_INFO_PARAM_NAME = "documentInfo"
        const val DOCUMENT_PARAM_NAME = "document"

        const val DEFAULT_DOCUMENT_INFO_FILENAME = "documentInfo.xml"
        const val DEFAULT_DOCUMENT_FILENAME = "document.pdf"
        const val DOCUMENT_IMAGE_FILENAME = "document.png"

        const val XML_MEDIA_TYPE = MediaType.APPLICATION_XML_VALUE
        const val PDF_MEDIA_TYPE = MediaType.APPLICATION_PDF_VALUE
        const val PNG_MEDIA_TYPE = MediaType.IMAGE_PNG_VALUE

        const val EXPECTED_ERROR_MISSING_SERVLET_PART = "MissingServletRequestPartException"
        const val EXPECTED_ERROR_INVALID_MEDIA_TYPE = "OffaInvalidDocumentMediaTypeException"
        const val EXPECTED_ERROR_XML_PARSE = "OffaDocumentInfoParseException"
        const val EXPECTED_ERROR_FILENAMES_MISMATCH = "OffaFilenamesNotMatchingException"
        const val EXPECTED_ERROR_NO_ISSUES_FOUND = "OffaNoIssuesWithWorksheetNumbersFoundException"
    }

    @BeforeEach
    fun setup() {
        config = TestConfigLoader.loadConfig()
        restClient = NeubauerRestTestClient(config.backend.baseUrl, config.offa)
    }

    @Test
    fun `calling store issue document endpoint with valid xml and pdf combination should be successful`() {
        val issueId = UUID.randomUUID().toString()
        val documentFilename = "$issueId.pdf"

        val documentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = "$issueId.xml",
            contentType = XML_MEDIA_TYPE,
            content = givenDocumentInfoContent(
                documentFilename = documentFilename,
                worksheets = listOf(issueId)
            )
        )

        val document = givenMockedMultipartFile(
            name = DOCUMENT_PARAM_NAME,
            originalFilename = documentFilename,
            contentType = PDF_MEDIA_TYPE,
            content = givenTestPDFContent()
        )

        givenLoggedInAsTechnician()
        givenIssue(issueId)

        val response = whenStoringIssueDocument(documentInfo, document)
        thenStatusCodeIs(HttpStatus.OK)
        thenResponseIsSuccess(response)
    }

    @Test
    fun `calling store issue document with missing parameter should return error`() {
        val response = whenStoringIssueDocument()
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response,
            expectedError = EXPECTED_ERROR_MISSING_SERVLET_PART,
        )
    }

    @Test
    fun `calling store issue document with wrong media types should return error`() {
        val wrongMediaTypeDocumentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_INFO_FILENAME,
            contentType = PNG_MEDIA_TYPE,
            content = givenDocumentInfoContent()
        )

        val wrongMediaTypeDocument = givenMockedMultipartFile(
            name = DOCUMENT_PARAM_NAME,
            originalFilename = DOCUMENT_IMAGE_FILENAME,
            contentType = PNG_MEDIA_TYPE,
            content = givenTestPDFContent()
        )

        // should fail because file that is expected to be xml is actually png
        val response = whenStoringIssueDocument(wrongMediaTypeDocumentInfo, wrongMediaTypeDocument)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response,
            expectedError = EXPECTED_ERROR_INVALID_MEDIA_TYPE,
        )

        val correctMediaTypeDocumentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_INFO_FILENAME,
            contentType = XML_MEDIA_TYPE,
            content = givenDocumentInfoContent()
        )

        // should fail because file that is expected to be pdf is actually png
        val response2 = whenStoringIssueDocument(correctMediaTypeDocumentInfo, wrongMediaTypeDocument)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response2,
            expectedError = EXPECTED_ERROR_INVALID_MEDIA_TYPE,
        )
    }

    @Test
    fun `calling store issue document with malformed xml format should return error`() {
        val documentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_INFO_FILENAME,
            contentType = XML_MEDIA_TYPE,
            content = givenMalformedXMLByteArray()
        )

        val document = givenMockedMultipartFile(
            name = DOCUMENT_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_FILENAME,
            contentType = PDF_MEDIA_TYPE,
            content = givenTestPDFContent()
        )

        val response = whenStoringIssueDocument(documentInfo, document)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response,
            expectedError = EXPECTED_ERROR_XML_PARSE,
        )
    }

    @Test
    fun `calling store issue document with missing required fields in xml should return error`() {
        val documentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_INFO_FILENAME,
            contentType = XML_MEDIA_TYPE,
            content = givenDocumentInfoContent(
                documentFilename = null
            )
        )

        val document = givenMockedMultipartFile(
            name = DOCUMENT_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_FILENAME,
            contentType = PDF_MEDIA_TYPE,
            content = givenTestPDFContent()
        )

        val response = whenStoringIssueDocument(documentInfo, document)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response,
            expectedError = EXPECTED_ERROR_XML_PARSE,
        )
    }

    @Test
    fun `calling store issue document with empty worksheets list should return error`() {
        val documentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_INFO_FILENAME,
            contentType = XML_MEDIA_TYPE,
            content = givenDocumentInfoContent(
                worksheets = listOf()
            )
        )

        val document = givenMockedMultipartFile(
            name = DOCUMENT_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_FILENAME,
            contentType = PDF_MEDIA_TYPE,
            content = givenTestPDFContent()
        )

        val response = whenStoringIssueDocument(documentInfo, document)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response,
            expectedError = EXPECTED_ERROR_XML_PARSE,
        )
    }

    @Test
    fun `calling store issue document with mismatching filenames should return error`() {
        val documentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_INFO_FILENAME,
            contentType = XML_MEDIA_TYPE,
            content = givenDocumentInfoContent(
                documentFilename = "mismatching_filename.pdf",
                worksheets = listOf("issueId1", "issueId2"),
            )
        )

        val document = givenMockedMultipartFile(
            name = DOCUMENT_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_FILENAME,
            contentType = PDF_MEDIA_TYPE,
            content = givenTestPDFContent()
        )

        val response = whenStoringIssueDocument(documentInfo, document)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response,
            expectedError = EXPECTED_ERROR_FILENAMES_MISMATCH,
        )
    }

    @Test
    fun `calling store issue document with only worksheet numbers that are not found in database should return error`() {
        val documentInfo = givenMockedMultipartFile(
            name = DOCUMENT_INFO_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_INFO_FILENAME,
            contentType = XML_MEDIA_TYPE,
            content = givenDocumentInfoContent(
                documentFilename = DEFAULT_DOCUMENT_FILENAME,
                worksheets = listOf("not_existing")
            )
        )

        val document = givenMockedMultipartFile(
            name = DOCUMENT_PARAM_NAME,
            originalFilename = DEFAULT_DOCUMENT_FILENAME,
            contentType = PDF_MEDIA_TYPE,
            content = givenTestPDFContent()
        )

        val response = whenStoringIssueDocument(documentInfo, document)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
        thenResponseMessageContainsExpectedError(
            response = response,
            expectedError = EXPECTED_ERROR_NO_ISSUES_FOUND,
        )
    }

    // givens
    private fun givenLoggedInAsTechnician() =
        restClient.login(LoginRequest(config.backend.technicianUsername, config.backend.technicianPassword))

    private fun givenMalformedXMLByteArray() = "<document></doc".toByteArray()

    private fun givenDocumentInfoContent(
        documentType: OffaDocumentType? = OffaDocumentType.OFFER,
        documentNumber: String? = "123",
        documentText: String? = "Document Text",
        documentFilename: String? = "filename.pdf",
        projectNumber: String? = "123",
        projectText: String? = "Project Text",
        worksheets: List<String>? = listOf(),
    ): ByteArray {
        val documentInfo = OffaDocumentInfoDto(
            documentType = documentType,
            documentNumber = documentNumber,
            documentText = documentText,
            documentFilename = documentFilename,
            project = OffaProjectDto(
                projectNumber = projectNumber,
                projectText = projectText,
            ),
            worksheets = worksheets?.map {
                OffaWorksheetDto(
                    worksheetNumber = it
                )
            }
        )

        val context: JAXBContext = JAXBContext.newInstance(OffaDocumentInfoDto::class.java)
        val marshaller = context.createMarshaller()
        marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, true)

        val stringWriter = StringWriter()
        marshaller.marshal(documentInfo, stringWriter)
        return stringWriter.toString().toByteArray()
    }

    private fun givenMockedMultipartFile(
        name: String,
        originalFilename: String,
        contentType: String,
        content: ByteArray,
    ) = MockMultipartFile(name, originalFilename, contentType, content)

    private fun givenIssue(issueId: String) = restClient.insertIssue(
        OffaIssueInsertDto(
            issueId,
            "Test Contact Person",
            "Gadollaplatz 1, 8010 Graz",
            OffsetDateTime.now().format(NeubauerDateFormatter.dateTimeFormatter),
            "Es muss etwas repariert werden."
        )
    )

    private fun givenTestPDFContent() = this::class.java.classLoader.getResource("document.pdf")!!.readBytes()

    // when
    private fun whenStoringIssueDocument(documentInfo: MultipartFile? = null, document: MultipartFile? = null) =
        restClient.storeIssueDocument(documentInfo, document)

    // then
    private fun thenStatusCodeIs(statusCode: HttpStatus) =
        assertThat(restClient.lastKnownStatusCode, equalTo(statusCode))

    private fun thenResponseIsSuccess(response: OffaApiResponse<Any>) =
        assert(response.data == "Document stored successfully" && response.error == null)

    private fun thenResponseMessageContainsExpectedError(response: OffaApiResponse<Any>, expectedError: String) =
        assert(response.data == null && response.error!!.message.contains(expectedError))
}
