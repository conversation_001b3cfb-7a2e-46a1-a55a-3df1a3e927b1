package at.derneubauer.backend.rest.test.appapi

import at.derneubauer.backend.rest.client.LoggingInsecureRestClient
import at.derneubauer.backend.util.NeubauerTestConfig
import at.derneubauer.backend.util.TestConfigLoader
import com.natpryce.hamkrest.assertion.assertThat
import com.natpryce.hamkrest.equalTo
import com.natpryce.hamkrest.isEmptyString
import org.apache.commons.codec.binary.Base64
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.http.*
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.HttpClientErrorException
import java.nio.charset.Charset
import java.time.ZoneId
import java.time.ZonedDateTime


@Tag("end2end")
class AppApiTest {

    companion object {
        val PERSON_INSERT_REGEX = "^i\\{.+?\\};(?<personId>.+?);Person;.*".toRegex()
        fun PERSON_INSERT_FOR_USERNAME_REGEX(username: String) = "^i\\{.+?\\};(?<personId>.+?);Person;\\[username;${username}\\|.*".toRegex()
        val ISSUE_INSERT_REGEX = "^i\\{.+?\\};(?<issueId>.+?);Issue;.*".toRegex()
        fun ISSUE_INSERT_FOR_EXTERNAL_ID_REGEX(externalId: String) = "^i\\{.+?\\};.+?;Issue;\\[externalId;${externalId}\\|.*".toRegex()
        fun ISSUE_UPDATE_FOR_EXTERNAL_ID_REGEX(externalId: String) = "^u\\{.+?\\};.+?;Issue;\\[externalId;${externalId}\\|.*".toRegex()
        val ACTION_ASSIGN_ISSUE_REGEX = "^a\\{.+?\\};assignIssue;.*".toRegex()
        fun ACTION_ASSIGN_ISSUE_FOR_TECHNICIAN_AND_ISSUE_REGEX(technicianId: String, issueId: String) = "^a\\{.+?\\};assignIssue;\\[issueId;${issueId}\\|personId;${technicianId}\\]".toRegex()
        val ACTION_REVOKE_ISSUE_REGEX = "^a\\{.+?\\};revokeIssue;.*".toRegex()
        fun ACTION_REVOKE_ISSUE_FOR_TECHNICIAN_AND_ISSUE_REGEX(technicianId: String, issueId: String) = "^a\\{.+?\\};revokeIssue;\\[issueId;${issueId}\\|personId;${technicianId}\\]".toRegex()
        val UPDATE_REGEX = "^u\\{.*".toRegex()
        val DELETE_REGEX = "^d\\{.*".toRegex()
    }

    lateinit var config: NeubauerTestConfig
    lateinit var apiClient: NeubauerAppApiTestClient

    @BeforeEach
    fun setup() {
        config = TestConfigLoader.loadConfig()
        apiClient = NeubauerAppApiTestClient(config.backend.baseUrl, config.backend.technicianUsername, config.backend.technicianPassword)
    }

    @Test
    fun initReturnsOnlyInserts() {
        val response = apiClient.init()
        thenStatusCodeIs(HttpStatus.OK)
        response ?: throw RuntimeException("No response")

        thenResponseHasLinesMatching(ISSUE_INSERT_REGEX, response)
        thenResponseHasNoLinesMatching(UPDATE_REGEX, response)
        thenResponseHasNoLinesMatching(DELETE_REGEX, response)

        thenResponseHasLinesMatching(ACTION_ASSIGN_ISSUE_REGEX, response)
        thenResponseHasLinesMatching(ACTION_REVOKE_ISSUE_REGEX, response)
    }

    @Test
    fun getEventsReturnsUpdatesForOlderIssuesAndInsertsForNewerIssues() {
        val response = apiClient.getEvents(ZonedDateTime.of(2018, 5, 1, 18, 30, 59, 5*1000*1000, ZoneId.of("UTC")))
        thenStatusCodeIs(HttpStatus.OK)
        response ?: throw RuntimeException("No response")

        thenResponseHasLinesMatching(ISSUE_INSERT_FOR_EXTERNAL_ID_REGEX("AS170228"), response)
        thenResponseHasLinesMatching(ISSUE_INSERT_FOR_EXTERNAL_ID_REGEX("AS170229"), response)
        thenResponseHasLinesMatching(ISSUE_INSERT_FOR_EXTERNAL_ID_REGEX("AS170230"), response)

        thenResponseHasLinesMatching(ISSUE_UPDATE_FOR_EXTERNAL_ID_REGEX("AS170223"), response)
        thenResponseHasLinesMatching(ISSUE_UPDATE_FOR_EXTERNAL_ID_REGEX("AS170224"), response)
        thenResponseHasLinesMatching(ISSUE_UPDATE_FOR_EXTERNAL_ID_REGEX("AS170225"), response)
        thenResponseHasLinesMatching(ISSUE_UPDATE_FOR_EXTERNAL_ID_REGEX("AS170226"), response)
        thenResponseHasLinesMatching(ISSUE_UPDATE_FOR_EXTERNAL_ID_REGEX("AS170227"), response)

        thenResponseHasLinesMatching(ACTION_ASSIGN_ISSUE_REGEX, response)
        thenResponseHasLinesMatching(ACTION_REVOKE_ISSUE_REGEX, response)
    }

    @Test
    fun getEventsReturnsNothingIfFutureDateIsUsed() {
        val response = apiClient.getEvents(ZonedDateTime.of(9999, 12, 31, 23, 59, 59, 999*1000*1000, ZoneId.of("UTC")))
        thenStatusCodeIs(HttpStatus.OK)

        assertThat(response ?: "", isEmptyString)
    }

    @Test
    fun returnsAssignedActionAfterAssignAndRevokeActionAfterRevoke() {
        val response = apiClient.init()?: throw RuntimeException("No response")
        thenStatusCodeIs(HttpStatus.OK)

        val personId = response.lines().mapNotNull { PERSON_INSERT_FOR_USERNAME_REGEX(config.backend.technicianUsername).matchEntire(it)?.groups?.get("personId")?.value }.first()
        val issueId = response.lines().mapNotNull { ISSUE_INSERT_REGEX.matchEntire(it)?.groups?.get("issueId")?.value }.last()

        apiClient.assignIssue(issueId)
        val response2 = apiClient.init() ?: throw RuntimeException("No response")
        val hasMatch2 = response2.lines().any { it.matches(ACTION_ASSIGN_ISSUE_FOR_TECHNICIAN_AND_ISSUE_REGEX(personId, issueId)) }
        assertThat(hasMatch2, equalTo(true))

        apiClient.putBackIssue(issueId)
        val response3 = apiClient.init() ?: throw RuntimeException("No response")
        val hasMatch3 = response3.lines().any { it.matches(ACTION_REVOKE_ISSUE_FOR_TECHNICIAN_AND_ISSUE_REGEX(personId, issueId)) }
        assertThat(hasMatch3, equalTo(true))

        apiClient.assignIssue(issueId)
        val response4 = apiClient.init() ?: throw RuntimeException("No response")
        val hasMatch4 = response4.lines().any { it.matches(ACTION_ASSIGN_ISSUE_FOR_TECHNICIAN_AND_ISSUE_REGEX(personId, issueId)) }
        assertThat(hasMatch4, equalTo(true))
    }

    @Test
    fun acceptedIssueCanBeMarkedAsDoneByTechnician() {
        val response = apiClient.init()?: throw RuntimeException("No response")
        thenStatusCodeIs(HttpStatus.OK)

        val personId = response.lines().mapNotNull { PERSON_INSERT_FOR_USERNAME_REGEX(config.backend.technicianUsername).matchEntire(it)?.groups?.get("personId")?.value }.first()
        val issueId = response.lines().mapNotNull { ISSUE_INSERT_REGEX.matchEntire(it)?.groups?.get("issueId")?.value }.last()

        apiClient.assignIssue(issueId)
        val response2 = apiClient.init() ?: throw RuntimeException("No response")
        val hasMatch2 = response2.lines().any { it.matches(ACTION_ASSIGN_ISSUE_FOR_TECHNICIAN_AND_ISSUE_REGEX(personId, issueId)) }
        assertThat(hasMatch2, equalTo(true))

        apiClient.markIssueAsTechnicianDone(issueId)
        apiClient.init() ?: throw RuntimeException("No response")
        val hasMatch3 = response2.lines().any { it.matches(ACTION_ASSIGN_ISSUE_FOR_TECHNICIAN_AND_ISSUE_REGEX(personId, issueId)) }
        assertThat(hasMatch3, equalTo(true))
    }

    // given

    // when

    // then

    private fun thenStatusCodeIs(expectedStatus: HttpStatus) {
        assertThat(apiClient.lastKnownStatusCode, equalTo(expectedStatus))
    }

    private fun thenResponseHasLinesMatching(regex: Regex, response: String) {
        val hasMatches = response.lines().find { it.matches(regex) }?.isNotEmpty()
        assertThat(hasMatches, equalTo(true))
    }

    private fun thenResponseHasNoLinesMatching(regex: Regex, response: String) {
        val hasNoMatches = response.lines().none { it.matches(regex) }
        assertThat(hasNoMatches, equalTo(true))
    }

    // helpers

}
class NeubauerAppApiTestClient : LoggingInsecureRestClient {
    private val AUTH_TOKEN_HEADER_NAME = "Authorization"
    private val backendBaseUrl: String
    private var user: String
    private var password: String
    var lastKnownStatusCode: HttpStatusCode? = null

    constructor(backendBaseUrl: String, user: String, password: String) {
        this.backendBaseUrl = "${backendBaseUrl}/neubauer-web/ClientConnector"
        this.user = user
        this.password = password
    }

    fun init(): String? {
        lastKnownStatusCode = null
        try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("action", "init")
            bodyMap.add("lastEventId", "0")
            val response = restTemplate().exchange(backendBaseUrl, HttpMethod.POST, entityWithAuth(bodyMap), String::class.java)
            lastKnownStatusCode = response.statusCode
            return response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            return null
        }
    }

    fun getEvents(since: ZonedDateTime): String? {
        lastKnownStatusCode = null
        try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("action", "getEvents")
            bodyMap.add("lastEventId", (since.toInstant().toEpochMilli() * 100000).toString())
            val response = restTemplate().exchange(backendBaseUrl, HttpMethod.POST, entityWithAuth(bodyMap), String::class.java)
            lastKnownStatusCode = response.statusCode
            return response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            return null
        }
    }

    fun assignIssue(issueId: String): String? {
        lastKnownStatusCode = null
        try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("action", "assignIssue")
            bodyMap.add("issueId", issueId)
            bodyMap.add("lastEventId", "0")
            val response = restTemplate().exchange(backendBaseUrl, HttpMethod.POST, entityWithAuth(bodyMap), String::class.java)
            lastKnownStatusCode = response.statusCode
            return response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            return null
        }
    }

    fun putBackIssue(issueId: String): String? {
        lastKnownStatusCode = null
        try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("action", "revokeIssue")
            bodyMap.add("issueId", issueId)
            bodyMap.add("lastEventId", "0")
            val response = restTemplate().exchange(backendBaseUrl, HttpMethod.POST, entityWithAuth(bodyMap), String::class.java)
            lastKnownStatusCode = response.statusCode
            return response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            return null
        }
    }

    fun markIssueAsTechnicianDone(issueId: String): String? {
        lastKnownStatusCode = null
        try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("action", "closeIssue")
            bodyMap.add("issueId", issueId)
            bodyMap.add("lastEventId", "0")
            val response = restTemplate().exchange(backendBaseUrl, HttpMethod.POST, entityWithAuth(bodyMap), String::class.java)
            lastKnownStatusCode = response.statusCode
            return response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            return null
        }
    }

    private fun entityWithAuth(request: Any? = null, isMultiPart: Boolean = false): HttpEntity<Any?> {
        val headers = HttpHeaders()
        val encodedAuth = String(Base64.encodeBase64("${user}:${password}".toByteArray(Charset.forName("US-ASCII"))))
        headers.set(AUTH_TOKEN_HEADER_NAME, "Basic ${encodedAuth}")
        headers.set("Content-Type", "application/x-www-form-urlencoded")

        if (isMultiPart) {
            headers.contentType = MediaType.MULTIPART_FORM_DATA
        }

        val entity = HttpEntity<Any?>(request, headers)
        return entity
    }
}
