package at.derneubauer.backend.rest.test

import at.derneubauer.backend.rest.LoginRequest
import at.derneubauer.backend.rest.client.NeubauerRestTestClient
import at.derneubauer.backend.util.NeubauerTestConfig
import at.derneubauer.backend.util.TestConfigLoader
import com.natpryce.hamkrest.assertion.assertThat
import com.natpryce.hamkrest.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus


@Tag("end2end")
class RegisterTest {

    lateinit var config: NeubauerTestConfig
    lateinit var restClient: NeubauerRestTestClient

    @BeforeEach
    fun setup() {
        config = TestConfigLoader.loadConfig()
        restClient = NeubauerRestTestClient(config.backend.baseUrl)
    }

    @Test
    fun loginSucceedsWithValidCredentials() {
        whenTryingToLogInWith(config.backend.username, config.backend.password)
        thenStatusCodeIs(HttpStatus.OK)
        thenCallRequiringAuthCanBePerformed()
    }

    @Test
    fun loginFailsWithUnknownUsername() {
        whenTryingToLogInWith("i-do-not-exist", config.backend.password)
        thenStatusCodeIs(HttpStatus.UNAUTHORIZED)
        thenCallRequiringAuthFails()
    }

    @Test
    fun loginFailsWithWrongPassword() {
        whenTryingToLogInWith(config.backend.username, "wrong-password")
        thenStatusCodeIs(HttpStatus.UNAUTHORIZED)
        thenCallRequiringAuthFails()
    }

    // given

    // when

    private fun whenTryingToLogInWith(email: String, password: String) {
        restClient.login(LoginRequest(email, password))
    }

    // then

    private fun thenCallRequiringAuthCanBePerformed() {
        restClient.permissions()
        thenStatusCodeIs(HttpStatus.OK)
    }

    private fun thenCallRequiringAuthFails() {
        restClient.permissions()
        thenStatusCodeIs(HttpStatus.UNAUTHORIZED)
    }

    private fun thenStatusCodeIs(expectedStatus: HttpStatus) {
        assertThat(restClient.lastKnownStatusCode, equalTo(expectedStatus))
    }

    // helpers

}
