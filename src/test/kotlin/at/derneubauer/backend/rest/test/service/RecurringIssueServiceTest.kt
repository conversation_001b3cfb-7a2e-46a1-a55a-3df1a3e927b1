package at.derneubauer.backend.rest.test.service

import at.derneubauer.backend.db.issue.RecurringIssueMapper
import at.derneubauer.backend.db.resourceplan.IssuesTechniciansAssignmentDo
import at.derneubauer.backend.db.resourceplan.IssuesTechniciansAssignmentMapper
import at.derneubauer.backend.service.*
import at.derneubauer.backend.web.userlist.RecurringIssueTimeUnit
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations
import java.time.ZoneId
import java.time.ZonedDateTime

@Tag("end2end")
class RecurringIssueServiceTest {

    @Mock
    private lateinit var issuesTechniciansAssignmentMapper: IssuesTechniciansAssignmentMapper

    @Mock
    private lateinit var recurringIssueMapper: RecurringIssueMapper

    @Mock
    private lateinit var resourcePlanService: ResourcePlanService

    private lateinit var recurringIssueService: RecurringIssueService

    @BeforeEach
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        recurringIssueService = RecurringIssueService(recurringIssueMapper, issuesTechniciansAssignmentMapper, resourcePlanService)
    }

    fun <T> any(): T = Mockito.any<T>()


    @Test
    fun testGenerateRecurringIssuesCorrectCount() {
        val date = ZonedDateTime.now()
        givenIssueExistsForDate(date)

        whenGeneratingIssues(3, 1, RecurringIssueTimeUnit.DAYS)

        thenNPlaceholdersGenerated(3)
    }

    @Test
    fun testGenerateRecurringIssuesForDaysSmallInterval() {

        val firstDate = generateDate(2020, 1, 1)
        givenIssueExistsForDate(firstDate)

        whenGeneratingIssues(3, 1, RecurringIssueTimeUnit.DAYS)

        val dates = listOf(
                generateDate(2020, 1, 2),
                generateDate(2020, 1, 3),
                generateDate(2020, 1, 4)
        )

        thenGeneratedPlaceholdersForDates(dates)
    }

    @Test
    fun testGenerateRecurringIssuesForDaysLargerInterval() {

        val firstDate = generateDate(2020, 1, 1)
        givenIssueExistsForDate(firstDate)

        whenGeneratingIssues(6, 7, RecurringIssueTimeUnit.DAYS)

        val dates = listOf(
                generateDate(2020, 1, 8),
                generateDate(2020, 1, 15),
                generateDate(2020, 1, 22),
                generateDate(2020, 1, 29),
                generateDate(2020, 2, 5),
                generateDate(2020, 2, 12)
        )

        thenGeneratedPlaceholdersForDates(dates)
    }

    @Test
    fun testGenerateRecurringIssuesForMonthInterval() {

        val firstDate = generateDate(2020, 1, 1)
        givenIssueExistsForDate(firstDate)

        whenGeneratingIssues(4, 2, RecurringIssueTimeUnit.MONTHS)

        val dates = listOf(
                generateDate(2020, 3, 1),
                generateDate(2020, 5, 1),
                generateDate(2020, 7, 1),
                generateDate(2020, 9, 1)
        )

        thenGeneratedPlaceholdersForDates(dates)
    }

    @Test
    fun testGenerateRecurringIssuesForSemesterInterval() {

        val firstDate = generateDate(2020, 1, 1)
        givenIssueExistsForDate(firstDate)

        whenGeneratingIssues(4, 6, RecurringIssueTimeUnit.MONTHS)

        val dates = listOf(
                generateDate(2020, 7, 1),
                generateDate(2021, 1, 1),
                generateDate(2021, 7, 1),
                generateDate(2022, 1, 1)
        )

        thenGeneratedPlaceholdersForDates(dates)
    }

    @Test
    fun testGenerateRecurringIssuesForYearInterval() {

        val firstDate = generateDate(2020, 1, 1)
        givenIssueExistsForDate(firstDate)

        whenGeneratingIssues(4, 12, RecurringIssueTimeUnit.MONTHS)

        val dates = listOf(
                generateDate(2021, 1, 1),
                generateDate(2022, 1, 1),
                generateDate(2023, 1, 1),
                generateDate(2024, 1, 1)
        )

        thenGeneratedPlaceholdersForDates(dates)
    }

    private fun givenIssueExistsForDate(date: ZonedDateTime) {
        `when`(issuesTechniciansAssignmentMapper.findByIssueId(1)).then {
            IssuesTechniciansAssignmentDo(
                    1, 1, 1, "1", null, "NEW", date, date, date, date
            )
        }
    }

    private fun whenGeneratingIssues(repetitions: Int, interval: Long, unit: RecurringIssueTimeUnit) {
        recurringIssueService.generateRecurringIssues(
                issueId = 1,
                repetitions = repetitions,
                interval = interval,
                unit = unit
        )
    }

    private fun thenNPlaceholdersGenerated(n: Int) {
        verify(recurringIssueMapper, times(n)).createRecurringIssue(issueId = eq(1L), subIssueId = eq(null),  placeholderDateFrom = any(), placeholderDateTo = any(), userId = eq(1L))
    }

    private fun thenGeneratedPlaceholdersForDates(dates: List<ZonedDateTime>) {
        dates.forEach {
            verify(recurringIssueMapper).createRecurringIssue(issueId = 1L, subIssueId = null, userId = 1, placeholderDateFrom = it, placeholderDateTo = it)
        }
    }

    private fun generateDate(year: Int, month: Int, day: Int): ZonedDateTime {
        return ZonedDateTime.of(year, month, day, 8, 0, 0, 0, ZoneId.systemDefault())
    }
}