package at.derneubauer.backend.rest.test.appapi

import at.derneubauer.backend.offa.db.OffaIssueInsertDto
import at.derneubauer.backend.rest.LoginRequest
import at.derneubauer.backend.rest.LoginResponse
import at.derneubauer.backend.rest.app.IssueSyncDto
import at.derneubauer.backend.rest.app.NoteRequest
import at.derneubauer.backend.rest.app.SyncResponse
import at.derneubauer.backend.rest.app.SyncStatus
import at.derneubauer.backend.rest.client.NeubauerRestTestClient
import at.derneubauer.backend.service.NeubauerIssueStatus
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.util.NeubauerTestConfig
import at.derneubauer.backend.util.TestConfigLoader
import com.natpryce.hamkrest.anything
import com.natpryce.hamkrest.assertion.assertThat
import com.natpryce.hamkrest.equalTo
import com.natpryce.hamkrest.hasElement
import com.natpryce.hamkrest.isEmpty
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import java.time.ZonedDateTime
import java.util.*

@Tag("end2end")
class AppSyncTest {

    lateinit var config: NeubauerTestConfig
    lateinit var restClient: NeubauerRestTestClient
    var loginResponse: LoginResponse? = null
    var syncResponse: SyncResponse? = null
    lateinit var externalIssueId: String

    @BeforeEach
    fun setup() {
        config = TestConfigLoader.loadConfig()
        restClient = NeubauerRestTestClient(config.backend.baseUrl)
        externalIssueId = "TI_${UUID.randomUUID().toString()}"
    }

    @Test
    fun initialSyncReturnsData() {
        givenLoggedInAsTechnician()

        whenPerformingInitialSync()

        thenSyncResponseContainsIssues()
        thenSyncResponseContainsTechnicians()
        thenSyncResponseContainsAcceptedIssues()
        thenSyncResponseContainsLoggedInTechnician()
        thenSyncResponseContainsImages()
    }

    @Test
    fun partialSyncSkipsIssues() {
        givenLoggedInAsTechnician()

        whenPerformingInitialSync()
        val initialSyncResponse = syncResponse!!

        val issue = initialSyncResponse.issues.last().issue
        val t = issue.updatedAt.minusSeconds(1)
        whenPerformingPartialSyncFrom(t)

        thenSyncResponseContainsIssues()
        assertThat(syncResponse!!.issues.filter { it.issue.updatedAt <= t }, isEmpty)
        assertThat(syncResponse!!.issues.map { it.issue }, hasElement(issue))
    }

    @Test
    fun partialSyncSkipsTechnicians() {
        givenLoggedInAsTechnician()

        whenPerformingInitialSync()
        val initialSyncResponse = syncResponse!!

        val technician = initialSyncResponse.technicians.last().technician
        val t = technician.updatedAt.minusSeconds(1)
        whenPerformingPartialSyncFrom(t)

        thenSyncResponseContainsTechnicians()
        assertThat(syncResponse!!.technicians.filter { it.technician.updatedAt <= t }, isEmpty)
        assertThat(syncResponse!!.technicians.map { it.technician }, hasElement(technician))
    }

    @Test
    fun partialSyncSkipsAcceptedIssues() {
        givenLoggedInAsTechnician()

        whenPerformingInitialSync()
        val initialSyncResponse = syncResponse!!

        val acceptedIssue = initialSyncResponse.acceptedIssues.last().acceptedIssue
        val t = acceptedIssue.createdAt.minusSeconds(1)
        whenPerformingPartialSyncFrom(t)

        thenSyncResponseContainsAcceptedIssues()
        assertThat(syncResponse!!.acceptedIssues.filter { it.acceptedIssue.createdAt <= t }, isEmpty)
        assertThat(syncResponse!!.acceptedIssues.map { it.acceptedIssue }, hasElement(acceptedIssue))
    }

    @Test
    fun partialSyncReturnsNewlyAcceptedIssueEvent() {
        givenLoggedInAsTechnician()
        givenIssue(newIssue(externalIssueId))

        whenPerformingInitialSync()
        val initialSyncResponse = syncResponse!!

        val issue = initialSyncResponse.issues.map { it.issue }.first { it.externalId == externalIssueId }

        restClient.claimIssue(issue.id)

        whenPerformingPartialSyncFrom(initialSyncResponse.latestTimestamp)

        assertThat(syncResponse!!.acceptedIssues, !isEmpty)

        val partialSyncAcceptedIssue = syncResponse!!.acceptedIssues.filter { it.syncStatus == SyncStatus.INSERTED && it.acceptedIssue.issueId == issue.id && it.acceptedIssue.technicianId == loginResponse!!.userId }
        assertThat(partialSyncAcceptedIssue, anything)
    }

    @Test
    fun canMarkIssueDoneWhenAccepted() {
        givenLoggedInAsTechnician()
        givenIssue(newIssue(externalIssueId))

        whenPerformingInitialSync()
        var issue = syncResponse!!.issues.map { it.issue }.first { it.externalId == externalIssueId }

        whenClaimingIssue(issue.id)
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        whenMarkingIssueAsDone(issue.id)
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)
        assertThat(issue.status, equalTo(NeubauerIssueStatus.DONE_TECHNICIAN.getName()))
    }

    @Test
    fun canNotMarkIssueDoneWhenNotAccepted() {
        givenLoggedInAsTechnician()
        givenIssue(newIssue(externalIssueId))

        whenPerformingInitialSync()
        var issue = syncResponse!!.issues.map { it.issue }.first { it.externalId == externalIssueId }

        whenMarkingIssueAsDone(issue.id)
        thenStatusCodeIs(HttpStatus.BAD_REQUEST)
    }

    @Test
    fun canPutBackIssueWhenAccepted() {
        givenLoggedInAsTechnician()
        givenIssue(newIssue(externalIssueId))

        whenPerformingInitialSync()
        var issue = syncResponse!!.issues.map { it.issue }.first { it.externalId == externalIssueId }

        whenClaimingIssue(issue.id)
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        whenPuttingBackIssue(issue.id)
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)

        assertThat(issue.status, equalTo(NeubauerIssueStatus.NEW.getName()))
    }

    @Test
    fun canReopenIssueWhenDone() {
        givenLoggedInAsTechnician()
        givenIssue(newIssue(externalIssueId))

        whenPerformingInitialSync()
        var issue = syncResponse!!.issues.map { it.issue }.first { it.externalId == externalIssueId }

        whenClaimingIssue(issue.id)
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        whenMarkingIssueAsDone(issue.id)
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)
        assertThat(issue.status, equalTo(NeubauerIssueStatus.DONE_TECHNICIAN.getName()))

        whenReopenIssue(issue.id)
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)
        assertThat(issue.status, equalTo(NeubauerIssueStatus.ACCEPTED.getName()))
    }

    @Test
    fun addNote() {
        givenLoggedInAsTechnician()
        givenIssue(newIssue(externalIssueId))

        whenPerformingInitialSync()
        var issue = syncResponse!!.issues.map { it.issue }.first { it.externalId == externalIssueId }

        val date = ZonedDateTime.now()
        val note = "test note"
        var expectedNote = generateNote(config.backend.technicianUsername, date, note)

        whenAddNote(issue.id, NoteRequest(note = note, date = date))
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)
        assertThat(issue.note, equalTo(expectedNote))

        //test if new message is appended and does not replace old message
        val date2 = ZonedDateTime.now()
        whenAddNote(issue.id, NoteRequest(note = note, date = date2))

        expectedNote += "\n\n" + generateNote(config.backend.technicianUsername, date2, note)
        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)
        assertThat(issue.note, equalTo(expectedNote))
    }

    @Test
    fun addNoteWithEmojiShouldWork() {
        givenLoggedInAsTechnician()
        givenIssue(newIssue(externalIssueId))

        whenPerformingInitialSync()
        var issue = syncResponse!!.issues.map { it.issue }.first { it.externalId == externalIssueId }

        val date = ZonedDateTime.now()
        val note = "test note \uD83D\uDE00"
        var expectedNote = generateNote(config.backend.technicianUsername, date, note)

        whenAddNote(issue.id, NoteRequest(note = note, date = date))
        thenStatusCodeIs(HttpStatus.NO_CONTENT)

        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)
        assertThat(issue.note, equalTo(expectedNote))

        //test if new message is appended and does not replace old message
        val date2 = ZonedDateTime.now()
        whenAddNote(issue.id, NoteRequest(note = note, date = date2))

        expectedNote += "\n\n" + generateNote(config.backend.technicianUsername, date2, note)
        issue = getIssueAfterPartialSync(syncResponse!!.latestTimestamp!!)
        assertThat(issue.note, equalTo(expectedNote))
    }

    // given
    fun givenLoggedInAsTechnician() {
        loginResponse = restClient.login(LoginRequest(config.backend.technicianUsername, config.backend.technicianPassword))
        thenStatusCodeIs(HttpStatus.OK)
    }

    fun givenIssue(issue: OffaIssueInsertDto) {
        restClient.insertIssue(issue)
    }

    // when
    fun whenPerformingInitialSync() {
        syncResponse = restClient.appSync(syncFrom = null)
    }

    fun whenPerformingPartialSyncFrom(syncFrom: ZonedDateTime?) {
        syncResponse = restClient.appSync(syncFrom)
    }

    fun whenClaimingIssue(issueId: Long) {
        restClient.claimIssue(issueId)
    }

    fun whenMarkingIssueAsDone(issueId: Long) {
        restClient.markDoneIssue(issueId)
    }

    fun whenPuttingBackIssue(issueId: Long) {
        restClient.putBackIssue(issueId)
    }

    fun whenReopenIssue(issueId: Long) {
        restClient.reopenIssue(issueId)
    }

    fun whenAddNote(issueId: Long, note: NoteRequest) {
        restClient.addNote(issueId, note)
    }

    // then
    fun thenStatusCodeIs(statusCode: HttpStatus) {
        assertThat(restClient.lastKnownStatusCode, equalTo(statusCode))
    }

    fun thenSyncResponseContainsIssues() {
        assertThat(syncResponse!!.issues, !isEmpty)
    }

    fun thenSyncResponseContainsTechnicians() {
        assertThat(syncResponse!!.technicians, !isEmpty)
    }

    fun thenSyncResponseContainsAcceptedIssues() {
        assertThat(syncResponse!!.acceptedIssues, !isEmpty)
    }

    fun thenSyncResponseContainsLoggedInTechnician() {
        assertThat(syncResponse!!.technicians.map { it.technician.id }, hasElement(loginResponse!!.userId))
    }

    fun thenSyncResponseContainsImages(){
        assertThat(syncResponse!!.images, !isEmpty)
    }

    // helper
    fun newIssue(externalIssueId: String): OffaIssueInsertDto {
        return OffaIssueInsertDto(
                externalIssueId,
                "Test Contact Person",
                "Gadollaplatz 1, 8010 Graz",
                ZonedDateTime.now().format(NeubauerDateFormatter.dateTimeFormatter),
                "Es muss etwas repariert werden."
        )
    }

    private fun getIssueAfterPartialSync(latestTimestamp: ZonedDateTime): IssueSyncDto {
        whenPerformingPartialSyncFrom(latestTimestamp)
        return syncResponse!!.issues.map { it.issue }.first { it.externalId == externalIssueId }
    }

    private fun generateNote(username: String, date: ZonedDateTime, message: String): String {
        return """
            $username, ${NeubauerDateFormatter.dateTimeFormatter.format(date)}
            $message
        """.trimIndent()
    }
}
