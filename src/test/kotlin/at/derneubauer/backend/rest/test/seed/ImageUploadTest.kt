package at.derneubauer.backend.rest.test.seed

import at.derneubauer.backend.rest.LoginRequest
import at.derneubauer.backend.rest.client.NeubauerRestTestClient
import at.derneubauer.backend.util.NeubauerTestConfig
import at.derneubauer.backend.util.TestConfigLoader
import com.natpryce.hamkrest.assertion.assertThat
import com.natpryce.hamkrest.equalTo
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import java.io.File
import java.util.zip.CRC32
import java.util.zip.CheckedInputStream

@Tag("end2end")
class ImageUploadTest {

    lateinit var config: NeubauerTestConfig
    lateinit var restClient: NeubauerRestTestClient

    @BeforeEach
    fun setup() {
        config = TestConfigLoader.loadConfig()
        restClient = NeubauerRestTestClient(config.backend.baseUrl)
    }

    @Test
    fun uploadUserImages() {
        givenLoggedInAsTechnician()

        uploadUserImage("images/1.jpg")
        uploadUserImage("images/2.jpg")
        uploadUserImage("images/3.jpg")
        uploadUserImage("images/4.jpg")
        uploadUserImage("images/5.jpg")
        uploadUserImage("images/6.jpg")
        uploadUserImage("images/7.jpg")
        uploadUserImage("images/8.jpg")
        uploadUserImage("images/9.jpg")
        uploadUserImage("images/10.jpg")
    }

    // TODO convert test to use issue image upload API for app, once implemented, also remove test support endpoint
    @Test
    fun seedIssueImages() {
        givenLoggedIn()

        val issueId: Long = 2
        uploadIssueImage("images/1.jpg", issueId)
        uploadIssueImage("images/2.jpg", issueId)
        uploadIssueImage("images/3.jpg", issueId)
        uploadIssueImage("images/4.jpg", issueId)
        uploadIssueImage("images/5.jpg", issueId)
        uploadIssueImage("images/6.jpg", issueId)
        uploadIssueImage("images/7.jpg", issueId)
        uploadIssueImage("images/8.jpg", issueId)
        uploadIssueImage("images/9.jpg", issueId)
        uploadIssueImage("images/10.jpg", issueId)
    }

    @Test
    fun userImageWithWrongChecksumIsRejected() {
        givenLoggedInAsTechnician()

        uploadUserImage("images/1.jpg", 9999, HttpStatus.BAD_REQUEST)
    }

    private fun uploadUserImage(imagePath: String, checksumOverride: Long? = null, expectedHttpStatusOverride: HttpStatus? = null) {
        val path = this::class.java.classLoader.getResource(imagePath).file
        val file = File(path)
        val crc32Checksum = checksumOverride ?: crc32Checksum(file)
        restClient.uploadUserImage(file, crc32Checksum)
        val expectedStatusCode = expectedHttpStatusOverride ?: HttpStatus.NO_CONTENT
        assertThat(restClient.lastKnownStatusCode, equalTo(expectedStatusCode))
    }

    private fun uploadIssueImage(imagePath: String, issueId: Long) {
        val path = this::class.java.classLoader.getResource(imagePath).file
        val file = File(path)
        restClient.uploadIssueImage(issueId, file)
    }

    // given

    // when

    private fun givenLoggedIn() {
        restClient.login(LoginRequest(config.backend.username, config.backend.password))
    }

    private fun givenLoggedInAsTechnician() {
        restClient.login(LoginRequest(config.backend.technicianUsername, config.backend.technicianPassword))
    }

    // helpers

    private fun crc32Checksum(file: File): Long {
        file.inputStream().use { inputStream ->
            CheckedInputStream(inputStream, CRC32()).use { cis ->
                val buf = ByteArray(128)
                while (cis.read(buf) >= 0) {}

                return cis.checksum.value
            }
        }
    }

}
