package at.derneubauer.backend.rest.client

import at.derneubauer.backend.offa.db.OffaIssueInsertDto
import at.derneubauer.backend.offa.rest.OffaApiError
import at.derneubauer.backend.offa.rest.OffaApiResponse
import at.derneubauer.backend.rest.LoginRequest
import at.derneubauer.backend.rest.LoginResponse
import at.derneubauer.backend.rest.app.NoteRequest
import at.derneubauer.backend.rest.app.SyncResponse
import at.derneubauer.backend.rest.permission.PermissionResponse
import at.derneubauer.backend.util.NeubauerDateFormatter
import at.derneubauer.backend.util.NeubauerOffaTestConfig
import org.apache.hc.client5.http.utils.Base64
import org.springframework.core.io.FileSystemResource
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatusCode
import org.springframework.http.MediaType
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.util.UriComponentsBuilder
import java.io.File
import java.net.URI
import java.nio.charset.Charset
import java.time.ZonedDateTime


class NeubauerRestTestClient(
    private val backendBaseUrl: String,
    private val offaConfig: NeubauerOffaTestConfig? = null,
) : LoggingInsecureRestClient() {
    private val AUTH_TOKEN_HEADER_NAME = "x-auth-token"
    private val restBaseUrl: String
    private var lastKnownAuthToken: String? = null
    var lastKnownStatusCode: HttpStatusCode? = null

    init {
        this.restBaseUrl = "${backendBaseUrl}/api/v1"
    }

    fun login(request: LoginRequest): LoginResponse? {
        lastKnownStatusCode = null
        return try {
            val response = restTemplate().postForEntity("${restBaseUrl}/login", request, LoginResponse::class.java)
            lastKnownAuthToken = response.headers[AUTH_TOKEN_HEADER_NAME]?.firstOrNull()
            lastKnownStatusCode = response.statusCode
            response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            null
        }
    }

    fun logout() {
        lastKnownStatusCode = null
        try {
            val response = restTemplate().exchange("${restBaseUrl}/logout", HttpMethod.POST, entityWithAuth(), Void::class.java)
            lastKnownAuthToken = null
            lastKnownStatusCode = response.statusCode
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
        }
    }

    fun appSync(syncFrom: ZonedDateTime?): SyncResponse? {
        lastKnownStatusCode = null

        val urlBuilder = UriComponentsBuilder.newInstance()
        urlBuilder.uri(URI("${restBaseUrl}/app/sync"))

        if (syncFrom != null) {
            urlBuilder.queryParam("sync-from", NeubauerDateFormatter.dateTimeWithTzFormatter.format(syncFrom))
        }

        val uri = urlBuilder.build(true).toUri()

        return try {
            val response = restTemplate().exchange(uri, HttpMethod.GET, entityWithAuth(), SyncResponse::class.java)
            lastKnownStatusCode = response.statusCode
            response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            null
        }
    }

    fun claimIssue(issueId: Long) {
        lastKnownStatusCode = try {
            val response = restTemplate().exchange("${restBaseUrl}/app/issue/${issueId}/claim", HttpMethod.POST, entityWithAuth(), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun markDoneIssue(issueId: Long) {
        lastKnownStatusCode = try {
            val response = restTemplate().exchange("${restBaseUrl}/app/issue/${issueId}/mark-done", HttpMethod.POST, entityWithAuth(), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun putBackIssue(issueId: Long) {
        lastKnownStatusCode = try {
            val response = restTemplate().exchange("${restBaseUrl}/app/issue/${issueId}/put-back", HttpMethod.POST, entityWithAuth(), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun reopenIssue(issueId: Long) {
        lastKnownStatusCode = try {
            val response = restTemplate().exchange("${restBaseUrl}/app/issue/${issueId}/reopen", HttpMethod.POST, entityWithAuth(), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun addNote(issueId: Long, note: NoteRequest) {
        lastKnownStatusCode = try {
            val response = restTemplate().exchange("${restBaseUrl}/app/issue/${issueId}/add-note", HttpMethod.POST, entityWithAuth(note), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun insertIssue(issue: OffaIssueInsertDto) {
        lastKnownStatusCode = try {
            val response = restTemplate().exchange("${restBaseUrl}/test-support/issues", HttpMethod.POST, entityWithAuth(issue), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun permissions(): PermissionResponse? {
        return try {
            val response = restTemplate().exchange("${restBaseUrl}/permissions", HttpMethod.GET, entityWithAuth(), PermissionResponse::class.java)
            lastKnownStatusCode = response.statusCode
            response.body
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            null
        }
    }

    fun uploadUserImage(image: File, crc32Checksum: Long) {
        lastKnownStatusCode = try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("crc32Checksum", crc32Checksum)
            bodyMap.add("file", FileSystemResource(image))
            val response = restTemplate().exchange("${restBaseUrl}/images/user-image-upload", HttpMethod.POST, entityWithAuth(bodyMap, isMultiPart = true), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun uploadIssueImage(issueId: Long, image: File) {
        lastKnownStatusCode = try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("issueId", issueId)
            bodyMap.add("file", FileSystemResource(image))
            val response = restTemplate().exchange("${restBaseUrl}/test-support/issue-image-upload", HttpMethod.POST, entityWithAuth(bodyMap, isMultiPart = true), Void::class.java)
            response.statusCode
        } catch (e: HttpClientErrorException) {
            e.statusCode
        }
    }

    fun storeIssueDocument(
        documentInfo: MultipartFile? = null,
        document: MultipartFile? = null
    ): OffaApiResponse<Any> {
        try {
            val bodyMap = LinkedMultiValueMap<String, Any>()
            bodyMap.add("documentInfo", documentInfo?.resource)
            bodyMap.add("document", document?.resource)

            val response = restTemplate().exchange(
                "${backendBaseUrl}/offa/issue/documents",
                HttpMethod.POST,
                entityWithBasicAuthForOffaEndpoints(bodyMap),
                OffaApiResponse::class.java
            )

            lastKnownStatusCode = response.statusCode

            return OffaApiResponse(
                data = response.body.data,
                error = response.body.error,
            )
        } catch (e: HttpClientErrorException) {
            lastKnownStatusCode = e.statusCode
            val errorMessage = e.responseBodyAsString
            return OffaApiResponse(
                error = OffaApiError(
                    message = errorMessage,
                    statusCode = e.statusCode.value(),
                )
            )
        }
    }

    private fun entityWithAuth(request: Any? = null, isMultiPart: Boolean = false): HttpEntity<Any?> {
        val headers = HttpHeaders()
        headers.set(AUTH_TOKEN_HEADER_NAME, lastKnownAuthToken)

        if (isMultiPart) {
            headers.contentType = MediaType.MULTIPART_FORM_DATA
        }

        val entity = HttpEntity<Any?>(request, headers)
        return entity
    }

    private fun entityWithBasicAuthForOffaEndpoints(request: Any? = null): HttpEntity<Any?> {
        val headers = HttpHeaders()

        val auth = "${offaConfig?.username}:${offaConfig?.password}"
        val encodedAuth = Base64.encodeBase64(auth.toByteArray(Charset.forName("US-ASCII")))
        val authHeader = "Basic ${String(encodedAuth)}"
        headers.set("Authorization", authHeader)

        headers.contentType = MediaType.MULTIPART_FORM_DATA

        return HttpEntity<Any?>(request, headers)
    }
}
