package at.derneubauer.backend.rest.client

import org.apache.hc.client5.http.impl.classic.CloseableHttpClient
import org.apache.hc.client5.http.impl.classic.HttpClients
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder
import org.apache.http.ssl.SSLContexts
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.web.client.RestTemplate
import java.security.cert.X509Certificate


open class LoggingInsecureRestClient {

    protected fun restTemplate(): RestTemplate {
        val acceptingTrustStrategy = { _: Array<X509Certificate>, _: String -> true }

        val httpClient: CloseableHttpClient = HttpClients.custom()
            .setConnectionManager(
                PoolingHttpClientConnectionManagerBuilder.create()
                    .setSSLSocketFactory(
                        SSLConnectionSocketFactoryBuilder.create()
                            .setSslContext(
                                SSLContexts.custom()
                                    .loadTrustMaterial(null, acceptingTrustStrategy)
                                    .build()
                            )
                            .setHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                            .build()
                    )
                    .build()
            )
            .build()

        val httpRequestFactory = HttpComponentsClientHttpRequestFactory()
        httpRequestFactory.httpClient = httpClient
        val requestFactory = BufferingClientHttpRequestFactory(httpRequestFactory)

        val restTemplate = RestTemplate(requestFactory)
        restTemplate.interceptors = listOf(LoggingRequestInterceptor())

        restTemplate.messageConverters.add(0, jacksonConverter())

        return restTemplate
    }

    private fun jacksonConverter(): org.springframework.http.converter.json.MappingJackson2HttpMessageConverter {
        val converter = org.springframework.http.converter.json.MappingJackson2HttpMessageConverter()
        converter.objectMapper = objectMapper()
        return converter
    }

    private fun objectMapper(): com.fasterxml.jackson.databind.ObjectMapper {
        val builder = org.springframework.http.converter.json.Jackson2ObjectMapperBuilder.json()
        val objectMapper: com.fasterxml.jackson.databind.ObjectMapper = builder.createXmlMapper(false).build()
        objectMapper.registerModule(com.fasterxml.jackson.datatype.jsr310.JavaTimeModule())
        objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
        return objectMapper
    }
}
