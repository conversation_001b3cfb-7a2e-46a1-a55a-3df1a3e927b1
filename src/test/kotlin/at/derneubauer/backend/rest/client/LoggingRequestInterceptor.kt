package at.derneubauer.backend.rest.client

import org.slf4j.LoggerFactory
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader

class LoggingRequestInterceptor : ClientHttpRequestInterceptor {

    @Throws(IOException::class)
    override fun intercept(request: HttpRequest, body: ByteArray, execution: ClientHttpRequestExecution): ClientHttpResponse {
        traceRequest(request, body)

        val response = execution.execute(request, body)
        traceResponse(response)

        return response
    }

    @Throws(IOException::class)
    private fun traceRequest(request: HttpRequest, body: ByteArray) {
        log.debug("===========================request begin================================================")
        log.debug("URI         : {}", request.getURI())
        log.debug("Method      : {}", request.getMethod())
        log.debug("Headers     : {}", request.getHeaders())
        log.debug("Request body: {}", String(body))
        log.debug("==========================request end================================================")
    }

    @Throws(IOException::class)
    private fun traceResponse(response: ClientHttpResponse) {
        log.debug("============================response begin==========================================")
        log.debug("Status code  : {}", response.statusCode)
        log.debug("Status text  : {}", response.statusText)
        log.debug("Headers      : {}", response.headers)

        val inputStringBuilder = StringBuilder()

        try {
            val body = response.body
            val bufferedReader = BufferedReader(InputStreamReader(body, "UTF-8"))
            var line: String? = bufferedReader.readLine()
            while (line != null && inputStringBuilder.length <= MAX_BODY_OUTPUT_SIZE) {
                inputStringBuilder.append(line)
                inputStringBuilder.append('\n')
                line = bufferedReader.readLine()
            }
            if (inputStringBuilder.length > MAX_BODY_OUTPUT_SIZE) {
                // caused mvn integration test run to crash after printing parts of a PDF on console
                //					log.debug("Response body (truncated to ~{} bytes): {}", MAX_BODY_OUTPUT_SIZE, inputStringBuilder.toString());
                log.debug("Response body omitted because it is too large.")
            } else {
                log.debug("Response body: {}", inputStringBuilder.toString())
            }
        } catch (_: IllegalArgumentException) {
        }

        log.debug("=======================response end=================================================")
    }

    companion object {

        private val log = LoggerFactory.getLogger(LoggingRequestInterceptor::class.java)

        private val MAX_BODY_OUTPUT_SIZE = 1024 * 50
    }
}
