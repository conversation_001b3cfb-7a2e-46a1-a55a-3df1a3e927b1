package at.derneubauer.backend.util

import org.yaml.snakeyaml.Yaml
import org.yaml.snakeyaml.constructor.Constructor

class TestConfigLoader {
    companion object {
        fun loadConfig(): NeubauerTestConfig {
            val yaml = Yaml(Constructor(NeubauerTestConfigHolder::class.java))
            val configHolder: NeubauerTestConfigHolder =
                yaml.load(this::class.java.getResourceAsStream("/test-config.yml")) as NeubauerTestConfigHolder
            return configHolder.neubauer
        }
    }
}

data class NeubauerTestConfigHolder(
    var neubauer: NeubauerTestConfig = NeubauerTestConfig()
)

data class NeubauerTestConfig(
    var backend: NeubauerBackendTestConfig = NeubauerBackendTestConfig(),
    var offa: NeubauerOffaTestConfig = NeubauerOffaTestConfig()
)

data class NeubauerBackendTestConfig(
    var baseUrl: String = "",
    var username: String = "",
    var password: String = "",
    var technicianUsername: String = "",
    var technicianPassword: String = ""
)

data class NeubauerOffaTestConfig(
    var username: String = "",
    var password: String = ""
)
