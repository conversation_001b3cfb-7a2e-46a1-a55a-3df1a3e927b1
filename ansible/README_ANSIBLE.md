# Ansible Deployment

## What is Ansible?

Ansible is an open-source software provisioning and deployment tool enabling infrastructure as code. 
It includes its own declarative language to describe system configuration.
In our use-case it is used for an automated deployment of the application to a server, and all its needed dependencies.

## Basics on Ansible Roles 

Roles are the main building blocks for Ansible. They are used to organize tasks and variables in a structured way.

## Basics on Ansible Inventories

Inventories define the variables for a specific host or group of hosts. They are used to define the target hosts for Ansible.

https://docs.ansible.com/ansible/latest/user_guide/intro_inventory.html

## Basics on Ansible Playbooks

Playbooks define which roles are executed on which hosts. They are the main entry point for Ansible.

https://docs.ansible.com/ansible/latest/user_guide/playbooks.html

## Setup

Install Ansible and check if the installed version is at least 2.10.x!

```
$ brew install ansible
$ ansible --version
```

Install required roles with this command

```
$ ansible-galaxy install -f -r roles/requirements.yml
```

### Test Server

```
$ ansible-playbook -i inventory-test-1 playbook.yml --private-key path/to/private/key
```

or

```
$ ansible-playbook -i inventory-test-2 playbook.yml --private-key path/to/private/key
```

If an ansible vault is in use, you also need to add either of these two options: 

1. `--ask-vault-pass` to enter the vault password manually
```
$ ansible-playbook -i inventory-test-1 playbook.yml --private-key path/to/private/key --ask-vault-pass
```

2. `--vault-password path/to/vault/password/file` to use a password file
```
$ ansible-playbook -i inventory-test-1 playbook.yml --private-key path/to/private/key --vault-password path/to/vault/password/file
```

### Staging Server

```
$ ansible-playbook -i inventory-staging playbook.yml --private-key path/to/private/key
```

### Production Server

```
$ ansible-playbook -i inventory-production playbook.yml --private-key path/to/private/key
```

## Update credentials

Credentials should never be unencrypted in a repository. Therefore Ansible Vault is used to securely encrypt data.
Encrypted variables are stored with Ansible's recommended method:
https://docs.ansible.com/ansible/latest/user_guide/playbooks_best_practices.html#keep-vaulted-variables-safely-visible

To update credentials use the following command. Consider securing your editor first as mentioned in the detail documentation.
https://docs.ansible.com/ansible/latest/user_guide/vault.html

```
$ ansible-vault edit inventory-staging/group_vars/web/vault.yml
```
