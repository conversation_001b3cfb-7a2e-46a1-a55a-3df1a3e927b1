---
- hosts: [test]
  become_user: root
  vars:
    commonSysUpgrade: false
    redeploy_proxy_config: false
    execute_certbot: false
    branch_to_use: "develop"
    app_dir: /home/<USER>/neubauer-backend
  any_errors_fatal: true
  pre_tasks:
    - name: Verify Ansible meets version requirements.
      assert:
        that: "ansible_version.full is version_compare('2.10', '>=')"
        msg: >
          "You must update Ansible to at least 2.10 to use this version."
  roles:
    - {
      role: bytepoets.infrastructure.certbot_hetzner_dns,
      become: yes,
      when: execute_certbot
    }

    - {
      role: bytepoets.infrastructure.nginx_proxy,
      become: yes,
      nginxConfigPaths: [ "{{ proxy_file }}" ],
      nginxConfigLinesToCommentOut: [ "gzip ", "ssl_protocols ", "ssl_prefer_server_ciphers " ],
      when: redeploy_proxy_config
    }

  tasks:
    - name: stop app container
      shell: ./shutdown-test.sh
      args:
        chdir: "{{ app_dir }}"

    - name: Git hard cleanup
      shell: git reset --hard && git clean -fdx
      args:
        chdir: "{{ app_dir }}"

    - name: whoami
      shell: whoami
      args:
        chdir: "{{ app_dir }}"

    - name: Git checkout & pull
      shell: git fetch && git checkout {{ branch_to_use }} && git pull
      args:
        chdir: "{{ app_dir }}"

    - name: builder build (startup-test.sh)
      shell: ./startup-test.sh
      args:
        chdir: "{{ app_dir }}"

    - name: Wait for API version endpoint to be ready
      uri:
        url: "{{ backend_url }}/api/v1/version"
        method: GET
        status_code: 200
        validate_certs: no
      register: result_api
      retries: 60
      delay: 10
      until: result_api.status == 200

    - name: Wait for login endpoint to be ready
      uri:
        url: "{{ backend_url }}/login"
        method: GET
        status_code: 200
        validate_certs: no
      register: result_login
      retries: 60
      delay: 10
      until: result_login.status == 200
