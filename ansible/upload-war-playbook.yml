---
- hosts: [staging, production]
  vars:
    commonSysUpgrade: false
  any_errors_fatal: true
  pre_tasks:
    - name: Verify Ansible meets version requirements.
      assert:
        that: "ansible_version.full is version_compare('2.10', '>=')"
        msg: >
          "You must update <PERSON><PERSON> to at least 2.10 to use this version."
  tasks:
    - name: create artifacts directory
      file:
        path: "{{ path }}"
        state: directory

    - name: copy war file to server
      copy:
        src: "{{ item }}"
        dest: "{{ path }}/."
      with_fileglob:
        - "../build/libs/neubauer-backend-*.war"
