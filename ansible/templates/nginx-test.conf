# hostname is the same for all endpoints
{% set backend_hostname = backend_url | urlsplit('hostname') %}

# hides the nginx version
server_tokens off;

# enable gzip for all
gzip on; # has to be removed from /etc/nginx/nginx.conf
gzip_types text/plain application/json application/xml text/css text/javascript;
gzip_min_length 1000;

# adjust keepalive sessions
keepalive_timeout 65;
keepalive_requests 10000;

# adjust ssl handling
ssl_protocols TLSv1.2 TLSv1.3; # has to be removed from /etc/nginx/nginx.conf
ssl_prefer_server_ciphers on; # has to be removed from /etc/nginx/nginx.conf
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_ciphers HIGH:!aNULL:!MD5;

# redirect http to https
server {
    listen 80;
    server_name {{ backend_hostname }};

    return 301 https://$host$request_uri;
}

server {
    listen {{ backend_url | urlsplit('port') }} ssl http2;
    server_name {{ backend_hostname }} ssl http2;

    ssl_certificate     /etc/letsencrypt/live/{{ backend_hostname }}/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/{{ backend_hostname }}/privkey.pem; # managed by Certbot

    # adjust buffering
    proxy_buffer_size 128k;
    proxy_buffers 4 256k;
    proxy_busy_buffers_size 256k;
    proxy_max_temp_file_size 0;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    send_timeout 60s;

    client_max_body_size 20M;

    error_page 497 https://$host$request_uri;

    location / {
        proxy_pass http://localhost:8080;
        proxy_redirect off;

        # Needed for spring boot
        proxy_set_header Host              $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port  $server_port;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Real-IP         $remote_addr;
    }
}

server {
    listen {{ minio_console_url | urlsplit('port') }} ssl http2;
    server_name {{ minio_console_url | urlsplit('hostname') }} ssl http2;

    ssl_certificate     /etc/letsencrypt/live/{{ backend_hostname }}/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/{{ backend_hostname }}/privkey.pem; # managed by Certbot

    # redirect http to https
    error_page 497 301 =307 https://$host:$server_port$request_uri;

    location / {
        proxy_pass http://localhost:{{ dockerized_minio_port_console }};
        proxy_redirect off;
    }
}
