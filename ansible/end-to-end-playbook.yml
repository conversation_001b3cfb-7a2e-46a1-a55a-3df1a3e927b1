---
- hosts: [test]
  vars:
    commonSysUpgrade: false
    app_dir: /home/<USER>/neubauer-backend
  any_errors_fatal: true
  pre_tasks:
    - name: Verify Ansible meets version requirements.
      assert:
        that: "ansible_version.full is version_compare('2.10', '>=')"
        msg: >
          "You must update Ansible to at least 2.10 to use this version."
  tasks:
    - name: Generate test server test config
      template:
        src: "../src/test/resources/test-config-testserver.yml"
        dest: "{{ app_dir }}/src/test/resources/test-config.yml"

    - name: Run end-to-end tests
      shell: |
        ./gradlew cleanTest test
      args:
        chdir: "{{ app_dir }}"
