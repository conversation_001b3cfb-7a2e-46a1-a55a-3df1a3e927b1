---
sentry_environment: test
backend_config_path: ../config/neubauer-config-test.yml
proxy_file: nginx-test.conf

frontend_url: https://test.neubauer.bpdev.at

certbot_hetzner_dns_api_token: "{{ vault_certbot_hetzner_dns_api_token }}"

test_username: "{{ vault_test_username }}"
test_password: "{{ vault_test_password }}"
test_technician_username: "{{ vault_test_technician_username }}"
test_technician_password: "{{ vault_test_technician_password }}"
test_offa_username: "{{ vault_test_offa_username }}"
test_offa_password: "{{ vault_test_offa_password }}"
