#!/bin/bash

set -o errexit
#set -o nounset
set -o pipefail
#set -o xtrace

readonly TRUE=0
readonly FALSE=1

BP_DOCKER_COMPOSE_FILE='docker-compose-dev.yml'
INCREMENTAL_BUILD=FALSE
VERY_INCREMENTAL_BUILD=FALSE
GRADLE_LOG_LEVEL=""

evaluate_parameters() {
  for i in "$@"
  do
  case "$i" in
    -i)
    INCREMENTAL_BUILD=TRUE
    ;;
    -ii)
    VERY_INCREMENTAL_BUILD=TRUE
    ;;
    -v)
    GRADLE_LOG_LEVEL="--info"
    ;;
    -vv)
    GRADLE_LOG_LEVEL="--debug"
    ;;
  esac
  done
}

incrementalBuildWar() {
    ./gradlew war $GRADLE_LOG_LEVEL
}

cleanBuildWar() {
    ./gradlew clean cleanWar war $GRADLE_LOG_LEVEL
}

copyWarToImageBuildDir() {
    cp -r ./build/libs ./docker/image-build/
}

cleanImageBuildDir() {
    rm -rf ./docker/image-build/libs
}

stopProxyContainer() {
    docker compose -f $BP_DOCKER_COMPOSE_FILE stop proxy
}

stopAppContainer() {
    docker compose -f $BP_DOCKER_COMPOSE_FILE stop app
}

stopAllContainers() {
    docker compose -f $BP_DOCKER_COMPOSE_FILE stop
}

nukeAllContainers() {
    docker compose -f $BP_DOCKER_COMPOSE_FILE stop \
        && docker compose -f $BP_DOCKER_COMPOSE_FILE rm -fv
}

dockerBuild() {
    docker compose -f $BP_DOCKER_COMPOSE_FILE build
}

dockerStartup() {
    docker compose -f $BP_DOCKER_COMPOSE_FILE up -d
}

dockerLogsForAppContainer() {
    docker compose -f $BP_DOCKER_COMPOSE_FILE logs --tail=0 -f app
}

restartProxyContainer() {
    docker restart `docker compose -f $BP_DOCKER_COMPOSE_FILE ps --quiet proxy`
}

redeployWarInRunningDockerContainer() {
    docker cp ./docker/image-build/libs/*.war `docker compose -f $BP_DOCKER_COMPOSE_FILE ps --quiet app`:/usr/local/tomcat/webapps/ROOT.war
}

fullBuild() {
    stopAppContainer \
        && cleanImageBuildDir \
        && cleanBuildWar \
        && copyWarToImageBuildDir \
        && dockerBuild \
        && dockerStartup \
        && postBuildAction
}

incrementalBuild() {
    stopAppContainer \
        && incrementalBuildWar \
        && copyWarToImageBuildDir \
        && dockerBuild \
        && dockerStartup \
        && postBuildAction
}

veryIncrementalBuild() {
    incrementalBuildWar \
        && copyWarToImageBuildDir \
        && redeployWarInRunningDockerContainer \
        && postBuildAction
}

build() {
    if [[ $INCREMENTAL_BUILD -eq FALSE ]] && [[ $VERY_INCREMENTAL_BUILD -eq FALSE ]]; then
        fullBuild
    fi
    if [[ $INCREMENTAL_BUILD -eq TRUE ]]; then
        incrementalBuild
    fi
    if [[ $VERY_INCREMENTAL_BUILD -eq TRUE ]]; then
        veryIncrementalBuild
    fi
}

postBuildAction() {
    : # no default post build action configured
}
