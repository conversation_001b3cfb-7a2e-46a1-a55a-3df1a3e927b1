neubauer:
  baseUrl: "https://test.neubauer.bpdev.at"
  dev:
    testSupportEndpointsActive: true

  filestore:
    s3Region: us-east-1
    s3Bucket: docker
    s3AccessKeyId: EKIEIOSFODNN7EXAMPLE
    s3SecretAccessKey: wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    s3Endpoint: http://filestore:9000
    pathStyleAccessEnabled: true
  issue-image-filestore:
    s3Region: us-east-1
    s3Bucket: issue-img
    s3AccessKeyId: EKIEIOSFODNN7EXAMPLE
    s3SecretAccessKey: wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    s3Endpoint: http://filestore:9000
    pathStyleAccessEnabled: true
    issuePrefixToRemove: AS
    issueScanFileExtension: .pdf
  issue-document-filestore:
    s3Region: us-east-1
    s3Bucket: issue-doc
    s3AccessKeyId: EKIEIOSFODNN7EXAMPLE
    s3SecretAccessKey: wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    s3Endpoint: http://filestore:9000
    pathStyleAccessEnabled: true
  offa:
    user: offa
    password: $2a$10$OnQRPFo8AvAeZ3nB4wkehOcwEm0i.kEtKMtVVX1/4503MEeS.WNiq
  sms:
    endpointUrl: https://api.websms.com/rest/
    accessToken: 1d4c4b56-597b-4096-90d6-d12830ea573f

spring:
  datasource:
    url: **************************
    username: neubauer
    password: sikrit
    driverClassName: org.mariadb.jdbc.Driver

  flyway:
    locations: classpath:db/migration,classpath:db/seed
    table: schema_version
    encoding: UTF-8

  web:
    resources:
      cache:
        cachecontrol:
          max-age: 31536000
      chain:
        cache: true
        strategy:
          content:
            enabled: true
            paths: /assets/**
      static-locations: classpath:/static/

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  session:
    storeType: redis

  data:
    redis:
      host: redis
      port: 6379

  thymeleaf:
    cache: false
    
server:
  forward-headers-strategy: native

logging:
  level:
    org.springframework: INFO
    at.derneubauer.backend: DEBUG
    at.derneubauer.backend.db: INFO
