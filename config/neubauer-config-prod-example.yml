neubauer:
  baseUrl: "https://verwaltung.derneubauer.at"
  dev:
    testSupportEndpointsActive: false

  filestore:
    s3Region: us-east-1
    s3Bucket: docker
    s3AccessKeyId: ACCESS_KEY_123ABC #S3_ACCESS_KEY#
    s3SecretAccessKey: SECRET_KEY_123XYZ #S3_SECRET_ACCESS_KEY#
    s3Endpoint: http://s3url:9000 #S3_HOST#
    pathStyleAccessEnabled: true
  issue-image-filestore:
    s3Region: us-east-1
    s3Bucket: docker
    s3AccessKeyId: ACCESS_KEY_123ABC #S3_ACCESS_KEY#
    s3SecretAccessKey: SECRET_KEY_123XYZ #S3_SECRET_ACCESS_KEY#
    s3Endpoint: http://s3url:9000 #S3_HOST#
    pathStyleAccessEnabled: true
    issuePrefixToRemove: AS
    issueScanFileExtension: .pdf
  issue-document-filestore:
    s3Region: us-east-1
    s3Bucket: issue-doc
    s3AccessKeyId: ACCESS_KEY_123ABC #S3_ACCESS_KEY#
    s3SecretAccessKey: SECRET_KEY_123XYZ #S3_SECRET_ACCESS_KEY#
    s3Endpoint: http://s3url:9000 #S3_HOST#
    pathStyleAccessEnabled: true
  offa:
    user: offa
    password: #OFFA_PASSWORD#
  sms:
    endpointUrl: https://api.websms.com/rest/
    accessToken: #SMS_AT_ACCESS_TOKEN#

spring:
  datasource:
    url: *************************************#
    username: #MARIA_USER#
    password: #MARIA_PASSWORD#
    driverClassName: org.mariadb.jdbc.Driver

  flyway:
    locations: classpath:db/migration
    table: schema_version
    encoding: UTF-8

  web:
    resources:
      cache:
        cachecontrol:
          max-age: 31536000
      chain:
        cache: true
        strategy:
          content:
            enabled: true
            paths: /assets/**
      static-locations: classpath:/static/

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  session:
    storeType: redis

  data:
    redis:
      host: #REDIS_HOST#
      port: 6379

  thymeleaf:
    cache: false

server:
  forward-headers-strategy: native

logging:
  level:
    org.springframework: INFO
    at.derneubauer.backend: DEBUG
    at.derneubauer.backend.db: INFO
