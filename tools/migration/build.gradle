buildscript {
    ext.kotlin_version = '1.2.30'

    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

group 'at.neubauer'
version '1.0-SNAPSHOT'

apply plugin: 'kotlin'
apply plugin: 'application'

mainClassName = "at.neubauer.migration.MainKt"

repositories {
    mavenCentral()
}

dependencies {
    compile "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    compile 'org.mybatis:mybatis:3.4.6'
    compile 'org.mariadb.jdbc:mariadb-java-client:2.2.3'
    compile 'mysql:mysql-connector-java:5.1.46'
    compile 'org.springframework:spring-web:4.3.17.RELEASE'
    compile 'org.springframework.security:spring-security-core:4.2.5.RELEASE'
    compile 'org.apache.httpcomponents:httpclient:4.5.5'
    compile "com.fasterxml.jackson.module:jackson-module-kotlin:*******"
    compile "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.4"

    testCompile "org.jetbrains.kotlin:kotlin-test-junit:${kotlin_version}"
}

compileKotlin {
    kotlinOptions.jvmTarget = "1.8"
}
compileTestKotlin {
    kotlinOptions.jvmTarget = "1.8"
}
