Neubauer Migration Guide
========================

## Connect to AWS Instance

* Copy private key from 1Password (Neubauer Migration Helper AWS EC2 Instance)
* `ssh -i ~/.ssh/neubauer-migration-helper.pem ubuntu@*************`



## Connect to tmux / start a new session

### Why tmux
* In case your ssh connection to the server is closed your tmux session will still be kept alive and you can reconnect to it once you restore your ssh connection.
* This allows you to execute long running commands that won't be stopped due to lost ssh connection.


### How
* On the AWS Instance check if there is a tmux session running with `tmux list-sessions`
* Connect to the instance with `tmux attach` otherwise create a new one with `tmux`



## Copy the SQL database backup dump from the legacy production system to the migration helper instance

### Prepare the backup on legacy production system (<PERSON><PERSON><PERSON>)
* Use the migration helper AWS instance as hop by connecting to it via ssh and then connecting to the legacy production server from there
* `ssh -p 2611 lbloder@***********` the password can be found in 1Password (Neubauer Baudoku Lösung NEB17 & Hetzner Server)
* `su` to switch to root user, password in 1Password (Neubauer Baudoku Lösung NEB17 & Hetzner Server)
* `cp /root/backups/neubauer_db_backup.sql /home/<USER>/neubauer_db_backup_BACKUPDATEHERE.sql` to copy the backup to the lblobder user home
* `chown lbloder:lbloder neubauer_db_backup_BACKUPDATEHERE.sql` so the lbloder user can copy the file via scp
* Disconnect from legacy production system

### Copy the backup file to the migration helper instance
* `scp -P 2611 lbloder@***********:/home/<USER>/neubauer_db_backup_BACKUPDATEHERE.sql .`



## Restore the backup on the migration helper instance
* Make sure the database exists and is empty first (throw away and recreate docker instance if necessary)!
* `mysql --host=127.0.0.1 --port=11062 --database=neubauer -u root -p < neubauer_db_backup_BACKUPDATEHERE.sql`
* The password can be found in `~/migration-helper/docker-compose.yml`



## Build the migration helper tool on your local machine
* `cd ~/repos/neubauer-backend/tools/migration`
* `./gradlew clean distZip`



## Copy the migration helper tool to the new production server
* Remove the old .zip and extracted directory from the target server first
* `scp build/distributions/migration-1.0-SNAPSHOT.zip root@*************:~/` for staging
* `scp build/distributions/migration-1.0-SNAPSHOT.zip root@*************:~/` for production



## Extract the migration tool on the staging / production server
* `unzip migration-1.0-SNAPSHOT.zip -d migration-tool`



## Create the config file if it doesn't exist yet
* `~/migration-tool/neubauer_migration.properties`:

```
db.legacy.url=***************************************
db.legacy.user=root
db.legacy.password=X
db.new.url=***************************************
db.new.user=root
db.new.password=X
backend.baseUrl=https://localhost:11080
backend.user=sne
backend.password=migration!2018-05-25
```



## Start the application server with test support endpoints enabled

* `vi /opt/tomcat/conf/neubauer-config.yml` TODO

```
...
neubauer:
...
  dev:
    testSupportEndpointsActive: true
  ...
...
```
* `service tomcat restart`



## Open an SSH tunnel to forward the database port to the AWS migration helper instance (execute command on new production server)
* `ssh -L 127.0.0.1:11062:127.0.0.1:11062 ubuntu@*************`



## Run the migration tool
* Use the migration helper AWS instance as hop and use a tmux session (see above)
* `ssh root@*************`
* `./migration-tool/migration-1.0-SNAPSHOT/bin/migration ~/migration-config.properties > migration.log 2>&1`



## Start the application server with test support endpoints disabled

* `vi /opt/tomcat/conf/neubauer-config.yml` TODO
* Remove the test support endpoint section from the config file again

```
...
neubauer:
...
  dev:
    testSupportEndpointsActive: false
  ...
...
```

* `service tomcat restart`


## Export a list of images that could not be migrated

* `cat migration.log | grep "error:"`
* `cat migration.log | grep "error:" | sed -n -e 's/^.*with ID//p' | sed -n -e 's/\.\.\..*$//p'`

```
SELECT i.external_id, a.id FROM ISSUES i INNER JOIN ATTACHMENTS a ON (i.id = a.issue_id) WHERE a.id IN (...);

SELECT p.username, a.id FROM PERSONS p INNER JOIN ATTACHMENTS a ON (p.id = a.person_id) WHERE a.id IN (...);
```
