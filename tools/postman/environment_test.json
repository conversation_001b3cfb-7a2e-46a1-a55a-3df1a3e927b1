{"id": "e3160b07-7758-a3f8-8fe1-903eb71f8b74", "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "values": [{"key": "neubauerBackendBaseUrl", "type": "text", "value": "https://test.neubauer.bpdev.at", "enabled": true}, {"key": "neubauerApiV1", "type": "text", "value": "{{neubauerBackendBaseUrl}}/api/v1", "enabled": true}, {"key": "neubauerAuthToken", "type": "text", "value": "f2e67379-7105-462b-ba80-8537277947c3", "enabled": true}, {"key": "neubauerLanguage", "value": "de", "type": "text", "enabled": true}], "timestamp": 1497275761797, "_postman_variable_scope": "environment", "_postman_exported_at": "2017-06-13T14:14:28.052Z", "_postman_exported_using": "Postman/4.8.3"}