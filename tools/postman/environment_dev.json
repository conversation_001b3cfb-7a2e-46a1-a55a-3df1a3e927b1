{"id": "096ffdbd-1dca-1fb9-1e3b-da3f4f43e9c5", "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "values": [{"key": "neubauerBackendBaseUrl", "type": "text", "value": "https://localhost:11080", "enabled": true}, {"key": "neubauerApiV1", "type": "text", "value": "{{neubauerBackendBaseUrl}}/api/v1", "enabled": true}, {"key": "neubauerAuthToken", "type": "text", "value": "13606dd0-5012-4ef6-87cc-954df84058ac", "enabled": true}, {"key": "neubauerLanguage", "type": "text", "value": "de", "enabled": true}], "timestamp": 1497350164934, "_postman_variable_scope": "environment", "_postman_exported_at": "2017-06-13T14:14:33.697Z", "_postman_exported_using": "Postman/4.8.3"}