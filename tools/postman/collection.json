{"variables": [], "info": {"name": "<PERSON><PERSON><PERSON><PERSON>", "_postman_id": "6c638afb-1f9b-c459-8d98-a2749504c40f", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "description": "", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["postman.setEnvironmentVariable(\"neubauerAuthToken\", postman.getResponseHeader(\"x-auth-token\"));"]}}], "request": {"url": "{{neubauerApiV1}}/login", "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "description": ""}], "body": {"mode": "raw", "raw": "{\n\t\"user\": \"<EMAIL>\",\n\t\"password\": \"Leibs-mengte-driftest-Balsams.2018Jan19.scannen\"\n}"}, "description": ""}, "response": []}, {"name": "Logout", "request": {"url": "{{neubauerApiV1}}/logout", "method": "POST", "header": [{"key": "x-auth-token", "value": "{{neubauerAuthToken}}", "description": ""}], "body": {"mode": "raw", "raw": ""}, "description": ""}, "response": []}]}, {"name": "ResourcePlanning", "description": "", "item": [{"name": "Technicians List", "request": {"url": "{{neubauerApiV1}}/technicians", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{neubauerAuthToken}}", "description": ""}], "body": {}, "description": ""}, "response": [{"id": "4e77b41c-4dde-4ce8-994d-50dc8377305e", "name": "success", "originalRequest": {"url": "{{mwbApiV1}}/customer/deliveries", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{mwbAuthToken}}"}], "body": {}, "description": ""}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate", "name": "Cache-Control", "description": "Tells all caching mechanisms from server to client whether they may cache this object. It is measured in seconds"}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Mon, 05 Mar 2018 14:00:15 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Expires", "value": "0", "name": "Expires", "description": "Gives the date/time after which the response is considered stale"}, {"key": "Pragma", "value": "no-cache", "name": "Pragma", "description": "Implementation-specific headers that may have various effects anywhere along the request-response chain."}, {"key": "Server", "value": "nginx/1.12.2", "name": "Server", "description": "A name for the server"}, {"key": "Transfer-Encoding", "value": "chunked", "name": "Transfer-Encoding", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"key": "X-Content-Type-Options", "value": "nosniff", "name": "X-Content-Type-Options", "description": "The only defined value, \"nosniff\", prevents Internet Explorer from MIME-sniffing a response away from the declared content-type"}, {"key": "X-Frame-Options", "value": "DENY", "name": "X-Frame-Options", "description": "Clickjacking protection: \"deny\" - no rendering within a frame, \"sameorigin\" - no rendering if origin mismatch"}, {"key": "X-XSS-Protection", "value": "1; mode=block", "name": "X-XSS-Protection", "description": "Cross-site scripting (XSS) filter"}], "cookie": [{"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "C53D53627EA59CA40C56A48DCBF6917C", "key": "JSESSIONID"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "eyJpdiI6ImU1eGhqUmZlNGVsWFdDbHhsRzAxOXc9PSIsInZhbHVlIjoiZDFmWmNsVjdiUlhFcmxCcFdqTkFhcTdPZmsxNCtHdzN5c1RTN2FYY3crWCthZ25SeWNBc0pVamJDQnBYK1l6blhtVjN5MGJIeGw1UDlRZ3l3c1FkS1E9PSIsIm1hYyI6IjlhODZkOWMwNjk0ZDQxNjdlNGZhNWQ3ZjZkYTNiMWIxMWQ5NDRhYTQ2NThmMzMxY2Y0MmNhNWIxMjJhMjI1N2MifQ%3D%3D", "key": "subauftrag_session"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "bb407f4e-7cb5-4a13-a401-37e375e3673e", "key": "SESSION"}], "responseTime": 3490, "body": "[{\"id\":2,\"title\":\"Delivery #2\",\"status\":{\"value\":\"IN_TRANSIT\",\"receivedAt\":\"2018-03-05T13:45:12.609Z\"},\"updatedAt\":\"2018-03-05T13:36:40Z\"},{\"id\":4,\"title\":\"Delivery #4\",\"status\":{\"value\":\"IN_TRANSIT\",\"receivedAt\":\"2018-03-05T13:45:12.61Z\"},\"updatedAt\":\"2018-03-05T13:36:40Z\"}]"}, {"id": "0f277f25-b6c6-4af6-9c06-335f9a12147e", "name": "empty", "originalRequest": {"url": "{{mwbApiV1}}/customer/deliveries", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{mwbAuthToken}}"}], "body": {}, "description": ""}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate", "name": "Cache-Control", "description": "Tells all caching mechanisms from server to client whether they may cache this object. It is measured in seconds"}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Mon, 05 Mar 2018 14:00:43 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Expires", "value": "0", "name": "Expires", "description": "Gives the date/time after which the response is considered stale"}, {"key": "Pragma", "value": "no-cache", "name": "Pragma", "description": "Implementation-specific headers that may have various effects anywhere along the request-response chain."}, {"key": "Server", "value": "nginx/1.12.2", "name": "Server", "description": "A name for the server"}, {"key": "Transfer-Encoding", "value": "chunked", "name": "Transfer-Encoding", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"key": "X-Content-Type-Options", "value": "nosniff", "name": "X-Content-Type-Options", "description": "The only defined value, \"nosniff\", prevents Internet Explorer from MIME-sniffing a response away from the declared content-type"}, {"key": "X-Frame-Options", "value": "DENY", "name": "X-Frame-Options", "description": "Clickjacking protection: \"deny\" - no rendering within a frame, \"sameorigin\" - no rendering if origin mismatch"}, {"key": "X-XSS-Protection", "value": "1; mode=block", "name": "X-XSS-Protection", "description": "Cross-site scripting (XSS) filter"}], "cookie": [{"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "C53D53627EA59CA40C56A48DCBF6917C", "key": "JSESSIONID"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "eyJpdiI6ImU1eGhqUmZlNGVsWFdDbHhsRzAxOXc9PSIsInZhbHVlIjoiZDFmWmNsVjdiUlhFcmxCcFdqTkFhcTdPZmsxNCtHdzN5c1RTN2FYY3crWCthZ25SeWNBc0pVamJDQnBYK1l6blhtVjN5MGJIeGw1UDlRZ3l3c1FkS1E9PSIsIm1hYyI6IjlhODZkOWMwNjk0ZDQxNjdlNGZhNWQ3ZjZkYTNiMWIxMWQ5NDRhYTQ2NThmMzMxY2Y0MmNhNWIxMjJhMjI1N2MifQ%3D%3D", "key": "subauftrag_session"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "bb407f4e-7cb5-4a13-a401-37e375e3673e", "key": "SESSION"}], "responseTime": 43, "body": "[]"}, {"id": "fdfb87f7-416c-478b-bea6-0233726bcf28", "name": "Technicians List success", "originalRequest": {"url": "{{neubauerApiV1}}/technicians", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{neubauerAuthToken}}", "description": "", "disabled": false}], "body": {}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate", "name": "Cache-Control", "description": "Tells all caching mechanisms from server to client whether they may cache this object. It is measured in seconds"}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Mon, 19 Mar 2018 16:26:39 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Expires", "value": "0", "name": "Expires", "description": "Gives the date/time after which the response is considered stale"}, {"key": "Pragma", "value": "no-cache", "name": "Pragma", "description": "Implementation-specific headers that may have various effects anywhere along the request-response chain."}, {"key": "Server", "value": "nginx/1.12.2", "name": "Server", "description": "A name for the server"}, {"key": "Transfer-Encoding", "value": "chunked", "name": "Transfer-Encoding", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"key": "X-Content-Type-Options", "value": "nosniff", "name": "X-Content-Type-Options", "description": "The only defined value, \"nosniff\", prevents Internet Explorer from MIME-sniffing a response away from the declared content-type"}, {"key": "X-Frame-Options", "value": "DENY", "name": "X-Frame-Options", "description": "Clickjacking protection: \"deny\" - no rendering within a frame, \"sameorigin\" - no rendering if origin mismatch"}, {"key": "X-XSS-Protection", "value": "1; mode=block", "name": "X-XSS-Protection", "description": "Cross-site scripting (XSS) filter"}], "cookie": [], "responseTime": 191, "body": "[{\"id\":5,\"displayName\":\"<EMAIL>\"},{\"id\":6,\"displayName\":\"<EMAIL>\"},{\"id\":7,\"displayName\":\"<EMAIL>\"},{\"id\":8,\"displayName\":\"<EMAIL>\"}]"}]}, {"name": "Technician Assignment List", "request": {"url": {"raw": "{{neubauerApiV1}}/technicians/assignments?from=2018-04-02T00:00:00.082Z", "host": ["{{neubauerApiV1}}"], "path": ["technicians", "assignments"], "query": [{"key": "from", "value": "2018-04-02T00:00:00.082Z", "equals": true, "description": ""}, {"key": "to", "value": "2018-04-02T23:59:59.237Z", "equals": true, "description": "", "disabled": true}], "variable": []}, "method": "GET", "header": [{"key": "x-auth-token", "value": "{{neubauerAuthToken}}", "description": ""}], "body": {}, "description": ""}, "response": [{"id": "206fc4f6-e907-42c8-9fce-1b9a43167774", "name": "success", "originalRequest": {"url": "{{mwbApiV1}}/customer/deliveries", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{mwbAuthToken}}"}], "body": {}, "description": ""}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate", "name": "Cache-Control", "description": "Tells all caching mechanisms from server to client whether they may cache this object. It is measured in seconds"}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Mon, 05 Mar 2018 14:00:15 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Expires", "value": "0", "name": "Expires", "description": "Gives the date/time after which the response is considered stale"}, {"key": "Pragma", "value": "no-cache", "name": "Pragma", "description": "Implementation-specific headers that may have various effects anywhere along the request-response chain."}, {"key": "Server", "value": "nginx/1.12.2", "name": "Server", "description": "A name for the server"}, {"key": "Transfer-Encoding", "value": "chunked", "name": "Transfer-Encoding", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"key": "X-Content-Type-Options", "value": "nosniff", "name": "X-Content-Type-Options", "description": "The only defined value, \"nosniff\", prevents Internet Explorer from MIME-sniffing a response away from the declared content-type"}, {"key": "X-Frame-Options", "value": "DENY", "name": "X-Frame-Options", "description": "Clickjacking protection: \"deny\" - no rendering within a frame, \"sameorigin\" - no rendering if origin mismatch"}, {"key": "X-XSS-Protection", "value": "1; mode=block", "name": "X-XSS-Protection", "description": "Cross-site scripting (XSS) filter"}], "cookie": [{"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "C53D53627EA59CA40C56A48DCBF6917C", "key": "JSESSIONID"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "eyJpdiI6ImU1eGhqUmZlNGVsWFdDbHhsRzAxOXc9PSIsInZhbHVlIjoiZDFmWmNsVjdiUlhFcmxCcFdqTkFhcTdPZmsxNCtHdzN5c1RTN2FYY3crWCthZ25SeWNBc0pVamJDQnBYK1l6blhtVjN5MGJIeGw1UDlRZ3l3c1FkS1E9PSIsIm1hYyI6IjlhODZkOWMwNjk0ZDQxNjdlNGZhNWQ3ZjZkYTNiMWIxMWQ5NDRhYTQ2NThmMzMxY2Y0MmNhNWIxMjJhMjI1N2MifQ%3D%3D", "key": "subauftrag_session"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "bb407f4e-7cb5-4a13-a401-37e375e3673e", "key": "SESSION"}], "responseTime": 3490, "body": "[{\"id\":2,\"title\":\"Delivery #2\",\"status\":{\"value\":\"IN_TRANSIT\",\"receivedAt\":\"2018-03-05T13:45:12.609Z\"},\"updatedAt\":\"2018-03-05T13:36:40Z\"},{\"id\":4,\"title\":\"Delivery #4\",\"status\":{\"value\":\"IN_TRANSIT\",\"receivedAt\":\"2018-03-05T13:45:12.61Z\"},\"updatedAt\":\"2018-03-05T13:36:40Z\"}]"}, {"id": "7f27f424-bbe2-490f-ae11-99dbcc56b532", "name": "empty", "originalRequest": {"url": "{{mwbApiV1}}/customer/deliveries", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{mwbAuthToken}}"}], "body": {}, "description": ""}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate", "name": "Cache-Control", "description": "Tells all caching mechanisms from server to client whether they may cache this object. It is measured in seconds"}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Mon, 05 Mar 2018 14:00:43 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Expires", "value": "0", "name": "Expires", "description": "Gives the date/time after which the response is considered stale"}, {"key": "Pragma", "value": "no-cache", "name": "Pragma", "description": "Implementation-specific headers that may have various effects anywhere along the request-response chain."}, {"key": "Server", "value": "nginx/1.12.2", "name": "Server", "description": "A name for the server"}, {"key": "Transfer-Encoding", "value": "chunked", "name": "Transfer-Encoding", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"key": "X-Content-Type-Options", "value": "nosniff", "name": "X-Content-Type-Options", "description": "The only defined value, \"nosniff\", prevents Internet Explorer from MIME-sniffing a response away from the declared content-type"}, {"key": "X-Frame-Options", "value": "DENY", "name": "X-Frame-Options", "description": "Clickjacking protection: \"deny\" - no rendering within a frame, \"sameorigin\" - no rendering if origin mismatch"}, {"key": "X-XSS-Protection", "value": "1; mode=block", "name": "X-XSS-Protection", "description": "Cross-site scripting (XSS) filter"}], "cookie": [{"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "C53D53627EA59CA40C56A48DCBF6917C", "key": "JSESSIONID"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "eyJpdiI6ImU1eGhqUmZlNGVsWFdDbHhsRzAxOXc9PSIsInZhbHVlIjoiZDFmWmNsVjdiUlhFcmxCcFdqTkFhcTdPZmsxNCtHdzN5c1RTN2FYY3crWCthZ25SeWNBc0pVamJDQnBYK1l6blhtVjN5MGJIeGw1UDlRZ3l3c1FkS1E9PSIsIm1hYyI6IjlhODZkOWMwNjk0ZDQxNjdlNGZhNWQ3ZjZkYTNiMWIxMWQ5NDRhYTQ2NThmMzMxY2Y0MmNhNWIxMjJhMjI1N2MifQ%3D%3D", "key": "subauftrag_session"}, {"expires": "Invalid Date", "httpOnly": true, "domain": "localhost", "path": "/", "secure": false, "value": "bb407f4e-7cb5-4a13-a401-37e375e3673e", "key": "SESSION"}], "responseTime": 43, "body": "[]"}, {"id": "5aa29a3f-73ff-47b5-9dcd-2984f1aa6a90", "name": "Technician Assignment List success", "originalRequest": {"url": {"raw": "{{neubauerApiV1}}/technicians/assignments?from=2018-04-02T00:00:00.082Z&to=2018-04-02T23:59:59.237Z", "host": ["{{neubauerApiV1}}"], "path": ["technicians", "assignments"], "query": [{"key": "from", "value": "2018-04-02T00:00:00.082Z", "equals": true, "description": ""}, {"key": "to", "value": "2018-04-02T23:59:59.237Z", "equals": true, "description": ""}], "variable": []}, "method": "GET", "header": [{"key": "x-auth-token", "value": "{{neubauerAuthToken}}", "description": ""}], "body": {}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "_postman_previewtype": "text", "header": [{"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate", "name": "Cache-Control", "description": "Tells all caching mechanisms from server to client whether they may cache this object. It is measured in seconds"}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Type", "value": "application/json;charset=UTF-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Mon, 19 Mar 2018 16:43:10 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "Expires", "value": "0", "name": "Expires", "description": "Gives the date/time after which the response is considered stale"}, {"key": "Pragma", "value": "no-cache", "name": "Pragma", "description": "Implementation-specific headers that may have various effects anywhere along the request-response chain."}, {"key": "Server", "value": "nginx/1.12.2", "name": "Server", "description": "A name for the server"}, {"key": "Transfer-Encoding", "value": "chunked", "name": "Transfer-Encoding", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"key": "X-Content-Type-Options", "value": "nosniff", "name": "X-Content-Type-Options", "description": "The only defined value, \"nosniff\", prevents Internet Explorer from MIME-sniffing a response away from the declared content-type"}, {"key": "X-Frame-Options", "value": "DENY", "name": "X-Frame-Options", "description": "Clickjacking protection: \"deny\" - no rendering within a frame, \"sameorigin\" - no rendering if origin mismatch"}, {"key": "X-XSS-Protection", "value": "1; mode=block", "name": "X-XSS-Protection", "description": "Cross-site scripting (XSS) filter"}], "cookie": [], "responseTime": 19, "body": "{\"start\":\"2018-04-02T00:00:00.082Z\",\"end\":\"2018-04-03T00:00:00.082Z\",\"items\":[{\"start\":\"2018-04-02T09:59:59Z\",\"end\":\"2018-04-02T11:59:59Z\",\"userId\":5,\"eventType\":\"ISSUE\",\"data\":{\"id\":1,\"title\":\"1\",\"description\":\"Oeverseegasse 1, 8010 Graz\"}},{\"start\":\"2018-04-02T10:59:59Z\",\"end\":\"2018-04-02T15:59:59Z\",\"userId\":7,\"eventType\":\"ISSUE\",\"data\":{\"id\":2,\"title\":\"2\",\"description\":\"Elisabethiergasse 22, 8020 Graz\"}},{\"start\":\"2018-04-02T12:13:59Z\",\"end\":\"2018-04-02T16:59:59Z\",\"userId\":5,\"eventType\":\"ISSUE\",\"data\":{\"id\":3,\"title\":\"3\",\"description\":\"Hirtengasse 13 8020 Graz\"}}]}"}]}]}, {"name": "Version", "request": {"url": "{{neubauerApiV1}}/version", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{neubauerAuthToken}}", "description": ""}], "body": {}, "description": ""}, "response": []}, {"name": "Permissions", "request": {"url": "{{neubauerApiV1}}/permissions", "method": "GET", "header": [{"key": "x-auth-token", "value": "{{neubauerAuthToken}}", "description": ""}], "body": {}, "description": ""}, "response": []}]}