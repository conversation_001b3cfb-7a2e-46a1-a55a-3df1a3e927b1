{"info": {"name": "NeubauerSms", "_postman_id": "8b4c9a88-ef5d-0efc-8317-bd41bcd0d696", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"name": "NeubauerSmsRequest", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer 1d4c4b56-597b-4096-90d6-d12830ea573f"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientAddressList\": [\"+436991234567\"],\n  \"contentCategory\": \"informational\",\n  \"test\": \"true\",\n  \"messageContent\": \"Rachn?\",\n  \"maxSmsPerMessage\": 1,\n  \"sendAsFlashSms\": true\n}"}, "url": "https://api.websms.com/rest/smsmessaging/text", "description": ""}, "response": []}]}