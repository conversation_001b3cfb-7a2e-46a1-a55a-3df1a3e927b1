'@@@LoadAssembly JdbNet.dll
'@@@LoadAssembly System.windows.forms.dll
'@@@LoadAssembly Microsoft.VisualBasic.dll
'@@@LoadAssembly OffaNet.CoreComponents.dll
Imports System
Imports System.Xml
'Imports System.Windows.Forms
Imports Juprowa.Offa
Imports Juprowa.Services
Imports Microsoft.VisualBasic

Public Class MoServX2
    Implements IWorkOrderExporter
    Private Const PluginName As String = "MoServX2"
    Private Const ScriptVersion As String = "1.0.0"
    Private Const PlatformUrl As String = "https://verwaltung.derneubauer.at/offa/issue/connector"
    Private Const UserName As String = "offa"
    Private Const UserPass As String = "Leibs-mengte-driftest-Balsams.2018Jan19.scannen"
    Public Sub OnSave(ByVal DocNr As String, ByVal SystemName As String, ByVal DataPath As String) Implements IWorkOrderExporter.OnSave
        Dim LogFileName As String = DataPath & "\Export\" & PluginName & "\Logfile.log"
        Dim XmlFileName As String = DataPath & "\Export\" & PluginName & "\AS-" & DocNr & ".xml"
        Try
            CheckDirectory(DataPath, "Export\" & PluginName)
            Dim w As System.IO.StreamWriter
            If System.IO.File.Exists(LogFileName) = False Then
                w = System.IO.File.CreateText(LogFileName)
                w.Flush()
                w.Close()
            End If
            w = System.IO.File.AppendText(LogFileName)
            w.WriteLine("{0} - {1}", Date.Now.ToString, DocNr)
            w.Flush()
            w.Close()

            Dim j As New JdbNet.Connection
            j.OpenConnection(DataPath & "\DAT")
            CreateDocument(DocNr, j, XmlFileName)
            j.CloseConnection()
            SendFile(XmlFileName)
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub
    Public Sub OnPrint(ByVal DocNr As String, ByVal SystemName As String, ByVal XmlFileName As String) Implements IWorkOrderExporter.OnPrint
    End Sub
    Function CreateDocument(ByVal DocNr As String, ByVal jConn As JdbNet.Connection, ByVal XmlFileName As String) As Boolean
        '?<?xml version="1.0"?>
        '<issue xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        '	xmlns="http://www.eyetea.biz"
        '	xsi:schemaLocation="http://www.eyetea.biz issue.xsd"
        '	issueNumber="AS100017">
        '	<object>
        '		<name>Gemeinnützige Grazer Wohnbaugenossenschaft</name>
        '		<street>Neuholdaugasse 5</street>
        '		<zipCode>8010</zipCode>
        '		<city>Graz</city>
        '		<telephone>+433168027</telephone>
        '	</object>
        '	<billing>
        '		<name>Gemeinnützige Grazer Wohnbaugenossenschaft</name>
        '		<street>Neuholdaugasse 5</street>
        '		<zipCode>8010</zipCode>
        '		<city>Graz</city>
        '		<telephone>+433168027</telephone>
        '	</billing>
        '	<technician>Christian Lischnig</technician>
        '	<appointmentDate>2010-03-11</appointmentDate>
        '	<appointmentTime>10:00:00</appointmentTime>
        '	<customerNumber>200001</customerNumber>
        '	<contactPerson>Fr. Partl</contactPerson>
        '	<workload>Whg. 1 Schneider 031665 Wohnungsabsperrung undicht</workload>
        '</issue>
        Dim rsKopf As JdbNet.Table = jConn.OpenTable("ARBKOPF.JDD", JdbNet.Connection.OpenModes.OPEN_READONLY)
        Dim rsGfall As JdbNet.Table = jConn.OpenTable("GESCHKOPF.JDD", JdbNet.Connection.OpenModes.OPEN_READONLY)
        Dim rsPers As JdbNet.Table = jConn.OpenTable("PERSONAL.PER", JdbNet.Connection.OpenModes.OPEN_READONLY)
        If Not rsKopf.SeekRecord("ID_SCHEINNR", JdbNet.Table.SeekMode.SEEK_EQUAL, DocNr) Then
            Throw New System.Exception("Arbeitsscheindaten nicht gefunden")
        ElseIf Not rsGfall.SeekRecord("GF_PROJNR", JdbNet.Table.SeekMode.SEEK_EQUAL, rsKopf.Field("ID_PROJEKTNR")) Then
            Throw New System.Exception("Geschäftsfalldaten nicht gefunden")
        ElseIf rsKopf.Field("ID_MONTEUR") <> "" AndAlso Not rsPers.SeekRecord("PE_NUMMER", JdbNet.Table.SeekMode.SEEK_EQUAL, rsKopf.Field("ID_MONTEUR")) Then
            Throw New System.Exception("Geschäftsfalldaten nicht gefunden")
        Else
            Dim OutFile As New Xml.XmlTextWriter(XmlFileName, System.Text.Encoding.UTF8)
            OutFile.Indentation = 4
            OutFile.IndentChar = " "
            OutFile.Formatting = Formatting.Indented

            OutFile.WriteStartDocument()
            OutFile.WriteStartElement("issue")
            OutFile.WriteElementString("number", rsKopf.Field("ID_SCHEINNR"))

            OutFile.WriteStartElement("object")
            OutFile.WriteElementString("name", rsKopf.Field("ID_BAUADR_NAM1"))
            OutFile.WriteElementString("street", rsKopf.Field("ID_BAUADR_STR"))
            OutFile.WriteElementString("zipCode", rsKopf.Field("ID_BAUADR_PLZ"))
            OutFile.WriteElementString("city", rsKopf.Field("ID_BAUADR_ORT"))
            OutFile.WriteElementString("telephone", rsKopf.Field("+433168027"))
            OutFile.WriteEndElement()

            OutFile.WriteStartElement("billing")
            OutFile.WriteElementString("name", rsGfall.Field("GF_READR_NAM1"))
            OutFile.WriteElementString("street", rsGfall.Field("GF_READR_STR"))
            OutFile.WriteElementString("zipCode", rsGfall.Field("GF_READR_PLZ"))
            OutFile.WriteElementString("city", rsGfall.Field("GF_READR_ORT"))
            OutFile.WriteElementString("telephone", rsKopf.Field("ID_KONTAKT_TELEFON1"))
            OutFile.WriteEndElement()

            If rsKopf.Field("ID_MONTEUR") <> "" Then
                OutFile.WriteElementString("technician", rsPers.Field("PE_NAME"))
            Else
                OutFile.WriteElementString("technician", "")
            End If
            OutFile.WriteElementString("appointmentDate", rsKopf.Field("ID_WUNDAT"))
            OutFile.WriteElementString("appointmentTime", rsKopf.Field("ID_WUNZEIT"))
            OutFile.WriteElementString("customerNumber", rsKopf.Field("ID_KUNDENNR"))
            OutFile.WriteElementString("contactPerson", rsKopf.Field("ID_KONTAKT_NAME"))
            OutFile.WriteElementString("workload", rsKopf.Field("ID_DURCHZUFUEHREN"))

            OutFile.WriteEndDocument()
            OutFile.Flush()
            OutFile.Close()
            System.Console.WriteLine(System.IO.File.ReadAllText(XmlFileName))
        End If
        rsPers.CloseTable()
        rsGfall.CloseTable()
        rsKopf.CloseTable()
        Return True
    End Function
    Private Sub CheckDirectory(ByVal BasePath As String, ByVal SubPath As String)
        If Not System.IO.Directory.Exists(BasePath & "\" & SubPath) Then
            System.IO.Directory.CreateDirectory(BasePath & "\" & SubPath)
            If Not System.IO.Directory.Exists(BasePath & "\" & SubPath) Then
                Throw New Exception("Verzeichnis '" & BasePath & "\" & SubPath & "' konnte nicht erzeugt werden")
            End If
        End If
    End Sub
    Private Sub SendFile(ByVal FileName As String)
        Dim Boundary As String = "4ca2d96ce99ffc22d5817ee13a141251"
        Dim ContentData As String = System.IO.File.ReadAllText(FileName)

        Dim Content As New System.Text.StringBuilder
        Content.AppendLine("")
        Content.AppendLine("--" & Boundary)
        Content.AppendLine("Content-Disposition: form-data; name='file'; filename='filename.xml'")
        Content.AppendLine("Content-Type: application/xml")
        Content.AppendLine()
        Content.AppendLine(ContentData)
        Content.AppendLine()
        Content.AppendLine("--" & Boundary & "--")
        Content.AppendLine()
        Content.AppendLine()

        Dim oWeb As New System.Net.WebClient()

        'HTTP Authentifizierung
        Dim UserNamePass As String = String.Format("{0}:{1}", UserName, UserPass)
        UserNamePass = Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes(UserNamePass))
        Dim headerValue As String = String.Format("Basic {0}", UserNamePass)
        oWeb.Headers.Add(System.Net.HttpRequestHeader.Authorization, headerValue)
        oWeb.Headers.Add("Content-Type", "multipart/form-data; boundary=" & Boundary)
        oWeb.Headers.Add("offaExportScriptVersion", ScriptVersion)

        Dim bData As Byte() = System.Text.Encoding.UTF8.GetBytes(Content.ToString)
        Dim bytRetData As Byte() = oWeb.UploadData(PlatformUrl, "POST", bData)
        Dim Result As String = System.Text.Encoding.ASCII.GetString(bytRetData)
        If Result.Length > 0 Then
            System.IO.File.WriteAllText(FileName & ".html", Result)
            System.IO.File.WriteAllText(FileName & ".debug.txt", Content.ToString)
            Throw New Exception("Fehler bei Übertragung zum Server aufgetreten")
        End If
    End Sub
End Class
