#!/bin/sh
# Create new file named by current timestamp and description
# -----------------------------------------------------------

OPTION=$1
DESCRIPTION=$2

NOW=$(date +%Y%m%d_%H%M%S)
FOLDER=""

case "$1" in
-m) FOLDER="migration" ;;
-s) FOLDER="seed" ;;
*) echo "Use option -m for migrations and -s for seeds!";exit -1 ;;
esac

FILE="../src/main/resources/db/$FOLDER/V$(date +%Y%m%d_%H%M%S)__$DESCRIPTION.sql"
touch $FILE
