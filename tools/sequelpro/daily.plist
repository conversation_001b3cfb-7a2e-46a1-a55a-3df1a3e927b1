<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SPConnectionFavorites</key>
	<array>
		<dict>
			<key>colorIndex</key>
			<integer>-1</integer>
			<key>database</key>
			<string></string>
			<key>host</key>
			<string>127.0.0.1</string>
			<key>id</key>
			<integer>-7102988734311981954</integer>
			<key>name</key>
			<string>Neubauer Daily</string>
			<key>port</key>
			<string>11082</string>
			<key>socket</key>
			<string></string>
			<key>sshHost</key>
			<string>braavos.bytepoets.local</string>
			<key>sshKeyLocation</key>
			<string></string>
			<key>sshKeyLocationEnabled</key>
			<integer>0</integer>
			<key>sshPort</key>
			<string></string>
			<key>sshUser</key>
			<string>forge</string>
			<key>sslCACertFileLocation</key>
			<string></string>
			<key>sslCACertFileLocationEnabled</key>
			<integer>0</integer>
			<key>sslCertificateFileLocation</key>
			<string></string>
			<key>sslCertificateFileLocationEnabled</key>
			<integer>0</integer>
			<key>sslKeyFileLocation</key>
			<string></string>
			<key>sslKeyFileLocationEnabled</key>
			<integer>0</integer>
			<key>type</key>
			<integer>2</integer>
			<key>useSSL</key>
			<integer>0</integer>
			<key>user</key>
			<string>root</string>
		</dict>
	</array>
</dict>
</plist>
