version: '2'
services:
    db:
        image: mariadb:10.11.4
        ports:
            - "127.0.0.1:11082:3306"
        environment:
            MYSQL_ROOT_PASSWORD: supersikrit
            MYSQL_USER: neubauer
            MYSQL_PASSWORD: sikrit
            <PERSON>_DATABASE: neubauer
        networks:
            - neubauernet
    app:
        build:
            context: "./docker/image-build"
            args:
                proxyport: 443
        ports:
            # debugger (JPDA)
            - "127.0.0.1:11084:5005"
            - "127.0.0.1:8080:8080"
        volumes:
            - ./config/neubauer-config-test.yml:/usr/local/tomcat/conf/neubauer-config.yml
        environment:
            JAVA_OPTS: "-Xdebug -Xmx1024m -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"
        networks:
            - neubauernet
    redis:
        image: redis:5
        ports:
            - "127.0.0.1:11085:6379"
        networks:
            - neubauernet
    filestore:
        image: minio/minio
        ports:
            - "127.0.0.1:19001:9001"
        command: server /export
        environment:
            - MINIO_ACCESS_KEY=EKIEIOSFODNN7EXAMPLE
            - MINIO_SECRET_KEY=wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
        networks:
            - neubauernet
    filewatch:
        build: "./docker/filewatch-s3"
        image: "filewatch-s3"
        environment:
            - S3_HOST=filestore:9000
            - S3_ACCESS_KEY=EKIEIOSFODNN7EXAMPLE
            - S3_SECRET_KEY=wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
            - S3_BUCKET=issue-img
            - S3_REGION=us-east-1
        volumes:
            - ./issue-img:/watch
        networks:
            - neubauernet

networks:
    neubauernet:
        driver: bridge
