Readme
======

## How to start the server

### Install Docker

https://www.docker.com/

### Setup Proxy

Before starting NEB for the first time (or when the `neubauer-backend-proxy-1` does not start because of ssl issues), do:

1. Rename the `/Users/<USER>/ssl` folder to sth else (backup for other applications)
2. Create a new `ssl` folder (for NEB): `mkdir /Users/<USER>/ssl`
3. Copy the ssl certificates from the docker folder into this newly created ssl folder and rename them to:
   - `neubuaer-backend/docker/frontend-proxy-dev/ssl-dev-priv.pem` -> `/Users/<USER>/ssl/developers.bytepoets.net.key`
   - `neubuaer-backend/docker/frontend-proxy-dev/ssl-dev-pub.pem` -> `/Users/<USER>/ssl/developers.bytepoets.net.cer`

To do steps 1, 2 and 3, simply execute [`setup_ssl.sh`](docker/frontend-proxy-dev/setup_ssl.sh) within the `docker/frontend-proxy-dev`-folder

If docker complains about mounting (after starting the webserver): "Are you trying to mount a directory onto a file (or vice-versa)?", execute [`nuke-dev.sh`](nuke-dev.sh).

### Start Webserver

1) In console go to root of the project

2) Enter
`docker-compose -f docker-compose-dev.yml stop app &&  docker-compose -f docker-compose-dev.yml build app && docker-compose -f docker-compose-dev.yml up -d && docker-compose -f docker-compose-dev.yml logs -f`

## How to switch kendo scheduler into full day mode

Run the following JavaScript in your browsers console:
`$("#scheduler .k-scheduler-fullday a").click()`

## How to setup S3 (for images)

1) Get filestore port from docker-compose.yml: e.g. http://localhost:11087

2) Get credentials from docker-compose.yml

3) Create bucket. Get bucket name from neubauer-config.yml, e.g. "docker"

4) Run ImageSeedTests for seeding images.

## Automatic File Syncing from Neubauer Windows Server to Production S3 intsance

On Neubauers Remote Windows Server, they save their scanned `Arbeitsscheine` in a folder. This folder is synced to the production S3 instance.
This happens via a Windows Scheduled Task, that executes every day at 4 a.m.

The command executed daily looks like this: 

```bash
cd C:\Program Files\S3 Browser New
s3browser-cli /file sync NewAbatonS3 D:\Daten\Scan\Arbeitsscheine s3:scandata nchs
```

the task actually executes a batch script, located on the desktop (`C:\syncfotos.bat`).

S3Browser is a Windows UI and CLI tool to communicate with a S3 instance.

`NewAbatonS3` is the name of the local S3 Account / Profile setup in the S3Browser UI.
The profile defines the endpoint and credentials for a S3 instance. 

A shortcut to the UI is located on the desktop of the `DNS\AdminBYTES` user as well.

Sync command doc here: [doc](https://s3browser.com/s3cmd.aspx#:~:text=bucket/docs/*.doc-,sync,-The%20sync%20command)

Remote Access to the Windows Server is done via TeamViewer (see 1PW).
