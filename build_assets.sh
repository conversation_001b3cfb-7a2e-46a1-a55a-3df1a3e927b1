#!/bin/bash

mkdir -p ./src/main/resources/static/assets/css/

cp -r ./src/main/resources/assets/css ./src/main/resources/static/assets
cp -r ./build/sass/main/assets/scss/*.css ./src/main/resources/static/assets/css/
cp -r ./build/sass/main/assets/scss/*.css.map ./src/main/resources/static/assets/css/

mkdir -p ./src/main/resources/static/assets/images/
cp -r ./src/main/resources/assets/images ./src/main/resources/static/assets

mkdir -p ./src/main/resources/static/assets/js/
cp -r ./src/main/resources/assets/js ./src/main/resources/static/assets
