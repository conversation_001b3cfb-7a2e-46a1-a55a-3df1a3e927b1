name: "Build and upload WAR (Staging/Prod)"
run-name: "🚀 #${{ github.run_number }} | 🌍${{ inputs.DEPLOYMENT_TARGET }} 🛠${{ github.ref_name }} 👥${{ github.actor }}"

on:
  workflow_dispatch: # Start manually
    inputs:
      DEPLOYMENT_TARGET:
        type: choice
        description: 'Select deployment target for Ansible.'
        options:
          - 'staging'
          - 'production'
      DEPLOYMENT:
        type: choice
        description: 'Select to deploy self hosted or via github provided runners'
        options:
          - 'github-provided'
          - 'self-hosted'

jobs:
  prerequisites:
    name: "Define Pre-Requisites 🚧"
    runs-on: ${{ inputs.DEPLOYMENT == 'self-hosted' && 'mercury' || 'ubuntu-24.04' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: print inputs
        run: |
          echo "deployment = ${{ inputs.DEPLOYMENT }}"
          echo "deployment target = ${{ inputs.DEPLOYMENT_TARGET }}"
          echo "-----"
          echo "commit message = $(git log -1 --pretty=%B)"
          echo "commit hash = ${{ github.sha }}"
        shell: bash

  build-and-upload:
    name: "Build and upload WAR 🛠"
    runs-on: ${{ inputs.DEPLOYMENT == 'self-hosted' && 'mercury' || 'ubuntu-24.04' }}
    needs: [prerequisites]
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # needed for getting version via tags

      - name: print version
        run: "./gradlew -q printVersion"

      - name: build war
        run: "./gradlew clean war"

      - name: setup ssh
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ inputs.DEPLOYMENT_TARGET == 'staging' && secrets.SSH_NEUBAUER_STAGING || '' }}
            ${{ inputs.DEPLOYMENT_TARGET == 'production' && secrets.SSH_NEUBAUER_PRODUCTION || '' }}

      - name: upload war
        run: "ansible-playbook -i inventory-${{ inputs.DEPLOYMENT_TARGET }} upload-war-playbook.yml"
        working-directory: ansible
