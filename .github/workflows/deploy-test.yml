name: "Build and deploy (Test)"
run-name: "🚀 #${{ github.run_number }} | 🛠${{ github.ref_name }} 👥${{ github.actor }}"

on:
  workflow_dispatch: # Start manually
    inputs:
      DEPLOYMENT:
        type: choice
        description: 'Select to deploy self hosted or via github provided runners'
        options:
          - 'github-provided'
          - 'self-hosted'
      REDEPLOY_PROXY_CONFIG:
        type: boolean
        description: 'Has the proxy config changed? redeploy the proxy config to the server.'
        default: false
      EXECUTE_CERTBOT:
        type: boolean
        description: 'rerun certbot'
        default: false

env:
  ANSIBLE_DIR: "${{ github.workspace }}/ansible"

jobs:
  prerequisites:
    name: "Define Pre-Requisites 🚧"
    runs-on: ${{ inputs.DEPLOYMENT == 'self-hosted' && 'mercury' || 'ubuntu-24.04' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: print inputs
        run: |
          echo "deployment (for test steps etc.) = ${{ inputs.DEPLOYMENT }}"
          echo "-----"
          echo "commit message = $(git log -1 --pretty=%B)"
          echo "commit hash = ${{ github.sha }}"
        shell: bash

  build-and-deploy:
    name: "Build and deploy WAR 🛠"
    defaults:
      run:
        working-directory: ${{ env.ANSIBLE_DIR }}
    runs-on: ${{ inputs.DEPLOYMENT == 'self-hosted' && 'mercury' || 'ubuntu-24.04' }}
    needs: [prerequisites]
    steps:
      - name: checkout
        uses: actions/checkout@v4

      - name: setup ssh
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ inputs.DEPLOYMENT == 'github-provided' && secrets.SSH_GITHUB_CI || '' }}
            ${{ secrets.SSH_NEUBAUER_TEST }}

      - name: add vault secret
        run: "echo ${{ secrets.ANSIBLE_VAULT_SECRET }} > vault"

      - name: update roles
        run: "ansible-galaxy install -f -r roles/requirements.yml"

      - name: deploy
        env:
          COMMIT_HASH: ${{ needs.define-commit-hash.outputs.commit-hash }}
        run: "ansible-playbook -i inventory-test deploy-playbook.yml --vault-password-file vault -e '{\"branch_to_use\":\"${{ github.ref_name }}\", \"redeploy_proxy_config\":${{ inputs.REDEPLOY_PROXY_CONFIG }}, \"execute_certbot\":${{ inputs.EXECUTE_CERTBOT }}}'"

      - name: end-to-end tests
        run: "ansible-playbook -i inventory-test end-to-end-playbook.yml --vault-password-file vault"
