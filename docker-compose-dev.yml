services:
    proxy:
        build: "./docker/frontend-proxy-dev"
        ports:
            - 11080:443
        networks:
            - neubauernet
        environment:
            SSL_PORT: 11080
            BP_SERVER_NAME: "mlo.developers.bytepoets.net"
        volumes:
            - /Users/<USER>/ssl/developers.bytepoets.net.cer:/etc/nginx/ssl-dev-pub.cer
            - /Users/<USER>/ssl/developers.bytepoets.net.key:/etc/nginx/ssl-dev-priv.key
    db:
        image: mariadb:10.11.4
        ports:
            - 11082:3306
        environment:
            MYSQL_ROOT_PASSWORD: supersikrit
            MYSQL_USER: neubauer
            MYSQL_PASSWORD: sikrit
            MYSQL_DATABASE: neubauer
        networks:
            - neubauernet
    app:
        build:
            context: "./docker/image-build"
            args:
                proxyport: 11080
        ports:
            # debugger (JPDA)
            - "11084:5005"
        volumes:
            - ./config/neubauer-config-dev.yml:/usr/local/tomcat/conf/neubauer-config.yml
        environment:
            JAVA_OPTS: "-Xdebug -Xmx512m -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"
        networks:
            - neubauernet
    redis:
        image: redis:5
        ports:
            - "11085:6379"
        networks:
            - neubauernet
    filestore:
        image: minio/minio:RELEASE.2020-01-25T02-50-51Z
        ports:
            - "11087:9000"
        command: server /export
        environment:
            - MINIO_ACCESS_KEY=EKIEIOSFODNN7EXAMPLE
            - MINIO_SECRET_KEY=wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
        networks:
            - neubauernet
    filewatch:
        build: "./docker/filewatch-s3"
        image: "filewatch-s3"
        environment:
            - S3_HOST=filestore:9000
            - S3_ACCESS_KEY=EKIEIOSFODNN7EXAMPLE
            - S3_SECRET_KEY=wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
            - S3_BUCKET=issue-img
            - S3_REGION=us-east-1
        volumes:
            - ./issue-img:/watch
        networks:
            - neubauernet

networks:
    neubauernet:
        driver: bridge
