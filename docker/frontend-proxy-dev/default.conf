gzip on;
gzip_min_length  500;
gzip_proxied     any;
gzip_comp_level 4;
gzip_types  text/css text/javascript text/xml text/plain text/x-component application/javascript application/json application/xml application/rss+xml font/truetype font/opentype application/vnd.ms-fontobject image/svg+xml;
gzip_vary on;
gzip_disable     "msie6";

server {
    listen              443 ssl;
    server_name         __server__name__;
    error_page 497      https://${server_name}:__the__docker__ssl__port__$request_uri;
    charset utf-8;
    ssl_certificate     /etc/nginx/ssl-dev-pub.cer;
    ssl_certificate_key /etc/nginx/ssl-dev-priv.key;
    ssl_protocols       TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers         HIGH:!aNULL:!MD5;

    client_max_body_size 20M;

    proxy_connect_timeout       300;
    proxy_send_timeout          300;
    proxy_read_timeout          300;
    send_timeout                300;

    location / {
        proxy_pass http://app:8080/;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port 443;
    }
}
