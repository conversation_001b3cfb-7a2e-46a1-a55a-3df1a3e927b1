# Execution is only necessary before starting NEB for the first time,
#  OR when the `neubauer-backend-proxy-1` does not start due to ssl issues

cd ../..

mv /Users/<USER>/ssl /Users/<USER>/ssl_but_not_for_NEB
mkdir /Users/<USER>/ssl

cp docker/frontend-proxy-dev/ssl-dev-priv.pem /Users/<USER>/ssl/.
cp docker/frontend-proxy-dev/ssl-dev-pub.pem /Users/<USER>/ssl/.
mv /Users/<USER>/ssl/ssl-dev-priv.pem /Users/<USER>/ssl/developers.bytepoets.net.key
mv /Users/<USER>/ssl/ssl-dev-pub.pem /Users/<USER>/ssl/developers.bytepoets.net.cer
