cp /etc/ssl/openssl.cnf ./openssl.cnf
echo '[ subject_alt_name ]' >> ./openssl.cnf
echo 'subjectAltName = DNS:localhost, DNS:127.0.0.1, DNS:braavos, DNS:braavos.bytepoets.local' >> ./openssl.cnf
openssl req -x509 -nodes -newkey rsa:2048 \
  -config ./openssl.cnf \
  -extensions subject_alt_name \
  -keyout ssl-dev-priv.pem \
  -out ssl-dev-pub.pem \
  -subj '/C=XX/ST=XXXX/L=XXXX/O=XXXX/OU=XXXX/CN=braavos.bytepoets.local/emailAddress=<EMAIL>'