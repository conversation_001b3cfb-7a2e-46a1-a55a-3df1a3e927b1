FROM tomcat:10.1.10-jdk17

ARG proxyport

RUN rm -rf /usr/local/tomcat/webapps/*
ADD libs/neubauer-backend-*.war /usr/local/tomcat/webapps/ROOT.war
ADD context.xml /usr/local/tomcat/conf/context.xml
RUN sed -i -e "s#<Engine name=\"Catalina\" defaultHost=\"localhost\">#<Engine name=\"Catalina\" defaultHost=\"localhost\">\n<Valve className=\"org.apache.catalina.valves.RemoteIpValve\" internalProxies=\".*\" remoteIpHeader=\"x-forwarded-for\" protocolHeader=\"x-forwarded-proto\" httpsServerPort=\"${proxyport}\" />#g" /usr/local/tomcat/conf/server.xml
