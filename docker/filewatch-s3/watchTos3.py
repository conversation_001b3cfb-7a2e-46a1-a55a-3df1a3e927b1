import sys
import os
import time
from watchdog.observers import Observer
from watchdog.events import FileModifiedEvent, FileCreatedEvent
import mimetypes
from minio import Minio
from minio.error import S3Error

s3_host = os.getenv('S3_HOST')
s3_access_key = os.getenv('S3_ACCESS_KEY')
s3_secret_key = os.getenv('S3_SECRET_KEY')
s3_bucket = os.getenv('S3_BUCKET')
s3_region = os.getenv('S3_REGION')

s3 = Minio(s3_host, access_key=s3_access_key, secret_key=s3_secret_key, region=s3_region, secure=False)

bucket_names = [s3_bucket]

found = s3.bucket_exists(s3_bucket)
if found:
    print("Bucket %s exists" % s3_bucket)
else:
    print("Creating bucket %s" % s3_bucket)
    s3.make_bucket(s3_bucket, s3_region)

class go2s3:
    def dispatch(self, event):
        filename = os.path.basename(event.src_path)
        ct = mimetypes.guess_type(filename)[0] or 'binary/octet-stream'
        if type(event) in [FileModifiedEvent, FileCreatedEvent]:
            print("Uploading File: %s" % filename)
            try:
                s3.fput_object(s3_bucket, filename, event.src_path)
            except ResponseError as err:
                print("Uploading File: %s failed" % filename)
                print(err)


if __name__ == "__main__":
    path = os.getenv('WATCH_PATH', '/watch')
    print("Watching path  %s" % path)
    if not os.path.exists(path):
        os.makedirs(path)
    event_handler = go2s3()
    observer = Observer()
    observer.schedule(event_handler, path, recursive=False)
    observer.start()
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
    observer.join()
