buildscript {
    ext {
        kotlin_version = '1.8.22'
        springBootVersion = '3.1.0'
    }
    repositories {
        mavenCentral()
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath("org.jetbrains.kotlin:kotlin-allopen:${kotlin_version}")
    }
}

plugins {
    id 'java'
    id "org.springframework.boot" version "${springBootVersion}"
    id "org.jetbrains.kotlin.plugin.spring" version "1.8.22"
    id "io.miret.etienne.sass" version "1.5.0"
}

apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'org.jetbrains.kotlin.plugin.spring'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'

ext['kotlin.version'] = "${kotlin_version}" // spring boot has old kotlin version pinned

group = 'at.derneubauer'

sourceCompatibility = 17
compileKotlin {
    kotlinOptions.jvmTarget = "17"
    sourceCompatibility = 17
    targetCompatibility = 17
}
compileTestKotlin {
    kotlinOptions.jvmTarget = "17"
    sourceCompatibility = 17
    targetCompatibility = 17
}

sass {
    // dart-sass version to use:
    version = '1.54.0'

    // Directory where to install dart-sass:
    directory = file ("${rootDir}/.gradle/sass")

    // Base URL where to download dart-sass from:
    baseUrl = 'https://github.com/sass/dart-sass/releases/download'
}

compileSass {
    outputDir = project.file ("${buildDir}/sass/main/assets/scss")
    sourceDir = project.file ("${projectDir}/src/main/resources/assets/scss")
}

tasks.register('copyAssets', Exec) {
    commandLine 'sh', './build_assets.sh'
}
copyAssets.dependsOn compileSass

repositories {
    mavenCentral()
}

sourceSets.main.kotlin.srcDirs += "$buildDir/generated/kotlin"

def rawVersion = 'git describe'.execute().text.trim() ?: 'unknown'
def gitDescribeRemoveSuffixRegex = ~/-(.*)/
def versionString = rawVersion == 'unknown' ? 'unknown' : rawVersion.replaceAll(gitDescribeRemoveSuffixRegex, '')

task printVersion {
    group = 'build'
    description = 'Prints raw and pretty version strings'

    doLast {
        println "raw version: $rawVersion"
        println "pretty version string: $versionString"
    }
}

springBoot {
    buildInfo {
        properties {
            version = versionString

            def branchName = System.getenv("GIT_BRANCH") ?:
                    "git rev-parse --abbrev-ref HEAD".execute().text.trim()

            additional = [
                    "git.branch"       : branchName.replace("origin/", ""),
                    "git.commitId"     : "git rev-parse HEAD".execute().text.trim(),
                    "git.commitIdShort": "git rev-parse --short HEAD".execute().text.trim(),
                    "raw.version"      : rawVersion
            ]
        }
    }
}

war {
    enabled = true
    archiveFileName = "neubauer-backend-${versionString}.war"
}

compileKotlin.dependsOn copyAssets
processResources.dependsOn copyAssets

dependencies {
    runtimeOnly 'org.springframework.boot:spring-boot-properties-migrator'
    implementation "org.flywaydb:flyway-core"
    implementation "org.flywaydb:flyway-mysql"
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.2'
    implementation 'org.mybatis:mybatis-typehandlers-jsr310:1.0.2'
    implementation "org.mariadb.jdbc:mariadb-java-client"
    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    providedRuntime "org.springframework.boot:spring-boot-starter-tomcat"
    implementation "org.springframework.boot:spring-boot-starter-thymeleaf"
    implementation "org.springframework.boot:spring-boot-starter-mail"
    implementation "org.springframework.boot:spring-boot-starter-json"
    providedRuntime("org.springframework.boot:spring-boot-devtools") { transitive = false }
    implementation "org.springframework.mobile:spring-mobile-device:1.1.5.RELEASE"
    implementation "nz.net.ultraq.thymeleaf:thymeleaf-layout-dialect"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlin_version}"
    implementation "org.jetbrains.kotlin:kotlin-reflect:${kotlin_version}"
    implementation "org.springframework.boot:spring-boot-starter-data-redis"
    implementation 'org.springframework.session:spring-session-data-redis'
    implementation 'org.apache.commons:commons-lang3:3.9'
    implementation 'org.apache.commons:commons-io:1.3.2'
    implementation 'org.xhtmlrenderer:flying-saucer-pdf-openpdf:9.1.20'
    implementation 'com.amazonaws:aws-java-sdk:1.12.492'
    implementation 'com.sun.xml.bind:jaxb-core'
    implementation 'com.sun.xml.bind:jaxb-impl'
    implementation 'javax.xml.bind:jaxb-api:2.3.1'
    implementation "com.sksamuel.scrimage:scrimage-core:4.0.37"
    implementation 'org.apache.httpcomponents.client5:httpclient5'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client:3.5.4'
    implementation 'org.springframework.boot:spring-boot-starter-webflux:3.5.4'

    testImplementation 'org.yaml:snakeyaml'
    testImplementation "org.springframework.boot:spring-boot-starter-test"
    testImplementation "org.springframework.security:spring-security-test:${dependencyManagement.importedProperties['spring-security.version']}"
    testImplementation "org.junit.jupiter:junit-jupiter:5.10.2"
    testImplementation 'com.natpryce:hamkrest:1.7.0.0'
}

test {
    useJUnitPlatform()
}
